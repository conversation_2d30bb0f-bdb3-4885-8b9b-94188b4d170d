<template>
	<div class="container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch">
					<i class="iconfontCis icon-small-search"></i>
					筛选
				</el-button>
				<el-button @click="handleReset">
					<i class="iconfontCis icon-reset"></i>
					重置
				</el-button>
			</el-form-item>
		</GcFormSimple>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handleChangePage"
			>
				<template #invoiceType="{ row }">
					<span>{{ getfilterNameFn($store.getters.dataList.invoiceType, row.invoiceType) }}</span>
				</template>
				<template #incvoiceInfo="{ row }">
					<el-button
						v-has="'payment_invoice_record-list'"
						type="text"
						size="medium"
						@click="gotoInvoiceDetail(row)"
					>
						查看开票
					</el-button>
				</template>
				<template #payRecord="{ row }">
					<el-button type="text" size="medium" @click="handleRecord(row, 'PaymentRecords')">
						查看缴费记录
					</el-button>
				</template>
			</GcTable>
		</div>
		<!-- 开票信息 -->
		<InvoiceInfo :invoiceDetail="invoiceDetail" :show.sync="showInvoiceInfo" />
	</div>
</template>

<script>
import { getfilterName } from '@/utils'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiInvoiceRecordList2 } from '@/api/userManage.api'
import InvoiceInfo from '../components/invoice-info'
export default {
	components: { InvoiceInfo },
	data() {
		const _t = this
		const pickerOptions = {
			shortcuts: [
				{
					text: '最近一年',
					onClick(picker) {
						const end = new Date(_t.dayjs().endOf('year').format('YYYY-MM-DD'))
						const start = new Date(_t.dayjs().startOf('year').format('YYYY-MM-DD'))
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近三年',
					onClick(picker) {
						const end = new Date(_t.dayjs().endOf('year').format('YYYY-MM-DD'))
						const start = new Date(_t.dayjs().subtract(2, 'year').startOf('year').format('YYYY-MM-DD'))
						picker.$emit('pick', [start, end])
					},
				},
			],
		}
		return {
			formData: {
				invoiceType: '',
				archivesIdentity: '',
				operateDate: [
					this.dayjs().subtract(2, 'year').startOf('year').format('YYYY-MM-DD'),
					this.dayjs().endOf('year').format('YYYY-MM-DD'),
				],
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '开票日期',
					prop: 'operateDate',
					attrs: {
						type: 'daterange',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						style: {
							width: '250px',
						},
						pickerOptions,
					},
				},
				{
					type: 'el-select',
					label: '开票类型',
					prop: 'invoiceType',
					options: this.$store.getters.dataList.invoiceType
						? this.$store.getters.dataList.invoiceType.map(item => {
								return {
									label: item.sortName,
									value: Number(item.sortValue),
								}
						  })
						: [],
					attrs: {
						col: 14,
					},
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '90px',
				rules: {
					operateDate: [{ required: true, message: '请选择开票日期', trigger: 'change' }],
				},
			},
			columns: [
				{
					key: 'invoiceOpenTime',
					name: '开票日期',
					tooltip: true,
				},
				{
					key: 'invoiceNo',
					name: '发票号码',
					tooltip: true,
				},
				{
					key: 'clerk',
					name: '开票人员',
					tooltip: true,
				},
				{
					key: 'exTaxAmount',
					name: '开票金额',
					tooltip: true,
				},
				{
					key: 'invoiceType',
					name: '开票类型',
					tooltip: true,
				},
				{
					key: 'payerName',
					name: '申请用户',
					tooltip: true,
				},
				{
					key: 'incvoiceInfo',
					name: '开票信息',
					fixed: 'right',
					width: 200,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
			invoiceDetail: {},
			showInvoiceInfo: false,
		}
	},
	methods: {
		getfilterNameFn(options = [], val, key = 'sortValue', label = 'sortName') {
			return getfilterName(options, val, key, label)
		},
		async getList() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) {
				return
			}
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
					userId: this.$route.query.userId || '',
				})
				if (formParams.operateDate && formParams.operateDate.length > 1) {
					formParams.invoiceOpenTimeStart =
						this.dayjs(formParams.operateDate[0]).format('YYYY-MM-DD') + ' 00:00:00'
					formParams.invoiceOpenTimeEnd =
						this.dayjs(formParams.operateDate[1]).format('YYYY-MM-DD') + ' 23:59:59'
				}
				delete formParams.operateDate
				const { records, total } = await apiInvoiceRecordList2(formParams)
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleSearch() {
			this.handleChangePage({ page: 1 })
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.handleSearch()
		},
		gotoInvoiceDetail(row) {
			this.$router.push({
				name: 'invoiceRecordDetail',
				query: {
					invoiceRecordId: row.invoiceRecordId,
				},
			})
		},
		// 查看缴费记录、关联账单记录
		handleRecord(row, tabName) {
			const path = row.userType == 3 ? '/meterManage/residentMeterView' : '/meterManage/companyMeterView'
			this.$router.push({
				path,
				query: {
					archivesId: row.archivesId,
					tabName,
				},
			})
		},
	},
}
</script>
