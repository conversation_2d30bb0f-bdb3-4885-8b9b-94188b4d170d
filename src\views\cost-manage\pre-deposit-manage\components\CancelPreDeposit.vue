<template>
	<GcElDialog
		:show="isShow"
		title="取消预存款"
		okText="确认取消"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
	</GcElDialog>
</template>

<script>
import { ruleRequired, validateMaxValue, RULE_FEES } from '@/utils/rules'
import { removeNullParams, trimParams, convertUTC } from '@/utils/index.js'
import { refundBalanceRecord } from '@/api/costManage.api'
export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				refundStatus: '',
				inputBalanceAmount: '',
				payMode: '1',
				archivesId: '',
				operationTime: '',
				reason: '',
			},
			formItems: [
				{
					type: 'el-radio',
					label: '请确认是否已进行线下退款',
					prop: 'refundStatus',
					options: [
						{
							value: 1,
							label: '是',
						},
						{
							value: 0,
							label: '否',
						},
					],
				},
				{
					type: 'el-input',
					label: '退款金额',
					prop: 'inputBalanceAmount',
					attrs: {},
				},
				{
					type: 'el-select',
					label: '渠道',
					prop: 'payMode',
					options: [
						{
							value: '1',
							label: '现金',
						},
					],
					attrs: {
						clearable: false,
					},
				},
				{
					type: 'el-date-picker',
					label: '操作时间',
					prop: 'operationTime',
					attrs: {
						placeholder: '请选择',
						type: 'datetime',
						valueFormat: 'yyyy-MM-dd HH:mm:ss',
					},
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					refundStatus: [ruleRequired('必填')],
					operationTime: [ruleRequired('必填')],
					inputBalanceAmount: [
						ruleRequired('必填'),
						RULE_FEES,
						{
							validator: (rule, value, callback) => {
								if (value <= 0) {
									callback(new Error(`输入值必须大于 0`))
								} else {
									callback()
								}
							},
							trigger: '',
						},
					],
				},
			},
		}
	},
	methods: {
		assignForm({ row }) {
			this.formData.accountId = row.accountId
			this.formData.inputBalanceAmount = row.meterBalanceAmount
			this.formData.operationTime = convertUTC(new Date())
			this.formData.payMode = '1'
			this.formAttrs.rules.inputBalanceAmount.splice(2, 1, validateMaxValue(row.meterBalanceAmount))
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const formParams = trimParams(removeNullParams(this.formData))
			const result = await refundBalanceRecord(formParams).catch(e => {
				this.$message.error(e.message || '取消预存款失败！')
			})
			if (result === null) {
				this.$message.success('取消预存款成功')
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetForm()
			this.$nextTick(() => {
				this.$refs.formRef.clearValidate()
			})
			this.isShow = false
		},
	},
}
</script>
