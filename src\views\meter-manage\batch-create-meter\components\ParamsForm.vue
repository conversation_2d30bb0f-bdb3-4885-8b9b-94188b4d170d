<template>
	<div class="form-container">
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:recordsInfo>
				<h5 class="gap-title">册本信息</h5>
			</template>
			<template v-slot:addressInfo>
				<h5 class="gap-title">地址信息</h5>
			</template>
			<template v-slot:priceInfo>
				<h5 class="gap-title">价格信息</h5>
			</template>
		</GcFormRow>
		<div class="level-v-price" v-show="formData.billingTypeId == 2">
			<h5>阶梯计价策略</h5>
			<el-table
				:data="levelTableData"
				border
				:header-cell-style="{
					background: 'rgb(240, 244, 250)',
					color: '#222',
				}"
			>
				<el-table-column prop="levelName" label="阶梯"></el-table-column>
				<el-table-column prop="waterUsecount" label="用水量（吨）"></el-table-column>
				<el-table-column prop="warnLevelBorder" label="报警阈值（吨）"></el-table-column>
				<el-table-column prop="unitPrice" label="价格（元/吨）"></el-table-column>
			</el-table>
		</div>
		<!-- 附加费 -->
		<div class="level-v-price">
			<h5>附加费</h5>
			<el-table
				:data="billItemTableData"
				border
				:header-cell-style="{
					background: 'rgb(240, 244, 250)',
					color: '#222',
				}"
			>
				<el-table-column prop="itemName" label="附加费用名称"></el-table-column>

				<el-table-column prop="billItemPrice" label="价格（元/吨）"></el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script>
import { getFormItems } from './formItem.js'
import { ruleRequired } from '@/utils/rules'
import { apiGetRegion, apiGetAddressAreaMap } from '@/api/addressManage.api.js'
import { getAlleyMap, getBookListNoAuth } from '@/api/meterReading.api.js'
import { apiEffectivePrice } from '@/api/meterManage.api.js'
import { accAdd } from '@/utils/calc'
export default {
	name: '',
	components: {},
	data() {
		return {
			bookRecords: [],
			formData: {
				orgCode: '',
				bookId: '',
				bookRecord: '',
				alleyId: '',
				regionCode: '',
				streetCode: '',
				communityCode: '',
				takeOver: 0,
				priceId: '',
				priceCode: '',
				priceName: '',
				billingTypeId: '',
				natureName: '',
				singlePrice: '',
			},
			addressAreaCode: '', // 街道/小区/乡镇/村庄
			addressFullName: '', // 完整地址
			formItems: getFormItems(this),
			formAttrs: {
				labelWidth: '110px',
				labelPosition: 'right',
				rules: {
					orgCode: [ruleRequired('必填')],
					bookId: [ruleRequired('必填')],
					alleyId: [ruleRequired('必填')],
					regionCode: [ruleRequired('必填')],
					streetCode: [ruleRequired('必填')],
					communityCode: [ruleRequired('必填')],
					priceCode: [ruleRequired('必填')],
					priceName: [ruleRequired('必填')],
				},
			},
			priceList: [],
			levelTableData: [],
			billItemTableData: [],
		}
	},
	watch: {
		'formData.bookId'(id) {
			if (id) {
				this.formData.bookRecord = this.bookRecords.find(book => book.bookId === id)
				return
			}
			this.formData.bookRecord = ''
		},
		'formData.orgCode'(val) {
			if (!val) {
				this.formData.alleyId = ''
				this.formData.bookId = ''
				this.formData.bookRecord = ''
				this.formItems.find(item => item.prop === 'alleyId').options = []
				this.formItems.find(item => item.prop === 'bookId').options = []
				this.$nextTick(() => {
					this.$refs.formRef.clearValidate()
				})
			}
		},
		'formData.alleyId'(val) {
			if (!val) {
				this.formData.bookId = ''
				this.formData.bookRecord = ''
				this.formItems.find(item => item.prop === 'bookId').options = []
				this.$nextTick(() => {
					this.$refs.formRef.clearValidate()
				})
			}
		},
		'formData.regionCode'(val) {
			if (!val) {
				this.formData.streetCode = ''
				this.formData.communityCode = ''
				this.formItems.find(item => item.prop === 'streetCode').options = []
				this.formItems.find(item => item.prop === 'communityCode').options = []
				this.$nextTick(() => {
					this.$refs.formRef.clearValidate()
				})
			}
		},
		'formData.streetCode'(val) {
			if (!val) {
				this.formData.communityCode = ''
				this.formItems.find(item => item.prop === 'communityCode').options = []
				this.$nextTick(() => {
					this.$refs.formRef.clearValidate()
				})
			}
		},
		'formData.priceCode'(val) {
			if (!val) {
				this.formData.priceName = ''
				this.formData.billingTypeId = ''
				this.formData.natureName = ''
				this.formData.singlePrice = ''
				this.levelTableData = []
				this.billItemTableData = []
				this.$nextTick(() => {
					this.$refs.formRef.clearValidate()
				})
			}
		},
	},
	created() {},
	methods: {
		// 获取坊别
		_getAlleyMap() {
			if (!this.formData.orgCode) {
				return
			}
			const alleySelect = this.formItems.find(item => item.prop === 'alleyId')
			alleySelect.loading = true
			alleySelect.options = []
			this.formData.alleyId = ''
			getAlleyMap({
				orgCode: this.formData.orgCode,
			})
				.then(res => {
					const arr = res.map(item => {
						return {
							value: item.id,
							label: item.alleyName,
							...item,
						}
					})
					alleySelect.options = arr
				})
				.finally(() => {
					alleySelect.loading = false
				})
		},
		// 获取册本
		async _getBookList() {
			const bookNoSelect = this.formItems.find(item => item.prop === 'bookId')
			bookNoSelect.loading = true
			bookNoSelect.options = []
			this.formData.bookId = ''
			this.formData.bookRecord = ''
			const formParams = {
				orgCode: this.formData.orgCode,
				alleyId: this.formData.alleyId,
				// 批量建档仅居民类型表册
				bookType: 2,
			}
			getBookListNoAuth(formParams)
				.then(({ records }) => {
					bookNoSelect.options = records.map(item => {
						return {
							value: item.bookId,
							label: item.bookNo + ' ' + item.meterReadingStaffName,
							...item,
						}
					})
					this.bookRecords = records
				})
				.finally(() => {
					bookNoSelect.loading = false
				})
		},
		// 获取区县数据
		async _getRegionData() {
			const { records } = await apiGetRegion({ regionCode: 2102 })
			const obj = this.formItems.find(item => item.prop === 'regionCode')
			if (!obj) return
			obj.options = records.map(item => {
				return {
					value: item.regionCode,
					label: item.regionName,
				}
			})
		},
		// 获取街道、小区、楼栋下拉数据
		async _getAddressAreaMap(value, key) {
			const data = await apiGetAddressAreaMap({
				parentCode: value,
				status: 1, // 启用
			})
			const obj = this.formItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = data.map(item => {
				return {
					value: item.addressAreaCode,
					label: item.addressAreaName,
					...item,
				}
			})
		},
		async _apiEffectivePrice() {
			try {
				const data = await apiEffectivePrice()

				this.priceList = data
				if (data && data.length > 0) {
					const priceNameObj = this.formItems.find(item => item.prop == 'priceName')
					const priceCodeObj = this.formItems.find(item => item.prop == 'priceCode')
					priceNameObj.options = data.map(item => {
						return {
							label: item.priceName,
							value: item.priceId,
							...item,
						}
					})
					priceCodeObj.options = data.map(item => {
						return {
							label: item.priceCode,
							value: item.priceId,
							...item,
						}
					})
				}
			} catch (error) {
				console.log(error)
			}
		},
		// 改变地址
		handleChangeAddress(value, type) {
			const regionIndex = this.formItems.findIndex(item => item.prop === 'regionCode')
			const streetIndex = this.formItems.findIndex(item => item.prop === 'streetCode')
			const communityIndex = this.formItems.findIndex(item => item.prop === 'communityCode')
			const regionObj = this.formItems[regionIndex].options.find(item => item.value === this.formData.regionCode)
			const streetObj = this.formItems[streetIndex].options.find(item => item.value === this.formData.streetCode)
			const communityObj = this.formItems[communityIndex].options.find(
				item => item.value === this.formData.communityCode,
			)

			if (type === 'regionCode') {
				this.formData.streetCode = ''
				this.formData.communityCode = ''
				this.formItems[streetIndex].options = []
				this.formItems[communityIndex].options = []
				if (value) {
					this._getAddressAreaMap(value, 'streetCode')
					this.addressAreaCode = regionObj.value
					this.addressFullName = regionObj.label
				} else {
					this.addressFullName = ''
					this.addressAreaCode = ''
				}
			} else if (type === 'streetCode') {
				this.formData.communityCode = ''
				this.formItems[communityIndex].options = []
				if (value) {
					this._getAddressAreaMap(value, 'communityCode')
					this.addressFullName = regionObj.label + streetObj.label
					this.addressAreaCode = value
				} else {
					this.addressFullName = regionObj.label
					this.addressAreaCode = regionObj.value
				}
			} else if (type === 'communityCode') {
				this.$refs.formRef.clearValidate('priceCode')
				if (value) {
					this.addressFullName = regionObj.label + streetObj.label + communityObj.label
					this.addressAreaCode = value
					this.formData.takeOver = communityObj.takeOver
				} else {
					this.addressFullName = regionObj.label + streetObj.label
					this.addressAreaCode = streetObj.value
				}
			}
		},
		handleChangePrice(v) {
			if (v) {
				const priceObj = this.priceList.find(item => item.priceId === v)
				this.packageLevelPriceTable(priceObj)
				this.billItemTableData = priceObj.priceBillItemList || []
				this.formData.priceName = priceObj.priceName
				this.formData.natureName = priceObj.natureName
				this.formData.priceCode = priceObj.priceCode
				this.formData.priceId = priceObj.priceId
				this.formData.singlePrice = priceObj.singlePrice
				this.formData.billingTypeId = priceObj.billingTypeId
				const index = this.formItems.findIndex(item => item.prop == 'singlePrice')
				// 阶梯价不展示 价格   单一价 展示价格
				if (this.formData.billingTypeId == 2 && index !== -1) {
					this.formItems.splice(index, 1)
				} else if (this.formData.billingTypeId == 1 && index === -1) {
					this.formItems.push({
						type: 'el-input',
						label: '价格（元/吨）',
						prop: 'singlePrice',
						attrs: {
							col: 10,
							disabled: true,
						},
					})
				}
			} else {
				this.levelTableData = []
				this.billItemTableData = []
				this.formData.priceName = ''
				this.formData.priceCode = ''
				this.formData.natureName = ''
				this.formData.priceId = ''
				this.formData.billingTypeId = ''
				this.formData.singlePrice = ''
			}
		},
		/**
		 * 根据价格详情返回levelBorder和levelPrice字段自定义处理阶梯计价策略表格数据
		 * levelBorder 12|18|26|33|99999999
		 * levelPrice 1.11|1.12|1.13|1.14|1.15
		 * 将levelBorder每个一|分割数据前求和得到类似[0, 12, 30, 56, 89, 99999999]格式的数据
		 */
		packageLevelPriceTable(currentPriceInfo) {
			const enumLevel = {
				0: '一',
				1: '二',
				2: '三',
				3: '四',
				4: '五',
			}
			const levelBorder = currentPriceInfo?.levelBorder
				? currentPriceInfo.levelBorder.split('|').map(o => parseInt(o))
				: []
			const levelPrice = currentPriceInfo?.levelPrice ? currentPriceInfo.levelPrice.split('|') : []
			const warnLevelBorder = currentPriceInfo?.warnLevelBorder ? currentPriceInfo.warnLevelBorder.split('|') : []
			// 计算数据前几个数据之和得到新的list
			const beforeSumLevel = levelBorder.reduce(
				(prev, cur) => {
					const sum = Math.min(prev[prev.length - 1] + cur, 99999999)
					prev.push(sum)
					return prev
				},
				[0],
			)
			this.levelTableData = levelPrice.map((o, index) => {
				return {
					unitPrice: o,
					waterUsecount: `${beforeSumLevel[index]} ~ ${beforeSumLevel[index + 1]}`,
					warnLevelBorder: warnLevelBorder[index]
						? index === levelPrice.length - 1
							? warnLevelBorder[index]
							: accAdd(beforeSumLevel[index], warnLevelBorder[index])
						: '--',
					levelName: `第${enumLevel[index]}阶梯`,
				}
			})
		},
		resetForm() {
			this.$refs.formRef.resetFields()
		},
		submitForm() {
			return this.$refs.formRef.validate()
		},
	},
	mounted() {
		this._getRegionData()
		this._apiEffectivePrice()
	},
}
</script>

<style lang="scss" scoped>
.form-container {
	height: auto;
	max-height: calc(100% - 56px);
	overflow-x: hidden;
}
.level-v-price {
	width: calc(100% / 24 * 20);
	margin-top: 0;
	padding: 0 0 0 110px;
	h5 {
		color: #222222;
		font-size: 14px;
		margin-bottom: 20px;
		font-weight: bold;
	}
	::v-deep .el-table {
		.cell {
			font-size: 14px;
		}
	}
	::v-deep .el-table__empty-block {
		border-bottom: 1px solid #ebeef5 !important;
	}
}
</style>
