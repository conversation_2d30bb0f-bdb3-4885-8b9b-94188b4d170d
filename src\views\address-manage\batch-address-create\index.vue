<template>
	<div class="page-layout" v-loading.fullscreen.lock="fullLoading">
		<div class="page-left">
			<GcModelHeader title="地址选择" :icon="require('@/assets/images/icon/title-address.png')"></GcModelHeader>
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
			<div class="table-container">
				<GcTable
					ref="tableRef"
					:loading="loading"
					:columns="columns"
					:table-data="tableData"
					:page-size="pageData.size"
					:total="pageData.total"
					:current-page="pageData.current"
					showPage
					:current-row-key="currentRow ? currentRow.addressAreaId : ''"
					isHighlightCurrent
					row-key="addressAreaId"
					@click="rowClick"
					@current-page-change="handlePageChange"
				>
					<template v-slot:index="{ $index }">{{ $index + 1 }}</template>
				</GcTable>
			</div>
		</div>
		<div class="page-right">
			<GcModelHeader
				title="详细地址批量创建"
				:icon="require('@/assets/images/icon/title-renter.png')"
			></GcModelHeader>
			<div class="detail-title">地址：{{ currentRow ? currentRow.addressAreaName : '' }}</div>
			<GcFormRow ref="formRowRef" v-model="formRowData" :formItems="formRowItems" :formAttrs="formRowAttrs">
				<template v-slot:unitSlot>
					<el-switch v-model="unitValue" :disabled="!currentRow" @change="handleSwitch('unit')"></el-switch>
				</template>
				<template v-slot:buildingSlot>
					<el-switch
						v-model="buildingValue"
						:disabled="!currentRow"
						@change="handleSwitch('building')"
					></el-switch>
				</template>
			</GcFormRow>
			<div class="btn-group">
				<el-button type="primary" style="width: 100px" round @click="handleSubmit" :disabled="!currentRow">
					确定创建
				</el-button>
			</div>
		</div>
	</div>
</template>

<script>
import {
	RULE_POSITIVEINTEGERONLY_STARTOFZERO,
	RULE_POSITIVEINTEGERONLY,
	ruleMaxLength,
	ruleRequired,
	validateMinValue,
	validateMaxValue,
} from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getFormItems, getFormRowItems } from './formItem.js'
import { apiGetRegion, apiGetAddressAreaMap, apiQueryAreaPage, apiBatchCreateAddress } from '@/api/addressManage.api.js'

export default {
	data() {
		return {
			formData: {},
			formItems: [],
			formAttrs: {
				inline: true,
				rules: {},
			},
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'index',
					name: '序号',
					width: 80,
				},
				{
					key: 'addressAreaName',
					name: '地址描述',
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			currentRow: null,
			// 右侧
			formRowData: {
				startNo1: '',
				count1: '',
				unit1: '单元',
				startNo2: '',
				count2: '',
				startNo3: '',
				count3: '',
				unit3: '室',
			},
			formRowAttrs: {
				rules: {
					startNo1: [RULE_POSITIVEINTEGERONLY_STARTOFZERO, validateMinValue(1), validateMaxValue(99)],
					startNo2: [RULE_POSITIVEINTEGERONLY_STARTOFZERO, validateMinValue(1), validateMaxValue(99)],
					startNo3: [
						ruleRequired('必填'),
						RULE_POSITIVEINTEGERONLY_STARTOFZERO,
						validateMinValue(1),
						validateMaxValue(99),
					],
					count1: [RULE_POSITIVEINTEGERONLY, validateMinValue(1), validateMaxValue(99)],
					count2: [RULE_POSITIVEINTEGERONLY, validateMinValue(1), validateMaxValue(99)],
					count3: [ruleRequired('必填'), RULE_POSITIVEINTEGERONLY, validateMinValue(1), validateMaxValue(99)],
					unit1: [ruleMaxLength(16)],
					unit3: [ruleRequired('必填'), ruleMaxLength(16)],
				},
			},
			unitValue: false,
			buildingValue: false,
			fullLoading: false,
		}
	},
	computed: {
		formRowItems() {
			return getFormRowItems(this)
		},
	},
	mounted() {
		this.formItems = getFormItems(this)
		this._getRegionData()
		this.handlePageChange({ page: 1 })
	},
	methods: {
		// 获取区县数据
		async _getRegionData() {
			const { records } = await apiGetRegion({
				regionCode: 2102,
			})
			const obj = this.formItems.find(item => item.prop === 'regionCode')
			if (!obj) return
			obj.options = records.map(item => {
				return {
					value: item.regionCode,
					label: item.regionName,
				}
			})
		},
		// 获取街道、小区数据  key:streetCode, neighbourhoodCode
		async _getAddressAreaMap(value, key) {
			const data = await apiGetAddressAreaMap({
				parentCode: value,
			})
			const obj = this.formItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = data.map(item => {
				return {
					value: item.addressAreaCode,
					label: item.addressAreaName,
				}
			})
		},
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
				})
				const { total = 0, records } = await apiQueryAreaPage(formParams)
				this.pageData.total = total
				this.tableData = records
				this.currentRow = records.length > 0 ? this.tableData[0] : null
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.handlePageChange({ page: 1 })
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size 
			}
			this.getList()
		},
		rowClick({ row }) {
			this.currentRow = row
		},
		// 右边==》
		handleSwitch(type) {
			if (!this.unitValue && type === 'unit') {
				this.formRowData.startNo1 = ''
				this.formRowData.count1 = ''
				this.formRowData.unit1 = '单元'
				this.$nextTick(() => {
					this.$refs.formRowRef.clearValidate('startNo1')
					this.$refs.formRowRef.clearValidate('count1')
				})
			} else if (!this.buildingValue && type === 'building') {
				this.formRowData.startNo2 = ''
				this.formRowData.count2 = ''
				this.$nextTick(() => {
					this.$refs.formRowRef.clearValidate('startNo2')
					this.$refs.formRowRef.clearValidate('count2')
				})
			}
		},
		async handleSubmit() {
			const fieldsToValidate = []
			if (this.buildingValue) {
				fieldsToValidate.push('startNo2', 'count2')
			}
			if (this.unitValue) {
				fieldsToValidate.push('startNo1', 'count1', 'unit1')
			}
			fieldsToValidate.push('startNo3', 'count3', 'unit3')
			const valid = await this.$refs.formRowRef.validateMultipleFields(fieldsToValidate)
			if (!valid) return

			try {
				this.fullLoading = true
				const formParams = trimParams(removeNullParams(this.formRowData))
				Object.assign(formParams, {
					areaId: this.currentRow?.addressAreaId,
				})
				await apiBatchCreateAddress(formParams)
				this.$message.success('创建成功')
				this.$store.dispatch('tagsView/delView', this.$route).then(() => {
					this.$router.push({
						path: '/addressManage/batchAddressList?type=isNew',
						query: {
							city: this.currentRow?.cityCode,
							region: this.currentRow?.regionCode,
							street: this.currentRow?.streetCode,
							community: this.currentRow?.neighbourhoodCode,
						},
					})
				})
			} catch (error) {
				console.log(error)
			} finally {
				this.fullLoading = false
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.model-header {
	padding-left: 0;
}
.page-left {
	flex: 0 0 50%;
	padding: 0 20px 20px 20px;
	display: flex;
	flex-direction: column;
	overflow: hidden;

	.el-form {
		flex: none;
	}
	.table-container {
		flex: 1;
		overflow: hidden;
	}
}
.page-right {
	padding: 0 20px 20px 20px;
	.detail-title {
		font-size: 14px;
		margin-bottom: 20px;
	}
	.btn-group {
		width: 100%;
		display: flex;
		justify-content: flex-end;
	}
}
</style>
