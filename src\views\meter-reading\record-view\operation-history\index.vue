<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-02 19:07:24
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 19:28:02
-->
<template>
	<div class="container">
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			/>
		</div>
	</div>
</template>

<script>
import { getBookModifyRecordList } from '@/api/meterReading.api.js'

export default {
	name: '',
	components: {},
	data() {
		return {
			loading: false,
			columns: [
				{
					key: 'createTime',
					name: '变更日期',
					tooltip: true,
				},
				{
					name: '操作人员',
					key: 'createStaffName',
					tooltip: true,
				},
				{
					name: '变更信息',
					key: 'afterContent',
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	computed: {},
	created() {
		this.getList()
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		async getList() {
			const bookId = this.$route.query.bookId
			if (!bookId) return

			this.loading = true

			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getBookModifyRecordList({
					bookId,
					current,
					size,
				})
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.container {
	padding: 20px;
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
}

.table-container {
	flex: 1;
	height: 0;
}
</style>
