<template>
	<div class="wrapper">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch(false)">查询</el-button>
				<el-button @click="handleReset">重置</el-button>
			</el-form-item>
		</GcFormSimple>
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			@current-page-change="handlePageChange"
		/>
	</div>
</template>

<script>
import { queryUrgeRecordPage } from '@/api/arrearageManage.api'
import { queryStaffByType } from '@/api/meterReading.api.js'

export default {
	name: 'CallReocrd',
	components: {},
	data() {
		return {
			formData: {
				dateRange: [],
				callUserId: '',
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '催缴日期',
					prop: 'dateRange',
					attrs: {
						type: 'daterange',
						clearable: true,
						valueFormat: 'yyyy-MM-dd',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
					},
				},
				{
					type: 'el-select',
					label: '催缴人',
					prop: 'callUserId',
					options: [],
					attrs: {
						clearable: true,
						placeholder: '请选择催缴人',
					},
				},
			],
			formAttrs: { inline: true, labelWidth: '80px' },

			loading: false,
			tableData: [],
			columns: [
				{
					key: 'callPaymentTime',
					name: '催缴时间',
					tooltip: true,
				},
				{
					key: 'callUserName',
					name: '催缴人',
					tooltip: true,
				},
				{
					key: 'billNo',
					name: '账单编号',
					tooltip: true,
				},
				{
					key: 'archivesNo',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'fullAddressName',
					name: '地址',
					tooltip: true,
				},
				{
					key: 'billUserName',
					name: '用户名称',
					tooltip: true,
				},
				{
					key: 'arrearsAmount',
					name: '总应缴金额',
					tooltip: true,
				},
				{
					key: 'remark',
					name: '催缴备注',
					tooltip: true,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},

	activated() {
		this.getStaffMapData()
		this.getList()
	},
	methods: {
		// 获取催缴人下拉框数据
		async getStaffMapData() {
			try {
				const res = await queryStaffByType({
					staffType: 2,
				})
				this.formItems[1].options = res.map(item => {
					const { staffId, staffName } = item
					return {
						value: staffId,
						label: staffName,
					}
				})
			} catch (error) {
				console.error(error)
				this.formItems[1].options = []
			}
		},
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.getList(1)
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { dateRange, ...rest } = this.formData
				const { total = 0, records = [] } = await queryUrgeRecordPage({
					size,
					current,
					startCallPaymentTime: dateRange?.length > 0 ? dateRange[0] : '',
					endCallPaymentTime: dateRange?.length > 0 ? dateRange[1] : '',
					...rest,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	padding: 20px;
}
</style>
