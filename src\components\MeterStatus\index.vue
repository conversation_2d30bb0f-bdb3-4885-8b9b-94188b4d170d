<template>
	<div class="meter-status-wrapper">
		<!--  :class="{
        active: !isBlank(info.valveStatus),
        open: info.valveStatus == 1,
        close: info.valveStatus === 0 || info.valveStatus == 6,
      }" -->
		<div
			class="status-item"
			:style="`color: ${!isBlank(info.valveStatus) ? valveStateColor[info.valveStatus] : '#AAB2C1'}`"
		>
			<span class="status-key">阀门：</span>
			<span
				class="status-icon iconfont"
				:class="!isBlank(info.valveStatus) ? valveStateIcon[info.valveStatus] : 'icon-damen-hui'"
				:style="`color: ${!isBlank(info.valveStatus) ? valveStateColor[info.valveStatus] : '#AAB2C1'}`"
			></span>
			{{ !isBlank(info.valveStatus) ? valveStateEnum[info.valveStatus] : '无' }}
		</div>
		<div class="status-item" :class="{ active: !isBlank(info.voltage) }">
			<span class="status-key">电压：</span>
			<span class="status-icon iconfont icon-voltage"></span>
			{{ !isBlank(info.voltage) ? info.voltage + 'V' : '无' }}
		</div>
		<div class="status-item" :class="{ active: !isBlank(info.signalGrade) }">
			<span class="status-key">信号：</span>
			<span class="status-icon iconfont icon-signal"></span>
			{{ !isBlank(info.signalGrade) ? toFixed(info.signalGrade * 100, 2) + '%' : '无' }}
		</div>
		<div class="status-item" :class="{ active: !isBlank(info.temperature) }">
			<span class="status-key">温度：</span>
			<span class="status-icon iconfont icon-temperate"></span>
			{{ !isBlank(info.temperature) ? Number(info.temperature).toFixed(1) + '℃' : '无' }}
		</div>
		<div class="status-item" :class="{ active: !isBlank(info.pressure) }">
			<span class="status-key">压强：</span>
			<span class="status-icon iconfont icon-pressure"></span>
			{{ !isBlank(info.pressure) ? Number(info.pressure).toFixed(1) + 'kpa' : '无' }}
		</div>
	</div>
</template>

<script>
import { isBlank } from '@/utils/validate.js'
import valveStateEnum from '@/mixin/valveStateEnum.js'
import { toFixed } from '@/utils/index.js'

const mockMeterStatus = {
	valveStatus: 0,
	pressure: 0,
	signalGrade: 0.8125,
	temperature: -9999,
	voltage: 5.99,
}
export default {
	name: 'MeterStatus',
	props: {
		info: {
			type: Object,
			default: () => mockMeterStatus,
		},
	},
	mixins: [valveStateEnum],
	methods: {
		isBlank,
		toFixed,
	},
}
</script>
<style lang="scss" scoped>
.meter-status-wrapper {
	display: flex;
	flex-wrap: wrap;
	background: white;
	.status-item {
		margin-bottom: 33px;
		flex: 0 0 33.33%;
		font-size: 14px;
		color: #c9c9c9;
		display: flex;
		align-items: center;
		line-height: 24px;
		.status-key {
			flex: 0 0 42px;
			font-size: 14px;
			font-weight: 500;
			color: #222222;
		}
		.status-icon {
			padding: 0 14px;
			font-size: 24px;
			// display: inline-block;
			// width: 24px;
			// height: 24px;
			// border: 1px solid;
			color: #aab2c1;
		}
		&.active {
			font-size: 14px;
			color: #4e4e4e;
			.icon-voltage {
				color: #12b3c7;
			}
			.icon-signal {
				color: #12b3c7;
			}
			.icon-pressure {
				color: #12b3c7;
			}
			.icon-temperate {
				color: #12b3c7;
			}
		}
	}
	.active.open {
		.icon-state-of-valve {
			color: #12b3c7;
		}
	}
	.active.close {
		.icon-state-of-valve {
			color: #ec6b60;
		}
	}
}
</style>
