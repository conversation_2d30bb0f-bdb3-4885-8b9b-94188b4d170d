<template>
	<gc-el-dialog
		:show="isShow"
		title="状态变更"
		custom-top="120px"
		width="600px"
		@open="handleOpen"
		@close="handleClose"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { ruleMaxLength } from '@/utils/rules'
import { modifyStatusValueArr } from '../water-meter-list/options'
import { modifyMeterStatus } from '@/api/waterMeter.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		isBatch: {
			type: Boolean,
			default: false,
		},
		// 当前操作数据的状态
		currentMeterStatus: {
			require: true,
			type: [String, Number],
		},
		// 选中/当前 数据对象数组
		data: {
			type: Array,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		afterMeterStatusOptions() {
			return (
				this.$store.getters?.dataList?.meterStatus
					?.map(item => {
						const { sortName: label, sortValue: value } = item
						// 过滤出可以进行状态变更的状态数据 且 移除当前状态下拉数据
						if (modifyStatusValueArr.includes(value)) {
							return {
								label,
								value,
							}
						}
					})
					.filter(Boolean) || []
			)
		},
	},
	data() {
		return {
			formData: {
				// 批量不展示
				meterNo: '',

				meterStatus: '',
				afterMeterStatus: '',
				operatorDate: '',
				operatorPerson: '',
				reason: '',
			},
			formItems: [
				{
					hide: this.isBatch,
					type: 'el-input',
					label: '水表编号',
					prop: 'meterNo',
					attrs: {
						col: 24,
						disabled: true,
					},
				},
				{
					type: 'el-select',
					label: '当前水表状态',
					prop: 'meterStatus',
					options: this.$store.getters?.dataList?.meterStatus.map(item => {
						return {
							label: item.sortName,
							value: item.sortValue,
						}
					}),
					attrs: {
						col: 24,
						disabled: true,
					},
				},
				{
					type: 'el-select',
					label: '变更后水表状态',
					prop: 'afterMeterStatus',
					options: [],
					attrs: {
						col: 24,
						placeholder: '请选择变更后水表状态',
					},
				},
				{
					type: 'el-date-picker',
					label: '操作时间',
					prop: 'operatorDate',
					attrs: {
						col: 24,
						valueFormat: 'yyyy-MM-dd',
						placeholder: '请选择操作时间',
					},
				},
				{
					type: 'el-input',
					label: '操作人员',
					prop: 'operatorPerson',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入操作人员',
					},
				},
				{
					type: 'el-input',
					label: '操作原因',
					prop: 'reason',
					attrs: {
						col: 24,
						type: 'textarea',
						clearable: true,
						placeholder: '请输入操作原因',
						maxlength: '64',
						showWordLimit: true,
						autosize: {
							minRows: 4,
							maxRows: 8,
						},
					},
				},
			],
			formAttrs: {
				labelWidth: '130px',
				labelPosition: 'top',
				rules: {
					operatorDate: [
						{
							required: true,
							message: '请选择操作时间',
							trigger: 'change',
						},
					],
					afterMeterStatus: [
						{
							required: true,
							message: '请选择变更后水表状态',
							trigger: 'change',
						},
					],
					operatorPerson: [ruleMaxLength(30, '操作人员')],
				},
			},
		}
	},
	watch: {
		currentMeterStatus(newVal) {
			this.formItems[2].options = this.afterMeterStatusOptions.filter(item => item.value !== newVal)
		},
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				if (this.isBatch) {
					await modifyMeterStatus({
						meterIds: this.data.map(item => item.meterId),
						...this.formData,
					})
				}
				// else {
				// await enableMeter({
				//   meterId: this.data.meterId,
				//   ...this.formData,
				// });
				// }
				this.$message.success('状态变更成功')
				this.$emit('success')
				this.isShow = false
			}
		},
		handleOpen() {
			this.formData.meterStatus = `${this.data[0]?.meterStatus}` ?? ''
			if (!this.isBatch) {
				this.formData.meterNo = this.data?.meterNo ?? ''
				return
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.el-form-item--small.el-form-item {
		margin-bottom: 12px;
	}
}
</style>
