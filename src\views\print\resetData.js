import { isBlank, isString } from '@/utils/validate.js'
export default {
	props: {
		// 弹窗显示/隐藏
		show: {
			type: Boolean,
			default: false,
		},
		// 详情数据
		detailData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		innerVisible: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		// 档案ID
		archivesId() {
			let id = ''
			if (this.detailData && Object.keys(this.detailData).length > 0) {
				if (!isBlank(this.detailData.archives.archivesId)) {
					id = this.detailData.archives.archivesId
				}
			}
			return id
		},
		// 表具ID
		meterId() {
			let id = ''
			if (this.detailData && Object.keys(this.detailData).length > 0) {
				if (!isBlank(this.detailData.meter.meterId)) {
					id = this.detailData.meter.meterId
				}
			}
			return id
		},
	},
	methods: {
		// 重置表单
		resetForm() {
			this.$refs.ruleForm.resetFields()
			this.ruleForm = this.$options.data().ruleForm
			this.innerVisible = false
		},
		// 添加ruleForm参数
		handleParameter(ruleForm, archivesIdFlag) {
			// ruleForm 表单数据
			// archivesIdFlag 是否判断档案ID必填
			let obj = {}
			if (archivesIdFlag) {
				if (this.archivesId === '') {
					this.$message.error('档案ID为空，请刷新重试')
					return false
				}
				obj['archivesId'] = this.archivesId
			}
			this.$listeners.controlLoading(true)
			for (let key in ruleForm) {
				if (!isBlank(ruleForm[key])) {
					if (isString(ruleForm[key])) {
						obj[key] = ruleForm[key].trim()
					} else {
						obj[key] = ruleForm[key]
					}
				}
			}
			return obj
		},
		// 处理业务办理成功逻辑
		handleBusinessSuccess(message) {
			this.resetForm()
			this.innerVisible = false
			this.$message.success(message)
			this.$listeners.refresh() //刷新详情数据
		},
	},
}
