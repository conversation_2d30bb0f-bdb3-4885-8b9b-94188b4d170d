export function getFormItems(_this) {
	return [
		{
			type: 'el-input',
			label: '小区/村庄',
			prop: 'neighbourhoodName',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: _this.editType === 'batchAdd' ? '起始楼栋号' : '楼栋号',
			prop: 'buildingNo',
			attrs: {
				placeholder: '例如：1',
			},
		},
		{
			type: 'el-input',
			label: '数量',
			prop: 'num',
			attrs: {
				placeholder: '例如：2',
			},
		},
		{
			type: 'el-input',
			label: '单位名称',
			prop: 'unit',
			attrs: {
				placeholder: '例如：幢',
			},
		},
	]
}
