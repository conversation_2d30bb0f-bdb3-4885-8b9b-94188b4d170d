<template>
	<GcElDialog :show="isShow" title="表具信息" :showFooter="false" @close="isShow = false">
		<GcGroupDetail :data="meterInfo"></GcGroupDetail>
	</GcElDialog>
</template>

<script>
import { getfilterName } from '@/utils'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		meterInfo() {
			const list = [
				{
					key: '水表编号',
					value: '--',
					field: 'meterNo',
				},
				{
					key: '水表仓库编号',
					value: '--',
					field: 'meterWarehouseCode',
				},
				{
					key: '水表标号',
					value: '--',
					field: 'baseMeterNo',
				},
				{
					key: '表具厂商',
					value: '--',
					field: 'manufacturerName',
				},
				{
					key: '表具类型',
					value: '--',
					field: 'meterTypeName',
				},
				{
					key: '表具型号',
					value: '--',
					field: 'meterModel',
				},
				{
					key: '防盗编号',
					value: '--',
					field: 'antiTheftCode',
				},
				{
					key: '量程',
					value: '--',
					field: 'ranges',
				},
				{
					key: '水表状态',
					value: '--',
					field: 'meterStatus',
				},
				{
					key: '指针数',
					value: '--',
					field: 'meterReading',
				},
				{
					key: '上次抄表时间',
					value: '--',
					field: 'meterReadingDate',
				},
				{
					key: '首次安装时间',
					value: '--',
					field: 'installationDate',
				},
				{
					key: '装表位置',
					value: '--',
					field: 'installPosition',
				},
				{
					key: '表井位置',
					value: '--',
					field: 'tableWellLocation',
				},
			]
			const extractedData = Object.assign({}, ...Object.values(this.tabData))
			const getValue = (field, value) => {
				const { meterStatus = [], installPosition = [] } = this.$store.getters.dataList || {}

				switch (field) {
					case 'meterStatus':
						return getfilterName(meterStatus, value, 'sortValue', 'sortName')
					case 'installPosition':
						return getfilterName(installPosition, value, 'sortValue', 'sortName')
					default:
						return value
				}
			}
			list.forEach(item => {
				item.value = getValue(item.field, extractedData[item.field])
			})
			return {
				list,
			}
		},
	},
}
</script>
