export function getFormItems(_this) {
	const arr = [
		{
			type: 'el-select',
			label: '区/县',
			prop: 'regionCode',
			options: [],
			attrs: {
				style: {
					width: '150px',
				},
			},
			events: {
				change: value => {
					const streetIndex = _this.formItems.findIndex(item => item.prop === 'streetCode')
					const neighbourhoodIndex = _this.formItems.findIndex(item => item.prop === 'neighbourhoodCode')
					_this.formItems[streetIndex].options = []
					_this.formItems[neighbourhoodIndex].options = []
					_this.formData.streetCode = ''
					_this.formData.neighbourhoodCode = ''
					if (value) {
						_this._getAddressAreaMap(value, 'streetCode')
					}
					_this.handleSearch()
				},
			},
		},
		{
			type: 'el-select',
			label: '街道/乡镇',
			prop: 'streetCode',
			options: [],
			attrs: {
				style: {
					width: '180px',
				},
			},
			events: {
				change: value => {
					const neighbourhoodIndex = _this.formItems.findIndex(item => item.prop === 'neighbourhoodCode')
					_this.formItems[neighbourhoodIndex].options = []
					_this.formData.neighbourhoodCode = ''
					if (value) {
						_this._getAddressAreaMap(value, 'neighbourhoodCode')
					}
					_this.handleSearch()
				},
			},
		},
		{
			type: 'el-select',
			label: '小区/村庄',
			prop: 'neighbourhoodCode',
			options: [],
			attrs: {
				style: {
					width: '180px',
				},
			},
			events: {
				change: () => {
					_this.handleSearch()
				},
			},
		},
	]
	arr.forEach(item => {
		_this.$set(_this.formData, item.prop, '')
	})
	return arr
}

export function getFormRowItems(_this) {
	const arr = [
		{
			type: 'slot',
			label: '单元',
			slotName: 'unitSlot',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-input',
			label: '起始号',
			prop: 'startNo1',
			attrs: {
				col: 8,
				disabled: !_this.unitValue,
			},
		},
		{
			type: 'el-input',
			label: '数量',
			prop: 'count1',
			attrs: {
				col: 8,
				disabled: !_this.unitValue,
			},
		},
		{
			type: 'el-input',
			label: '单位名称',
			prop: 'unit1',
			attrs: {
				col: 8,
				disabled: !_this.unitValue,
			},
		},
		{
			type: 'slot',
			label: '楼层',
			slotName: 'buildingSlot',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-input',
			label: '起始号',
			prop: 'startNo2',
			attrs: {
				col: 8,
				disabled: !_this.buildingValue,
			},
		},
		{
			type: 'el-input',
			label: '数量',
			prop: 'count2',
			attrs: {
				col: 8,
				disabled: !_this.buildingValue,
			},
		},
		{
			type: 'slot',
			label: '房间',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-input',
			label: '起始号',
			prop: 'startNo3',
			attrs: {
				col: 8,
				disabled: !_this.currentRow,
			},
		},
		{
			type: 'el-input',
			label: '数量',
			prop: 'count3',
			attrs: {
				col: 8,
				disabled: !_this.currentRow,
			},
		},
		{
			type: 'el-input',
			label: '单位名称',
			prop: 'unit3',
			attrs: {
				col: 8,
				disabled: !_this.currentRow,
			},
		},
	]
	return arr
}
