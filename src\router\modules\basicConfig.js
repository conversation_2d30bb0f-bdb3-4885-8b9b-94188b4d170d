import Layout from '@/layout'

export default [
	{
		path: '/basicConfig',
		name: 'BasicConfig',
		component: Layout,
		redirect: '/basicConfig/filialeManage',
		meta: {
			title: '配置',
			icon: 'icon-cis_yj_jichupeizhi',
			permissions: [
				'cpm_businessHall_queryBusinessHallList',
				'cpm_businessHall_updateBusinessHallBaseInfo',
				'cpm_businessHall_updateBusinessHallCode',
				'cpm_businessHall_updateBusinessHallDigits',
				'cpm_businessHall_updateAccountOpeningDate',
				'cpm_alley_queryAlleyPage',
				'cpm_alley_addAlley',
				'cpm_alley_updateAlley',
				'cpm_alley_deleteAlley',
				'cpm_balanceSheetDate_queryBalanceSheetDateList',
				'cpm_balanceSheetDate_addBalanceSheetDate',
				'cpm_balanceSheetDate_updateBalanceSheetDate',
				'cpm_bill_item_query',
				'cpm_bill_item_add',
				'cpm_bill_item_modify',
				'cpm_price_nature_list',
				'cpm_price_nature_add',
				'cpm_price_nature_modify',
				'cpm_prices_list',
				'cpm_prices_add',
				'cpm_prices_adjust',
				'cpm_prices_modify',
				'cpm_prices_disable',
				'cpm_prices_enable',
				'cpm_prices_delete',
				// 工种配置
				'cpm_meterReadingStaff_getWorkStaffPage',
			],
		},
		children: [
			{
				path: 'filialeManage',
				name: 'FilialeManage',
				component: () => import('@/views/basic-config/filiale-manage/index.vue'),
				meta: {
					title: '分公司管理',
					keepAlive: true,
					icon: 'icon-cis_ej_fengongsi',
					permissions: [
						'cpm_businessHall_queryBusinessHallList',
						'cpm_businessHall_updateBusinessHallBaseInfo',
						'cpm_businessHall_updateBusinessHallCode',
						'cpm_businessHall_updateBusinessHallDigits',
						'cpm_businessHall_updateAccountOpeningDate',
					],
				},
			},
			{
				path: 'alleyManage',
				name: 'AlleyManage',
				component: () => import('@/views/basic-config/alley-manage/index.vue'),
				meta: {
					title: '坊别管理',
					keepAlive: true,
					icon: 'icon-cis_ej_fangbieguanli',
					permissions: [
						'cpm_alley_queryAlleyPage',
						'cpm_alley_addAlley',
						'cpm_alley_updateAlley',
						'cpm_alley_deleteAlley',
					],
				},
			},
			{
				path: 'accountDayConfig',
				name: 'AccountDayConfig',
				component: () => import('@/views/basic-config/account-day-config/index.vue'),
				meta: {
					title: '结账日配置',
					keepAlive: true,
					icon: 'icon-cis_ej_jiezhangri',
					permissions: [
						'cpm_balanceSheetDate_queryBalanceSheetDateList',
						'cpm_balanceSheetDate_addBalanceSheetDate',
						'cpm_balanceSheetDate_updateBalanceSheetDate',
					],
				},
			},
			{
				path: 'accountMaintenance',
				name: 'AccountMaintenance',
				component: () => import('@/views/basic-config/account-maintenance/index.vue'),
				meta: {
					title: '账项维护',
					keepAlive: true,
					icon: 'icon-cis_ej_zhangdanguanli',
					permissions: ['cpm_bill_item_query', 'cpm_bill_item_add', 'cpm_bill_item_modify'],
				},
			},
			{
				path: 'waterNatureManage',
				name: 'WaterNatureManage',
				component: () => import('@/views/basic-config/water-nature-manage/index.vue'),
				meta: {
					title: '用水性质管理',
					keepAlive: true,
					icon: 'icon-cis_ej_yongshuixingzhi',
					permissions: ['cpm_price_nature_list', 'cpm_price_nature_add', 'cpm_price_nature_modify'],
				},
			},
			{
				path: 'priceManage',
				name: 'PriceManage',
				component: () => import('@/views/price-manage/index.vue'),
				meta: {
					title: '价格管理',
					keepAlive: true,
					icon: 'icon-cis_ej_jiageguanli',
					permissions: [
						'cpm_prices_list',
						'cpm_prices_add',
						'cpm_prices_adjust',
						'cpm_prices_modify',
						'cpm_prices_disable',
						'cpm_prices_enable',
						'cpm_prices_delete',
					],
				},
			},
			{
				path: 'workConfig',
				name: 'WorkConfig',
				component: () => import('@/views/basic-config/work-config/index.vue'),
				meta: {
					title: '工种配置',
					keepAlive: true,
					icon: 'icon-cis_ej_gzpz',
					permissions: ['cpm_meterReadingStaff_getWorkStaffPage'],
				},
			},
			{
				path: 'collectionBankManage',
				name: 'CollectionBankManage',
				component: () => import('@/views/basic-config/collection-bank-manage/index.vue'),
				meta: {
					title: '托收银行管理',
					keepAlive: true,
					icon: 'icon-erji-danganguanli',
					permissions: [],
				},
				hidden: true,
			},
			{
				path: 'collectionOrderConfig',
				name: 'CollectionOrderConfig',
				component: () => import('@/views/basic-config/collection-order-config/index.vue'),
				meta: {
					title: '托收订单配置',
					keepAlive: true,
					icon: 'icon-erji-danganguanli',
					permissions: [],
				},
				hidden: true,
			},
			{
				path: 'generalDictConfig',
				name: 'GeneralDictConfig',
				component: () => import('@/views/basic-config/general-dict-config/index.vue'),
				meta: {
					title: '通用字典配置',
					keepAlive: true,
					icon: 'icon-erji-danganguanli',
					permissions: [],
				},
				hidden: true,
			},
		],
	},
]
