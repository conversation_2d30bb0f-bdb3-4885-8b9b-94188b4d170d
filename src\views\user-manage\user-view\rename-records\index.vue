<template>
	<div class="container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch">
					<i class="iconfontCis icon-small-search"></i>
					筛选
				</el-button>
				<el-button @click="handleReset">
					<i class="iconfontCis icon-reset"></i>
					重置
				</el-button>
			</el-form-item>
		</GcFormSimple>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handleChangePage"
			></GcTable>
		</div>
	</div>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiModifyUserNameRecord } from '@/api/userManage.api'
export default {
	data() {
		return {
			formData: {
				operateDate: [
					this.dayjs().startOf('year').format('YYYY-MM-DD'),
					this.dayjs().endOf('year').format('YYYY-MM-DD'),
				],
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '业务操作时间',
					prop: 'operateDate',
					attrs: {
						type: 'daterange',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						style: {
							width: '250px',
						},
					},
				},
			],
			formAttrs: {
				inline: true,
			},
			columns: [
				{
					key: 'createTime',
					name: '业务操作时间',
					tooltip: true,
				},
				{
					key: 'oldUserName',
					name: '更名前用户名称',
					tooltip: true,
				},
				{
					key: 'newUserName',
					name: '更名后用户名称',
					tooltip: true,
				},
				{
					key: 'operatorPerson',
					name: '业务操作人',
					tooltip: true,
				},
				{
					key: 'operatorReason',
					name: '更名原因',
					tooltip: true,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
		}
	},
	methods: {
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
					userId: this.$route.query.userId || '',
				})
				if (formParams.operateDate && formParams.operateDate.length > 1) {
					formParams.modifyTimeStart = this.dayjs(formParams.operateDate[0]).format('YYYY-MM-DD')
					formParams.modifyTimeEnd = this.dayjs(formParams.operateDate[1]).format('YYYY-MM-DD')
					delete formParams.operateDate
				} else {
					delete formParams.operateDate
				}
				const { records, total } = await apiModifyUserNameRecord(formParams)
				this.tableData = records.map(item => {
					const logDetail = JSON.parse(item.logDetail)
					return {
						...item,
						...logDetail,
					}
				})
				this.pageData.total = total
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleSearch() {
			this.handleChangePage({ page: 1 })
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.handleSearch()
		},
	},
}
</script>
