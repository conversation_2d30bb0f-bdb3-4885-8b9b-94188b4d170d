export const getFormItem = () => {
	return [
		{
			type: 'el-input',
			label: '表卡编号',
			prop: 'archivesIdentity',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-date-picker',
			label: '销账调整时间',
			prop: 'payTime',
			attrs: {
				type: 'datetime',
				valueFormat: 'yyyy-MM-dd HH:mm:ss',
			},
		},
		{
			type: 'el-input',
			label: '销账调整原因',
			prop: 'reason',
			attrs: {
				type: 'textarea',
				maxlength: '64',
				showWordLimit: true,
				autosize: {
					minRows: 2,
					maxRows: 4,
				},
			},
		},
	]
}
