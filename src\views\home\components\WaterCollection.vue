<template>
	<CardContainer :cardDetail="cardDetail">
		<div class="content-box">
			<div class="top">
				<el-cascader
					v-model="selValue"
					:options="natureOptions"
					filterable
					:show-all-levels="false"
					:placeholder="`请选择`"
					:props="{
						label: 'natureName',
						value: 'priceNatureId',
						emitPath: false,
						checkStrictly: true,
					}"
					@visible-change="handleChange"
				/>
				<el-radio-group v-model="tabValue" @input="getList">
					<el-radio-button :label="0">月</el-radio-button>
					<el-radio-button :label="1">年</el-radio-button>
				</el-radio-group>
			</div>
			<div class="bottom">
				<Gcline1
					:dataZoom="{
						show: true,
						start: 0,
						end: 30,
						zoomLock: true,
						disabled: true,
					}"
					:xAxis="{
						data: chartOptions.xData,
						axisLabel: {
							interval: 0,
							rotate: 40,
						},
					}"
					:yAxis="{
						name: chartOptions.unit,
					}"
					:seriesData="chartOptions.seriesData"
				/>
			</div>
		</div>
	</CardContainer>
</template>

<script>
import CardContainer from './CardContainer.vue'
import { apiGetWaterPayCostChart } from '@/api/home.api'
export default {
	name: 'WaterCollection',
	components: { CardContainer },
	props: {
		natureOptions: {
			type: Array,
			default: () => [],
		},
		orgCode: {
			type: String,
			default: '',
		},
	},
	watch: {
		natureOptions: {
			handler: function (arr) {
				if (arr && arr.length) {
					this.selValue = Number(arr[0].priceNatureId)
				}
			},
			deep: true,
		},
		orgCode(v) {
			if (v) {
				if (!this.$has('cpm_home_charts_waterPayCostChart')) {
					return
				}
				this.getList()
			}
		},
		'cardDetail.activeTab'() {
			this.getList()
		},
	},
	data() {
		return {
			cardDetail: {
				titleList: [
					{
						label: '当月水费回收率',
						value: '--',
						unit: '%',
					},
					{
						label: '全年水费回收率',
						value: '--',
						unit: '%',
					},
				],
				activeTab: '3',
				tabList: [
					{
						name: '居民表卡',
						value: '3',
					},
					{
						name: '企业表卡',
						value: '4',
					},
				],
				bg: require('@/assets/images/bg/home-bg3.png'),
				name: '水费回收率',
			},
			tabValue: 0,
			selValue: '',
			chartOptions: {
				unit: '%',
				xData: [],
				seriesData: [],
				seriesName: '',
			},
		}
	},
	methods: {
		handleChange(v) {
			if (v === false) {
				this.chartOptions.seriesData = []
				this.getList()
			}
		},
		// 递归查找
		findNatureItem(list, selValue) {
			for (const item of list) {
				if (item.priceNatureId == selValue) {
					return item
				}
				if (Array.isArray(item.children) && item.children.length) {
					const found = this.findNatureItem(item.children, selValue)
					if (found) return found
				}
			}
			return null
		},
		async getList() {
			try {
				const { monthSameRatio, yearSameRatio, naturePayAmtList } = await apiGetWaterPayCostChart({
					dimension: this.tabValue,
					orgCode: this.orgCode,
					userType: this.cardDetail.activeTab,
				})
				this.cardDetail.titleList[0].value = monthSameRatio
				this.cardDetail.titleList[1].value = yearSameRatio
				const typeMap = this.cardDetail.tabList.reduce((map, item) => {
					map[item.value] = item.name.replace('表卡', '')
					return map
				}, {})
				this.chartOptions.seriesName = typeMap[this.cardDetail.activeTab] || ''

				if (Array.isArray(naturePayAmtList) && naturePayAmtList.length) {
					const result = this.findNatureItem(naturePayAmtList, this.selValue)
					if (result && Array.isArray(result.children) && result.children.length) {
						this.chartOptions.xData = result.children.map(item => item.natureName)
						const data = result.children.map(item => item.payAmtRate)

						this.chartOptions.seriesData = [
							{
								name: this.chartOptions.seriesName,
								data,
								type: 'line',
								smooth: true,
								color: '#FF928A',
								areaStyle: {
									color: new window.echarts.graphic.LinearGradient(0, 0, 0, 1, [
										{ offset: 0, color: 'rgba(255, 146, 138, 0.3)' },
										{ offset: 1, color: 'rgba(255, 146, 138, 0.05)' },
									]),
								},
							},
						]
					} else {
						console.log(111111111, result)
						this.chartOptions.xData = [result.natureName]
						this.chartOptions.seriesData = [
							{
								name: this.chartOptions.seriesName,
								data: [result.payAmtRate],
								type: 'line',
								smooth: true,
								color: '#FF928A',
								areaStyle: {
									color: new window.echarts.graphic.LinearGradient(0, 0, 0, 1, [
										{ offset: 0, color: 'rgba(255, 146, 138, 0.3)' },
										{ offset: 1, color: 'rgba(255, 146, 138, 0.05)' },
									]),
								},
							},
						]
					}
				}
			} catch (error) {
				console.error(error)
			}
		},
	},
}
</script>
