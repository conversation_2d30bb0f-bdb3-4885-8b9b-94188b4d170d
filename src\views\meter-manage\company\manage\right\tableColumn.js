import { getfilterName } from '@/utils'
export function getColumn(_this) {
	const { chargingMethod = [], archiveState = [], meterStatus = [], virtualMeterType = [] } =
		_this.$store.getters.dataList || {}
	return [
		{
			key: 'archivesStatus',
			name: '表卡状态',
			tooltip: true,
			render: (h, row) => {
				const archivesStatus = row.archives.archivesStatus
				const colorMap = {
					1: '#19BE6B',
					2: '#CCCCCC',
					3: '#CCCCCC',
					4: '#EC6B60',
				}
				return (
					<div
						style={{
							display: 'flex',
							'justify-content': 'flex-start',
							'align-items': 'center',
							color: archivesStatus === 4 ? '#EC6B60' : '#4E4E4E',
							cursor: archivesStatus === 4 ? 'pointer' : 'default',
						}}>
						<span
							style={{
								display: 'inline-block',
								width: '6px',
								height: '6px',
								'border-radius': '50%',
								'background-color': colorMap[archivesStatus],
								'margin-right': '6px',
							}}></span>
						<span
							style={{
								'border-bottom': archivesStatus === 4 ? '1px solid #EC6B60' : 'none',
							}}>
							{getfilterName(archiveState, archivesStatus, 'sortValue', 'sortName')}
						</span>
					</div>
				)
			},
		},
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
			render: (h, row, total, scope) => {
				return h('span', {}, row.archives && row.archives[scope.column.property])
			},
		},
		{
			key: 'virtualMeterType',
			name: '表卡类型',
			tooltip: true,
			render: (h, row, total, scope) => {
				if (row.archives) {
					const valueStr = getfilterName(
						virtualMeterType,
						row.archives[scope.column.property],
						'sortValue',
						'sortName',
					)
					return h('span', {}, valueStr)
				}
			},
		},
		{
			key: 'enterpriseNumber',
			name: '企业编号',
			tooltip: true,
			render: (h, row, total, scope) => {
				return h('span', {}, row.user && row.user[scope.column.property])
			},
		},
		{
			key: 'userName',
			name: '用户名称',
			tooltip: true,
			render: (h, row, total, scope) => {
				return h('span', {}, row.user && row.user[scope.column.property])
			},
		},
		{
			key: 'addressFullName',
			name: '地址',
			tooltip: true,
			render: (h, row, total, scope) => {
				return h('span', {}, row.address && row.address[scope.column.property])
			},
		},
		{
			key: 'chargingMethod',
			name: '收费方式',
			tooltip: true,
			render: (h, row, total, scope) => {
				if (row.user) {
					return h(
						'span',
						{},
						getfilterName(chargingMethod, row.user[scope.column.property], 'sortValue', 'sortName'),
					)
				}
			},
		},
		{
			key: 'collectionAgreementNumber',
			name: '托收协议号',
			tooltip: true,
			render: (h, row, total, scope) => {
				return h('span', {}, row.user && row.user[scope.column.property])
			},
		},
		{
			key: 'priceCode',
			name: '价格',
			tooltip: true,
			render: (h, row) => {
				const price = row.price || {}
				const str = price.priceCode + '-' + price.priceName

				return h('span', {}, str)
			},
		},
		{
			key: 'userMobile',
			name: '手机号码',
			tooltip: true,
			render: (h, row) => {
				return h('span', {}, row.user && row.user.userMobile)
			},
		},
		{
			key: 'archivesTime',
			name: '建档时间',
			minWidth: 180,
			tooltip: true,
			render: (h, row, total, scope) => {
				return h('span', {}, row.archives && row.archives[scope.column.property])
			},
		},

		{
			key: 'meterStatus',
			name: '水表状态',
			tooltip: true,
			render: (h, row, total, scope) => {
				if (row.meter) {
					const valueStr = getfilterName(
						meterStatus,
						row.meter[scope.column.property],
						'sortValue',
						'sortName',
					)
					return h('span', {}, valueStr)
				}
			},
		},
	]
}
