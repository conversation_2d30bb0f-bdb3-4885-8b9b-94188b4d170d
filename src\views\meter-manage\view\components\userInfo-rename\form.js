export const getFormItems = () => {
	return [
		{
			type: 'el-input',
			label: '原用户名称',
			prop: 'oldUserName',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '业务操作人员',
			prop: 'operatorPerson',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '新用户名称',
			prop: 'userName',
		},
		{
			type: 'el-input',
			label: '更名纠错原因',
			prop: 'reason',
			attrs: {
				type: 'textarea',
				maxlength: '250',
				showWordLimit: true,
				autosize: {
					minRows: 4,
					maxRows: 8,
				},
			},
		},
	]
}
