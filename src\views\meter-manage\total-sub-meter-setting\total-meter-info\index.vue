<template>
	<div class="container-wrapper">
		<gc-model-header
			class="info-title"
			title="总表信息"
			:icon="require('@/assets/images/icon/title-common-parameters.png')"
		></gc-model-header>
		<div class="container">
			<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<template v-slot:dmaArchivesIdentity>
					<el-input v-model="formData.dmaArchivesIdentity">
						<el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
					</el-input>
				</template>
			</GcFormRow>
			<div class="button-group">
				<button class="gc-button gc-button-three" type="button" @click="handleReset">重 置</button>
				<button v-has="'cpm_dma-archives'" class="gc-button gc-button-two" type="button" @click="handleSave">
					确认选择
				</button>
			</div>
		</div>
	</div>
</template>
<script>
import { getFormItems } from './formItem.js'
import { ruleMaxLength, ruleRequired, RULE_INTEGERONLY } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiGetDmaArchivesDetail, apiCreateDmaArchives } from '@/api/meterManage.api.js'
export default {
	name: '',
	components: {},
	data() {
		return {
			formData: {
				dmaArchivesIdentity: '',
				dmaArchivesType: '',
				dmaMonitorBuildingNum: '',
				dmaAddress: '',
				caliber: '',
				dmaStationNo: '',
				dmaRemark: '',
				dmaMonitoringRange: '',
			},
			formItems: getFormItems(this),
			formAttrs: {
				labelWidth: '110px',
				labelPosition: 'right',
				rules: {
					dmaArchivesIdentity: [ruleRequired('必填'), ruleMaxLength(30), RULE_INTEGERONLY],
					dmaArchivesType: [ruleRequired('必填')],
					dmaStationNo: [ruleRequired('必填'), ruleMaxLength(30), RULE_INTEGERONLY],
					dmaMonitorBuildingNum: [ruleMaxLength(10), RULE_INTEGERONLY],
					dmaAddress: [ruleMaxLength(255)],
					caliber: [ruleMaxLength(16)],
					dmaRemark: [ruleMaxLength(255)],
					dmaMonitoringRange: [ruleMaxLength(255)],
				},
			},
			dmaArchivesId: '',
			cloneFormData: {},
		}
	},
	watch: {
		formData: {
			handler: 'validateForm',
			deep: true,
		},
	},
	created() {},
	methods: {
		async validateForm() {
			const valid = await this.$refs.formRef.validate()
			const isEqual = this._.isEqual(this.formData, this.cloneFormData)
			let flag = valid && this.dmaArchivesId && isEqual
			let message = ' '
			if (!valid) {
				message = '请完善必填项'
			} else if (!isEqual || !this.dmaArchivesId) {
				message = '请确认选择'
			}
			this.$emit('getValid', 'totalMeterInfo', flag, message)
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.validateForm()
			this.dmaArchivesId = ''
		},
		async handleSearch() {
			try {
				const formParams = trimParams({
					dmaArchivesIdentity: this.formData.dmaArchivesIdentity,
				})
				const data = await apiGetDmaArchivesDetail(formParams)
				if (data) {
					this.assignForm(data)
					this.cloneFormData = this._.cloneDeep(this.formData)
					this.dmaArchivesId = data.dmaArchivesId
					this.$emit('getDmaArchivesId', this.dmaArchivesId)
					this.validateForm()
				} else {
					this.$message.success('未查询到数据')
				}
			} catch (error) {
				console.log(error)
			}
		},
		assignForm(obj) {
			const keys = Object.keys(this.formData)
			keys.forEach(key => {
				if (obj && Object.prototype.hasOwnProperty.call(obj, key)) {
					this.formData[key] = obj[key]
				}
			})
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const formParams = trimParams(removeNullParams(this.formData))

			if (this.dmaArchivesId) {
				Object.assign(formParams, {
					dmaArchivesId: this.dmaArchivesId,
				})
			}
			try {
				const { dmaArchivesId } = await apiCreateDmaArchives(formParams)
				this.dmaArchivesId = dmaArchivesId
				this.$message.success(this.dmaArchivesId ? '档案更新成功' : '建档成功')
				this.cloneFormData = this._.cloneDeep(this.formData)
				this.validateForm()
				this.$emit('changeTab', 'subMeterSelect')
				this.$emit('getDmaArchivesId', this.dmaArchivesId)
			} catch (error) {
				console.log(error)
			}
		},
	},
	mounted() {
		this.validateForm()
		this.cloneFormData = this._.cloneDeep(this.formData)
	},
}
</script>

<style lang="scss" scoped>
.container-wrapper {
	height: 100%;
}
.container {
	height: calc(100% - 60px);
	overflow-y: auto;
	overflow-x: hidden;
}
.button-group {
	width: 100%;
	margin-top: 20px;
	padding: 0 20px;
	.gc-button {
		margin-right: 8px;
	}
}
</style>
