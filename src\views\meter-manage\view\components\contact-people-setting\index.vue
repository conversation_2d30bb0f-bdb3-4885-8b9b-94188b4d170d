<template>
	<GcElDialog
		:show="isShow"
		:title="title + '联系人'"
		custom-top="50px"
		width="1200px"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:taxpayerIdentity>
				<el-autocomplete
					style="width: 100%"
					v-model="formData.taxpayerIdentity"
					:fetch-suggestions="queryTaxpayerIdentity"
					placeholder="请输入"
					@select="handleTaxpayerIdentitySelect"
					@change="handleTaxpayerIdentityChange"
				>
					<template slot-scope="{ item }">
						<div class="billing-information-item">
							<p class="billing-information-item-row">纳税人识别号：{{ item.taxpayerIdentity }}</p>
							<p class="billing-information-item-row">开票抬头：{{ item.userName }}</p>
						</div>
					</template>
				</el-autocomplete>
			</template>
		</GcFormRow>
	</GcElDialog>
</template>

<script>
import _ from 'lodash'
import { removeNullParams, trimParams } from '@/utils/index.js'
// import { isBlank } from '@/utils/validate.js'
import {
	ruleRequired,
	RULE_INTEGERONLY,
	RULE_PHONE,
	RULE_INCORRECTEMAIL,
	ruleMaxLength,
	RULE_INT_ENGLISH,
} from '@/utils/rules.js'
import { getFormItems } from './form.js'
import { apiAddContact, apiModifyContact } from '@/api/meterManage.api.js'
import { apiQueryInvoiceBuyer } from '@/api/userManage.api'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	created() {
		this.queryTaxpayerIdentity = _.debounce(this.queryTaxpayerIdentity, 250)
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		title() {
			return this.contactId ? '编辑' : '新增'
		},
		formItems() {
			return getFormItems(this)
		},
	},
	data() {
		return {
			formData: {
				contactName: '',
				contactMobile: '',
				invoiceType: '',
				email: '',
				taxpayerIdentity: '',
				openBank: '',
				bankAccount: '',
				mailingAddress: '',
				buyerName: '',
			},
			formAttrs: {
				labelWidth: '120px',
				rules: {
					contactName: [ruleRequired('必填'), ruleMaxLength(64)],
					contactMobile: [RULE_PHONE],
					email: [RULE_INCORRECTEMAIL],
					mailingAddress: [ruleMaxLength(64)],
					openBank: [ruleMaxLength(32)],
					taxpayerIdentity: [RULE_INT_ENGLISH, ruleMaxLength(20)],
					bankAccount: [RULE_INTEGERONLY, ruleMaxLength(32)],
					buyerName: [ruleMaxLength(32)],
				},
			},
			contactId: '',
			archivesId: '',
			billingInfoDisabled: false,
		}
	},
	methods: {
		assignForm(obj) {
			this.formData = Object.assign(this.formData, obj)
			this.billingInfoDisabled = false
		},
		setOtherInfo(obj) {
			const { contactId, archivesId } = obj
			this.contactId = contactId
			this.archivesId = archivesId
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) {
				this.$message.error('表单信息未完善')
				return
			}
			const params = trimParams(removeNullParams(this.formData))
			const newParams = this.contactId
				? Object.assign(params, { contactId: this.contactId, archivesId: this.archivesId })
				: Object.assign(params, { archivesId: this.archivesId })
			try {
				this.contactId ? await apiModifyContact(newParams) : await apiAddContact(newParams)
				this.$message.success(this.title + '成功')
				this.handleClose()
				this.$emit('success')
			} catch (error) {
				console.log(error)
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		async queryTaxpayerIdentity(taxpayerIdentity, callback) {
			taxpayerIdentity = taxpayerIdentity || ''
			try {
				let result = await apiQueryInvoiceBuyer({
					taxpayerIdentity,
				})
				result = result || []
				const list = result.map(item => {
					const { taxpayerIdentity } = item
					return { ...item, value: taxpayerIdentity }
				})

				callback(list)
			} catch (e) {
				console.log(e)
				callback([])
			}
		},
		handleTaxpayerIdentitySelect(item) {
			const { userName, openBank, bankAccount } = item
			this.formData.openBank = openBank
			this.formData.buyerName = userName
			this.formData.bankAccount = bankAccount
			this.billingInfoDisabled = true
		},
		handleTaxpayerIdentityChange() {
			this.billingInfoDisabled = false
		},
	},
}
</script>
