<template>
	<el-dropdown trigger="click" @command="handleCommand" @visible-change="handleVisibleChange">
		<!--  class="gc-button-export"  -->
		<gc-button class="gc-button-export" :btn-type="'two'" :is-disabled="disabled">
			导出报表
			<i class="el-icon--right" :class="showDropDown ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
		</gc-button>
		<el-dropdown-menu slot="dropdown">
			<el-dropdown-item command="pdf">PDF下载</el-dropdown-item>
			<el-dropdown-item command="excel">Excel下载</el-dropdown-item>
		</el-dropdown-menu>
	</el-dropdown>
</template>

<script>
export default {
	name: 'GcExportStatement',
	components: {},
	props: {
		disabled: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			showDropDown: false,
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		// 选择表单项后
		handleCommand(command) {
			this.$emit('on-command-change', command)
		},
		// 下拉框显隐
		handleVisibleChange(show) {
			this.showDropDown = show
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-export {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 106px;
	height: 28px;
	line-height: 26px;
	box-sizing: border-box;
	border-radius: 4px;
	font-size: 12px;
	// border: 1px solid $base-color-blue;
	// color: $base-color-blue;
	text-align: center;
	background: white;
	&.disabled {
		background: #f2f2f2;
		border-color: #f2f2f2 !important;
		color: #d1d1d1 !important;
	}
	::v-deep .el-icon--right {
		margin-left: 6px;
	}
}
</style>
