<template>
	<gc-el-dialog :show="isShow" title="修改指针" custom-top="120px" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { modifyMeterReading } from '@/api/waterMeter.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				meterNo: '',
				meterReading: undefined,
			},
			formItems: [
				{
					type: 'el-input',
					label: '水表编号',
					prop: 'meterNo',
					attrs: {
						col: 24,
						disabled: true,
					},
				},
				{
					type: 'el-input',
					label: '指针数',
					prop: 'meterReading',
					attrs: {
						col: 24,
						placeholder: '请输入指针数',
						maxlength: 8,
					},
					events: {
						input: val => {
							// 只允许输入数字，实时过滤非数字字符
							const numericValue = val.replace(/[^0-9]/g, '')
							// 限制最大值
							const maxValue = 99999999
							const finalValue = numericValue ? Math.min(parseInt(numericValue), maxValue).toString() : ''

							if (finalValue !== val) {
								this.formData.meterReading = finalValue
							}
						},
					},
				},
			],
			formAttrs: {
				labelWidth: '100px',
				labelPosition: 'top',
				rules: {
					meterReading: [{ required: true, message: '请输入指针数', trigger: 'blur' }],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				await modifyMeterReading({
					meterId: this.formData.meterId,
					meterReading: this.formData.meterReading,
				})
				this.$message.success(`指针数修改成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.formData.meterReading = undefined
			this.isShow = false
		},
		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.el-form-item--small.el-form-item {
		margin-bottom: 12px;
	}

	.el-input-number {
		width: 100%;
		.el-input__inner {
			text-align: left;
		}
	}
}
</style>
