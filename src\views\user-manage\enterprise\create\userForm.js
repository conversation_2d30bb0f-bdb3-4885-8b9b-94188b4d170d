export default function (_this) {
	return [
		{
			type: 'el-select',
			label: '营业分公司',
			prop: 'orgCode',
			options: _this.$store.getters.orgList,
			attrs: {
				col: 6,
			},
		},
		{
			type: 'slot',
			label: '企业编号',
			prop: 'enterpriseNumber',
			slotName: 'enterpriseNumber',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '企业名称',
			prop: 'enterpriseName',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '联系人',
			prop: 'contactPeople',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '企业地址',
			prop: 'enterpriseAddress',
			attrs: {
				col: 12,
				type: 'textarea',
				maxlength: '128',
				showWordLimit: true,
				autosize: {
					minRows: 3,
					maxRows: 3,
				},
			},
		},
		{
			type: 'el-select',
			label: '收费类型',
			prop: 'chargingMethod',
			options:
				_this.$store.getters?.dataList?.chargingMethod?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 6,
			},
		},
		{
			type: 'slot',
			label: '托收协议号',
			prop: 'collectionAgreementNumber',
			slotName: 'collectionAgreementNumber',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '联系手机',
			prop: 'userMobile',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '联系电话',
			prop: 'contactPhone',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '邮寄地址',
			prop: 'mailingAddress',
			attrs: {
				col: 12,
				type: 'textarea',
				maxlength: '64',
				showWordLimit: true,
				autosize: {
					minRows: 2,
					maxRows: 2,
				},
			},
		},

		{
			type: 'el-input',
			label: '邮编',
			prop: 'zipCode',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '电子邮箱',
			prop: 'email',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-select',
			label: '开票类型',
			prop: 'invoiceType',
			options:
				_this.$store.getters?.dataList?.invoiceType?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 13,
			},
		},
		{
			type: 'slot',
			label: '纳税人识别号',
			prop: 'taxpayerIdentity',
			slotName: 'taxpayerIdentity',
			attrs: {
				col: 13,
			},
		},
		{
			type: 'el-input',
			label: '开票抬头',
			prop: 'buyerName',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 13,
			},
		},
		{
			type: 'el-input',
			label: '开户银行',
			prop: 'openBank',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 13,
			},
		},
		{
			type: 'el-input',
			label: '银行账户',
			prop: 'bankAccount',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 13,
			},
		},
		{
			type: 'slot',
			label: '同步修改用户信息',
			prop: 'userSyn',
			slotName: 'userSyn',
			attrs: {
				col: 13,
			},
		},
	]
}
