<template>
	<div class="container">
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handleChangePage"
			/>
		</div>
	</div>
</template>

<script>
import { getfilterName } from '@/utils'
import { apiGetHistoryPrice } from '@/api/meterManage.api.js'
export default {
	props: {
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			columns: [
				{
					key: 'createTime',
					name: '操作时间',
					tooltip: true,
				},
				{
					key: 'operatorType',
					name: '操作类型',
					width: 100,
					tooltip: true,
					render: (h, row) => {
						const valueStr = this.$store.getters.dataList.archivesOperatorType
							? getfilterName(
									this.$store.getters.dataList.archivesOperatorType,
									row.operatorType,
									'sortValue',
									'sortName',
							  )
							: ''
						return h('span', {}, valueStr)
					},
				},
				{
					key: 'staffName',
					name: '操作人',
					width: 100,
					tooltip: true,
				},
				{
					key: 'priceCode',
					name: '价格编号',
					width: 100,
					tooltip: true,
				},
				{
					key: 'priceVersion',
					name: '版本号',
					width: 100,
					tooltip: true,
				},
				{
					key: 'billingTypeId',
					name: '计费类型',
					width: 100,
					tooltip: true,
					render: (h, row, total, scope) => {
						const valueStr = getfilterName(
							this.$store.getters.dataList?.billingType,
							row[scope.column.property],
							'sortValue',
							'sortName',
						)
						return h('span', {}, valueStr)
					},
				},
				{
					key: 'priceDesc',
					name: '价格',
					minWidth: 220,
					tooltip: true,
					render: (h, row) => {
						const str = row.priceDesc ? row.priceDesc.replace(/<br\/>/g, '\n') : ''
						return h('span', { style: { whiteSpace: 'pre-line' } }, str)
					},
				},
				{
					key: 'priceBillItemList',
					name: '附加费',
					tooltip: true,
					render: (h, row) => {
						let str = ''
						if (row.priceBillItemList.length > 0) {
							str = this.surchangePrice(row.priceBillItemList)
						}
						return h('span', { style: { whiteSpace: 'pre-line' } }, str)
					},
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
		}
	},
	methods: {
		async getList() {
			this.loading = true
			try {
				const extractedData = Object.assign({}, ...Object.values(this.tabData))
				const { current, size } = this.pageData
				const formParams = {
					current,
					size,
					archivesId: extractedData?.archivesId,
				}
				const { records, total } = await apiGetHistoryPrice(formParams)
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 附加费
		surchangePrice(data) {
			const arr = data.map(item => {
				return `${item.itemName}: ${item.billItemPrice}元/吨`
			})
			return arr ? arr.join('\n') : ''
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleSearch() {
			this.handleChangePage({ page: 1 })
		},
	},
}
</script>
