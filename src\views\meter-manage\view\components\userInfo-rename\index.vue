<template>
	<GcElDialog
		:show="isShow"
		title="更名纠错"
		width="500px"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
	</GcElDialog>
</template>

<script>
import { getFormItems } from './form.js'
import { ruleRequired, ruleMaxLength } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiModifyUserName, apiModifyUserName2, apiModifyUserName3 } from '@/api/userManage.api.js'
export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		userDetail: {
			type: Object,
			default: () => {
				return {}
			},
		},
		archivesId: {
			type: String,
			default: '',
		},
		permissionCode: {
			type: String,
			default: 'cpm_user_modify-user-name',
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		newDetailData() {
			return Object.assign({}, ...Object.values(this.userDetail))
		},
	},
	watch: {
		isShow: {
			handler(val) {
				if (val) {
					this.formData.oldUserName = this.userDetail?.user?.userName
				}
			},
		},
	},
	data() {
		return {
			formData: {
				oldUserName: '',
				userName: '',
				operatorPerson: this.$store.getters.userInfo.staffName,
				reason: '',
			},
			formItems: getFormItems(),
			formAttrs: {
				rules: {
					userName: [ruleRequired('必填'), ruleMaxLength(32)],
					reason: [ruleRequired('必填')],
				},
			},
		}
	},
	methods: {
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const params = trimParams(removeNullParams(this.formData))
			Object.assign(params, { userId: this.userDetail?.user?.userId })
			// 表卡档案更名需要
			if (['cpm_user_modify-user-name2', 'cpm_user_modify-user-name3'].includes(this.permissionCode)) {
				Object.assign(params, { archivesId: this.userDetail?.archives?.archivesId })
			}
			delete params.oldUserName
			const apiMethods = {
				'cpm_user_modify-user-name': apiModifyUserName,
				'cpm_user_modify-user-name2': apiModifyUserName2,
				'cpm_user_modify-user-name3': apiModifyUserName3,
			}
			await apiMethods[this.permissionCode](params)
			this.$message.success('更名纠错成功')
			this.handleClose()
			this.$emit('success')
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
	},
}
</script>
