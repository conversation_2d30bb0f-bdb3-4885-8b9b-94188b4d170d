<template>
	<gc-el-dialog :show="isShow" :title="`${typeText}托收银行`" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { ruleMaxLength } from '@/utils/rules.js'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		editType: {
			type: String,
			default: 'add',
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},

		typeText() {
			return this.editType === 'add' ? '新增' : '编辑'
		},
	},
	data() {
		return {
			formData: {
				bankName: '',
				bankNo: '',
				status: 1,
			},
			formItems: [
				{
					type: 'el-input',
					label: '银行名称',
					prop: 'bankName',
					attrs: {
						clearable: true,
						placeholder: '请输入银行名称',
					},
				},
				{
					type: 'el-input',
					label: '行号',
					prop: 'bankNo',
					attrs: {
						clearable: true,
						placeholder: '请输入行号',
					},
				},
				{
					type: 'el-radio',
					label: '状态',
					prop: 'status',
					options: [
						{
							label: '在用',
							value: 1,
						},
						{
							label: '停用',
							value: 0,
						},
					],
					attrs: {
						col: 24,
						placeholder: '',
					},
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					bankName: [
						{
							required: true,
							message: '请输入银行名称',
							trigger: 'blur',
						},
						ruleMaxLength(50, '银行名称'),
					],
					bankNo: [
						{
							required: true,
							message: '请输入行号',
							trigger: 'blur',
						},
					],
					status: [
						{
							required: true,
							message: '请选择状态',
							trigger: 'change',
						},
					],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				console.log('this.formData', this.formData)
				if (this.editType === 'add') {
					// TODO: 对接接口
				} else {
					// TODO: 对接接口
				}
				this.$message.success(`${this.typeText}托收银行成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
</style>
