<template>
	<div class="input-container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch">
					<i class="iconfontCis icon-small-search"></i>
					筛选
				</el-button>
				<el-button @click="handleReset">
					<i class="iconfontCis icon-reset"></i>
					重置
				</el-button>
			</el-form-item>
		</GcFormSimple>
		<el-form class="table-form" ref="tableFormRef" :model="tableForm" style="height: calc(100% - 60px)">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableForm.tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handleChangePage"
			>
				<template v-slot:curMeterReading="{ row, $index }">
					<el-form-item
						v-show="row.isEdit"
						:prop="`tableData[${$index}].curMeterReading`"
						:rules="[judgeRequired()]"
					>
						<el-input v-model="tableForm.tableData[$index].curMeterReading" placeholder="请输入"></el-input>
					</el-form-item>
					<span class="cell-desc" v-show="!row.isEdit">
						{{ judgeBlank(row.curMeterReading) ? '--' : row.curMeterReading }}
					</span>
				</template>
				<template v-slot:recordDate="{ row, $index }">
					<el-form-item
						v-show="row.isEdit"
						:prop="`tableData[${$index}].recordDate`"
						:rules="[judgeRequired()]"
					>
						<el-date-picker
							v-model="tableForm.tableData[$index].recordDate"
							value-format="yyyy-MM-dd"
							type="date"
							placeholder="选择日期时间"
							style="width: 100%"
						></el-date-picker>
					</el-form-item>
					<span class="cell-desc" v-show="!row.isEdit">
						{{ judgeBlank(row.recordDate) ? '--' : row.recordDate }}
					</span>
				</template>
				<template v-slot:meterReadingStaffId="{ row, $index }">
					<el-form-item
						v-show="row.isEdit"
						:prop="`tableData[${$index}].meterReadingStaffId`"
						:rules="[judgeRequired()]"
					>
						<el-select
							v-model="tableForm.tableData[$index].meterReadingStaffId"
							placeholder="请选择"
							clearable
							filterable
						>
							<el-option
								v-for="item in staffOptions"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							></el-option>
						</el-select>
					</el-form-item>
					<span class="cell-desc" v-show="!row.isEdit">{{ filterStaffName(row.meterReadingStaffId) }}</span>
				</template>
				<template v-slot:operate="{ row, $index }">
					<el-button
						v-has="'cpm_archives_reading-first-record'"
						type="text"
						v-show="!row.isEdit && row.meterId"
						@click="handleRowEdit($index)"
					>
						修改
					</el-button>
					<el-button type="text" v-show="row.isEdit" @click="handleSave(row, $index)">保存</el-button>
					<el-button type="text" v-show="row.isEdit" @click="handleCancel(row, $index)">取消</el-button>
				</template>
			</GcTable>
		</el-form>
	</div>
</template>

<script>
import { getfilterName } from '@/utils'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { ruleRequired } from '@/utils/rules.js'
import { isBlank } from '@/utils/validate.js'
import { apiGetReadingFirstRecord, apiSaveOrUpdateReadingFirstRecords } from '@/api/meterManage.api'
import { getStaffMap } from '@/api/meterReading.api.js'
export default {
	props: {
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						placeholder: '请输入',
						style: {
							width: '150px',
						},
					},
				},
			],
			formAttrs: {
				inline: true,
			},
			columns: [
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'meterId',
					name: '水表在装情况',
					tooltip: true,
					render: (h, row) => {
						return h('span', {}, row.meterId ? '在装' : '未安装')
					},
				},
				{
					key: 'addressFullName',
					name: '地址',
					tooltip: true,
				},
				{
					key: 'curMeterReading',
					name: '指针数',
				},
				{
					key: 'recordDate',
					name: '抄表时间',
				},
				{
					key: 'meterReadingStaffId',
					name: '抄表人',
				},
				{
					key: 'operate',
					name: '操作',
					fixed: 'right',
				},
			],
			tableForm: {
				tableData: [],
			},
			cloneTableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
			staffOptions: [],
		}
	},
	mounted() {
		this._getStaff()
	},
	methods: {
		// 获取抄表员工
		async _getStaff() {
			try {
				const res = await getStaffMap()
				this.staffOptions = res.map(item => {
					const { staffId, staffName } = item
					return {
						value: staffId,
						label: staffName,
					}
				})
			} catch (error) {
				console.log(error)
				this.staffOptions = []
			}
		},
		async _getList() {
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					addressAreaCode: this.tabData.addressAreaCode,
					current,
					size,
				})
				const { records = [], total = 0 } = await apiGetReadingFirstRecord(formParams)
				this.tableForm.tableData = records.map(item => {
					item.isEdit = false
					return item
				})
				this.cloneTableData = this._.cloneDeep(this.tableForm.tableData)
				this.pageData.total = total
			} catch (error) {
				console.log(error)
				this.tableForm.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
				this.cloneTableData = []
			}
		},
		judgeRequired() {
			return ruleRequired('必填', 'blur')
		},
		judgeBlank(val) {
			return isBlank(val)
		},
		handleSearch() {
			this.handleChangePage({ page: 1 })
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.handleSearch()
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this._getList()
		},
		filterStaffName(val) {
			return this.staffOptions ? getfilterName(this.staffOptions, val) : ''
		},
		handleRowEdit(index) {
			this.$set(this.tableForm.tableData[index], 'isEdit', true)
		},
		handleCancel(row, index) {
			this.$refs.tableFormRef.clearValidate([
				`tableData[${index}].curMeterReading`,
				`tableData[${index}].recordDate`,
				`tableData[${index}].meterReadingStaffId`,
			])
			this.tableForm.tableData.splice(index, 1, {
				...this.cloneTableData[index],
				isEdit: false,
			})
		},
		async handleSave(row, index) {
			const promise1 = new Promise(resolve => {
				this.$refs.tableFormRef.validateField(`tableData[${index}].curMeterReading`, errorMessage => {
					resolve(!errorMessage)
				})
			})
			const promise2 = new Promise(resolve => {
				this.$refs.tableFormRef.validateField(`tableData[${index}].recordDate`, errorMessage => {
					resolve(!errorMessage)
				})
			})
			const promise3 = new Promise(resolve => {
				this.$refs.tableFormRef.validateField(`tableData[${index}].meterReadingStaffId`, errorMessage => {
					resolve(!errorMessage)
				})
			})
			const valids = await Promise.all([promise1, promise2, promise3])

			if (!valids.includes(false)) {
				const {
					archivesId,
					archivesIdentity,
					curMeterReading,
					meterReadingRecordId,
					recordDate,
					meterReadingStaffId,
				} = row
				const params = {
					archivesId,
					archivesIdentity,
					curMeterReading,
					meterReadingRecordId,
					recordDate,
					meterReadingStaffId,
				}
				try {
					await apiSaveOrUpdateReadingFirstRecords(params)
					this.$set(this.tableForm.tableData[index], 'isEdit', false)
					this.$message.success('保存成功')
					this.handleSearch()
				} catch (error) {
					console.log(error)
				}
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.input-container {
	display: flex;
	flex-direction: column;
	flex: 1;
	background-color: #fff;
	padding: 20px;
	height: 100%;
	overflow: hidden;
}
::v-deep {
	.el-table {
		.el-form-item {
			margin: 16px 0;
		}
		.el-table__body {
			td {
				padding: 0;
			}
			.cell {
				.cell-desc {
					display: inline-block;
					margin: 20.5px 0;
				}
			}
		}
	}
}
</style>
