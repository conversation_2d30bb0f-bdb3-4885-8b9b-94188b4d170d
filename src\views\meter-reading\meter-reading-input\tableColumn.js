export function getColumn(_) {
	return [
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'userName',
			name: '用户名称',
			tooltip: true,
		},
		{
			key: 'addressFullName',
			name: '地址',
			minWidth: 250,
			tooltip: true,
		},
		{
			key: 'readingStatusDesc',
			name: '是否已抄',
		},
		{
			key: 'lastMeterReading',
			name: '上次指针',
			tooltip: true,
		},
		{
			key: 'curMeterReading',
			name: '本次指针',
		},
		{
			key: 'useAmount',
			name: '本次水量',
		},
		{
			key: 'checkStatus',
			name: '抄表情况',
			width: 160,
		},
		{
			key: 'imageUrl',
			name: '照片',
			tooltip: true,
		},
		{
			key: 'thisRecordDate',
			name: '抄表时间',
			width: 216,
		},
		{
			key: 'meterReadingStaffId',
			name: '抄表人',
			tooltip: true,
		},
		{
			hide: !_.$has('plan-collection_meterReadingTask_updateMeterReadingRecord'),
			key: 'deal',
			name: '操作',
			width: 140,
		},
	]
}
