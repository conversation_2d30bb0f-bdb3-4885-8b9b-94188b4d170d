<template>
	<div class="entry-wrap">
		<template v-for="(item, index) in quickEntry">
			<div class="entry-item" :key="index" @click="goToPath(item)">
				<img :src="`${item.url}`" alt="" />
				<span class="text">{{ item.label }}</span>
			</div>
		</template>
	</div>
</template>

<script>
export default {
	name: 'QuickEntry',
	data() {
		return {
			quickEntry: [
				{
					label: '居民表卡创建',
					url: require('@/assets/images/icon/create-a-file.svg'),
					path: `/meterManage/residentMeterCreate`,
					authcode: 'cpm_archives_create',
				},
				{
					label: '企业表卡创建',
					url: require('@/assets/images/icon/qiyebiaoka.svg'),
					path: '/meterManage/companyMeterCreate',
					authcode: 'cpm_archives_create',
				},
				{
					label: '批量建表',
					url: require('@/assets/images/icon/piliangjianbiao.svg'),
					path: '/meterManage/batchCreateMeter',
					authcode: 'cpm_archives_create',
				},
				{
					label: '虚表拆分',
					url: require('@/assets/images/icon/xubiaochaifen.svg'),
					path: '/meterManage/virtualMeterSplit',
					authcode: 'cpm_archives_create',
				},
				{
					label: '总分表建立',
					url: require('@/assets/images/icon/zongfenbiao.svg'),
					path: '/meterManage/totalSubMeterSetting',
					authcode: 'cpm_archives_create',
				},
				{
					label: '册本移交',
					url: require('@/assets/images/icon/cebenyijiao.svg'),
					path: '/meterReading/meterReadingTransfer',
					authcode: 'cpm_archives_create',
				},
				{
					label: '缴费',
					url: require('@/assets/images/icon/cashier.svg'),
					path: '/costManage/paymentPage',
					authcode: 'cpm_archives_create',
				},
				{
					label: '抄表追收',
					url: require('@/assets/images/icon/create-files.svg'),
					path: '/meterReading/meterReadingCollect',
					authcode: 'cpm_archives_create',
				},
				{
					label: '创建企业',
					url: require('@/assets/images/icon/create-company.svg'),
					path: '/userManage/enterpriseCreate',
					authcode: 'cpm_enterprise_add',
				},
			],
		}
	},
	methods: {
		goToPath(item) {
			this.$router.push(item.path)
		},
	},
}
</script>
<style lang="scss" scoped>
.entry-wrap {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
	padding: 41px 20px 0 20px;
	.entry-item {
		width: 33%;
		display: flex;
		align-items: center;
		flex-direction: column;
		cursor: pointer;
		margin-bottom: 20px;
		img {
			width: 56%;
		}
		.text {
			font-size: 12px;
			color: #797c80;
			margin-top: 14px;
		}
	}
}
</style>
