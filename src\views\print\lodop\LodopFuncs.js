import { MessageBox } from 'element-ui'

// ====页面动态加载C-Lodop云打印必须的文件CLodopfuncs.js====
var head = document.head || document.getElementsByTagName('head')[0] || document.documentElement

var oscript = document.createElement('script')

// 让本机的浏览器打印(更优先一点)：
oscript = document.createElement('script')
oscript.src = 'http://localhost:8000/CLodopfuncs.js?priority=2'
head.insertBefore(oscript, head.firstChild)

// 加载双端口(8000和18000）避免其中某个端口被占用：
oscript = document.createElement('script')
oscript.src = 'http://localhost:18000/CLodopfuncs.js?priority=1'
head.insertBefore(oscript, head.firstChild)

// 下载loadLodop
function loadLodop() {
	let a = document.createElement('a')
	a.href = '/CLodop_Setup_for_Win32NT.exe'
	a.click()
}

// ====获取LODOP对象的主过程：====
export function getLodop() {
	var LODOP
	try {
		LODOP = getCLodop()
		if (!LODOP && document.readyState !== 'complete') {
			MessageBox.alert('C-Lodop打印控件还没准备好，请稍后再试！')
			return
		}
		//清理原例子内的object或embed元素，避免乱提示：
		// if (oEMBED && oEMBED.parentNode) oEMBED.parentNode.removeChild(oEMBED);
		// if (oOBJECT && oOBJECT.parentNode) oOBJECT.parentNode.removeChild(oOBJECT);
		LODOP.SET_LICENSES(
			'金卡高科技股份有限公司',
			'93F97C59713B7FEE54D7B4CC96DA49AF',
			'金卡高科技股份有限公司',
			'BC3E4B2EF0727F6224A6B39658DAE7CA',
		)
		LODOP.SET_LICENSES('THIRD LICENSE', '', 'GOLDCARD HIGH-TECH CO.,LTD.', '3DA78DA5E5C5786F0C29A2F0F8E55029')
		return LODOP
	} catch (err) {
		MessageBox({
			title: '温馨提示',
			type: 'warning',
			showCancelButton: true,
			closeOnClickModal: false,
			message: '您还未安装打印控件，点击确定下载打印控件，安装成功后刷新页面即可进行打印',
			callback: res => {
				if (res === 'confirm') {
					loadLodop()
				}
			},
		})
	}
}
