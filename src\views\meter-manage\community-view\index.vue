<template>
	<div class="community-detail" v-loading.fullscreen.lock="loading">
		<!-- 表卡信息 -->
		<CommunityInfo :reqData="detailData" v-has="'cpm_address-areas_info'" />
		<!-- 详情tab切换区 -->
		<div class="community-content">
			<GcDetailTab
				ref="detailTabRef"
				:tab-list="tabList"
				:default-active-name.sync="defaultActiveName"
				@refresh="_apiDetail"
				@tab-change="handleTabChange"
			></GcDetailTab>
		</div>
	</div>
</template>

<script>
import { apiGetAddressAreasInfo } from '@/api/meterManage.api'
import CommunityInfo from './community-info' // 小区信息
import ArchivesList from './archives-list' // 表卡列表
import FirstReadingInput from './first-reading-input' // 首抄录入
export default {
	name: 'CommunityView',
	components: {
		CommunityInfo,
	},
	computed: {
		tabList() {
			const arr = []
			this.$has('cpm_address-areas_info') &&
				arr.push({
					name: 'ArchivesList',
					label: '表卡列表',
					component: ArchivesList,
					data: this.detailData,
				})
			this.$has('cpm_archives_reading-first-record') &&
				arr.push({
					name: 'FirstReadingInput',
					label: '指针修正',
					component: FirstReadingInput,
					data: this.detailData,
				})
			return arr
		},
	},
	data() {
		return {
			loading: false,
			defaultActiveName: 'ArchivesList',
			detailData: {},
		}
	},
	activated() {
		if (this.$route.query.id) {
			this._apiDetail()
		}
	},
	methods: {
		async _apiDetail() {
			try {
				const data = await apiGetAddressAreasInfo({
					addressAreaId: this.$route.query.id,
				})
				this.detailData = data
			} catch (error) {
				console.log(error)
			}
		},
		handleTabChange(data) {
			this.$refs.detailTabRef.$refs.componentRef[data.index].handleSearch()
		},
	},
}
</script>
<style lang="scss" scoped>
.community-detail {
	width: 100%;
	height: 100%;
	display: flex;
	.community-content {
		width: calc(100% - 290px);
	}
}
</style>
