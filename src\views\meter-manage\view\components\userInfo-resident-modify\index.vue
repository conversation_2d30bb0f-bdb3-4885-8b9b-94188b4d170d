<template>
	<GcElDialog
		:show="isShow"
		title="用户信息修改"
		custom-top="50px"
		width="1200px"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:otherMobile>
				<el-form-item class="other-mobile" label="其他手机" prop="otherMobile">
					<AddOtherMobile v-model="formData.otherMobile" :mobileList.sync="formData.mobileList" />
				</el-form-item>
			</template>
			<template v-slot:taxpayerIdentity>
				<el-autocomplete
					style="width: 100%"
					v-model="formData.taxpayerIdentity"
					:fetch-suggestions="queryTaxpayerIdentity"
					placeholder="请输入"
					@select="handleTaxpayerIdentitySelect"
					@change="handleTaxpayerIdentityChange"
				>
					<template slot-scope="{ item }">
						<div class="billing-information-item">
							<p class="billing-information-item-row">纳税人识别号：{{ item.taxpayerIdentity }}</p>
							<p class="billing-information-item-row">开票抬头：{{ item.userName }}</p>
						</div>
					</template>
				</el-autocomplete>
			</template>
		</GcFormRow>
	</GcElDialog>
</template>

<script>
import _ from 'lodash'
import { removeNullParams, trimParams } from '@/utils/index.js'
import {
	ruleRequired,
	RULE_INTEGERONLY,
	RULE_PHONE,
	RULE_INCORRECTEMAIL,
	ruleMaxLength,
	RULE_INT_ENGLISH,
	RULE_INCORRECTIDCARD,
	RULE_POSTALCODE,
} from '@/utils/rules.js'
import { isBlank } from '@/utils/validate.js'
import { getFormItems } from './form.js'
import { apiModifyUser2, apiQueryInvoiceBuyer } from '@/api/userManage.api'
import AddOtherMobile from '../../../components/AddOtherMobile'

export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		detailData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	components: { AddOtherMobile },
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		formItems() {
			const { isShow } = this
			if (isShow) {
				return getFormItems(this)
			} else {
				return []
			}
		},
	},
	created() {
		this.queryTaxpayerIdentity = _.debounce(this.queryTaxpayerIdentity, 250)
	},
	data() {
		return {
			formData: {
				mobileList: [], // 其他手机号
			},
			formAttrs: {
				labelWidth: '120px',
				rules: {
					userName: [ruleRequired('必填'), ruleMaxLength(32)],
					userMobile: [RULE_PHONE],
					chargingMethod: [ruleRequired('必填')],
					nameUsedBefore: [ruleMaxLength(32)],
					contactPhone: [ruleMaxLength(128)],
					zipCode: [RULE_POSTALCODE],
					certificateNo: [RULE_INCORRECTIDCARD],
					otherCertificateNo: [ruleMaxLength(32)],
					email: [RULE_INCORRECTEMAIL],
					mailingAddress: [ruleMaxLength(64)],
					otherMobile: [RULE_PHONE],
					openBank: [ruleMaxLength(32)],
					taxpayerIdentity: [RULE_INT_ENGLISH, ruleMaxLength(20)],
					bankAccount: [RULE_INTEGERONLY, ruleMaxLength(32)],
					buyerName: [ruleMaxLength(32)],
				},
			},
			billingInfoDisabled: false,
		}
	},
	watch: {
		detailData: {
			handler(data) {
				data = data || {}

				let { user, archives } = data
				user = user || {}
				archives = archives || {}
				const { formItems } = this
				const newDetailData = Object.assign({}, archives, user)
				formItems.forEach(item => {
					const value = newDetailData[item.prop]

					if (['otherMobile', 'userSubType'].includes(item.prop)) {
						this.$set(this.formData, item.prop, value)
					} else {
						this.$set(this.formData, item.prop, isBlank(value) ? value : String(value))
					}
				})
				this.formData.mobileList = user.otherContactPhone ? user.otherContactPhone.split(',') : []
				this.billingInfoDisabled = false
			},
			immediate: true,
			deep: true,
		},
	},
	methods: {
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) {
				this.$message.error('表单信息未完善')
				return
			}
			const userVO = trimParams(removeNullParams(this.formData))
			const otherContactPhone = userVO.mobileList ? userVO.mobileList.join(',') : ''
			delete userVO.mobileList
			Object.assign(userVO, {
				userId: this.detailData?.user?.userId,
				otherContactPhone,
				userType: 3,
			})
			try {
				await apiModifyUser2({ userVO, archivesId: this.detailData?.archives?.archivesId })
				this.$message.success('修改用户信息成功')
				this.handleClose()
				this.$emit('success')
			} catch (error) {
				console.log(error)
			}
		},
		handleClose() {
			this.isShow = false
		},
		async queryTaxpayerIdentity(taxpayerIdentity, callback) {
			taxpayerIdentity = taxpayerIdentity || ''
			try {
				let result = await apiQueryInvoiceBuyer({
					taxpayerIdentity,
				})
				result = result || []
				const list = result.map(item => {
					const { taxpayerIdentity } = item
					return { ...item, value: taxpayerIdentity }
				})

				callback(list)
			} catch (e) {
				console.log(e)
				callback([])
			}
		},
		handleTaxpayerIdentitySelect(item) {
			const { userName, openBank, bankAccount } = item
			this.formData.openBank = openBank
			this.formData.buyerName = userName
			this.formData.bankAccount = bankAccount
			this.billingInfoDisabled = true
		},
		handleTaxpayerIdentityChange() {
			this.billingInfoDisabled = false
		},
	},
}
</script>
<style lang="scss" scoped>
.other-mobile {
	::v-deep {
		.el-form-item__error {
			position: absolute;
			top: 36px;
		}
	}
}
::v-deep {
	.title {
		.el-form-item__label {
			font-weight: 500 !important;
			color: #000000 !important;
		}
	}
	.el-input-group__append {
		padding: 0;
		img {
			display: block;
			padding: 0 10px;
			height: 30px;
			object-fit: none;
			cursor: pointer;
		}
	}
}
.billing-information-item {
	padding: 8px 0;
}
</style>
