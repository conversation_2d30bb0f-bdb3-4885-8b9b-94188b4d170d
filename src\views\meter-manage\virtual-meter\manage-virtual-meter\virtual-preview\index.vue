<template>
	<GcElDialog
		:show="isShow"
		title="查看虚表"
		custom-top="220px"
		width="1200px"
		:showFooter="false"
		@close="handleClose"
	>
		<div class="distributionMode">分配方式：{{ currentRow.distributionMode | formatDistributionMode }}</div>
		<div class="container-table">
			<GcTable :columns="columns" :table-data="tableData" />
		</div>
	</GcElDialog>
</template>

<script>
import { distributionModeOptions } from '@/consts/optionList.js'
import { getColumn } from './tableColumn.js'
import { apiGetPriceList_all, apiGetVirtualArchivesDetail } from '@/api/meterManage.api.js'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		currentRow: {
			type: Object,
			default: () => {},
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		columns() {
			return getColumn(this)
		},
	},
	data() {
		return {
			tableData: [],
			priceList: [],
		}
	},
	filters: {
		formatDistributionMode(value) {
			const enums = distributionModeOptions.reduce((acc, option) => {
				acc[option.value] = option.label
				return acc
			}, {})
			return enums[value]
		},
	},
	watch: {
		async isShow(val) {
			if (val) {
				try {
					await this._apiGetPriceList_all()
					this.currentRow?.archivesId && this._apiGetVirtualArchivesDetail(this.currentRow?.archivesId)
				} catch (error) {
					console.log(error)
				}
			}
		},
	},
	methods: {
		handleClose() {
			this.isShow = false
			this.tableData = []
		},
		// 价格列表
		async _apiGetPriceList_all() {
			const { records } = await apiGetPriceList_all()
			this.priceList = records
		},
		// 虚表列表
		async _apiGetVirtualArchivesDetail(v) {
			const params = {
				archivesId: v,
				tenantId: this.$store.getters.userInfo?.tenantId,
			}
			const res = await apiGetVirtualArchivesDetail(params)
			this.tableData = res
			// 回显价格信息
			res.map((item, index) => {
				this.assignPriceInfo(index)
			})
		},
		assignPriceInfo(index) {
			const priceObj = this.priceList.find(item => item.priceCode == this.tableData[index].priceCode)
			if (priceObj) {
				this.tableData[index]['priceName'] = priceObj.priceName
				this.tableData[index]['priceId'] = priceObj.priceId
			} else {
				this.tableData[index]['priceName'] = ''
				this.tableData[index]['priceId'] = ''
				this.tableData[index]['priceCode'] = ''
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.distributionMode {
	margin-bottom: 10px;
}
.container-table {
	height: 500px;
}
</style>
