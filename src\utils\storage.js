import Vue from 'vue'
import Storage from 'vue-ls'
import _ from 'lodash'
import lsKey from '@/consts/lsKey'

Vue.use(_.clone(Storage), {
	namespace: 'ui_', // key prefix
	name: 'ls', // name variable Vue.[ls] or this.[$ls],
	storage: 'local', // storage name session, local, memory
})
export const getLS = key => Vue.ls.get(key)
export const setLS = (key, value) => Vue.ls.set(key, value)
export const removeLS = key => Vue.ls.remove(key)
export const clearLS = () => Vue.ls.clear()

Vue.use(_.clone(Storage), {
	namespace: 'icReadData_',
	name: 'icLs',
	storage: 'local',
})
export const getIcLS = key => Vue.icLs.get(key)
export const setIcLS = (key, value) => Vue.icLs.set(key, value)
export const removeIcLS = key => Vue.icLs.remove(key)
export const clearIcLS = () => Vue.icLs.clear()

Vue.use(_.clone(Storage), {
	namespace: 'ui_',
	name: 'ss',
	storage: 'session',
})
export const getSS = key => Vue.ss.get(key)
export const setSS = (key, value) => Vue.ss.set(key, value)
export const removeSS = key => Vue.ss.remove(key)
export const clearSS = () => Vue.ss.clear()

const {
	AccessToken,
	Permissions,
	UserInfo,
	Routes,
	TagsViews,
	FieldName,
	DataList,
	OrgList,
	Tenant,
	ModifyParamsList,
	ArchivesUpdateList,
	NewNotice,
	OrgsTemplateTree,
	InvoiceSet,
} = lsKey

// accessToken：登录认证
export const getToken = () => getLS(AccessToken)
export const setToken = val => setLS(AccessToken, val)
export const removeToken = () => removeLS(AccessToken)
// permissions：用户权限
export const getPermission = () => getLS(Permissions)
export const setPermission = val => setLS(Permissions, val)
export const removePermission = () => removeLS(Permissions)
// userInfo：用户信息（avatar，staffname，，，）
export const getUserInfo = () => getLS(UserInfo)
export const setUserInfo = val => setLS(UserInfo, val)
export const removeUserInfo = () => removeLS(UserInfo)
// routes：用户路由权限表
export const getRoutes = () => getLS(Routes)
export const setRoutes = val => setLS(Routes, val)
export const removeRoutes = () => removeLS(Routes)
// tagsViews：多标签
export const getTagsViews = () => getLS(TagsViews)
export const setTagsViews = val => setLS(TagsViews, val)
export const removeTagsViews = () => removeLS(TagsViews)
// FieldName：水气差异字段名
export const getFieldName = () => getLS(FieldName)
export const setFieldName = val => setLS(FieldName, val)
export const removeFieldName = () => removeLS(FieldName)
// DataList: 数据字典
export const getDataList = () => getLS(DataList)
export const setDataList = val => setLS(DataList, val)
export const removeDataList = () => removeLS(DataList)
// orgList: 营业分公司
export const getOrgList = () => getLS(OrgList)
export const setOrgList = val => setLS(OrgList, val)
export const removeOrgList = () => removeLS(OrgList)
// Tenant：切换租户
export const getTenant = () => getLS(Tenant)
export const setTenant = val => setLS(Tenant, val)
export const removeTenant = () => removeLS(Tenant)
// 存储修改档案参数（不显示在url地址栏的参数）
export const getModifyParamsList = () => getLS(ModifyParamsList)
export const setModifyParamsList = val => setLS(ModifyParamsList, val)
export const removeModifyParamsList = () => removeLS(ModifyParamsList)
// 档案详情更新（修改档案后档案详情更新）
export const getArchivesUpdateList = () => getLS(ArchivesUpdateList)
export const setArchivesUpdateList = val => setLS(ArchivesUpdateList, val)
export const removeArchivesUpdateList = () => removeLS(ArchivesUpdateList)

// NewNotice 通知
export const setNewNotice = val => setLS(NewNotice, val)
// OrgsTemplateTree 组织机构树
export const setOrgsTemplateTree = val => setLS(OrgsTemplateTree, val)

// 发票设置信息
export const getInvoiceSet = () => getLS(InvoiceSet)
export const setInvoiceSet = val => setLS(InvoiceSet, val)
export const removeInvoiceSet = () => removeLS(InvoiceSet)
