export const getFormItems = function (_this) {
	return [
		{
			type: 'slot',
			label: '',
			slotName: 'meterInfo',
			attrs: {
				col: 24,
				className: 'title',
			},
		},
		{
			type: 'el-input',
			label: '水表编号',
			prop: 'meterNo',
		},
		{
			type: 'el-input',
			label: '水表仓库编号',
			prop: 'meterWarehouseCode',
		},
		{
			type: 'el-input',
			label: '防盗编号',
			prop: 'antiTheftCode',
		},
		{
			type: 'el-input',
			label: '水表标号',
			prop: 'baseMeterNo',
		},
		{
			type: 'el-select',
			label: '水表类型',
			prop: 'meterTypeId',
			options: _this.meterTypeOptions,
			events: {
				change: value => {
					const data = _this.meterTypeOptions.find(item => item.value === value)
					if (data) {
						const { manufacturerName, meterRange, useYears } = data
						_this.formData.manufacturerName = manufacturerName
						_this.formData.ranges = meterRange
						_this.formData.useYears = useYears
					} else {
						_this.formData.manufacturerName = ''
						_this.formData.ranges = ''
						_this.formData.useYears = ''
					}
				},
			},
		},
		{
			type: 'el-input',
			label: '水表厂商',
			prop: 'manufacturerName',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '水表型号',
			prop: 'meterModel',
		},
		{
			type: 'el-input',
			label: '口径',
			prop: 'caliber',
		},
		{
			type: 'el-date-picker',
			label: '装表时间',
			prop: 'installationDate',
			attrs: {
				type: 'date',
				placeholder: '选择日期',
				valueFormat: 'yyyy-MM-dd',
			},
		},
		{
			type: 'el-input',
			label: '初始指针数',
			prop: 'startMeterReading',
			attrs: {
				maxlength: 8,
			},
			events: {
				input: val => {
					// 只允许输入数字，实时过滤非数字字符
					const numericValue = val.replace(/[^0-9]/g, '')
					// 限制最大值
					const maxValue = 99999999
					const finalValue = numericValue ? Math.min(parseInt(numericValue), maxValue).toString() : ''

					if (finalValue !== val) {
						_this.formData.startMeterReading = finalValue
					}
				},
			},
		},

		{
			type: 'el-input',
			label: '量程',
			prop: 'ranges',
		},
		{
			type: 'el-input',
			label: '服役年限',
			prop: 'useYears',
			attrs: {
				disabled: true,
			},
		},
	]
}
