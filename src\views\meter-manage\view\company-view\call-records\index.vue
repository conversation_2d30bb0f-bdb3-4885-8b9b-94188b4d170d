<template>
	<div class="container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch">
					<i class="iconfontCis icon-small-search"></i>
					筛选
				</el-button>
				<el-button @click="handleReset">
					<i class="iconfontCis icon-reset"></i>
					重置
				</el-button>
			</el-form-item>
		</GcFormSimple>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handleChangePage"
			/>
		</div>
	</div>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { queryUrgeRecordPage3 } from '@/api/arrearageManage.api'
import { queryStaffByType } from '@/api/meterReading.api.js'
export default {
	props: {
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		const _t = this
		const pickerOptions = {
			shortcuts: [
				{
					text: '最近一年',
					onClick(picker) {
						const end = new Date(_t.dayjs().endOf('year').format('YYYY-MM-DD'))
						const start = new Date(_t.dayjs().startOf('year').format('YYYY-MM-DD'))
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近三年',
					onClick(picker) {
						const end = new Date(_t.dayjs().endOf('year').format('YYYY-MM-DD'))
						const start = new Date(_t.dayjs().subtract(2, 'year').startOf('year').format('YYYY-MM-DD'))
						picker.$emit('pick', [start, end])
					},
				},
			],
		}
		return {
			formData: {
				modifyTime: [
					this.dayjs().subtract(2, 'year').startOf('year').format('YYYY-MM-DD'),
					this.dayjs().endOf('year').format('YYYY-MM-DD'),
				],
				callUserId: '',
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '催缴日期',
					prop: 'modifyTime',
					attrs: {
						type: 'daterange',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						style: {
							width: '250px',
						},
						pickerOptions,
					},
				},
				{
					type: 'el-select',
					label: '催缴人',
					prop: 'callUserId',
					options: [],
					attrs: {
						style: {
							width: '150px',
						},
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '90px',
				rules: {
					modifyTime: [{ required: true, message: '请选择催缴日期', trigger: 'change' }],
				},
			},
			columns: [
				{
					key: 'callPaymentTime',
					name: '催缴时间',
					minWidth: 180,
					tooltip: true,
				},
				{
					key: 'callUserName',
					name: '催缴人',
					tooltip: true,
				},
				{
					key: 'billNo',
					name: '账单编号',
					minWidth: 190,
					tooltip: true,
				},
				{
					key: 'archivesNo',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'fullAddressName',
					name: '地址',
					tooltip: true,
				},
				{
					key: 'billUserName',
					name: '用户名称',
					tooltip: true,
				},
				{
					key: 'arrearsAmount',
					name: '总应缴金额',
					tooltip: true,
				},
				{
					key: 'remark',
					name: '催缴备注',
					tooltip: true,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
		}
	},
	watch: {
		tabData: {
			handler() {
				this.getStaffMapData()
			},
			deep: true,
		},
	},
	methods: {
		// 获取催缴人下拉框数据
		async getStaffMapData() {
			const orgCode = (this.tabData.archives && this.tabData.archives.orgCode) || ''
			try {
				const res = await queryStaffByType({
					staffType: 2,
					orgCode,
				})
				this.formItems[1].options = res.map(item => {
					const { staffId, staffName } = item
					return {
						value: staffId,
						label: staffName,
					}
				})
			} catch (error) {
				console.error(error)
				this.formItems[1].options = []
			}
		},
		async getList() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) {
				return
			}
			this.loading = true
			try {
				const extractedData = Object.assign({}, ...Object.values(this.tabData))
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
					archivesId: extractedData?.archivesId,
				})
				if (formParams.modifyTime && formParams.modifyTime.length > 1) {
					Object.assign(formParams, {
						startCallPaymentTime: this.dayjs(formParams.modifyTime[0]).format('YYYY-MM-DD'),
						endCallPaymentTime: this.dayjs(formParams.modifyTime[1]).format('YYYY-MM-DD'),
					})
					delete formParams.modifyTime
				}
				const { total = 0, records = [] } = await queryUrgeRecordPage3(formParams)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleSearch() {
			this.handleChangePage({ page: 1 })
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.handleSearch()
		},
	},
}
</script>
