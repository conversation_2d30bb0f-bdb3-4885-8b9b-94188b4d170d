<template>
	<!-- 设计原则，尽可能把按钮留在父组件中操作，注意使用具名插槽-->
	<div class="detail-card">
		<div class="card-header">
			<img class="detail-bg" :src="detailCardInfo.bgUrl" alt="" />
			<div class="header-title">
				<img :src="detailCardInfo.signUrl" alt="" />
				<span class="title-text">{{ detailCardInfo.cardName }}</span>
				<!-- tag的背景色，字体颜色等不定，所以请自行定义背景色，字体颜色，小圆点颜色，传入格式见注释 -->
				<slot name="tag"></slot>
			</div>
			<div class="header-num">
				<div class="num-show">
					<p>{{ headerNum.value }}</p>
					<p>{{ headerNum.key }}</p>
				</div>
				<!-- 操作按钮 -->
				<slot name="header-button"></slot>
			</div>
		</div>
		<!-- 默认如下，可自行使用slot传入，类名保持一致即可-主要是为了避免某项需要对应生成按钮，便于父组件自行判断 -->
		<slot name="card-content">
			<div class="card-content">
				<div class="content-item" v-for="item in displayList" :key="item.key">
					<p class="field">{{ item.key }}</p>
					<p class="value">{{ item.value }}</p>
				</div>
				<!-- 具有特殊样式的,传入div,布局遵循下列class即可-->
				<slot name="special-bg">
					<div class="special-bg" v-if="specialFlag">
						<div class="small-card-header">
							<div class="label">
								<span class="identification"></span>
								<span>所属档案</span>
							</div>
							<!-- 如不想整块完全代替，可只替换此块，其他采用传参即可 -->
							<slot name="archives-status">
								<div class="status">
									<span>已建档</span>
									<span class="next">&gt;</span>
								</div>
							</slot>
						</div>
						<div class="small-card-content">
							<div class="content-item" v-for="item in specialDisplayList" :key="item.key">
								<p class="field">{{ item.key }}</p>
								<p class="value" v-if="!item.company">{{ item.value }}</p>
								<p class="value" v-else>
									<span class="money">{{ item.value }}</span>
									{{ item.company }}
								</p>
							</div>
						</div>
					</div>
				</slot>
			</div>
		</slot>
		<!-- 底部按钮，请传入div,class为'card-footer' -->
		<slot name="card-footer"></slot>
	</div>
</template>

<script>
export default {
	name: 'GcDetailCard',
	props: {
		// 头部信息(静态信息)
		detailCardInfo: {
			type: Object,
			required: true,
			default: () => {
				return {}
			},
		},
		// 头部展示字段
		headerNum: {
			type: Object,
			required: true,
			default: () => {
				return {}
			},
		},
		// 内容区展示字段
		displayList: {
			type: Array,
			default: () => [],
		},
		// 特殊样式
		specialFlag: {
			type: Boolean,
			default: false,
		},
		// 特殊内容区展示字段
		specialDisplayList: {
			type: Array,
			default: () => [],
		},
	},
}
</script>
<style lang="scss" scoped>
.detail-card {
	width: 270px;
	min-width: 270px;
	height: 100%;
	background: #fff;
	border-radius: 4px;
	box-shadow: 0px 6px 23px 0px rgba(0, 0, 0, 0.03);
	overflow: hidden;
	margin-right: $base-margin;
	padding-bottom: 24px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	font-size: $base-font-size-default;
	.card-header {
		position: relative;
		.detail-bg {
			width: 100%;
			position: absolute;
			top: 0;
			left: 0;
		}
		.header-title {
			padding: 117px 20px 23px 20px;
			img,
			span {
				vertical-align: middle;
			}
			img {
				width: 22px;
				height: 22px;
				margin-right: 8px;
			}
			.title-text {
				color: $base-color-6;
				font-size: $base-font-size-default;
			}
			.tag {
				margin-left: 6px;
				display: inline-block;
				border-radius: 2px;
				font-size: $base-font-size-small;
				transform: scale(0.8, 0.8);
				padding: 4px;
				i {
					display: inline-block;
					width: 4px;
					height: 4px;
					border-radius: 50%;
					vertical-align: middle;
					margin-right: 3px;
				}
			}
		}
		.header-num {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-bottom: $base-padding;
			margin: 0 $base-margin;
			border-bottom: 1px solid #cccccc;
			.el-button {
				border-radius: 17px;
			}
			.num-show {
				p:nth-child(1) {
					@include base-bold(18);
					word-break: break-all;
					font-weight: 600;
				}
				p:nth-child(2) {
					font-size: 10px;
					color: $base-color-9;
					padding-top: 8px;
				}
			}
		}
	}
	.card-content {
		flex: 1;
		overflow: auto;
		margin-right: 3px;
		.content-item {
			padding: $base-padding $base-padding 0 $base-padding;
			.field {
				font-size: $base-font-size-small;
				color: $base-color-9;
			}
			.value {
				font-size: $base-font-size-default;
				color: #4a4a4a;
				margin-top: 10px;
				word-break: break-all;
				.money {
					color: $base-color-green;
				}
			}
		}
		.special-bg {
			background: #f5f9ff;
			border-radius: 4px;
			margin: 20px 10px 20px 10px;
			padding: $base-padding 0;
			.small-card-header {
				display: flex;
				justify-content: space-between;
				padding: 0 10px;
				.label {
					font-size: $base-font-size-default;
					color: $base-color-3;
					span {
						display: inline-block;
						vertical-align: middle;
					}
					.identification {
						width: 2px;
						height: 100%;
						background: $base-color-blue;
						margin-right: 6px;
					}
				}
				.status {
					font-size: $base-font-size-default;
					color: $base-color-blue;
					.next {
						margin-left: 8px;
					}
				}
			}
		}
	}
	.card-footer {
		padding: 0 $base-padding;
		span + span {
			margin-left: 16px;
		}
		span {
			cursor: pointer;
		}
	}
}
</style>
