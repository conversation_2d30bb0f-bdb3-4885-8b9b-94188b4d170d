<template>
	<gc-detail-tab
		ref="detailTabRef"
		:tab-list="tabList"
		:default-active-name.sync="defaultActiveName"
		@tab-change="handleTabChange"
	>
		<div class="wrapper">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			/>
		</div>
	</gc-detail-tab>
</template>

<script>
export default {
	name: 'CollectionOrderConfig',
	components: {},
	data() {
		return {
			defaultActiveName: 'rm',
			tabList: [
				{
					name: 'rm',
					label: '人民银行',
				},
				{
					name: 'gs',
					label: '工商银行',
				},
				{
					name: 'lh',
					label: '联合收费中心',
				},
				{
					name: 'yz',
					label: '邮政储蓄银行',
				},
			],

			loading: false,
			tableData: [{}],
			columns: [
				{
					key: 'orgName',
					name: '营业分公司',
					tooltip: true,
				},
				{
					key: 'bankCode',
					name: '银行代码',
					tooltip: true,
				},
				{
					key: 'tsNo',
					name: '托收代码',
					tooltip: true,
				},
				{
					key: 'bankNo',
					name: '银行行号',
					tooltip: true,
				},
				{
					key: 'account',
					name: '账号',
					tooltip: true,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},

	created() {
		this.getList()
	},
	methods: {
		handleTabChange(tab) {
			console.log('🚀 ~ handleTabChange ~ data:', tab)
		},

		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				// TODO: 接口对接
				// const { current, size } = this.pageData;
				// const { total = 0, records = [] } = await getBookList({
				//   size,
				//   current,
				//   ...this.params,
				// });
				// this.pageData.total = total;
				// this.tableData = records;
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	padding: 20px;
}

.right-top {
	display: flex;
	justify-content: flex-end;
	align-items: flex-start;
	margin-bottom: 12px;
}
</style>
