<template>
	<gc-el-dialog :show="isShow" title="用水量监控设置" width="400px" @close="handleClose">
		<el-form ref="formRef" :inline="true" :model="formData" :rules="formRules" class="use-monitor-form">
			<el-form-item label="月平均水平偏差" prop="waterAmount">
				<el-input v-model="formData.waterAmount" type="number"></el-input>
			</el-form-item>
			%
		</el-form>
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { updateBusinessHallWaterAmount } from '@/api/basicConfig.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				orgCode: '',
				waterAmount: '',
			},
			formRules: {
				waterAmount: [{ required: true, message: '请输入百分比偏差数值', trigger: 'blur' }],
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			this.$refs.formRef.validate(valid => {
				console.info(valid)
				if (valid) {
					this.$confirm('是否提交该用水量监控设置？', '用水量监控设置确认')
						.then(async () => {
							await updateBusinessHallWaterAmount(this.formData)
							this.$message.success('用水量监控设置成功')
							this.$emit('success')
							this.isShow = false
						})
						.catch(() => {})
				}
			})
		},
		handleClose() {
			this.formData = {
				orgCode: '',
				waterAmount: '',
			}
			this.$refs.formRef.resetFields()
			this.isShow = false
		},

		// 设置orgCode
		setFormData(data) {
			const { orgCode, waterAmount } = data || {}
			this.formData.orgCode = orgCode || ''
			this.formData.waterAmount = waterAmount || ''
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
.use-monitor-form {
	display: flex;
	align-items: center;
	margin: 30px 0;
	.el-form-item {
		margin-bottom: 0;
		::v-deep {
			.el-form-item__label {
				padding-right: 10px;
			}
			.el-input__inner {
				width: 60px;
			}
			.el-input-number--small,
			.el-input-number--small .el-input__inner {
				width: 100px;
			}
		}
	}
}
</style>
