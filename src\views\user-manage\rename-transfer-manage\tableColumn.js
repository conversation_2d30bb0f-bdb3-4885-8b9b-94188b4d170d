import { getfilterName } from '@/utils'
export function getColumn(_this) {
	let renameArr = [
		{
			key: 'createTime',
			name: '业务操作时间',
			tooltip: true,
		},
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'addressFullName',
			name: '地址',
			tooltip: true,
		},
		{
			key: 'userType',
			name: '档案类型',
			tooltip: true,
			render: (h, row) => {
				const { userType = [] } = _this.$store.getters.dataList || {}
				const value = getfilterName(userType, row.userType, 'sortValue', 'sortName')
				return h('span', {}, value)
			},
		},
		{
			key: 'oldUserName',
			name: '更名前用户名称',
			tooltip: true,
		},
		{
			key: 'newUserName',
			name: '更名后用户名称',
			tooltip: true,
		},
		{
			key: 'operatorPerson',
			name: '业务操作人',
			tooltip: true,
		},
		{
			key: 'operatorReason',
			name: '更名原因',
			tooltip: true,
		},
	]
	let transferArr = [
		{
			key: 'beforeContent',
			name: '过户前用户名称',
			tooltip: true,
		},
		{
			key: 'afterContent',
			name: '过户后用户名称',
			tooltip: true,
		},
		{
			key: 'userType',
			name: '档案类型',
			tooltip: true,
			render: (h, row) => {
				const { userType = [] } = _this.$store.getters.dataList || {}
				const value = getfilterName(userType, row.userType, 'sortValue', 'sortName')
				return h('span', {}, value)
			},
		},
		{
			key: 'addressFullName',
			name: '地址',
			tooltip: true,
		},
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'createTime',
			name: '业务操作时间',
			tooltip: true,
		},
		{
			key: 'createStaffName',
			name: '操作人',
			tooltip: true,
		},
	]

	let modifyArr = [
		{
			key: 'enterpriseNumber',
			name: '企业编号',
			tooltip: true,
		},
		{
			key: 'beforeContent',
			name: '修改前企业信息',
			tooltip: true,
		},
		{
			key: 'afterContent',
			name: '修改后企业信息',
			tooltip: true,
		},
		{
			key: 'operatorReason',
			name: '是否同步修改用户',
			tooltip: true,
		},
		{
			key: 'createTime',
			name: '业务操作时间',
			tooltip: true,
		},
		{
			key: 'createStaffName',
			name: '操作人',
			tooltip: true,
		},
	]

	const TABLE_MAP = {
		rename: renameArr,
		transfer: transferArr,
		modify: modifyArr,
	}

	return TABLE_MAP[_this.activeTab] || []
}
