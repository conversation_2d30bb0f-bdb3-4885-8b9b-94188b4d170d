<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-02 19:07:24
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 15:01:53
-->
<template>
	<div class="container">
		<div class="container-title">
			<gc-model-header
				class="info-title"
				title=""
				:icon="require('@/assets/images/icon/title-common-parameters.png')"
			>
				<template v-slot:left>
					<GcFormSimple
						ref="formRef"
						v-model="formData"
						:formItems="formItemsLeft"
						:formAttrs="formAttrs"
					></GcFormSimple>
				</template>
				<template v-slot:right>
					<GcFormSimple
						ref="formRef"
						v-model="formData"
						:formItems="formItemsRight"
						:formAttrs="formAttrs"
					></GcFormSimple>
				</template>
			</gc-model-header>
		</div>
		<el-radio-group v-model="type">
			<el-radio-button :label="1">复核异常</el-radio-button>
			<el-radio-button v-has="'plan-collection_meterReadingReview_v2_getReviewDetailList2'" :label="2">
				自动审核通过
			</el-radio-button>
			<el-radio-button v-has="'plan-collection_meterReadingReview_v2_getReviewDetailList3'" :label="3">
				水量拆分
			</el-radio-button>
			<el-radio-button v-has="'plan-collection_meterReadingReview_v2_getReviewDetailList4'" :label="4">
				复核通过
			</el-radio-button>
		</el-radio-group>
		<Abnormal ref="table1" v-if="type === 1" :type="type" :top-params="formData" />
		<AutoPass ref="table2" v-if="type === 2" :type="type" :top-params="formData" />
		<WaterSplit ref="table3" v-if="type === 3" :type="type" :top-params="formData" />
		<Pass ref="table4" v-if="type === 4" :type="type" :top-params="formData" />
	</div>
</template>

<script>
import { bookTypeOptions } from '@/consts/optionList.js'
import { getAlleyMap, getReviewNewBooks } from '@/api/meterReading.api.js'
import Abnormal from './abnormal/index.vue'
import AutoPass from './auto-pass/index.vue'
import Pass from './pass/index.vue'
import WaterSplit from './water-split/index.vue'
export default {
	name: '',
	components: { Abnormal, AutoPass, Pass, WaterSplit },
	data() {
		return {
			type: 1,
			formData: {
				orgCode: '',
				taskYear: this.dayjs().format('YYYY'),
				taskMonth: Number(this.dayjs().format('MM')).toString(),
				bookType: 1,
				alleyId: '',
			},
			formItemsLeft: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						col: 24,
						clearable: false,
						placeholder: '请选择营业分公司',
					},
					events: {
						change: () => {
							this.formData.alleyId = ''
							this.formData.bookIds = []
							this.getAlleyMapData()
							this.getBookMapData()
							this.getTableList()
						},
					},
				},
				{
					type: 'el-date-picker',
					label: '抄表年',
					prop: 'taskYear',
					attrs: {
						col: 24,
						type: 'year',
						clearable: false,
						valueFormat: 'yyyy',
					},
					formItemAttrs: {
						labelWidth: '60px',
					},
					events: {
						change: () => {
							this.formData.bookIds = []
							this.getBookMapData()
							this.getTableList()
						},
					},
				},
				{
					type: 'el-select',
					label: '抄表月',
					prop: 'taskMonth',
					options: [
						{
							value: '1',
							label: '1月',
						},
						{
							value: '2',
							label: '2月',
						},
						{
							value: '3',
							label: '3月',
						},
						{
							value: '4',
							label: '4月',
						},
						{
							value: '5',
							label: '5月',
						},
						{
							value: '6',
							label: '6月',
						},
						{
							value: '7',
							label: '7月',
						},
						{
							value: '8',
							label: '8月',
						},
						{
							value: '9',
							label: '9月',
						},
						{
							value: '10',
							label: '10月',
						},
						{
							value: '11',
							label: '11月',
						},
						{
							value: '12',
							label: '12月',
						},
					],
					attrs: {
						col: 24,
						clearable: false,
					},
					formItemAttrs: {
						labelWidth: '60px',
						class: 'task-month',
					},
					events: {
						change: () => {
							this.formData.bookIds = []
							this.getBookMapData()
							this.getTableList()
						},
					},
				},
				{
					type: 'el-select',
					label: '册本类型',
					prop: 'bookType',
					options: bookTypeOptions,
					attrs: {
						col: 24,
						clearable: false,
					},
					formItemAttrs: {
						class: 'book-type',
					},
					events: {
						change: () => {
							this.formData.bookIds = []
							this.getBookMapData()
							this.getTableList()
						},
					},
				},
			],
			formItemsRight: [
				{
					type: 'el-select',
					label: '坊别',
					prop: 'alleyId',
					options: [],
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择坊别',
					},
					events: {
						change: () => {
							this.formData.bookIds = []
							this.getBookMapData()
							this.getTableList()
						},
					},
				},
				{
					type: 'el-select',
					label: '册本选择',
					prop: 'bookIds',
					options: [],
					attrs: {
						col: 24,
						clearable: true,
						remote: true,
						filterable: true,
						loading: false,
						multiple: true,
						collapseTags: true,
						placeholder: '请选择册本',
						remoteMethod: this.apiGetBookData,
					},
					events: {
						change: () => {
							this.getTableList()
						},
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '80px',
			},
		}
	},
	computed: {
		orgOptions() {
			return this.formItemsLeft[0].options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					// 默认当前登录人
					this.formData.orgCode = newVal[0].value
					this.getAlleyMapData()
					this.getBookMapData()
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		// 获取坊别数据
		async getAlleyMapData() {
			try {
				const res = await getAlleyMap({
					orgCode: this.formData.orgCode,
				})
				if (res) {
					this.formItemsRight[0].options = res.map(item => {
						return {
							value: item.id,
							label: item.alleyName,
						}
					})
				}
			} catch (error) {
				console.error(error)
				this.formItemsRight[0].options = []
			}
		},
		// 册本
		async getBookMapData() {
			try {
				const res = await getReviewNewBooks(this.formData)

				const data = res.map(item => {
					return {
						label: item.bookNo,
						value: item.bookId,
						disabled: false,
					}
				})
				this.formItemsRight[1].options = [...data]
			} catch (error) {
				console.error(error)
				this.formItemsRight[1].options = []
			}
		},
		// 查询列表
		getTableList() {
			this.$refs[`table${this.type}`]?.getList(1)
		},
	},
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
}
.container-title {
	display: flex;
	align-items: center;
}
.el-radio-group {
	padding: 0 20px;
}
.data-item {
	display: flex;
	align-items: center;
	.label {
		color: #4e4e4e;
		font-size: 14px;
	}
	.value {
		font-weight: bold;
	}
}
.ml12 {
	margin-left: 12px;
}
.info-title {
	width: 100%;
	height: auto;
	min-height: 60px;
	::v-deep {
		.el-form-item {
			margin-bottom: 0;
			.el-form-item__label {
				padding-right: 10px;
			}
		}
		input {
			width: 180px;
		}
		.el-date-editor input,
		.book-type input {
			width: 90px;
		}
		.task-month input {
			width: 90px;
		}
	}
}
</style>
