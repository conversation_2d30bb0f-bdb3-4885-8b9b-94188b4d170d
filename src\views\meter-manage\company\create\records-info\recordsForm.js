export function getFormItems(_this) {
	const baseFormItemsArr = [
		{
			type: 'slot',
			slotName: 'baseInfo',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'slot',
			label: '表卡编号',
			prop: 'archivesIdentity',
			slotName: 'archivesIdentity',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-radio',
			label: '小区总表',
			prop: 'summaryArchives',
			options: [
				{ label: '否', value: 0 },
				{ label: '是', value: 1 },
			],
			attrs: {
				col: 6,
			},
			events: {
				input: value => {
					_this.changeSummaryArchives(value)
					_this.$nextTick(() => {
						_this.$refs.formRef.clearValidate()
						_this.validateForm()
					})
				},
			},
		},
		{
			type: 'el-input',
			label: '账号',
			prop: 'accountNumber',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '旧表卡编号',
			prop: 'oldArchivesIdentity',
			attrs: {
				placeholder: '请输入',
				col: 8,
			},
		},
		{
			type: 'slot',
			slotName: 'otherInfo',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-select',
			label: '价格名称',
			prop: 'priceName',
			options: [],
			attrs: {
				col: 8,
				clearable: true,
				disabled: Boolean(_this.$route.query.archivesId),
			},
			events: {
				change: _this.handleChangePrice,
			},
		},
		{
			type: 'el-select',
			label: '价格编号',
			prop: 'priceCode',
			options: [],
			attrs: {
				filterable: true,
				col: 8,
				disabled: Boolean(_this.$route.query.archivesId),
			},
			events: {
				change: _this.handleChangePrice,
			},
		},
		{
			type: 'el-input',
			label: '用水性质',
			prop: 'natureName',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-select',
			label: '计费类型',
			prop: 'billingTypeId',
			options:
				_this.$store.getters?.dataList?.billingType?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 8,
				disabled: true,
			},
		},
	]
	return baseFormItemsArr
}
