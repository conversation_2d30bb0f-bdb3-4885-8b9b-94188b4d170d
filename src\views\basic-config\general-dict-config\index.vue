<template>
	<div class="page-layout">
		<div class="page-left">
			<gc-model-header
				class="info-title"
				title="字典类型"
				:icon="require(`@/assets/images/icon/title-list.png`)"
			/>
			<el-button type="primary" size="small" style="margin-bottom: 12px" @click="handleAddDictType">
				新增字典类型
			</el-button>
			<div class="list-box">
				<div
					class="list-item"
					v-for="(item, index) in dictTypeList"
					:key="item.id"
					:class="{ active: listActive === index }"
					@click="handleYearClick(index)"
				>
					<div class="label">{{ item.name }}</div>
					<el-dropdown @click.stop>
						<i class="icon-more el-icon-more" @click.stop></i>
						<el-dropdown-menu slot="dropdown">
							<el-dropdown-item @click.native="handleAdjustDictType(item)">修改</el-dropdown-item>
							<el-dropdown-item @click.native="handleDeleteDictType(item)">删除</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</div>
			</div>
		</div>
		<div class="page-right">
			<div class="right-top">
				<el-button type="primary" @click="handleAddEnums">新增枚举</el-button>
			</div>
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:deal="{ row }">
					<el-button type="text" size="medium" @click="handleAdjustEnums(row)">修改</el-button>
					<el-button type="text" size="medium" @click="handleDeleteEnums(row)">删除</el-button>
				</template>
			</GcTable>
		</div>

		<!-- 新增、修改字典类型弹窗 -->
		<UpdateDictTypeDialog
			ref="updateDictTypeDialogRef"
			:show.sync="showUpdateDictTypeDialog"
			:edit-type="updateType"
			@success="getDictTypeList(listActive)"
		/>
		<!-- 新增、修改枚举弹窗 -->
		<UpdateEnumsDialog
			ref="updateEnumsDialogRef"
			:show.sync="showUpdateEnumsDialog"
			:edit-type="updateType"
			@success="getList"
		/>
	</div>
</template>

<script>
import UpdateDictTypeDialog from './components/UpdateDictTypeDialog.vue'
import UpdateEnumsDialog from './components/UpdateEnumsDialog.vue'

export default {
	name: 'GeneralDictConfig',
	components: { UpdateDictTypeDialog, UpdateEnumsDialog },
	data() {
		return {
			// 左侧列表
			listActive: 0,
			dictTypeList: [],
			// 右侧列表
			loading: false,
			tableData: [{}],
			columns: [
				{
					key: 'dictType',
					name: '字典类型',
					tooltip: true,
				},
				{
					key: 'enumsDesc',
					name: '枚举描述',
					tooltip: true,
				},
				{
					key: 'enumsCode',
					name: '枚举编码',
					tooltip: true,
				},
				{
					key: 'remark',
					name: '备注',
					tooltip: true,
				},
				{
					key: 'deal',
					name: '操作',
					fixed: 'right',
					width: 100,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 弹窗类型
			updateType: 'add',
			// 新增、修改字典类型弹窗
			showUpdateDictTypeDialog: false,
			showUpdateEnumsDialog: false,
		}
	},
	computed: {},
	created() {
		this.getDictTypeList()
	},
	methods: {
		handleYearClick(index) {
			if (this.listActive !== index) {
				this.listActive = index
				this.getList()
			}
		},
		// 获取左侧字典类型列表
		async getDictTypeList(active = 0) {
			try {
				// const res = await xxxx
				this.dictTypeList = [
					{
						id: 1,
						name: '字典类型1',
					},
					{
						id: 2,
						name: '字典类型2',
					},
					{
						id: 3,
						name: '字典类型3',
					},
				]
				this.listActive = active
				this.getList(1)
			} catch (error) {
				console.error(error)
				this.dictTypeList = []
				this.listActive = 0
			}
		},
		// 新增字典类型
		handleAddDictType() {
			this.updateType = 'add'
			this.showUpdateDictTypeDialog = true
		},
		// 修改字典类型
		handleAdjustDictType(data) {
			this.updateType = 'edit'
			this.showUpdateDictTypeDialog = true
			this.$nextTick(() => {
				this.$refs.updateDictTypeDialogRef.setFormData(data)
			})
		},
		// 删除字典类型
		handleDeleteDictType(data) {
			console.log('🚀 ~ handleDelete ~ data:', data)
			this.$confirm('确定要删除该字典类型吗？').then(() => {
				this.$message.success('删除成功')
				this.getDictTypeList()
			})
		},

		// 右侧列表
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				// TODO: 接口对接
				// const { current, size } = this.pageData;
				// const { total = 0, records = [] } = await getBookList({
				//   size,
				//   current,
				//   ...this.params,
				// });
				// this.pageData.total = total;
				// this.tableData = records;
			} catch (error) {
				console.error(error)
				this.tableData = []
			} finally {
				this.loading = false
			}
		},
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		// 新增枚举
		handleAddEnums() {
			this.updateType = 'add'
			this.showUpdateEnumsDialog = true
		},
		// 修改枚举
		handleAdjustEnums(data) {
			this.updateType = 'edit'
			this.showUpdateEnumsDialog = true
			this.$nextTick(() => {
				this.$refs.updateEnumsDialogRef.setFormData(data)
			})
		},
		// 删除枚举
		handleDeleteEnums(data) {
			console.log('🚀 ~ handleDelete ~ data:', data)
			this.$confirm(`正在对【${data.enumsDesc ?? '--'}】进行删除，请确认是否删除？`).then(() => {
				this.$message.success('删除成功')
				this.getList(1)
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.page-left {
	overflow: auto;
}
.right-top {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-bottom: 12px;
}
.info-title {
	height: auto;
	padding: 0;
	margin-bottom: 12px;
}
.icon-more {
	transform: rotate(90deg);
}
.list-box {
	flex: 1;
	overflow: auto;
	.list-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 42px;
		padding: 0 12px;
		cursor: pointer;
		.label {
			flex: 1;
			margin-right: 12px;
			@include text-overflow;
		}
		&.active {
			color: #2f87fe;
			background-color: rgba(196, 221, 255, 0.5);
			.el-dropdown {
				display: block;
			}
		}
	}
	.el-dropdown {
		display: none;
	}
}
::v-deep {
	.el-dropdown-menu__item {
		min-width: auto;
	}
}
</style>
