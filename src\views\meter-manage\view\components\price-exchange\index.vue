<template>
	<GcElDialog :show="isShow" title="换价" okText="保存" @close="handleClose" @cancel="handleClose" @ok="handleSave">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
	</GcElDialog>
</template>

<script>
import { getFormItems } from './form.js'
import { ruleRequired } from '@/utils/rules.js'
import { getfilterName } from '@/utils'
import { isBlank } from '@/utils/validate.js'
import { apiArchivesChangePrice, apiArchivesChangePrice1, apiEffectivePrice } from '@/api/meterManage.api.js'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		detailData: {
			type: Object,
			default: () => ({}),
		},
		permissionCode: {
			type: String,
			default: 'cpm_archives_change-price',
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	watch: {
		isShow: {
			async handler(val) {
				if (val) {
					this.formItems = getFormItems(this)
					await this._apiEffectivePrice()
				}
			},
		},
	},
	data() {
		return {
			formData: {
				priceId: '',
				priceInfo: '',
				natureName: '',
				billingTypeName: '',
				effectiveTime: '',
				bilItemList: '',
				isReset: '',
			},
			formItems: [],
			formAttrs: {
				rules: {
					priceId: [ruleRequired('必填')],
					isReset: [],
				},
			},
		}
	},
	methods: {
		async _apiEffectivePrice() {
			const data = await apiEffectivePrice()
			const obj = this.formItems.find(item => item.prop === 'priceId')
			obj.options = data
				.filter(item => item.priceId !== this.detailData.priceId)
				.map(item => {
					return {
						label: `${item.priceName} - ${item.priceCode}`,
						value: item.priceId,
						...item,
					}
				})
		},
		changePrice(v) {
			const obj = this.formItems.find(item => item.prop === 'priceId')
			const priceObj = obj.options.find(item => item.value === v)
			const isResetObj = this.formItems.find(item => item.prop === 'isReset')
			if (v) {
				if (priceObj.levelPrice) {
					const levelBorderStr = `阶梯量: ${priceObj.levelBorder}（吨）`
					const levelPriceStr = `阶梯价: ${priceObj.levelPrice}（元/吨）`
					this.formData.priceInfo = levelPriceStr + '\n' + levelBorderStr
				} else {
					this.formData.priceInfo = `单一价: ${priceObj.singlePrice}（元/吨）`
				}
				// 只要阶梯价格换阶梯价格，需要展示重置余量的选项，其余情况不展示 默认为清零
				if (!this.detailData.levelPrice || !priceObj.levelPrice) {
					isResetObj.hide = true
					this.formAttrs.rules.isReset = []
					this.formData.isReset = ''
				} else {
					isResetObj.hide = false
					this.formAttrs.rules.isReset = [ruleRequired('必填')]
					this.formData.isReset = '1'
				}
				if (priceObj.priceBillItemList && priceObj.priceBillItemList.length) {
					const arr = priceObj.priceBillItemList.map(item => {
						return `${item.itemName}: ${item.billItemPrice}元/吨`
					})
					this.formData.bilItemList = arr.join('\n')
				} else {
					this.formData.bilItemList = ''
				}
				this.formData.natureName = priceObj.natureName
				this.formData.billingTypeName = getfilterName(
					this.$store.getters.dataList.billingType,
					priceObj.billingTypeId,
					'sortValue',
					'sortName',
				)
				this.formData.effectiveTime = priceObj.effectiveTime
			} else {
				this.$refs.formRef.resetFields()
			}
		},
		handleClose() {
			this.isShow = false
			this.$refs.formRef.resetFields()
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const params = {
				priceId: this.formData.priceId,
				archivesId: this.detailData.archivesId,
			}
			const apiMethods = {
				'cpm_archives_change-price': apiArchivesChangePrice,
				'cpm_archives_change-price1': apiArchivesChangePrice1,
			}
			const confirmAndSave = async () => {
				try {
					await apiMethods[this.permissionCode](params)
					this.$message.success('换价成功')
					this.$emit('refresh')
					this.handleClose()
				} catch (error) {
					console.error('换价失败:', error)
				}
			}

			if (!isBlank(this.formData.isReset)) {
				Object.assign(params, { isReset: this.formData.isReset })

				const obj = this.formItems.find(item => item.prop === 'priceId')
				const priceObj = obj.options.find(item => item.value === params.priceId)
				const isResetStr = params.isReset == '0' ? '是' : '否'
				const h = this.$createElement
				const message = h('div', [
					h('p', { style: 'marginBottom:10px' }, `更换价格为：${priceObj.label}`),
					h('p', { style: 'marginBottom:10px' }, `是否重置余量：${isResetStr}`),
					h('p', '请确认是否继续操作?'),
				])
				this.$confirm(message, '确认框').then(confirmAndSave)
			} else {
				await confirmAndSave()
			}
		},
	},
}
</script>
