import { isBlank } from '@/utils/validate'

// 是否支持小时抄表  supportHourReading  1支持，2不支持 默认空
export const supportHourReading = val => {
	if (val === null) return ''
	return val === 1 ? '是' : '否'
}

/**
 * 根据sortValue过滤出数据字典中sortName值
 * @param {*} sortValue
 * @param {Array} array
 * @returns sortName || --
 */
export const filterSortName = (sortValue, array = []) => {
	const result = array.find(item => item.sortValue == sortValue)
	return result?.sortName || '--'
}

// 过滤各阶余量
export const filterCycSurplus = (str, unit = 'm³') => {
	if (isBlank(str)) return '--'
	const arr = str.split('|')
	arr[arr.length - 1] = '∞'
	return arr.join(' | ') + ` （${unit}）`
}
