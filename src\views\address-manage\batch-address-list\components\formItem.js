export function getFormItems(_this) {
	const arr = [
		{
			type: 'el-select',
			label: '市',
			prop: 'cityCode',
			options: [],
			events: {
				change: value => {
					const regionIndex = _this.formItems.findIndex(item => item.prop === 'regionCode')
					const streetIndex = _this.formItems.findIndex(item => item.prop === 'streetCode')
					const neighbourhoodIndex = _this.formItems.findIndex(item => item.prop === 'neighbourhoodCode')
					const buildingIndex = _this.formItems.findIndex(item => item.prop === 'buildingCode')
					_this.formItems[regionIndex].options = []
					_this.formItems[streetIndex].options = []
					_this.formItems[neighbourhoodIndex].options = []
					_this.formItems[buildingIndex].options = []
					_this.formData.regionCode = ''
					_this.formData.streetCode = ''
					_this.formData.neighbourhoodCode = ''
					_this.formData.buildingCode = ''
					if (value) {
						_this._getCityOriRegionData(value, 'regionCode')
					}
				},
			},
		},
		{
			type: 'el-select',
			label: '区/县',
			prop: 'regionCode',
			options: [],
			events: {
				change: value => {
					const streetIndex = _this.formItems.findIndex(item => item.prop === 'streetCode')
					const neighbourhoodIndex = _this.formItems.findIndex(item => item.prop === 'neighbourhoodCode')
					const buildingIndex = _this.formItems.findIndex(item => item.prop === 'buildingCode')
					_this.formItems[streetIndex].options = []
					_this.formItems[neighbourhoodIndex].options = []
					_this.formItems[buildingIndex].options = []
					_this.formData.streetCode = ''
					_this.formData.neighbourhoodCode = ''
					_this.formData.buildingCode = ''

					if (value) {
						_this._getAddressAreaMap(value, 'streetCode')
					}
				},
			},
		},
		{
			type: 'el-select',
			label: '街道/乡镇',
			prop: 'streetCode',
			options: [],
			events: {
				change: value => {
					const neighbourhoodIndex = _this.formItems.findIndex(item => item.prop === 'neighbourhoodCode')
					const buildingIndex = _this.formItems.findIndex(item => item.prop === 'buildingCode')
					_this.formItems[buildingIndex].options = []
					_this.formItems[neighbourhoodIndex].options = []
					_this.formData.buildingCode = ''
					_this.formData.neighbourhoodCode = ''
					if (value) {
						_this._getAddressAreaMap(value, 'neighbourhoodCode')
					}
				},
			},
		},
		{
			type: 'el-select',
			label: '小区/村庄',
			prop: 'neighbourhoodCode',
			options: [],
			events: {
				change: value => {
					const buildingIndex = _this.formItems.findIndex(item => item.prop === 'buildingCode')
					_this.formItems[buildingIndex].options = []
					_this.formData.buildingCode = ''
					if (value) {
						_this._getAddressAreaMap(value, 'buildingCode')
					}
				},
			},
		},
		{
			type: 'el-select',
			label: '楼栋',
			prop: 'buildingCode',
			options: [],
		},
		{
			type: 'el-input',
			label: '详细地址',
			prop: 'addressName',
		},
	]
	arr.forEach(item => {
		_this.$set(_this.formData, item.prop, '')
	})
	return arr
}
