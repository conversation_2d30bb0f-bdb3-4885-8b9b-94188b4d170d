// 数据字典取值
export default {
	computed: {
		// 报警等级-数据字典
		alarmLevelArr() {
			return this.$store.getters.dataList.alarmLevel || []
		},
		// 档案状态-数据字典
		archiveStateArr() {
			return this.$store.getters.dataList.archiveState || []
		},
		// 计费方式-数据字典
		paymentTypeArr() {
			return this.$store.getters.dataList.paymentType || []
		},
		// 报警状态-数据字典
		alarmStatusArr() {
			return this.$store.getters.dataList.alarmStatus || []
		},
		// 表类别-数据字典
		meterCategoryArr() {
			return this.$store.getters.dataList.meterCategory || []
		},
		// 表具流量-数据字典
		meterFlowArr() {
			return this.$store.getters.dataList.meterFlow || []
		},
		// 安装位置-数据字典
		installPositionArr() {
			return this.$store.getters.dataList.installPosition || []
		},
		// 进气方向-数据字典
		gasDirectionArr() {
			return this.$store.getters.dataList.gasDirection || []
		},
		// 计费类型-数据字典
		billingTypeArr() {
			return this.$store.getters.dataList.billingType || []
		},
		// 操作类型-数据字典
		archivesOperatorTypeArr() {
			return this.$store.getters.dataList.archivesOperatorType || []
		},
		// 用户类型-数据字典
		userTypeArr() {
			return this.$store.getters.dataList.userType || []
		},
		// 其他证件-数据字典
		certificateTypeArr() {
			return this.$store.getters.dataList.certificateType || []
		},
		//费用类型-数据字典
		costTypeArr() {
			return this.$store.state.apiCache.costType || []
		},
		//操作类型-数据字典
		costOperationTypeArr() {
			return this.$store.getters.dataList.costOperationType || []
		},
		//费用状态-数据字典
		costRecordStatusArr() {
			return this.$store.getters.dataList.costRecordStatus || []
		},
		// 停用类型
		disableTypeArr() {
			return this.$store.getters.dataList.disableType || []
		},
		// 采集频率
		frequencyIdArr() {
			return [
				{ sortValue: '7', sortName: '1min' },
				{ sortValue: '1', sortName: '2min' },
				{ sortValue: '2', sortName: '5min' },
				{ sortValue: '3', sortName: '10min' },
				{ sortValue: '4', sortName: '30min' },
				{ sortValue: '5', sortName: '60min' },
				{ sortValue: '6', sortName: '120min' },
			]
		},
		// 分时价格
		timeUnitCodeArr() {
			return this.$store.getters.dataList.timeUnitCode || []
		},
		// 操作类型
		operatorTypeArr() {
			return this.$store.getters.dataList.operatorType || []
		},
		// 发票状态
		invoiceStateArr() {
			return this.$store.getters.dataList.invoiceState || []
		},
	},
}
