import store from '@/store'
import { isBlank } from '@/utils/validate.js'
import { JSEncrypt } from 'jsencrypt'

// toString
export const _toString = Object.prototype.toString
// 数组分割
export const splitArr = (arr, num) => {
	let length = arr.length
	let [...arrTemp] = arr
	let result = []
	for (let i = 0; i < length; i += num) {
		result.push(arrTemp.splice(0, num))
	}
	return result
}
// Excel解析后的日期格式化
export const excelDateFormat = (numb, format) => {
	try {
		const time = new Date((numb - 1) * 24 * 3600000 + 1)
		time.setYear(time.getFullYear() - 70)
		const year = time.getFullYear() + ''
		const month = time.getMonth() + 1 + ''
		const date = time.getDate() + ''
		if (format && format.length === 1) {
			return year + format + month + format + date
		}
		return year + '-' + (month < 10 ? '0' + month : month) + '-' + (date < 10 ? '0' + date : date)
	} catch (err) {
		console.error('日期格式化失败:', err)
		return undefined
	}
}

/**
 * 加密
 * @param { String } data 需要加密的数据
 * @param { String } key 公钥key
 * @param { String } iv
 */
export const encrypt = data => {
	const publicKey =
		'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMRpvV7/iqttMdvvCQzm2E7ZqnRWGp15RwHrYf9aUsN8KScoQ49VN8uhLJu07HKoWwlul28vfgBqnUKJ25R36b8CAwEAAQ=='
	const encryptor = new JSEncrypt()
	encryptor.setPublicKey(publicKey)
	return encryptor.encrypt(data)
}

/**
 * 日期格式化
 * @param {日期} date
 * @param {分隔符} seperator
 * @param {类型：秒级，日期级} type
 * @returns
 */
export const dateFormat = (date, seperator = '-', type = 'date') => {
	if (!date) date = new Date()
	// 小于10时前面加0
	let addZero = v => {
		if (v >= 0 && v <= 9) {
			return '0' + v
		} else {
			return v
		}
	}
	var year = date.getFullYear() // 年
	var month = addZero(date.getMonth() + 1) // 月
	var today = addZero(date.getDate()) // 日
	var hour = addZero(date.getHours()) // 时
	var minute = addZero(date.getMinutes()) // 分
	var second = addZero(date.getSeconds()) // 秒
	if (type == 'date') {
		return year + seperator + month + seperator + today
	} else {
		return year + month + today + hour + minute + second
	}
}
// 带T的时间格式化
// sp为分割符
export const convertUTC = (UTCDateString, sp, type) => {
	try {
		if (!UTCDateString) {
			return '-'
		}
		// 小于9的数字添加0
		let addZero = str => {
			return str > 9 ? str : '0' + str
		}
		let date = new Date(UTCDateString)
		let [year, month, day, hour, minute, second] = [
			date.getFullYear(),
			addZero(date.getMonth() + 1),
			addZero(date.getDate()),
			addZero(date.getHours()),
			addZero(date.getMinutes()),
			addZero(date.getSeconds()),
		]
		if (type == 'day') {
			return year + '-' + month + '-' + day
		} else {
			if (sp === '') {
				return year + sp + month + sp + day + sp + hour + sp + minute + sp + second
			} else {
				return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second
			}
		}
	} catch (err) {
		console.error('convertUTC转化时间时出错', err)
		return '--'
	}
}
// 把数字或字符串类型的数字保留n位的小数
// 当retain0为true时，保留多余的0；当retain0为false时，去除多余的0
export const NumToFix = (x, n, retain0) => {
	if (isBlank(x)) return '--'
	if (isNaN(x + 0)) {
		console.error('非数字')
		return ''
	}
	let num = Number(x)
	// 保留0,返回值类型会变成String！
	if (retain0) {
		return num.toFixed(n)
	}
	// 去除多余的0，返回值类型为Number
	else {
		return Number(num.toFixed(n))
	}
}
// opt: 解决浮点数二进制丢失问题
export const toFixed = (num, s) => {
	var times = Math.pow(10, s)
	var des = num * times + 0.5
	des = parseInt(des, 10) / times
	return des + ''
}
// 判断后端返回的msg中是否含有字母
export const hasLetter = str => {
	if (!str) {
		return false
	}
	for (var i in str) {
		var asc = str.charCodeAt(i)
		if ((asc >= 65 && asc <= 90) || (asc >= 97 && asc <= 122)) {
			return true
		}
	}
	return false
}
// 去除空字符串和null的参数
export const removeNullParams = data => {
	if (data.constructor === Object) {
		let obj = {}
		Object.keys(data).map(item => {
			data[item] !== '' && data[item] !== null ? (obj[item] = data[item]) : null
		})
		return obj
	} else {
		throw 'removeNullParams：参数非对象类型!'
	}
}
// 去除对象中每个值前后的空格
export const trimParams = data => {
	let resData = {}
	for (let i in data) {
		if (typeof data[i] == 'string' && data[i].constructor == String) {
			resData[i] = data[i].trim() // 去除两端的空格
		} else {
			resData[i] = data[i]
		}
	}
	return resData
}
// 数据字典value转name
export const nameConversion = (value, arr) => {
	let name = '--'
	arr.map(item => {
		if (value == item.sortValue) {
			name = item.sortName
		}
	})
	return name
}

// 支持廊坊铭顺 付款方式特殊处理
export const getFitPayMode = (arr, key = 'sortValue') => {
	if (!arr) return []
	return arr.filter(value => ![11, 12].includes(+value[key]))
}

//   等保三级认证，密码至少包含两种字符组合(数字/字母/特殊符号)
export const isComplexPass = value => {
	let totalPoint = 0,
		rule_digit = 0,
		rule_letter = 0,
		rule_special = 0
	// 数字
	if (value.match(/\d+/g)) {
		rule_digit = 1
	}
	// 字母
	if (value.match(/[a-zA-Z]+/g)) {
		rule_letter = 1
	}
	// 特殊字符
	if (value.match(/[^\w]+/g)) {
		rule_special = 1
	}
	totalPoint = rule_digit + rule_letter + rule_special
	return totalPoint < 2 ? false : true
}

// 数据字典数据项转枚举
export const getDataListEnum = data => {
	if (!data || _toString.call(data) !== '[object Array]') return
	return data.reduce((obj, item) => {
		obj[item.sortValue] = item.sortName
		return obj
	}, {})
}

// 权限判断
// 传入 单个权限字符串 或 权限字符串组成的数组
// Vue.$has(['archiveAdd']) 或 Vue.$has('archiveAdd')
export const has = curPermissions => {
	if (!curPermissions) {
		return false
	}
	const permissions = store.getters && store.getters.permissions
	const argType = _toString.call(curPermissions)

	if (argType === '[object Array]' && curPermissions.length > 0) {
		return permissions.some(role => {
			return curPermissions.includes(role)
		})
	} else if (argType === '[object String]') {
		return permissions.includes(curPermissions)
	}
	return false
}

// int补0转字符串
export const intAddZero = (value, n = 12) => {
	if (!value) return value
	if (String(value).length > 12) {
		return value
	} else {
		return (Array(n).join(0) + value).slice(-n)
	}
}

/**
 * 阶梯价或分时价处理
 * @param {*} str 阶梯价或分时价字符串
 * @returns Array
 */
export const handlePrice = str => {
	if (!str) return []
	const arr = str.split(/<br\/>/g)
	const result = arr.map(item => {
		const [key, value] = item.split(' ')
		return { key, value }
	})
	return result
}

/**
 * 拼接地址
 * @param {Object} address 地址
 * @returns String
 */
export const joinAddress = (address = {}) => {
	const { regionName = '', streetName = '', addressAreaName = '', addressName = '' } = address
	let result = ''
	// 接口字段返回可能为"null"，需要判空
	if (!isBlank(regionName)) result += regionName
	if (!isBlank(streetName)) result += streetName
	if (!isBlank(addressAreaName)) result += addressAreaName
	if (!isBlank(addressName)) result += addressName
	return result || '--'
}

// 从枚举项中找出某个value相对应的键名,常见于表格中某字段展示时
// eg：[{label:'名称1',value:1},{label:'名称2',value:2}] 已知value=2,需要展示“名称2”
export const getfilterName = (options, val, key = 'value', label = 'label') => {
	let filterName = '--'
	let filterArr = options.filter(it => it[key] == val)
	if (filterArr.length) {
		filterName = filterArr[0][label]
	}
	return filterName
}

/**
 * 截取小数点后指定位数
 * @param {*} count
 * @param {*} num
 */
export const decimalPointNumber = (count, num) => {
	const nums = String(count).split('.')
	if (nums.length === 2) {
		return Number(nums[0] + '.' + nums[1].substring(0, num))
	} else if (nums.length === 1) {
		return Number(nums[0])
	}
	return count
}

// blob下载
// export function exportBlob(res, name, type) {
// 	let blob = new Blob([res], {
// 		type: type || 'application/vnd.ms-excel',
// 	})
// 	window.URL = window.URL || window.webkitURL
// 	let href = URL.createObjectURL(blob)
// 	let downA = document.createElement('a')
// 	downA.href = href
// 	downA.download = name ? name : '文件.xls'
// 	document.body.appendChild(downA)
// 	downA.click()
// 	window.URL.revokeObjectURL(href)
// 	document.body.removeChild(downA)
// }

export function exportBlob(res, name, format = 'xls') {
	// 默认格式为 'xls'
	// 根据 format 参数设置 MIME 类型和默认文件名
	let mimeType
	let defaultFileName

	if (format === 'xls') {
		mimeType = 'application/vnd.ms-excel'
		defaultFileName = '文件.xls'
	} else if (format === 'xlsx') {
		mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
		defaultFileName = '文件.xlsx'
	} else {
		throw new Error('Invalid format. Please specify "xls" or "xlsx".')
	}

	// 创建 Blob 对象
	let blob = new Blob([res], {
		type: mimeType,
	})

	// 创建对象 URL
	window.URL = window.URL || window.webkitURL
	let href = URL.createObjectURL(blob)

	// 创建下载链接
	let downA = document.createElement('a')
	downA.href = href
	downA.download = name ? name : defaultFileName // 使用传入的文件名，如果没有则使用默认文件名
	document.body.appendChild(downA)
	downA.click()

	// 清理
	window.URL.revokeObjectURL(href)
	document.body.removeChild(downA)
}
