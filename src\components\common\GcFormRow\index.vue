<template>
	<el-form :model="formData" v-bind="formAttrs" ref="formRef" :inline="true">
		<el-row :gutter="20">
			<el-col v-for="(item, index) in formItems" :key="index" :span="(item.attrs && item.attrs.col) || 12">
				<el-form-item
					v-if="!item.hide"
					:label="item.label"
					:prop="item.prop"
					:label-width="item.labelWidth"
					:class="(item.attrs && item.attrs.className) || ''"
					:style="{ display: formAttrs.displayItem ? 'block' : 'flex' }"
				>
					<!-- slot具名插槽 -->
					<template v-if="item.type === 'slot'">
						<slot :name="item.slotName"></slot>
					</template>
					<!-- span-->
					<template v-if="item.type.indexOf('span') !== -1">
						<span>{{ formData[item.prop] }}</span>
					</template>
					<!-- el-input、el-input-number -->
					<template v-if="item.type.indexOf('el-input') !== -1">
						<component
							:is="item.type"
							v-model="formData[item.prop]"
							v-bind="{ placeholder: '请输入', ...item.attrs }"
							v-on="item.events"
						/>
					</template>
					<!-- el-select -->
					<template v-if="item.type === 'el-select'">
						<el-select
							v-model="formData[item.prop]"
							v-bind="{
								placeholder: '请选择',
								clearable: true,
								filterable: true,
								...item.attrs,
							}"
							v-on="item.events"
						>
							<el-option
								v-for="(option, selectIndex) in item.options"
								:key="selectIndex"
								:label="option.label"
								:value="option.value"
								:disabled="option.disabled"
							></el-option>
						</el-select>
					</template>
					<!-- el-radio -->
					<template v-if="item.type === 'el-radio'">
						<el-radio-group v-model="formData[item.prop]" v-bind="item.attrs" v-on="item.events">
							<el-radio
								v-for="(option, radioIndex) in item.options"
								:key="radioIndex"
								:label="option.value"
							>
								{{ option.label }}
							</el-radio>
						</el-radio-group>
					</template>
					<!-- el-date-picker -->
					<template v-if="item.type === 'el-date-picker'">
						<el-date-picker
							v-model="formData[item.prop]"
							v-bind="{
								placeholder: '请选择',
								clearable: true,
								...item.attrs,
							}"
							:type="(item.attrs && item.attrs.type) || 'date'"
						></el-date-picker>
					</template>
					<!-- el-cascader -->
					<template v-if="item.type === 'el-cascader'">
						<el-cascader
							v-model="formData[item.prop]"
							v-bind="{ placeholder: '请选择', clearable: true, ...item.attrs }"
							style="width: 100%"
						></el-cascader>
					</template>
					<!-- tofix:待补充 -->
				</el-form-item>
			</el-col>
			<!-- 插槽 -->
			<slot></slot>
		</el-row>
	</el-form>
</template>

<script>
export default {
	name: 'GcFormRow',
	props: {
		value: {
			type: Object,
			required: true,
		},
		formItems: {
			type: Array,
			required: true,
		},
		formAttrs: {
			type: Object,
		},
		isRow: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		formData: {
			get() {
				return this.value
			},
			set(newVal) {
				this.$emit('input', newVal)
			},
		},
	},
	methods: {
		validate() {
			return new Promise(resolve => {
				this.$refs.formRef.validate(valid => {
					resolve(valid)
				})
			})
		},
		validateField(prop) {
			return new Promise(resolve => {
				this.$refs.formRef.validateField(prop, valid => {
					resolve(valid)
				})
			})
		},
		// 校验多个字段
		validateMultipleFields(props) {
			return new Promise((resolve, reject) => {
				Promise.all(
					props.map(
						field =>
							new Promise(resolve => {
								this.$refs.formRef.validateField(field, resolve)
							}),
					),
				)
					.then(errorMessages => {
						// 检查是否所有错误信息都为空
						const valid = errorMessages.every(errorMessage => !errorMessage)
						resolve(valid)
					})
					.catch(reject)
			})
		},
		resetFields() {
			this.$refs.formRef.resetFields()
		},
		clearValidate(data = '') {
			this.$refs.formRef.clearValidate(data)
		},
		submitForm() {
			this.$refs.formRef.validate(valid => {
				if (valid) {
					this.resetForm()
				} else {
					console.log('error submit!!')
					return false
				}
			})
		},
		resetForm() {
			Object.keys(this.formData).forEach(key => {
				const value = this.formData[key]
				if (Array.isArray(value)) {
					this.$set(this.formData, key, [])
				} else if (typeof value === 'boolean') {
					this.$set(this.formData, key, false)
				} else if (value === null || value === undefined) {
					this.$set(this.formData, key, null)
				} else if (typeof value === 'object' && value !== null) {
					this.$set(this.formData, key, {})
				} else {
					this.$set(this.formData, key, '')
				}
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.el-form {
	::v-deep {
		.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label,
		.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label {
			position: relative;
			&:before {
				position: static !important;
				transform: none !important ;
			}
		}

		.el-row {
			.el-form-item {
				display: flex;
				width: 100%;
				.el-form-item__content {
					flex: 1;
				}
				.el-form-item__label {
					font-weight: 350;
					color: #5f627d;
				}
			}
		}
		.el-select {
			width: 100%;
		}
		.el-date-editor {
			width: 100%;
		}
	}
}
</style>
