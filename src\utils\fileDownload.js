import service from '@/api/request'

export default function fileDownload({ path, fileName, params = {} }) {
	const searchParams = new URLSearchParams(params).toString()
	if (searchParams) {
		path = `${path}?${searchParams}`
	}
	return new Promise((resolve, reject) => {
		service({
			url: path,
			method: 'GET',
			responseType: 'blob',
		})
			.then(response => {
				console.log('response', response)
				const blobUrl = window.URL.createObjectURL(response)
				let a = document.createElement('a')
				a.href = blobUrl
				a.download = fileName
				a.click()
				window.URL.revokeObjectURL(blobUrl)
				a = null
				resolve()
			})
			.catch(error => {
				reject(error)
			})
	})
}
