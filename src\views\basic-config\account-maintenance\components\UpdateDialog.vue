<template>
	<gc-el-dialog :show="isShow" :title="`${typeText}账项`" width="600px" @open="handleOpen" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { billStatusOptions } from '@/consts/optionList'
import { ruleMaxLength } from '@/utils/rules.js'
import { addBillItem, updateBillItem } from '@/api/basicConfig.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		editType: {
			type: String,
			default: 'add',
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},

		typeText() {
			return this.editType === 'add' ? '新增' : '编辑'
		},
	},
	data() {
		return {
			formData: {
				itemType: '',
				generateType: '',
				itemName: '',
				isSurcharge: 1,
				itemAmount: undefined,
				itemStatus: 1,
			},
			formItems: [
				{
					type: 'el-select',
					label: '账项类别',
					prop: 'itemType',
					options:
						this.$store.getters?.dataList?.billItemType?.map(item => {
							return {
								label: item.sortName,
								value: Number(item.sortValue),
							}
						}) || [],
					attrs: {
						col: 24,
						disabled: false,
						placeholder: '请选择账项类别',
					},
				},
				{
					type: 'el-select',
					label: '费用生成方式',
					prop: 'generateType',
					options:
						this.$store.getters?.dataList?.expenseGenerateWay?.map(item => {
							return {
								label: item.sortName,
								value: Number(item.sortValue),
							}
						}) || [],
					attrs: {
						clearable: true,
						disabled: false,
						placeholder: '请选择费用生成方式',
					},
				},
				{
					type: 'el-input',
					label: '账项名称',
					prop: 'itemName',
					attrs: {
						clearable: true,
						disabled: false,
						placeholder: '请输入账项名称',
					},
				},
				{
					type: 'el-radio',
					label: '是否附加费',
					prop: 'isSurcharge',
					options: [
						{
							label: '是',
							value: 1,
						},
						{
							label: '否',
							value: 0,
						},
					],
					attrs: {
						col: 24,
						disabled: false,
						placeholder: '',
					},
				},
				{
					type: 'el-input-number',
					label: '默认金额',
					prop: 'itemAmount',
					attrs: {
						clearable: true,
						placeholder: '请输入默认金额',
						controls: false,
					},
				},
				{
					type: 'el-radio',
					label: '状态',
					prop: 'itemStatus',
					options: billStatusOptions,
					attrs: {
						col: 24,
						placeholder: '',
					},
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					itemType: [
						{
							required: true,
							message: '请选择账项类别',
							trigger: 'change',
						},
					],
					generateType: [
						{
							required: true,
							message: '请选择费用生成方式',
							trigger: 'change',
						},
					],
					itemName: [
						{
							required: true,
							message: '请输入账项名称',
							trigger: 'blur',
						},
						ruleMaxLength(32, '账项名称'),
					],
					isSurcharge: [
						{
							required: true,
							message: '请选择是否附加费',
							trigger: 'change',
						},
					],
					itemAmount: [
						{
							required: true,
							message: '请输入默认金额',
							trigger: 'blur',
						},
						{
							pattern: /^(0|[1-9][0-9]{0,9})(\.\d{1,2})?$/,
							message: '最多10位正整数，2位小数',
							trigger: 'blur',
						},
					],
					itemStatus: [
						{
							required: true,
							message: '请选择状态',
							trigger: 'change',
						},
					],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				console.log('this.formData', this.formData)
				if (this.editType === 'add') {
					await addBillItem(this.formData)
				} else {
					await updateBillItem(this.formData)
				}
				this.$message.success(`${this.typeText}账项成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleOpen() {
			const isEdit = this.editType === 'edit'
			this.formItems[0].attrs.disabled = this.formItems[1].attrs.disabled = this.formItems[2].attrs.disabled = this.formItems[3].attrs.disabled = isEdit
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.formData = {
				itemType: '',
				generateType: '',
				itemName: '',
				isSurcharge: 1,
				itemAmount: undefined,
				itemStatus: 1,
			}
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}

::v-deep {
	.el-input-number {
		width: 100%;
		.el-input__inner {
			text-align: left;
		}
	}
}
</style>
