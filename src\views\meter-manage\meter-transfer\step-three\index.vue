<template>
	<div class="step-three-wrapper">
		<GcModelHeader title="用户信息" :icon="require('@/assets/images/icon/title-common-parameters.png')">
			<template slot="right">
				<Search v-show="userType === 3" :userType="3" :active-tab="{ id: 2 }" @use="handleSearchUser" />
			</template>
		</GcModelHeader>
		<div class="container">
			<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<template v-slot:otherInfo>
					<h5 class="gap-title">开票信息</h5>
				</template>
				<template v-slot:contract>
					<GcUploadFile v-model="formData.purchaseContractUrl" />
				</template>
				<template v-slot:otherMobile>
					<el-form-item class="other-mobile" label="其他手机" prop="otherMobile">
						<AddOtherMobile v-model="formData.otherMobile" :mobileList.sync="formData.mobileList" />
					</el-form-item>
				</template>
				<template v-slot:businessLicenseUrl>
					<GcUploadFile v-model="formData.businessLicenseUrl" />
				</template>
				<template v-slot:purchaseContractUrl>
					<GcUploadFile v-model="formData.purchaseContractUrl" />
				</template>
			</GcFormRow>
		</div>
	</div>
</template>

<script>
import getFormItems from './userForm.js'
import {
	ruleRequired,
	RULE_PHONE,
	RULE_INCORRECTEMAIL,
	ruleMaxLength,
	RULE_INCORRECTIDCARD,
	RULE_POSTALCODE,
	RULE_INTEGERONLY,
} from '@/utils/rules'
import { queryEnterprisePage, apiQueryEnterpriseInfoByNumber } from '@/api/userManage.api'
import AddOtherMobile from '@/views/meter-manage/components/AddOtherMobile'
import Search from '@/views/meter-manage/view/components/search'
export default {
	name: '',
	components: { AddOtherMobile, Search },
	props: {
		userType: {
			type: Number,
			default: 3,
		},
	},
	data() {
		return {
			formData: {
				userName: '',
				nameUsedBefore: '',
				contractNum: '',
				contactPeople: '',
				userMobile: '',
				contactPhone: '',
				households: '',
				resiPopulation: '',
				propertyOwner: '',
				zipCode: '',
				certificateNo: '',
				certificateType: '',
				otherCertificateNo: '',
				email: '',
				userSubType: '',
				chargingMethod: '',
				mailingAddress: '',
				otherMobile: '', // 其他手机号
				mobileList: [], // 其他手机号
				purchaseContractUrl: [],
				userId: '',
				// 企业用户特有字段
				enterpriseNumber: '',
				enterpriseName: '',
				collectionAccountId: '',
				businessLicenseUrl: [],
			},
			companyList: [],
			orgCode: '',
		}
	},
	computed: {
		formItems() {
			return getFormItems(this)[this.userType]
		},
		formAttrs() {
			const commonRules = {
				userName: [ruleRequired('必填'), ruleMaxLength(32)],
				userMobile: [RULE_PHONE],
				email: [RULE_INCORRECTEMAIL],
				mailingAddress: [ruleMaxLength(64)],
				otherMobile: [RULE_PHONE],
			}

			const resident = {
				labelWidth: '110px',
				labelPosition: 'right',
				rules: {
					...commonRules,
					chargingMethod: [ruleRequired('必填')],
					nameUsedBefore: [ruleMaxLength(32)],
					contractNum: [ruleMaxLength(64)],
					contactPeople: [ruleMaxLength(64)],
					contactPhone: [ruleMaxLength(32)],
					households: [
						{
							pattern: /^(?:[0-9]{1,2}|30)$/,
							message: '请输入0-30的整数',
							trigger: '',
						},
					],
					resiPopulation: [
						{
							pattern: /^(?:[0-9]{1,2}|64)$/,
							message: '请输入0-64的整数',
							trigger: '',
						},
					],
					propertyOwner: [ruleMaxLength(32)],
					zipCode: [RULE_POSTALCODE],
					certificateNo: [RULE_INCORRECTIDCARD],
					otherCertificateNo: [ruleMaxLength(32)],
				},
			}

			const company = {
				labelWidth: '115px',
				labelPosition: 'right',
				rules: {
					...commonRules,
					enterpriseNumber: [
						ruleRequired('必填'),
						{
							pattern: /^\d{7}$/,
							message: '必须为数字且7位',
							trigger: '',
						},
					],
					contactPeople: [ruleMaxLength(64)],
					contactPhone: [ruleMaxLength(32)],
					zipCode: [RULE_POSTALCODE],
					contractNum: [ruleMaxLength(64)],
					collectionAccountId: [RULE_INTEGERONLY],
				},
			}
			return this.userType === 3 ? resident : company
		},
	},
	watch: {
		formData: {
			handler: 'validateForm',
			deep: true,
		},
	},
	methods: {
		assignForm(obj) {
			this.formData = Object.assign(this.formData, obj)
			// 其他手机号回显
			this.formData.mobileList = obj.otherContactPhone ? obj.otherContactPhone.split(',') : []
			// 购房合同回显
			this.formData.purchaseContractUrl = obj.purchaseContractUrl ? JSON.parse(obj.purchaseContractUrl) : []
		},
		// 获取企业
		_apiGetCompany(orgCode) {
			if (this.orgCode !== orgCode) {
				this.companyList = []
				this.handleResetCompany()
			}
			this.orgCode = orgCode
		},
		async apiGetCompanyBySelect(queryString) {
			if (queryString) {
				const { records } = await queryEnterprisePage({
					current: 1,
					size: 10,
					orgCode: this.orgCode,
					enterpriseNumber: queryString,
				})
				this.companyList = records.map(item => ({
					label: item.enterpriseNumber + '',
					value: item.enterpriseNumber + '',
					obj: item,
				}))
			} else {
				this.companyList = []
			}

			if (!this.companyList.length) {
				this.handleResetCompany()
			}
		},
		handleResetCompany() {
			this.formData.enterpriseNumber = ''
			this.formData.enterpriseName = ''
			this.formData.chargingMethod = ''
			this.formData.collectionAccountId = ''
		},
		// 选择企业编号
		async handleChangeEnterprise(v) {
			const obj = this.formItems.find(item => item.prop === 'enterpriseNumber')
			obj.options.forEach(item => {
				if (item.value === v) {
					this.formData.enterpriseName = item.obj.enterpriseName
					this.formData.chargingMethod = item.obj.chargingMethod
					this.formData.collectionAccountId = item.obj.collectionAccountId
				}
			})
			if (v) {
				const data = await apiQueryEnterpriseInfoByNumber({ enterpriseNumber: v })
				this.assignForm(data)
				this.formData.userName = data.enterpriseName
				this.$emit('handleSelectEnterprise', data)
			}
		},
		handleSearchUser(obj) {
			this.assignForm(obj)
		},
		handleReset() {
			this.$nextTick(() => {
				this.$refs.formRef.resetForm()
				this.$refs.formRef.clearValidate()
			})
		},
		async validateForm() {
			const valid = await this.$refs.formRef.validate()
			return valid
		},
	},
}
</script>

<style lang="scss" scoped>
.step-three-wrapper {
	flex: 1;
	width: 100%;
}
::v-deep {
	.el-form {
		padding: 0 20px;
	}
}
.other-mobile {
	::v-deep {
		.el-form-item__error {
			position: absolute;
			top: 36px;
		}
	}
}
</style>
