/*  全局主题变量配置 */
@import './mixin.scss';

/*  色彩规范 */
$base-color-blue: #2f87fe;
$base-color-yellow: #ff9d57;
$base-color-green: #12b3c7;
$base-color-red: #ec6b60;
$base-color-black: #222;
$base-color-white: #fff;
$base-color-grey: rgba(0, 0, 0, 0.65);
/*  字体大小配置 */
$base-font-size-small: 12px;
$base-font-size-default: 14px;
$base-font-size-big: 16px;
$base-font-size-bigger: 18px;
$base-font-size-max: 22px;
/*  字体颜色配置 */
$base-color-2: #222222;
$base-color-3: #333333;
$base-color-4: #4e4e4e;
$base-color-6: #666666;
$base-color-9: #999999;

/*  菜单色彩配置 */
$base-column-submenu-background: #eff5ff; //分栏菜单背景色
$base-column-submenu-background-active: #c4ddff; //分栏菜单选中项背景色
$base-column-submenu-background-active-area: #d8e8fe; //分栏菜单选中菜单区域背景色
$base-menu-background: $base-color-blue; //横向、纵向菜单背景色
$base-menu-color: rgba(255, 255, 255, 0.7); //菜单文字颜色
$base-menu-color-active: #fff; //菜单选中文字颜色
$base-main-menu-background-active: rgba(255, 255, 255, 0.35); //主菜单选中背景色
$base-menu-background-active: rgba(255, 255, 255, 0.2); //菜单选中背景色
/*  尺寸配置 */
$base-border-radius: 2.5px; //圆角
$base-margin: 20px; //默认margin
$base-padding: 20px; //默认padding
$base-top-bar-height: 65px; //横向top-bar、logo、一级菜单的高度
$base-logo-height: 85px; //分栏logo的高度
$base-nav-height: 48px; //顶部nav-bar的高度
$base-tabs-height: 34px; //顶部标签页tabs-bar的高度
$base-tag-item-height: 34px; //顶部标签页tabs-bar中每一个item的高度
$base-menu-item-height: 40px; //菜单li标签的高度
$base-keep-alive-height: calc(
	100vh - #{$base-nav-height} - #{$base-tabs-height} - #{$base-padding} * 2
); //app-main的高度
$base-left-menu-width-min: 55px; //左侧菜单已折叠宽度
$base-left-menu-width: 289px; //左侧菜单未折叠宽度
$base-right-content-width: calc(100% - #{$base-left-menu-width}); //左侧菜单未折叠右侧区域宽度
/*  其他配置 */
$base-color-background: #eceff8; //主体背景色
$base-title-color: #fff; //标题颜色
$base-border-color: #dcdfe6; //边框颜色
$base-box-shadow: 2px 0px 4px 0px rgba(41, 46, 51, 0.1); //默认阴影
$base-z-index: 999; //默认层级
$base-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), border 0s, color 0.1s, font-size 0s; //默认动画
$base-color-scrollbar: #adacac; //滚动条背景色

$base-font-family: 'SourceHanSansCN-Regular, SourceHanSansCN';

/* 按钮相关 */
$base-button-radius: 50%; // 按钮圆角

:export {
	base-color-blue: $base-color-blue; // 主题色
	menu-color: $base-menu-color; // 菜单文字颜色
	menu-color-active: $base-menu-color-active; // 菜单选中文字颜色
	menu-background: $base-menu-background; // 菜单背景色
	column-submenu-background: $base-column-submenu-background; // 分栏菜单背景色
}
