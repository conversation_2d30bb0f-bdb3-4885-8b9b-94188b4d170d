<template>
	<div class="page-layout">
		<div class="page-left">
			<el-tabs v-model="activeTab" type="border-card" @tab-click="initFormData">
				<el-tab-pane v-for="(tab, index) in tabs" :key="tab.name" :label="tab.label" :name="tab.name">
					<GcFormSimple
						v-if="activeTab === tab.name"
						:ref="'formRef' + index"
						v-model="formData"
						:formItems="formItems"
						:formAttrs="formAttrs"
					/>
				</el-tab-pane>
			</el-tabs>
			<div class="btn-group">
				<el-button style="width: 50%" round @click="initFormData">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div class="right-top">
				<el-button type="primary" v-show="hasAddPermission" @click="handleSetting('add')">
					{{ buttonTitle }}
				</el-button>
				<el-button v-show="hasOperateRecordPermission" @click="handleView">操作记录</el-button>
			</div>
			<GcTable
				ref="tableRef"
				:key="activeTab"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:operate="{ row }">
					<el-button
						type="text"
						size="mini"
						v-show="hasSettingPermission"
						@click="handleSetting('edit', row)"
					>
						修改
					</el-button>
					<el-button
						type="text"
						size="mini"
						v-show="['community', 'street'].includes(activeTab) && hasEnablePermission"
						@click="hanldeIfEnable(row)"
					>
						{{ row.status === 1 ? '禁用' : '启用' }}
					</el-button>
					<el-button type="text" size="mini" v-show="activeTab == 'community'" @click="goBuilding(row)">
						楼栋维护
					</el-button>
				</template>
			</GcTable>
		</div>
		<!-- 新增修改小区弹窗 -->
		<AddressSetting
			ref="addressSettingRef"
			:show.sync="showDialog"
			:tab="activeTab"
			:editType="editType"
			:data="rowData"
			@success="handlePageChange({ page: 1 })"
		/>
	</div>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getColumn } from './tableColumn'
import { getFormItems } from './formItem.js'
import {
	apiGetRegion,
	apiUpdateStreetStatus,
	apiQueryNeighbourhood,
	apiQueryStreetPage,
	apiQueryCounty,
	apiGetAddressAreaMap,
} from '@/api/addressManage.api.js'
import AddressSetting from './components/AddressSetting.vue'

export default {
	name: 'SummaryAddressManage',
	components: { AddressSetting },
	data() {
		return {
			activeTab: 'community',
			formData: {},
			addressAreaCode: '', // 街道/小区/乡镇/村庄
			addressFullName: '', // 完整地址
			formItems: [],
			formAttrs: {
				rules: {},
			},
			// 右侧列表
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			showDialog: false,
			editType: 'add',
			rowData: null,
		}
	},
	computed: {
		tabs() {
			const arr = []
			if (this.$has('cpm_area_queryNeighbourhood')) {
				arr.push({ label: '小区管理', name: 'community' })
			}
			if (this.$has('cpm_area_queryStreetPage')) {
				arr.push({ label: '街道/乡镇管理', name: 'street' })
			}
			if (this.$has('cpm_region_countyList')) {
				arr.push({ label: '区县管理', name: 'region' })
			}
			return arr
		},
		columns() {
			return getColumn(this)
		},
		buttonTitle() {
			const enums = {
				community: '新增小区/村庄',
				street: '新增街道/乡镇',
				region: '新增区县',
			}
			return enums[this.activeTab]
		},
		refName() {
			return `formRef${this.tabs.findIndex(item => item.name === this.activeTab)}`
		},
		// 权限
		// 新增小区、街道、区县权限
		hasAddPermission() {
			const enums = {
				community: 'cpm_area_addNeighbourhood',
				street: 'cpm_area_addStreet',
				region: 'cpm_region_addCounty',
			}
			return this.$has(enums[this.activeTab])
		},
		// 操作记录权限
		hasOperateRecordPermission() {
			const enums = {
				community: 'cpm_area_queryRecordPage',
				street: 'cpm_area_queryStreetRecordPage',
				region: 'cpm_region_queryRecordPage',
			}
			return this.$has(enums[this.activeTab])
		},
		// 修改权限
		hasSettingPermission() {
			const enums = {
				community: 'cpm_area_updateNeighbourhood',
				street: 'cpm_area_updateStreet',
				region: 'cpm_region_updateCounty',
			}
			return this.$has(enums[this.activeTab])
		},
		// 启用禁用权限
		hasEnablePermission() {
			const enums = {
				community: 'cpm_area_updateNeighbourhoodStatus',
				street: 'cpm_area_updateStreetStatus',
			}
			return this.$has(enums[this.activeTab])
		},
	},
	watch: {
		tabs: {
			handler() {
				this.activeTab = this.tabs[0]?.name
			},
			immediate: true,
		},
	},
	methods: {
		// 获取市、区县数据  key:cityCode、regionCode
		async _getCityOriRegionData(value, key) {
			const { records } = await apiGetRegion({ regionCode: value })
			const obj = this.formItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = records.map(item => {
				return {
					value: item.regionCode,
					label: item.regionName,
				}
			})
		},
		// 获取街道数据
		async _getStreetData() {
			const data = await apiGetAddressAreaMap({
				parentCode: this.formData.regionCode,
			})
			const obj = this.formItems.find(item => item.prop === 'streetCode')
			if (!obj) return
			obj.options = data.map(item => {
				return {
					value: item.addressAreaCode,
					label: item.addressAreaName,
				}
			})
		},
		// 初始化form表单
		initFormData() {
			this.formItems = getFormItems(this)
			this._getCityOriRegionData(21, 'cityCode')
			this.$nextTick(() => {
				this.$refs[this.refName][0].$refs.formRef.resetFields()
				this.handleSearch()
			})
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		async handleSearch() {
			const valid = await this.$refs[this.refName][0].validate()
			if (!valid) return
			this.handlePageChange({ page: 1 })
		},
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const params = trimParams(removeNullParams(this.formData))
				Object.assign(params, {
					current,
					size,
				})
				const apiMethods = {
					community: params => apiQueryNeighbourhood(params),
					street: params => apiQueryStreetPage({ ...params, level: 4 }),
					region: params => apiQueryCounty({ ...params, parentCode: params.cityCode }),
				}
				const apiMethod = apiMethods[this.activeTab]
				const { total = 0, records } = await apiMethod(params)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 操作记录
		handleView() {
			this.$router.push({
				path: '/addressManage/operateRecord',
				query: {
					type: this.activeTab,
				},
			})
		},
		// 启用禁用
		hanldeIfEnable(row) {
			const type = row.status === 1 ? 'disable' : 'enable'
			const enableStr = type === 'disable' ? '禁用' : '启用'
			const message =
				this.activeTab === 'community'
					? `确定要${enableStr}该条小区/村庄吗？`
					: `确定要${enableStr}该条街道/乡镇吗？`
			this.$confirm(message).then(async () => {
				try {
					await apiUpdateStreetStatus({
						addressAreaId: row.addressAreaId,
						status: type === 'disable' ? 2 : 1,
					})
					this.$message.success(`${enableStr}成功`)
					this.getList()
				} catch (error) {
					console.log(error)
				}
			})
		},
		// 新增/修改
		handleSetting(type, row = {}) {
			this.editType = type
			this.showDialog = true
			this.rowData = row
		},
		// 跳转楼栋维护
		goBuilding(row) {
			const { addressAreaCode, addressAreaName } = row
			this.$router.push({
				path: '/addressManage/buildingMaintenance',
				query: {
					addressAreaCode,
					addressAreaName,
				},
			})
		},
	},
	mounted() {
		this.initFormData()
	},
}
</script>

<style lang="scss" scoped>
.page-left {
	padding-top: 0;
	padding-left: 0;
	padding-right: 0;
	flex: 0 0 310px;
	.btn-group {
		padding: 0 20px;
	}
	// 隐掉tab的边框
	::v-deep {
		.el-tabs {
			width: 100%;
			height: 100%;
			box-shadow: none;
			border: none;
			border-radius: 4px 4px 0 0;
			overflow: hidden;
			.el-tabs__content {
				padding: 0;
				height: calc(100% - 38px);
				.el-tab-pane {
					height: 100%;
				}
			}
		}
		.el-tabs--border-card > .el-tabs__header {
			border: none;
			height: 38px;
			margin-bottom: 10px;
			.el-tabs__nav {
				display: flex;
				align-items: center;
				width: 100%;
				border: none;
				height: 38px;
				.el-tabs__item {
					flex: 1;
					margin: 0;
					background: #e1ebfa;
					border: none;
					font-size: 14px;
					color: #6d7480;
					padding: 0;
					text-align: center;
					height: 38px;
					&.is-active {
						background: #ffffff;
						font-weight: 500;
						color: #2f87fe;
					}
				}
			}
		}
		.el-tabs__item:focus.is-active.is-focus:not(:active) {
			-webkit-box-shadow: none;
			box-shadow: none;
		}
		.el-form {
			padding: 0 20px;
		}
	}
}
.right-top {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-bottom: 12px;
	.el-button {
		margin-left: 0;
	}
	.el-button:nth-of-type(1) {
		margin-right: 10px;
	}
}
</style>
