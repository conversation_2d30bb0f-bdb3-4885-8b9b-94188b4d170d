<template>
	<el-date-picker
		class="dateRange"
		v-model="dateRange"
		value-format="yyyy-MM-dd HH:mm:ss"
		type="datetimerange"
		range-separator="L"
		:clearable="true"
		start-placeholder="开始时间"
		end-placeholder="结束时间"
		:picker-options="pickerOptions"
		@change="handleDateChange"
		:default-time="['00:00:00', '23:59:59']"
	></el-date-picker>
</template>

<script>
export default {
	name: 'dateRange',
	components: {},
	props: {},
	watch: {
		value(val) {
			this.dateRange = val
		},
	},
	data() {
		return {
			pickerOptions: {
				shortcuts: [
					{
						text: '当天',
						onClick(picker) {
							const end = new Date()
							const start = new Date()
							start.setHours(0, 0, 0)
							end.setHours(23, 59, 59)
							picker.$emit('pick', [start, end])
						},
					},
					{
						text: '最近一周',
						onClick(picker) {
							const end = new Date()
							const start = new Date()
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
							start.setHours(0, 0, 0)
							end.setHours(23, 59, 59)
							picker.$emit('pick', [start, end])
						},
					},
					{
						text: '最近一个月',
						onClick(picker) {
							const end = new Date()
							const start = new Date()
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
							start.setHours(0, 0, 0)
							end.setHours(23, 59, 59)
							picker.$emit('pick', [start, end])
						},
					},
					{
						text: '最近三个月',
						onClick(picker) {
							const end = new Date()
							const start = new Date()
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
							start.setHours(0, 0, 0)
							end.setHours(23, 59, 59)
							picker.$emit('pick', [start, end])
						},
					},
				],
			},
			dateRange: this.value,
		}
	},
	methods: {
		handleDateChange(val) {
			this.$emit('date-range-change', val)
		},
	},
}
</script>
<style lang="scss">
.dateRange.el-range-editor.el-input__inner {
	height: auto !important;
	flex-wrap: wrap !important;
	.el-range-input {
		float: left;
		width: 177px;
		line-height: 32px;
		height: 32px;
		text-align: left;
		box-sizing: border-box;
		&:first-child {
			flex: 0 0 190px;
		}
	}
	.el-range-separator {
		margin-left: -5px;
		width: 25px;
		font-size: 14px;
		color: #c0c4cc;
		box-sizing: border-box;
	}
	.el-range__close-icon {
		margin-left: -6px;
		margin-top: -32px;
		float: left;
		box-sizing: border-box;
	}
}
</style>
