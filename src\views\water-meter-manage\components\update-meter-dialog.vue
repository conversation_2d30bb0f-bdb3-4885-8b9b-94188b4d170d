<template>
	<gc-el-dialog :show="isShow" :title="`${typeText}水表`" custom-top="120px" width="600px" @close="handleClose">
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { ruleMaxLength, RULE_POSITIVEINTEGERONLY_STARTOFZERO } from '@/utils/rules'
import { apiGetMeterType } from '@/api/meterManage.api'
import { addWaterMeter, modifyMeterInfo } from '@/api/waterMeter.api'

export default {
	name: '',
	props: {
		// 弹窗显示/隐藏
		show: {
			type: <PERSON>olean,
			default: false,
		},
		editType: {
			type: String,
			default: 'add',
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		typeText() {
			return this.editType === 'add' ? '新增' : '编辑'
		},
	},
	data() {
		return {
			// 水表类型数据
			meterTypeData: [],

			formData: {
				meterNo: '',
				meterWarehouseCode: '',
				antiTheftCode: '',
				baseMeterNo: '',
				meterTypeId: '',
				manufacturerName: '',
				meterModel: '',
				caliber: '',
				ranges: undefined,
				useYears: '',
				tableWellLocation: '',
				installPosition: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '水表编号',
					prop: 'meterNo',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入水表编号',
					},
				},
				{
					type: 'el-input',
					label: '水表仓库编号',
					prop: 'meterWarehouseCode',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入水表仓库编号',
					},
				},
				{
					type: 'el-input',
					label: '防盗编号',
					prop: 'antiTheftCode',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入防盗编号',
					},
				},
				{
					type: 'el-input',
					label: '水表标号',
					prop: 'baseMeterNo',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入水表标识',
					},
				},
				{
					type: 'el-select',
					label: '水表类型',
					prop: 'meterTypeId',
					options: [],
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择水表类型',
					},
					events: {
						change: value => {
							const data = this.meterTypeData.find(item => item.meterTypeId === value)
							if (data) {
								const { useYears = undefined, manufacturerName = '', meterRange = undefined } = data
								this.formData.useYears = useYears
								this.formData.manufacturerName = manufacturerName
								this.formData.ranges = meterRange
							} else {
								this.formData.useYears = this.formData.manufacturerName = this.formData.ranges = ''
							}
						},
					},
				},
				{
					type: 'el-input',
					label: '水表厂商',
					prop: 'manufacturerName',
					attrs: {
						col: 24,
						disabled: true,
						clearable: true,
						placeholder: ' ',
					},
				},
				{
					type: 'el-input',
					label: '水表型号',
					prop: 'meterModel',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入水表型号',
					},
				},
				{
					type: 'el-input',
					label: '水表口径',
					prop: 'caliber',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入水表口径',
					},
				},
				{
					type: 'el-input-number',
					label: '量程',
					prop: 'ranges',
					attrs: {
						col: 24,
						controls: false,
						min: 0,
						max: 999999999,
						clearable: true,
						placeholder: '请输入量程',
					},
				},
				{
					type: 'el-select',
					label: '装表位置',
					prop: 'installPosition',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入装表位置',
					},
					options: this.$store.getters.dataList.installPosition
						? this.$store.getters.dataList.installPosition.map(item => {
								return {
									label: item.sortName,
									value: Number(item.sortValue),
								}
						  })
						: [],
				},
				{
					type: 'el-input',
					label: '表井位置',
					prop: 'tableWellLocation',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入表井位置',
					},
				},
				{
					type: 'el-input',
					label: '服役年限',
					prop: 'useYears',
					attrs: {
						col: 24,
						clearable: true,
						disabled: true,
						placeholder: ' ',
					},
				},
			],
			formAttrs: {
				labelWidth: '120px',
				labelPosition: 'right',
				rules: {
					meterNo: [
						{ required: true, message: '请输入水表编号', trigger: 'blur' },
						ruleMaxLength(32, '水表编号'),
						RULE_POSITIVEINTEGERONLY_STARTOFZERO,
					],
					meterWarehouseCode: [ruleMaxLength(32, '水表仓库编号')],
					antiTheftCode: [ruleMaxLength(32, '防盗编号')],
					baseMeterNo: [ruleMaxLength(32, '水表标号')],
					meterTypeId: [{ required: true, message: '请选择水表类型', trigger: 'change' }],
					meterModel: [ruleMaxLength(30, '水表型号')],
					caliber: [ruleMaxLength(10, '口径')],
					ranges: [{ required: true, message: '请输入量程', trigger: 'blur' }],
					tableWellLocation: [ruleMaxLength(64)],
				},
			},
		}
	},
	created() {
		this.getMeterTypeList()
	},
	methods: {
		// 获取水表类型下拉数据
		async getMeterTypeList() {
			try {
				const res = await apiGetMeterType({
					tenantId: this.$store.getters.userInfo.tenantId,
				})
				this.meterTypeData = res || []
				this.formItems[4].options = res.map(item => {
					return {
						value: item.meterTypeId,
						label: item.meterTypeName,
					}
				})
			} catch (error) {
				console.error(error)
				this.formItems[4].options = []
			}
		},

		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				if (this.editType === 'add') {
					await addWaterMeter({
						meterVO: this.formData,
					})
				} else {
					await modifyMeterInfo({
						meterVO: this.formData,
					})
				}
				this.$message.success(`水表${this.typeText}成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}

.range-form-item {
	::v-deep .el-form-item__content {
		display: flex;
		align-items: center;
	}
}

::v-deep {
	.el-input-number {
		width: 100%;
		.el-input__inner {
			text-align: left;
		}
	}
}
</style>
