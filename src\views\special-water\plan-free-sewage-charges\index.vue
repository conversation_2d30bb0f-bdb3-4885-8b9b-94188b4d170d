<template>
	<div class="page-layout">
		<div class="page-left">
			<el-button v-has="'cpm_planWaste_import'" type="primary" @click="handleImport">
				导入新一年免污水费用表
			</el-button>
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>

			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div v-has="'cpm_planWaste_add'" class="right-top">
				<el-button v-has="'cpm_planWaste_add'" type="primary" @click="handleAdd">添加免污水费用表卡</el-button>
			</div>
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:deal="{ row }">
					<el-button v-has="'cpm_planWaste_remove'" type="text" size="medium" @click="handleRemove(row)">
						移除表卡
					</el-button>
				</template>
			</GcTable>
		</div>

		<!-- 导入弹窗 -->
		<ImportDialog ref="importDialogRef" :show.sync="showImport" @success="getList(1)" />

		<!-- 添加免污水费用表卡弹窗 -->
		<UpdateDialog ref="updateDialogRef" :show.sync="showUpdate" :editType="editType" @success="getList(1)" />
	</div>
</template>

<script>
import ImportDialog from './components/ImportDialog.vue'
import UpdateDialog from './components/UpdateDialog.vue'
import { queryPlanWastePage, deletePlanWaste } from '@/api/specialWater.api'

export default {
	name: 'PlanFreeSewageChargesManage',
	components: { ImportDialog, UpdateDialog },
	data() {
		return {
			// 左侧查询
			formData: {
				orgCode: '',
				planYear: this.dayjs().format('YYYY'),
				userName: '',
				archivesNo: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						col: 24,
						clearable: false,
						placeholder: '请选择营业分公司',
					},
				},
				{
					type: 'el-date-picker',
					label: '年份',
					prop: 'planYear',
					attrs: {
						col: 24,
						type: 'year',
						clearable: false,
						valueFormat: 'yyyy',
					},
				},
				{
					type: 'el-input',
					label: '用户名称',
					prop: 'userName',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入用户名称',
					},
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesNo',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
			],
			formAttrs: {
				rules: {
					orgCode: {
						required: true,
						message: '请选择营业分公司',
						trigger: 'change',
					},
				},
			},
			// 右侧列表
			loading: false,
			columns: [
				{
					key: 'archivesNo',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'userName',
					name: '用户名称',
					tooltip: true,
				},
				{
					key: 'fullAddressName',
					name: '地址',
					tooltip: true,
				},
				{
					key: 'priceCode',
					name: '价格编号',
					tooltip: true,
				},
				{
					hide: !this.$has('cpm_planWaste_remove'),
					key: 'deal',
					name: '操作',
					fixed: 'right',
					width: 100,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 导入弹窗
			showImport: false,
			// 添加免污水费用表卡弹窗
			editType: 'add',
			showUpdate: false,
		}
	},
	computed: {
		orgOptions() {
			return this.formItems[0].options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.$nextTick(() => {
						this.handleSearch()
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetFormSmooth()
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			this.selectedData = []
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await queryPlanWastePage({
					size,
					current,
					...this.formData,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 移除表卡
		handleRemove(data) {
			this.$confirm('确定要移除该表卡吗？').then(async () => {
				await deletePlanWaste({
					recordId: data.recordId,
				})
				this.$message.success('移除成功')
				this.getList(1)
			})
		},
		// 添加免污水费用表卡
		handleAdd() {
			this.editType = 'add'
			this.showUpdate = true
		},
		// 导入
		handleImport() {
			this.showImport = true
		},
	},
}
</script>

<style lang="scss" scoped>
.right-top {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	margin-bottom: 12px;
}
</style>
