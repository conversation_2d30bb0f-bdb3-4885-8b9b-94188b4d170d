export function getFormItems(_this) {
	const dataList = _this.$store.getters.dataList

	const pickerOptions = {
		shortcuts: [
			{
				text: '今天',
				onClick(picker) {
					const day = _this.dayjs().format('YYYY-MM-DD')
					picker.$emit('pick', [day, day])
				},
			},
			{
				text: '最近三天',
				onClick(picker) {
					const end = new Date(_this.dayjs().endOf('day').format('YYYY-MM-DD'))
					const start = new Date(_this.dayjs().subtract(2, 'day').startOf('day').format('YYYY-MM-DD'))
					picker.$emit('pick', [start, end])
				},
			},
			{
				text: '最近一周',
				onClick(picker) {
					const end = new Date(_this.dayjs().endOf('day').format('YYYY-MM-DD'))
					const start = new Date(_this.dayjs().subtract(6, 'day').startOf('day').format('YYYY-MM-DD'))
					picker.$emit('pick', [start, end])
				},
			},
			{
				text: '最近一个月',
				onClick(picker) {
					const end = new Date(_this.dayjs().endOf('day').format('YYYY-MM-DD'))
					const start = new Date(_this.dayjs().subtract(29, 'day').startOf('day').format('YYYY-MM-DD'))
					picker.$emit('pick', [start, end])
				},
			},
			{
				text: '最近三个月',
				onClick(picker) {
					const end = new Date(_this.dayjs().endOf('day').format('YYYY-MM-DD'))
					const start = new Date(_this.dayjs().subtract(90, 'day').startOf('day').format('YYYY-MM-DD'))
					picker.$emit('pick', [start, end])
				},
			},
		],
	}

	const arr = [
		{
			type: 'el-select',
			label: '营业分公司',
			prop: 'orgCode',
			options: _this.$store.getters.orgList,
		},
		{
			type: 'el-input',
			label: '表卡编号',
			prop: 'archivesIdentity',
			attrs: {
				clearable: true,
			},
		},
		{
			type: 'el-select',
			label: '操作方式',
			prop: 'costOperationType',
			options: dataList.costOperationType.map(item => {
				return {
					label: item.sortName,
					value: item.sortValue,
				}
			}),
		},
		{
			type: 'el-select',
			label: '渠道',
			prop: 'payChannel',
			options: dataList.payChannel.map(item => {
				return {
					label: item.sortName,
					value: item.sortValue,
				}
			}),
		},
		{
			type: 'el-date-picker',
			label: '日期',
			prop: 'archivesTime',
			attrs: {
				type: 'daterange',
				startPlaceholder: '开始日期',
				endPlaceholder: '结束日期',
				valueFormat: 'yyyy-MM-dd',
				pickerOptions,
			},
		},
	]

	return arr
}
