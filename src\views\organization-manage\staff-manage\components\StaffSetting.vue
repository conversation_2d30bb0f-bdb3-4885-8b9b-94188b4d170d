<template>
	<GcElDialog :show="isShow" :title="`${typeText}员工`" custom-top="60px" @close="handleClose">
		<GcFormRow
			v-show="['add', 'edit'].includes(newEditType)"
			ref="formRef"
			v-model="formData"
			:formItems="formItems"
			:formAttrs="formAttrs"
		>
			<template #baseInfo>
				<p>基本信息</p>
			</template>
			<template #dash>
				<div class="dash"></div>
				<p>登录信息</p>
			</template>
		</GcFormRow>
		<GcGroupDetail v-show="!['add', 'edit'].includes(newEditType)" :data="staffInfo">
			<template #token>
				<el-button type="text" @click="showBindToken = true">绑定登录令牌</el-button>
			</template>
		</GcGroupDetail>
		<template #footer v-if="!['add', 'edit'].includes(newEditType)">
			<GcButton v-has="'v1_tos_staff_modify'" btn-type="three" @click.native="newEditType = 'edit'">
				修改
			</GcButton>
			<GcButton @click.native="handleClose">关闭</GcButton>
		</template>
		<template #footer v-else>
			<GcButton btn-type="three" @click.native="handleClose">取消</GcButton>
			<GcButton @click.native="handleSave">保存</GcButton>
		</template>
		<!-- 二维码 -->
		<BindToken :show.sync="showBindToken" :accountName="editData.account_name" />
	</GcElDialog>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { encrypt, getfilterName } from '@/utils'
import { resignedTypeOptions } from '@/consts/optionList.js'
import { ruleRequired, ruleMaxLength, RULE_PHONE, RULE_INCORRECTIDCARD } from '@/utils/rules.js'
import identity from '@/mixin/identity.js'
import { getStaffFormItems } from './formItems.js'
import { apiRoleList, apiCreateStaff, apiModifyStaff, apiGetDepartmentTree } from '@/api/organizationManage.api.js'

import BindToken from './BindToken.vue'
export default {
	mixins: [identity],
	components: { BindToken },
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		editType: {
			type: String,
			default: 'add',
		},
		editData: {
			type: Object,
			default: () => {},
		},
		departmentData: {
			type: Object,
			default: () => {},
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		newEditType: {
			get: function () {
				return this.editType
			},
			set: function (val) {
				this.$emit('update:editType', val)
			},
		},
		typeText() {
			const enums = {
				add: '新建',
				edit: '编辑',
				view: '查看',
			}
			return enums[this.newEditType] || '--'
		},
		staffInfo() {
			const {
				name = '--',
				orgName = '--',
				role_name = '--',
				staff_no = '--',
				is_resigned = false,
				mobile = '--',
				phone = '--',
				remark = '--',
				id_card_no = '--',
			} = this.editData

			return {
				list: [
					{ key: '员工姓名', value: name },
					{ key: '所属部门', value: orgName },
					{ key: '所属角色', value: role_name },
					{ key: '员工号', value: staff_no },
					{
						key: '状态',
						value: getfilterName(resignedTypeOptions, is_resigned),
					},
					{ key: '手机号码', value: mobile },
					{ key: '办公电话', value: phone },
					{ key: '身份证号', value: id_card_no },
					{ key: '备注', value: remark },
					{ slot: 'token' },
				],
			}
		},
	},
	watch: {
		isShow: {
			async handler(val) {
				if (val) {
					this.formItems = getStaffFormItems(this)
					this.$has('v1_tos_staff_modify') && this._apiRoleList()
					this._getOrgStruData()
					this.$nextTick(() => {
						this.$refs.formRef.clearValidate()
					})
					if (['view', 'edit'].includes(this.editType) && Object.keys(this.editData).length) {
						this.assignForm(this.editData)
					} else {
						this.formData.is_resigned = false
						this.formData.account_password = '000000'
					}
					// 默认回显当前开发部门
					this.formData.department_code = this.departmentData.department_code
				}
			},
			deep: true,
		},
	},
	data() {
		return {
			formData: {
				name: '',
				department_code: '',
				gender: '',
				role_id: '',
				staff_no: '',
				is_resigned: '',
				mobile: '',
				phone: '',
				id_card_no: '',
				remark: '',
				account_name: '',
				account_password: '',
			},
			formItems: [],
			formAttrs: {
				displayItem: 'block',
				labelPosition: 'top',
				rules: {
					name: [ruleRequired('必填'), ruleMaxLength(20)],
					department_code: [ruleRequired('必填')],
					role_id: [ruleRequired('必填')],
					staff_no: [ruleMaxLength(16)],
					mobile: [RULE_PHONE],
					id_card_no: [RULE_INCORRECTIDCARD],
					account_name: [ruleRequired('必填'), ruleMaxLength(32)],
				},
			},

			showBindToken: false,
		}
	},
	methods: {
		// 获取所属角色
		async _apiRoleList() {
			try {
				const { data } = await apiRoleList({
					paged: false,
					tid: this.tenantId,
				})
				const obj = this.formItems.find(item => item.prop === 'role_id')
				if (!obj) return
				obj.options = data.map(item => {
					return {
						value: item.id,
						label: item.name,
					}
				})
			} catch (error) {
				console.log(error)
			}
		},
		formatOrganizeTreeList(data, map = {}) {
			data = data || []
			return data.map(depData => {
				let {
					tenant_organization,
					department,
					children,
					department_code,
					department_name,
					parent_org_code,
				} = depData
				tenant_organization = tenant_organization || {}
				department = department || []
				children = children || []

				const value =
					department_code === undefined
						? `${tenant_organization.uid}_${tenant_organization.org_code}`
						: department_code
				const label = department_name === undefined ? tenant_organization.name : department_name
				const parentOrgCode = parent_org_code === undefined ? '' : parent_org_code
				const formatedChildList = this.formatOrganizeTreeList([...department, ...children], map)

				const item = {
					value,
					label,
					parentOrgCode,
					children: department_code === undefined ? formatedChildList : null,
				}

				map[value] = item
				return item
			})
		},
		// 获取所属部门
		async _getOrgStruData() {
			try {
				const result = await apiGetDepartmentTree({})
				const organizaTreeMap = {}
				const treeList = this.formatOrganizeTreeList(result, organizaTreeMap)
				const obj = this.formItems.find(item => item.prop === 'department_code')
				this.organizaTreeMap = organizaTreeMap
				if (!obj) return
				this.$set(obj.attrs, 'options', treeList)
			} catch (e) {
				console.log(e)
			}
		},
		/**
		 * 根据指定keyName值找到指定对象object
		 * @param { Array } data 需要指定寻找数据的源数据
		 * @param { String } keyName 指定数据key值 支持object.key.key
		 * @param { Number | String } value 指定寻找数据值
		 */
		findDeepTreeByKey(data, keyName, value) {
			for (let i = 0; i < data.length; i++) {
				const kList = keyName.split('.')
				const targetValue = kList.reduce((prev, cur) => {
					return prev[cur]
				}, data[i])
				if (targetValue === value) {
					return data[i]
				}
				if (data[i].children && data[i].children.length) {
					if (this.findDeepTreeByKey(data[i].children, keyName, value)) {
						return this.findDeepTreeByKey(data[i].children, keyName, value)
					}
				}
			}
		},
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				const {
					department_code,
					account_name,
					account_password,
					is_resigned,
					name,
					role_id,
					gender,
					id_card_no,
					mobile,
					phone,
					remark,
					staff_no,
				} = this.formData
				const params = {
					account_name,
					account_password,
					is_resigned,
					name,
					role_id,
				}
				const basicParams = {
					gender,
					id_card_no,
					mobile,
					phone,
					remark,
					staff_no,
				}

				const organizaTreeMap = this.organizaTreeMap || {}
				const targetDeparment = organizaTreeMap[department_code] || {}
				const newParams = trimParams(removeNullParams(params))
				const newBasicParams = trimParams(removeNullParams(basicParams))
				const formParams = Object.assign(
					{},
					newParams,
					{
						basic: newBasicParams,
					},
					{
						org_code: targetDeparment?.parentOrgCode,
						departmentCode: department_code,
					},
				)
				if (formParams.account_password && this.newEditType === 'add') {
					formParams.account_password = encrypt(formParams.account_password)
				}

				try {
					if (this.editType === 'add') {
						await apiCreateStaff(formParams)
					} else {
						Object.assign(formParams, {
							id: this.editData.id,
						})
						delete formParams.account_password
						await apiModifyStaff(formParams, this.editData.id)
					}
					this.$message.success(`${this.typeText}成功`)
					this.$emit('success')
					this.handleClose()
				} catch (error) {
					console.log(error)
				}
			}
		},
		handleClose() {
			this.$refs.formRef.resetForm()
			this.isShow = false
		},
		// 设置表单数据
		assignForm(obj) {
			const keys = Object.keys(this.formData)
			keys.forEach(key => {
				if (obj && Object.prototype.hasOwnProperty.call(obj, key)) {
					this.formData[key] = obj[key]
				}
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.el-dialog__body {
		padding-top: 5px !important;
	}
	.el-form-item {
		margin-bottom: 6px;
	}
	.el-row {
		.el-col:nth-child(1),
		.el-col:nth-child(12) {
			.el-form-item {
				margin-bottom: 0;
				p {
					color: #ababab;
					font-size: 12px;
					margin: 0 0 0 10px;
				}
			}
			.dash {
				margin-top: 40px;
				position: relative;
				&:after {
					content: '';
					left: 10px;
					right: 10px;
					height: 1px;
					border-top: 1px dashed #cccccc80;
					position: absolute;
					top: -20px;
				}
			}
		}
	}
}
</style>
