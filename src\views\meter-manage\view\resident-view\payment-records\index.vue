<template>
	<div class="container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch">
					<i class="iconfontCis icon-small-search"></i>
					筛选
				</el-button>
				<el-button @click="handleReset">
					<i class="iconfontCis icon-reset"></i>
					重置
				</el-button>
			</el-form-item>
		</GcFormSimple>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handleChangePage"
			/>
		</div>
	</div>
</template>

<script>
import { getfilterName } from '@/utils'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiGetBillPayRecordList } from '@/api/meterManage.api'
export default {
	props: {
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		const _t = this
		const pickerOptions = {
			shortcuts: [
				{
					text: '最近一年',
					onClick(picker) {
						const end = new Date(_t.dayjs().endOf('year').format('YYYY-MM-DD'))
						const start = new Date(_t.dayjs().startOf('year').format('YYYY-MM-DD'))
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近三年',
					onClick(picker) {
						const end = new Date(_t.dayjs().endOf('year').format('YYYY-MM-DD'))
						const start = new Date(_t.dayjs().subtract(2, 'year').startOf('year').format('YYYY-MM-DD'))
						picker.$emit('pick', [start, end])
					},
				},
			],
		}
		return {
			formData: {
				payDate: [
					this.dayjs().subtract(2, 'year').startOf('year').format('YYYY-MM-DD'),
					this.dayjs().endOf('year').format('YYYY-MM-DD'),
				],
				payMode: '',
				payRecordStatus: '',
				invoiceStatus: '',
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '缴费日期',
					prop: 'payDate',
					attrs: {
						type: 'daterange',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						style: {
							width: '250px',
						},
						pickerOptions,
					},
				},
				{
					type: 'el-select',
					label: '缴费方式',
					prop: 'payMode',
					options: this.$store.getters?.dataList?.payMode?.map(item => {
						return {
							label: item.sortName,
							value: item.sortValue,
						}
					}),
					attrs: {
						style: {
							width: '150px',
						},
						clearable: true,
					},
				},
				{
					type: 'el-select',
					label: '缴费状态',
					prop: 'payRecordStatus',
					options: this.$store.getters.dataList.payRecordStatus
						? this.$store.getters.dataList.payRecordStatus.map(item => {
								return {
									label: item.sortName,
									value: item.sortValue,
								}
						  })
						: [],
					attrs: {
						style: {
							width: '150px',
						},
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '90px',
				rules: {
					payDate: [{ required: true, message: '请选择缴费日期', trigger: 'change' }],
				},
			},
			columns: [
				{
					key: 'payTime',
					name: '缴费时间',
					tooltip: true,
				},
				{
					key: 'payMode',
					name: '缴费方式',
					tooltip: true,
					render: (h, row, total, scope) => {
						const valueStr = getfilterName(
							this.$store.getters.dataList.payMode,
							row[scope.column.property],
							'sortValue',
							'sortName',
						)
						return h('span', {}, valueStr)
					},
				},
				{
					key: 'paidAmount',
					name: '缴费金额',
					tooltip: true,
				},
				{
					key: 'payRecordStatus',
					name: '缴费状态',
					tooltip: true,
					render: (h, row, total, scope) => {
						const valueStr = getfilterName(
							this.$store.getters.dataList.payRecordStatus,
							row[scope.column.property],
							'sortValue',
							'sortName',
						)
						return h('span', {}, valueStr)
					},
				},
				{
					key: 'operate',
					name: '操作',
					render: (h, row) => {
						let btnArr = [
							h(
								'el-button',
								{
									props: {
										type: 'text',
										size: 'medium',
									},
									on: {
										click: this.goView(row),
									},
								},
								'查看账单明细',
							),
						]
						return h('div', {}, btnArr)
					},
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
		}
	},
	methods: {
		goView() {
			return () => {
				this.$router.push({
					path: '/costManage/billManage',
					query: {
						archivesIdentity: this.tabData?.archives.archivesIdentity,
					},
				})
			}
		},
		async getList() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) {
				return
			}
			this.loading = true
			try {
				const extractedData = Object.assign({}, ...Object.values(this.tabData))
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
					archivesId: extractedData?.archivesId,
				})
				if (formParams.payDate && formParams.payDate.length > 1) {
					formParams.payDateBegin = this.dayjs(formParams.payDate[0]).format('YYYY-MM-DD')
					formParams.payDateEnd = this.dayjs(formParams.payDate[1]).format('YYYY-MM-DD')
					delete formParams.payDate
				}

				const { records, total } = await apiGetBillPayRecordList(formParams)
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleSearch() {
			this.handleChangePage({ page: 1 })
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.handleSearch()
		},
	},
}
</script>
