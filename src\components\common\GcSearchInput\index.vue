<template>
	<div class="gc-search-input" :class="{ input_focus: isFocus }">
		<div class="input_prepend" v-if="prependName || $slots.prepend">
			<div v-if="$slots.prepend">
				<slot name="prepend"></slot>
			</div>
			<p v-else>{{ prependName }}</p>
		</div>
		<div class="search-input">
			<input
				ref="input"
				@input="handleInput"
				@focus="handleFocus"
				@blur="handleBlur"
				@keyup.enter="handleKeyenter"
				@change="handleChange"
				:type="type"
				:placeholder="placeholder"
			/>
		</div>
		<div class="gc-input__suffix">
			<transition name="el-fade-in">
				<span @click="clear" v-if="clearable && value.length" class="clear-icon el-icon-circle-close"></span>
			</transition>
			<span @click="handleSearch" class="iconfontCis icon-small-search"></span>
		</div>
	</div>
</template>

<script>
export default {
	name: 'GcSearchInput',
	model: {
		prop: 'value',
		event: 'input',
	},
	props: {
		prependName: String,
		value: String,
		type: {
			type: String,
			default: 'text',
		},
		placeholder: {
			type: String,
			default: '请输入搜索关键字',
		},
		clearable: {
			type: Boolean,
			default: false,
		},
	},

	data() {
		return {
			isFocus: false,
		}
	},

	created() {
		this.$nextTick(() => {
			this.$refs.input.value = this.value === undefined ? '' : this.value
		})
	},

	methods: {
		handleInput(e) {
			this.$emit('input', e.target.value)
		},

		handleChange(e) {
			this.$emit('change', e.target.value)
		},

		handleFocus(e) {
			this.isFocus = true
			this.$emit('focus', e)
		},

		handleBlur(e) {
			this.isFocus = false
			this.$emit('blur', e)
		},

		handleKeyenter(e) {
			this.$emit('keyupEnter', e)
		},

		handleSearch() {
			this.$emit('search')
		},

		clear() {
			this.$emit('change', '')
			this.$emit('input', '')
			this.$emit('clear')
		},
	},

	watch: {
		value(val) {
			this.$refs.input.value = val
		},
	},
}
</script>

<style lang="scss" scoped>
.gc-search-input {
	width: 420px;
	height: 32px;
	background: #ffffff;
	border-radius: 4px;
	border: 1px solid #d9d9d9;
	background: #fff;
	display: flex;
	position: relative;
	transition: 0.3s;
	&.input_focus {
		border: 1px solid #2f87fe;
	}
	.input_prepend {
		flex-shrink: 0;
		padding: 0 8px;
		position: relative;
		cursor: default;
		&:after {
			content: '';
			width: 1px;
			height: 12px;
			background: #d9d9d9;
			position: absolute;
			right: 0;
			top: 10px;
		}
		& > p {
			font-size: 14px;
			color: #4e4e4e;
			line-height: 32px;
		}
	}
	.search-input {
		padding: 0 50px 0 12px;
		flex: 1;
		input {
			width: 100%;
			height: 100%;
			outline: none;
			border: none;
			font-size: 14px;
			color: #4c4c4c;
			background: none;
			&::placeholder {
				color: #ccc;
			}
		}
	}
	.gc-input__suffix {
		position: absolute;
		position: absolute;
		right: 10px;
		top: 8px;
		span {
			&.iconfont {
				font-size: 13px;
				cursor: pointer;
				color: #aab2c1;
				transition: 0.16s;
				&:active {
					opacity: 0.6;
				}
			}
			&.clear-icon {
				font-size: 13px;
				color: #999;
				cursor: pointer;
				margin-right: 2px;
			}
		}
	}
}
</style>
