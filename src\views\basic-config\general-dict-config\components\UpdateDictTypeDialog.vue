<template>
	<gc-el-dialog :show="isShow" :title="`${typeText}字典类型`" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { ruleMaxLength } from '@/utils/rules.js'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		editType: {
			type: String,
			default: 'add',
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		typeText() {
			return this.editType === 'add' ? '新增' : '编辑'
		},
	},
	data() {
		return {
			formData: {
				dictType: '',
				dictNo: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '字典类型',
					prop: 'dictType',
					attrs: {
						col: 24,
						placeholder: '请输入字典类型',
					},
				},
				{
					type: 'el-input',
					label: '字典类型编码',
					prop: 'dictNo',
					attrs: {
						col: 24,
						placeholder: '请输入字典类型编码',
					},
				},
			],
			formAttrs: {
				rules: {
					dictType: [
						{
							required: true,
							message: '请输入字典类型',
							trigger: 'change',
						},
						ruleMaxLength(12, '字典类型'),
					],
					dictNo: [
						{
							required: true,
							message: '请输入字典类型编码',
							trigger: 'blur',
						},
						ruleMaxLength(99, '字典类型编码'),
					],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			console.log('this.formData', this.formData)
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				if (this.editType === 'add') {
					// TODO: 对接接口
				} else {
					// TODO: 对接接口
				}
				this.$message.success(`${this.typeText}字典类型`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.date-form-item {
		&.el-form-item {
			margin: 12px 0;
		}
	}
	.el-form-item__error {
		padding-top: 0;
	}
}
</style>
