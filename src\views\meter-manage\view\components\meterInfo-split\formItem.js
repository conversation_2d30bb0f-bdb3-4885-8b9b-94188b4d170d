export const getFormItems = function () {
	return [
		{
			type: 'el-input',
			label: '表卡编号',
			prop: 'archivesIdentity',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '水表编号',
			prop: 'meterNo',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-date-picker',
			label: '拆表时间',
			prop: 'removeDate',
		},
		{
			type: 'el-input',
			label: '拆表操作人员',
			prop: 'removePerson',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '拆表原因',
			prop: 'removeReason',
			attrs: {
				type: 'textarea',
				maxlength: '64',
				showWordLimit: true,
				autosize: {
					minRows: 4,
					maxRows: 8,
				},
			},
		},
	]
}
