import Layout from '@/layout'

export default [
	{
		path: '/meterManage',
		name: 'MeterManage',
		component: Layout,
		redirect: '/meterManage/resident/create',
		meta: {
			title: '表卡',
			icon: 'icon-cis_yj_biaoka',
			permissions: [
				// 居民表卡
				'cpm_archives_list',
				'cpm_report_archives-list_export_excel',
				'cpm_archives_add',
				'cpm_archives_update',
				// 企业表卡管理
				'cpm_archives_list2',
				'cpm_report_archives-list_export2_excel',
				'cpm_archives_add2',
				// 虚表管理
				'cpm-archives-update-virtual-records',
				'cpm-archives-virtual-archives-list',
				// 小区接管管理
				'cpm-address-areas-record-archives-list',
				'cpm_address-areas_record-take-over',
				// 停水表卡管理
				'cpm_archives_stop-using-list',
				// 销卡表卡管理
				'cpm_archives_cancellation-list',
				// 阶梯水量预警
				'cpm_report_water-amount-warning_preview',
				'cpm_report_water-amount-warning_export_excel',
				// 总分表管理
				'cpm_dma-archives',
				'cpm_dma-archives_list-dma-archives',
				// 表卡转换记录
				'cpm_archives_change-modifyrecords',
				// 批量建档
				'cpm_archives-batch',
				'cpm_archives-batch_record_list',
				// 表卡转换
				'cpm_archives_transfer-tablecard',
				'cpm_archives_transfer-tablecard2',
			],
		},
		children: [
			{
				path: 'residentMeterManage',
				name: 'ResidentMeterManage',
				component: () => import('@/views/meter-manage/resident/manage/index.vue'),
				meta: {
					title: '居民表卡管理',
					icon: 'icon-cis_ej_juminbiaoka',
					keepAlive: true,
					permissions: ['cpm_archives_list', 'cpm_report_archives-list_export_excel'],
				},
			},
			{
				path: 'residentMeterCreate',
				name: 'ResidentMeterCreate',
				component: () => import('@/views/meter-manage/resident/create/index.vue'),
				hidden: true,
				meta: {
					title: '居民表卡创建',
					keepAlive: true,
					permissions: ['cpm_archives_add'],
				},
			},
			{
				path: 'companyMeterCreate',
				name: 'CompanyMeterCreate',
				component: () => import('@/views/meter-manage/company/create/index.vue'),
				meta: {
					title: '企业表卡创建',
					keepAlive: true,
					permissions: ['cpm_archives_add2'],
				},
				hidden: true,
			},
			{
				path: 'companyMeterManage',
				name: 'CompanyMeterManage',
				component: () => import('@/views/meter-manage/company/manage/index.vue'),
				meta: {
					title: '企业表卡管理',
					icon: 'icon-cis_ej_qiyebiaoka',
					keepAlive: true,
					permissions: ['cpm_archives_list2'],
				},
			},
			{
				path: 'residentMeterView',
				name: 'ResidentMeterView',
				component: () => import('@/views/meter-manage/view/resident-view/index.vue'),
				meta: {
					title: '居民表卡视图',
					icon: 'icon-erji-xiaoquguanli',
					keepAlive: true,
					permissions: ['cpm_archives_detail'],
				},
				hidden: true,
			},
			{
				path: 'companyMeterView',
				name: 'CompanyMeterView',
				component: () => import('@/views/meter-manage/view/company-view/index.vue'),
				meta: {
					title: '企业表卡视图',
					icon: 'icon-erji-xiaoquguanli',
					keepAlive: true,
					permissions: ['cpm_archives_detail5'],
				},
				hidden: true,
			},
			{
				path: 'virtualMeterSplit',
				name: 'VirtualMeterSplit',
				component: () => import('@/views/meter-manage/virtual-meter/split-virtual-meter/index.vue'),
				meta: {
					title: '虚表拆分',
					keepAlive: true,
					permissions: ['cpm-archives-update-virtual-records'],
				},
				hidden: true,
			},
			{
				path: 'virtualMeterSplitPerfect',
				name: 'VirtualMeterSplitPerfect',
				hidden: true,
				component: () => import('@/views/meter-manage/virtual-meter/split-virtual-meter/index.vue'),
				meta: {
					title: '编辑虚表拆分',
					keepAlive: true,
					permissions: ['cpm-archives-update-virtual-records'],
				},
			},
			{
				path: 'virtualMeterManage',
				name: 'VirtualMeterManage',
				component: () => import('@/views/meter-manage/virtual-meter/manage-virtual-meter/index.vue'),
				meta: {
					title: '虚表管理',
					icon: 'icon-cis_ej_xubiaoguanli',
					keepAlive: true,
					permissions: ['cpm-archives-virtual-archives-list'],
				},
			},
			{
				path: 'communityTakeOverManage',
				name: 'CommunityTakeOverManage',
				component: () => import('@/views/meter-manage/community-takeover-manage/index.vue'),
				meta: {
					title: '小区接管管理',
					icon: 'icon-cis_ej_xiaoqujieguan',
					keepAlive: true,
					permissions: ['cpm-address-areas-record-archives-list', 'cpm_address-areas_record-take-over'],
				},
			},
			{
				path: 'communityView',
				name: 'CommunityView',
				component: () => import('@/views/meter-manage/community-view/index.vue'),
				meta: {
					title: '小区表卡视图',
					keepAlive: true,
					permissions: [
						'cpm_address-areas_info',
						'cpm_archives_reading-first-record',
						'cpm_archives-batch_reading-first-record',
					],
				},
				hidden: true,
			},
			{
				path: 'cutOffManage',
				name: 'CutOffManage',
				component: () => import('@/views/meter-manage/cut-off-manage/index.vue'),
				meta: {
					title: '停水表卡管理',
					icon: 'icon-cis_ej_tingshuibiaoka',
					keepAlive: true,
					permissions: ['cpm_archives_stop-using-list'],
				},
			},
			{
				path: 'cancelCardManage',
				name: 'CancelCardManage',
				component: () => import('@/views/meter-manage/cancel-card-manage/index.vue'),
				meta: {
					title: '销卡表卡管理',
					icon: 'icon-cis_ej_xiaokabiaoka',
					keepAlive: true,
					permissions: ['cpm_archives_cancellation-list'],
				},
			},
			{
				path: 'ladderWaterWarning',
				name: 'LadderWaterWarning',
				component: () => import('@/views/meter-manage/ladder-water-warning/index.vue'),
				meta: {
					title: '阶梯水量预警',
					icon: 'icon-cis_ej_jietishuiliang',
					keepAlive: true,
					permissions: [
						'cpm_report_water-amount-warning_preview',
						'cpm_report_water-amount-warning_export_excel',
					],
				},
			},
			{
				path: 'totalSubMeterSetting',
				name: 'TotalSubMeterSetting',
				component: () => import('@/views/meter-manage/total-sub-meter-setting/index.vue'),
				meta: {
					title: '总分表建立',
					keepAlive: true,
					permissions: ['cpm_dma-archives'],
				},
				hidden: true,
			},
			{
				path: 'totalSubMeterSettingModify',
				name: 'TotalSubMeterSettingModify',
				component: () => import('@/views/meter-manage/total-sub-meter-setting/index.vue'),
				hidden: true,
				meta: {
					title: '总分表修改',
					keepAlive: true,
					permissions: ['cpm_dma-archives'],
				},
			},
			{
				path: 'totalSubMeterManage',
				name: 'TotalSubMeterManage',
				component: () => import('@/views/meter-manage/total-sub-meter-manage/index.vue'),
				meta: {
					title: '总分表管理',
					icon: 'icon-cis_ej_zongfenbiao',
					keepAlive: true,
					permissions: ['cpm_dma-archives_list-dma-archives'],
				},
			},
			{
				path: 'meterConvertRecords',
				name: 'MeterConvertRecords',
				component: () => import('@/views/meter-manage/meter-convert-records/index.vue'),
				meta: {
					title: '表卡转换记录',
					icon: 'icon-cis_ej_biaokazhuanhuan',
					keepAlive: true,
					permissions: ['cpm_archives_change-modifyrecords'],
				},
			},
			{
				path: 'batchCreateMeter',
				name: 'BatchCreateMeter',
				component: () => import('@/views/meter-manage/batch-create-meter/index.vue'),
				meta: {
					title: '批量创建表卡',
					keepAlive: true,
					permissions: ['cpm_archives-batch', 'cpm_archives-batch_record_list'],
				},
				hidden: true,
			},
			{
				path: 'meterTransfer',
				name: 'MeterTransfer',
				component: () => import('@/views/meter-manage/meter-transfer/index.vue'),
				meta: {
					title: '表卡转换',
					keepAlive: true,
					permissions: ['cpm_archives_transfer-tablecard', 'cpm_archives_transfer-tablecard2'],
				},
				hidden: true,
			},
		],
	},
]
