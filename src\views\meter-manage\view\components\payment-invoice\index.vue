<template>
	<GcElDialog
		:show="isShow"
		title="合并开票"
		width="700px"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleClose"
	>
		<div class="container">
			<div
				class="content-item"
				:class="{ active: radioValue === item.value }"
				@click="radioValue = item.value"
				v-for="(item, index) in radioList"
				:key="index"
			>
				<el-radio v-model="radioValue" :label="item.value"></el-radio>
				<div class="form">
					<el-row :gutter="40">
						<el-col v-for="(subItem, subIndex) in item.list" :key="subIndex" :span="12">
							<div class="label">{{ subItem.label }}</div>
							<div class="value">{{ subItem.value }}</div>
						</el-col>
					</el-row>
				</div>
			</div>
		</div>
	</GcElDialog>
</template>

<script>
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			radioValue: '1',
			radioList: [
				{
					value: '1',
					list: [
						{
							label: '用户名称',
							value: '易联云计算有限责任公司',
						},
						{
							label: '纳税人识别号',
							value: '98899594400500',
						},
						{
							label: '纳税人识别号',
							value: '98899594400500',
						},
						{
							label: '银行账号',
							value: '98899594400500',
						},
					],
				},
				{
					value: '2',
					list: [
						{
							label: '用户名称',
							value: '易联云计算有限责任公司',
						},
						{
							label: '纳税人识别号',
							value: '98899594400500',
						},
						{
							label: '纳税人识别号',
							value: '98899594400500',
						},
						{
							label: '银行账号',
							value: '98899594400500',
						},
					],
				},
				{
					value: '3',
					list: [
						{
							label: '用户名称',
							value: '易联云计算有限责任公司',
						},
						{
							label: '纳税人识别号',
							value: '98899594400500',
						},
						{
							label: '纳税人识别号',
							value: '98899594400500',
						},
						{
							label: '银行账号',
							value: '98899594400500',
						},
					],
				},
			],
		}
	},
	methods: {
		handleClose() {
			this.isShow = false
		},
	},
}
</script>
<style lang="scss" scoped>
.container {
	.active {
		border-color: #2f87fe !important;
	}
	.content-item {
		padding: 16px;
		cursor: pointer;
		margin-bottom: 16px;
		border: 1px solid #eef0f3;
		height: 116px;
		display: flex;
		align-items: flex-start;
		.label {
			font-family: Source Han Sans CN;
			font-size: 12px;
			font-weight: 400;
			line-height: 18px;
			color: #999999;
		}
		.value {
			font-family: Source Han Sans CN;
			font-size: 14px;
			font-weight: 400;
			line-height: 24px;
			color: #4e4e4e;
		}
	}

	::v-deep {
		.el-radio {
			margin-top: 2px;
		}
		.el-radio__label {
			display: none;
		}
		.el-col {
			margin-bottom: 2px;
		}
	}
}
</style>
