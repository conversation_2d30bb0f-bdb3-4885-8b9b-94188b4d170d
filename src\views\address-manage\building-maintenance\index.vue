<template>
	<div class="wrapper">
		<GcModelHeader :title="title" :icon="require('@/assets/images/icon/title-address.png')"></GcModelHeader>
		<div class="button-container">
			<div>
				<el-button v-has="'cpm_area_addBuilding'" type="primary" @click="handleSetting('add')">
					新增楼栋
				</el-button>
				<el-button v-has="'cpm_area_batchAddBuilding'" @click="handleSetting('batchAdd')">
					批量新增楼栋
				</el-button>
			</div>
			<el-button
				v-has="'cpm_area_batchDeleteBuilding'"
				:disabled="!selectedData.length"
				@click="handleDelete('all')"
			>
				批量删除楼栋
			</el-button>
		</div>
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			needType="selection"
			@selectChange="handleSelectChange"
			@current-page-change="handlePageChange"
		>
			<template v-slot:operate="{ row }">
				<el-button v-has="'cpm_area_updateBuilding'" type="text" @click="handleSetting('edit', row)">
					修改
				</el-button>
				<el-button v-has="'cpm_area_deleteBuilding'" type="text" @click="handleDelete('single', row)">
					删除
				</el-button>
			</template>
		</GcTable>
		<BuildingSetting
			:show.sync="showDialog"
			:editType="editType"
			:data="rowData"
			@success="handlePageChange({ page: 1 })"
		/>
	</div>
</template>

<script>
import { apiGetBuildingList, apiBatchDeleteBuilding, apiDeleteBuilding } from '@/api/addressManage.api'
import BuildingSetting from './components/BuildingSetting.vue'
export default {
	name: 'BuildingMaintenance',
	components: { BuildingSetting },
	computed: {
		title() {
			const addressAreaName = this.$route.query.addressAreaName ? this.$route.query.addressAreaName : '--'
			return '小区/村庄：' + addressAreaName
		},
	},
	data() {
		return {
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'buildingNo',
					name: '楼栋号',
					tooltip: true,
				},
				{
					key: 'unit',
					name: '单位名称',
					tooltip: true,
				},
				{
					key: 'addressAreaName',
					name: '地址描述',
					tooltip: true,
				},
				{
					key: 'createTime',
					name: '创建时间',
					tooltip: true,
				},
				{
					key: 'operate',
					name: '操作',
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			selectedData: [],
			showDialog: false,
			editType: 'add',
			rowData: null,
		}
	},
	activated() {
		if (this.$route.query.addressAreaCode) {
			this.handlePageChange({ page: 1 })
		}
	},
	methods: {
		async getList() {
			this.loading = true
			this.selectedData = []
			try {
				const { current, size } = this.pageData
				const { addressAreaCode } = this.$route.query
				const { total = 0, records = [] } = await apiGetBuildingList({
					size,
					current,
					areaCode: addressAreaCode,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleSelectChange(arr) {
			this.selectedData = arr
		},
		handleDelete(type, obj) {
			let ids = []
			if (type === 'all') {
				ids = this.selectedData.map(item => item.addressAreaId)
			} else {
				ids = [obj.addressAreaId]
			}
			const apiName = type === 'all' ? apiBatchDeleteBuilding : apiDeleteBuilding
			this.$confirm('确定要删除所选择的楼栋吗?').then(async () => {
				try {
					await apiName({
						addressAreaId: ids,
					})
					this.$message.success('删除成功')
				} catch (error) {
					console.error(error)
				} finally {
					this.handlePageChange({ page: 1 })
				}
			})
		},
		handleSetting(type, row) {
			this.editType = type
			this.showDialog = true
			if (row) {
				this.rowData = row
			}
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	padding: 0 20px 20px 20px;
	.model-header {
		padding-left: 0;
	}
	.button-container {
		margin-bottom: 10px;
		display: flex;
		justify-content: space-between;
	}
}
</style>
