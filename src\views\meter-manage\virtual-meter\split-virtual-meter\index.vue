<template>
	<div class="wrapper">
		<div class="left-wrapper">
			<div class="left-title">实表拆分简目录</div>
			<GcTab :tabList="tabList" @changeTab="changeTab" :defaultTab="activeTab" />
		</div>
		<div class="right-wrapper">
			<RealMeter ref="realMeterRef" v-show="activeTab == 'real'" @changeTab="changeTab" @getValid="getValid" />
			<VirtualMeter
				ref="virtualMeterRef"
				v-show="activeTab == 'virtual'"
				:isShow="activeTab == 'virtual'"
				:realMeterData="realMeterData"
				@changeTab="changeTab"
				@getValid="getValid"
			/>
		</div>
		<div class="button-group">
			<el-button
				v-has="'cpm-archives-update-virtual-records'"
				class="btn-create"
				type="primary"
				@click="handleSubmit"
			>
				确定拆分
			</el-button>
		</div>
	</div>
</template>

<script>
import RealMeter from './real-meter/RealMeter.vue'
import VirtualMeter from './virtual-meter/VirtualMeter.vue'
import { apiUpdateVirtualRecords } from '@/api/meterManage.api'
export default {
	name: '',
	components: { RealMeter, VirtualMeter },
	data() {
		return {
			tabList: [
				{
					label: '实表选择',
					value: 'real',
					status: 1,
					disabled: false,
					tip: '实表信息待完善',
				},
				{
					label: '虚表分配',
					value: 'virtual',
					status: 1,
					disabled: false,
					tip: '待完善',
				},
			],
			activeTab: '',
			realMeterData: {},
			tableData: [],
		}
	},
	computed: {},

	activated() {
		const { archivesIdentity, archivesId } = this.$route.query
		if (archivesId) {
			this.$nextTick(async () => {
				await this.$refs.virtualMeterRef._apiGetPriceList_all()
				this.$refs.realMeterRef.formData.archivesIdentity = archivesIdentity
				await this.$refs.realMeterRef._apiGetArchivesDetail(archivesIdentity)
				this.$refs.virtualMeterRef._apiGetVirtualArchivesDetail(archivesId)
			})
		} else {
			this.$nextTick(async () => {
				await this.$refs.virtualMeterRef._apiGetPriceList_all()
				this.$refs.realMeterRef.validateForm()
			})
		}
	},
	methods: {
		changeTab(v) {
			this.activeTab = v
		},
		getValid(key, flag, data, errorMessage) {
			console.log('getValid', key, flag, data, errorMessage)
			// 没有选择实表 其余菜单不可点击
			if (key === 'real') {
				this.tabList
					.filter(item => item.value !== 'real')
					.forEach(item => {
						item.disabled = !flag
					})
				this.realMeterData = data
			} else {
				this.tableData = data
			}
			const obj = this.tabList.find(item => item.value === key)
			if (obj) {
				if (!flag) {
					obj.tip = errorMessage ? errorMessage : obj.tip
				}
				obj.status = flag ? 2 : 1
			}
		},
		handleSubmit() {
			const obj = this.tabList.find(item => item.status == 1)
			if (obj) {
				this.$message.error(`${obj.tip}`)
				this.activeTab = obj.value
			} else {
				this._apiUpdateVirtualRecords()
			}
		},
		async _apiUpdateVirtualRecords() {
			const distributionMode = this.$refs.virtualMeterRef.radioValue
			const archivesId = this.realMeterData.archivesId
			const virtualArchivesList = this.tableData.map((item, index) => {
				const obj = {
					archivesId: item.archivesId,
					archivesIdentity: item.archivesIdentity,
					sort: index + 1,
					distributionMode,
					usageMeasure: item.usageMeasure,
					priceId: item.priceId,
				}
				if (distributionMode == 0) {
					obj['subDistributionMode'] = 0
				} else if (distributionMode == 1) {
					obj['subDistributionMode'] = 1
				} else if (distributionMode == 2) {
					obj['subDistributionMode'] = item.subDistributionMode
				} else if (distributionMode == 4) {
					obj['subDistributionMode'] = item.subDistributionMode
				}
				return obj
			})
			const params = {
				archivesId,
				distributionMode,
				virtualArchivesList,
			}

			await apiUpdateVirtualRecords(params)
			this.$message.success('拆分成功')
			this.$store.dispatch('tagsView/delView', this.$route).then(() => {
				this.$router.push({
					path: '/meterManage/virtualMeterManage',
				})
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-wrap: wrap;
	height: 100%;
}
.left-wrapper {
	position: relative;
	padding: 20px;
	flex-grow: 0;
	flex-shrink: 0;
	width: 280px;
	height: calc(100% - 40px);
	background-color: #fff;
	.left-title {
		height: 48px;
		line-height: 48px;
		color: #000000;
		font-family: Source Han Sans CN;
		font-size: 16px;
		font-weight: 500;
	}
}
.left-wrapper:after {
	position: absolute;
	top: 20px;
	bottom: 20px;
	right: 0;
	content: '';
	display: block;
	clear: both;
	width: 1px;
	border-right: 1px dashed #eef0f3;
}
.right-wrapper {
	width: 0;
	flex: 1;
	padding: 20px;
	height: calc(100% - 40px);
	background-color: #fff;
}
.button-group {
	width: 100%;
	height: 40px;
	.btn-create,
	.btn-preview {
		margin-top: 20px;
		border-radius: 4px;
		height: 32px;
	}
	.btn-create {
		width: 216px;
	}
	.btn-preview {
		width: 110px;
		border: 1px solid #2f87fe;
		color: #2f87fe;
	}
}
</style>
