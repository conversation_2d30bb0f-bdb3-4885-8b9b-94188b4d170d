<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-10 16:12:11
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 20:27:37
-->
<template>
	<div class="right-container">
		<div class="btn-box">
			<div class="btn-group">
				<el-button
					v-has="'plan-collection_meterReadingBook_addBook'"
					type="primary"
					@click="handleRecord('add')"
				>
					新建册本
				</el-button>
				<el-button v-has="'meterReadingBook_transfer_meterCardOpsPage'" type="primary" @click="handleTransfer">
					册本移交
				</el-button>
				<el-button
					v-has="'plan-collection_meterReadingBook_getBookArchivesList2'"
					type="primary"
					@click="handleBookAdjust"
				>
					册本调整
				</el-button>
				<el-button
					v-has="'plan-collection_report_bookList_export_excel'"
					type="primary"
					:disabled="this.tableData.length === 0"
					@click="handleBookListExport"
				>
					册本导出
				</el-button>
			</div>
			<div class="btn-grop">
				<span class="checked-total">表册已选择：{{ checkedTotal }}个</span>
				<el-button type="primary" @click="handleCheckAllButtonClick">
					{{ checkAll ? '取消选中所有页' : '选中所有页' }}
				</el-button>
				<el-button
					v-has="'plan-collection_meterReadingBook_meterReadingStaff'"
					type="primary"
					:disabled="isChangeButtonDisabled"
					@click="handleChangeButtonClick"
				>
					更改抄表员
				</el-button>
			</div>
		</div>
		<div class="table-wrapper" v-loading="loading">
			<div class="table-container">
				<el-table
					v-show="tableData.length"
					ref="tableRef"
					:data="tableData"
					:total="pageData.total"
					:header-cell-style="{
						background: '#F0F4FA',
						color: '#222222',
						'font-weight': 600,
					}"
					height="100%"
					size="medium"
					border
					@select="handleSelect"
					@select-all="handleSelectAll"
					@row-dblclick="handleBookDetail"
				>
					<el-table-column type="selection" width="55"></el-table-column>
					<template v-for="item in columns">
						<el-table-column
							v-if="!item.hide"
							:key="item.key"
							:prop="item.key"
							:label="item.name"
							:show-overflow-tooltip="item.tooltip"
							:width="item.width"
							:min-width="item.minWidth || 150"
							:fixed="item.fixed"
							:align="item.align || 'left'"
						>
							<template slot-scope="{ row }">
								<template v-if="item.key === 'deal'">
									<el-button
										v-has="'plan-collection_meterReadingBook_updateBook'"
										type="text"
										size="medium"
										@click="handleRecord('edit', row)"
									>
										编辑
									</el-button>
									<el-button
										v-has="'plan-collection_report_bookArchivesList_export_excel'"
										type="text"
										size="medium"
										@click="handleBookArchivesListExport(row)"
									>
										导出册本表卡明细
									</el-button>
									<el-button
										v-has="'plan-collection_meterReadingTask_createFirstTask'"
										type="text"
										size="medium"
										@click="handleCreateTask(row.bookId)"
									>
										生成抄表任务
									</el-button>
									<el-button
										v-has="'plan-collection_meterReadingBook_updateBookNo'"
										v-if="row.meterNum < 1"
										type="text"
										size="medium"
										@click="handelBooKNoEdit(row)"
									>
										编辑表册编号
									</el-button>
									<el-button
										v-has="'plan-collection_meterReadingBook_deleteBook'"
										type="text"
										size="medium"
										@click="handleDelete(row.bookId)"
									>
										删除
									</el-button>
								</template>
								<template v-else>
									{{ judgeBlank(row, item) }}
								</template>
							</template>
						</el-table-column>
					</template>
				</el-table>

				<gc-empty v-show="!tableData.length"></gc-empty>
			</div>
			<GcPagination
				v-show="tableData.length"
				:pageSize="pageData.size"
				:total="pageData.total"
				:currentPage="pageData.current"
				@current-page-change="handlePageChange"
			></GcPagination>
		</div>

		<!-- 册本新建修改弹窗 -->
		<RecordDialog ref="recordDialogRef" :show.sync="showRecord" :editType="editType" @success="getList(1)" />
		<bookNoEditDialog ref="bookNoEditDialogRef" :show.sync="showBookNoEditDialog" @success="getList(1)" />
		<ChangeMeterReadingStaffDialog
			ref="changeMeterReadingStaffDialogRef"
			:orgCode="params.orgCode"
			:params="changeMeterReadingStaffParams"
			:show.sync="showChangeMeterReadingStaffDialog"
			@success="handleChangeMeterReadingStaffSuccess"
		></ChangeMeterReadingStaffDialog>
	</div>
</template>

<script>
import RecordDialog from '../components/recordDialog.vue'
import bookNoEditDialog from '../components/bookNoEditDialog.vue'
import ChangeMeterReadingStaffDialog from '../components/ChangeMeterReadingStaffDialog.vue'
import { getColumn } from './tableColumn.js'
import { exportBlob } from '@/utils/index.js'
import { isBlank } from '@/utils/validate.js'
import {
	getBookList,
	deleteMeterReadingBook,
	bookListExport,
	bookArchivesListExport,
	createFirstTask,
} from '@/api/meterReading.api.js'

export default {
	name: '',
	components: { RecordDialog, bookNoEditDialog, ChangeMeterReadingStaffDialog },
	props: {
		params: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			// 列表
			loading: false,
			columns: getColumn(this),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 新建、编辑
			showRecord: false,
			showBookNoEditDialog: false,
			showChangeMeterReadingStaffDialog: false,
			editType: 'add',
			// 选中所有
			checkAll: false,
			checkedTotal: 0,
			selectData: [], // 全局选中的数据
			selectedRows: [], // 当前页选中的数据
			unSelectData: [], // 全局未选中的数据
			unSelectedRows: [], // 当前页未选中的数据
			changeMeterReadingStaffParams: {},
		}
	},
	computed: {
		isChangeButtonDisabled() {
			const { checkedTotal } = this
			return !checkedTotal
		},
	},
	methods: {
		// 新建、编辑册本
		handleRecord(type, row) {
			this.editType = type
			this.showRecord = true
			if (type !== 'add') {
				this.$nextTick(() => {
					const {
						bookId,
						orgCode,
						alleyId,
						bookNo,
						bookType,
						meterReadingCycle,
						meterReadingNumber,
						meterReadingStaffId,
						rangeStart,
						rangeEnd,
						meterReadingStaffPhone,
						remark,
					} = row
					this.$refs.recordDialogRef.setFormData({
						bookId,
						orgCode,
						alleyId,
						bookNo,
						bookType,
						meterReadingCycle,
						meterReadingNumber,
						meterReadingStaffId,
						staffPhone: meterReadingStaffPhone,
						rangeStart,
						rangeEnd,
						remark,
					})
				})
			} else {
				this.$nextTick(() => {
					const orgList = this.$store.getters.orgList || []
					const firstOrgItem = orgList[0] || {}
					this.$refs.recordDialogRef.setFormData({
						orgCode: firstOrgItem.value || '',
					})
				})
			}
		},
		handelBooKNoEdit(row) {
			this.showBookNoEditDialog = true
			this.$nextTick(() => {
				const { bookId, orgCode, alleyId, bookNo, bookType } = row
				this.$refs.bookNoEditDialogRef.setFormData({
					bookId,
					orgCode,
					alleyId,
					bookNo,
					bookType,
				})
			})
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		// 册本列表
		async getList(curPage, isSearch) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}

			if (isSearch) {
				this.clearData()
				this.checkAll = false
			}

			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getBookList({
					size,
					current,
					...this.params,
				})
				this.pageData.total = total
				this.tableData = records
				// 选中全部  勾选复选框
				if (this.checkAll) {
					// 筛选出当前页未勾选数据
					const matchedData = this.unSelectData.filter(item =>
						this.tableData.some(t => t.bookId === item.bookId),
					)
					if (matchedData.length) {
						this.selectedRows = this.tableData.filter(
							item => !matchedData.some(m => m.bookId == item.bookId),
						)
					} else {
						this.selectedRows = this.tableData
					}
					this.addGlobalSelectData()
				}
				this.updateSelection()
			} catch (error) {
				console.error(error)
				// this.tableData = []
				// this.pageData = {
				// 	current: 1,
				// 	size: 10,
				// 	total: 0,
				// }
			} finally {
				this.loading = false
			}
		},
		// 册本移交
		handleTransfer() {
			this.$router.push('/meterReading/meterReadingTransfer')
		},
		// 册本调整
		handleBookAdjust() {
			this.$router.push('/meterReading/recordAdjust')
		},
		// 册本导出
		async handleBookListExport() {
			const res = await bookListExport(this.params)
			exportBlob(res, '册本列表')
		},
		// 册本表卡明细导出
		async handleBookArchivesListExport(row) {
			const res = await bookArchivesListExport({
				bookId: row.bookId,
			})
			exportBlob(res, '册本表卡明细')
		},
		// 册本视图详情
		handleBookDetail(row) {
			if (!this.$has('plan-collection_meterReadingBook_getBookArchivesList')) return

			const { bookId } = row
			this.$router.push({
				path: '/meterReading/recordView',
				query: {
					bookId,
				},
			})
		},
		// 删除
		handleDelete(bookId) {
			this.$confirm('将对册本进行删除，请确认是否继续？', '册本删除提示').then(async () => {
				await deleteMeterReadingBook({ bookId })
				this.$message.success('删除成功')
				this.getList(1)
			})
		},
		// 生成抄表任务
		async handleCreateTask(bookId) {
			try {
				await createFirstTask({
					bookId,
				})
				this.$message.success('生成抄表任务成功')
				this.getList(1)
			} catch (error) {
				console.error(error)
			}
		},
		resetTableData() {
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
		handleCheckAllButtonClick() {
			this.checkAll = !this.checkAll
			if (this.checkAll) {
				this.clearData()
				this.selectedRows = this.tableData
				this.addGlobalSelectData()
				this.updateSelection()
				this.checkedTotal = this.pageData.total
			} else {
				this.clearData()
				this.$refs.tableRef.clearSelection()
			}
		},
		clearData() {
			this.unSelectData = []
			this.unSelectedRows = []
			this.selectData = []
			this.selectedRows = []
			this.checkedTotal = 0
		},
		// 手动勾选全部
		handleSelectAll(selection) {
			// 选中
			if (selection.length) {
				// 之前已经选中过, 总数减去之前选中数目
				const pastArr = this.selectData.filter(i => selection.some(s => s.bookId === i.bookId))
				this.checkedTotal = this.checkedTotal - pastArr.length + this.tableData.length
				this.unSelectData = this.unSelectData.filter(i => !selection.some(s => s.bookId === i.bookId))
				this.selectedRows = selection
				this.addGlobalSelectData()
			} else {
				// 取消选中
				this.selectData = this.selectData.filter(i => !this.tableData.some(t => t.bookId === i.bookId))
				this.checkedTotal -= this.tableData.length
				this.unSelectedRows = this.tableData
				this.addGlobalUnSelectData()
			}
		},
		// 手动勾选单个
		handleSelect(selection, row) {
			const ifChecked = selection.find(item => item.bookId === row.bookId)
			if (ifChecked) {
				// 选中
				this.unSelectData = this.unSelectData.filter(item => item.bookId !== row.bookId)
				this.selectedRows = selection
				this.addGlobalSelectData()
				this.checkedTotal += 1
			} else {
				//取消选中
				this.selectData = this.selectData.filter(item => item.bookId !== row.bookId)
				this.unSelectedRows = [row]
				this.addGlobalUnSelectData()
				this.checkedTotal -= 1
			}
		},
		mergeAndDeduplicateArrays(array1, array2) {
			const mergedArray = [...array1, ...array2]
			return Array.from(new Map(mergedArray.map(item => [item.bookId, item])).values())
		},
		addGlobalSelectData() {
			this.selectData = this.mergeAndDeduplicateArrays(this.selectData, this.selectedRows)
		},
		addGlobalUnSelectData() {
			this.unSelectData = this.mergeAndDeduplicateArrays(this.unSelectData, this.unSelectedRows)
		},
		// 更新选中状态
		updateSelection() {
			this.$nextTick(() => {
				if (this.$refs.tableRef) {
					this.$refs.tableRef.clearSelection()
					this.selectData.forEach(item => {
						const row = this.tableData.find(row => row.bookId === item.bookId)
						if (row) {
							this.$refs.tableRef.toggleRowSelection(row, true)
						}
					})
				}
			})
		},
		judgeBlank(row, item) {
			return isBlank(row[item.key]) ? item.emptyCellText || '--' : row[item.key]
		},
		handleChangeButtonClick() {
			const { checkAll, selectData, unSelectData, params } = this
			let data = {}

			if (checkAll) {
				data = {
					all: true,
					notBookIdList: unSelectData.map(item => {
						return item.bookId
					}),
					...(params || {}),
				}
			} else {
				data = {
					all: false,
					bookIdList: selectData.map(item => {
						return item.bookId
					}),
				}
			}

			this.changeMeterReadingStaffParams = data
			this.showChangeMeterReadingStaffDialog = true
		},
		handleChangeMeterReadingStaffSuccess() {
			this.clearData()
			this.checkAll = false
			// this.updateSelection()
			this.getList(1)
		},
	},
}
</script>

<style lang="scss" scoped>
.right-container {
	width: 0;
	flex: 1;
	padding: 20px;
	background-color: #fff;
	display: flex;
	flex-direction: column;
}

.btn-box {
	margin-bottom: 20px;
	display: flex;
	justify-content: space-between;
}

.checked-total {
	margin-right: 8px;
}

.el-button--text {
	margin-left: 10px;
	margin-right: 0;
}

.table-wrapper {
	flex: 1;
	overflow: hidden;

	.table-container {
		height: calc(100% - 64px);
	}

	::v-deep .el-table--border {
		border: none;
	}

	::v-deep .table-container {
		.el-table tr {
			th:nth-child(1) {
				border-left: 1px solid rgba(170, 178, 193, 0.2);
			}

			td:nth-child(1) {
				border-left: 1px solid rgba(170, 178, 193, 0.2);
			}

			th:last-child {
				border-right: 2px solid rgba(170, 178, 193, 0.2);
			}

			td:last-child {
				border-right: 2px solid rgba(170, 178, 193, 0.2);
			}
		}

		.el-table td {
			border-right: 1px solid rgba(170, 178, 193, 0.2);
		}

		.el-table th {
			border-top: 1px solid rgba(170, 178, 193, 0.2);
			border-right: 1px solid rgba(170, 178, 193, 0.2);

			&.gutter {
				border: 1px solid transparent !important;
			}
		}

		.el-table.el-table--scrollable-y {
			.el-table__body-wrapper.is-scrolling-none {
				padding-right: 2px;

				tr {
					th:last-child {
						border-right: 1px solid rgba(170, 178, 193, 0.2);
					}

					td:last-child {
						border-right: 1px solid rgba(170, 178, 193, 0.2);
					}
				}
			}
		}
	}
}
</style>
