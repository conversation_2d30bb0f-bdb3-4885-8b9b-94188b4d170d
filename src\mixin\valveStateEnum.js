import { getDataListEnum } from '@/utils/index.js'
export default {
	computed: {
		valveStateEnum() {
			const { valveState } = this.$store.getters.dataList
			return getDataListEnum(valveState)
		},
		valveStateIcon() {
			return {
				0: 'icon-famen-hong', //关",
				1: 'icon-famen-lv', //开",
				5: 'icon-damen-hui', //无阀门",
				2: 'icon-famen-cheng', //正在关",
				3: 'icon-famen-cheng', //正在开",
				4: 'icon-famen-guzhang', //故障",
				6: 'icon-famen-hong', //普关",
			}
		},
		valveStateColor() {
			return {
				0: '#EC6B60', //关",
				1: '#12B3C7', //开",
				5: '#AAB2C1', //无阀门",
				2: '#FF9D57', //正在关",
				3: '#FF9D57', //正在开",
				4: '#EC6B60', //故障",
				6: '#EC6B60', //普关",
			}
		},
	},
}
