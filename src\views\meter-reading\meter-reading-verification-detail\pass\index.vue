<template>
	<div class="wrapper">
		<div class="container-search">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" @click="getList(1)">筛选</el-button>
					<el-button @click="handleReset">重置</el-button>
				</el-form-item>
			</GcFormSimple>
			<div class="btn-group">
				<!-- TODO -->
				<el-button v-has="'plan-collection_report_reviewList_export_excel4'" type="primary" disabled>
					导出审核结果
				</el-button>
				<el-button
					v-has="'billing_bill_meterTaskCreateBill'"
					type="primary"
					:disabled="tableData.length === 0"
					@click="handleCreateBillByMeterTask"
				>
					整册开账
				</el-button>
				<el-button
					v-has="'billing_bill_create'"
					type="primary"
					:disabled="selectedData.length === 0"
					@click="handleCreateBill(selectedData)"
				>
					账单开账
				</el-button>
			</div>
		</div>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				needType="selection"
				:selectable="handleSelectable"
				@selectChange="handleSelectChange"
				@current-page-change="handlePageChange"
			>
				<!-- 抄表经纬度 -->
				<template v-slot:latitudeLongitude="{ row }">
					<span>
						{{ row.longitude && row.latitude ? `${row.longitude},${row.latitude}` : '--' }}
					</span>
				</template>
				<template v-slot:imageUrl="{ row }">
					<UploadImgSimple v-model="row.imageUrl" />
				</template>
				<template v-slot:deal="{ row }">
					<el-button
						v-if="row.recordState === 1 && row.useAmount && row.archivesMeterType === 1"
						type="text"
						size="medium"
						@click="handleCreateBill([row])"
					>
						账单开账
					</el-button>
				</template>
			</GcTable>
		</div>
	</div>
</template>

<script>
import UploadImgSimple from '@/components/UploadImgSimple'
import { getColumn } from './tableColumn.js'
import { getReviewDetailList4, createMeterTaskBill, createBill } from '@/api/meterReading.api.js'

export default {
	name: '',
	components: { UploadImgSimple },
	props: {
		type: Number,
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
				isOpenBill: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
				{
					type: 'el-select',
					label: '是否开账',
					prop: 'isOpenBill',
					attrs: {
						clearable: false,
					},
					options: [
						{
							label: '全部',
							value: '',
						},
						{
							label: '是',
							value: 1,
						},
						{
							label: '否',
							value: 2,
						},
					],
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '80px',
			},
			loading: false,
			columns: getColumn(),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			// 列表选中数据
			selectedData: [],
		}
	},
	computed: {},
	created() {
		this.getList()
	},
	methods: {
		handleSelectable(row) {
			const { useAmount, recordState, archivesMeterType } = row
			// useAmount 本次水量
			// recordState 账单状态：0-未生成账单 1-待生成账单 2-已生成账单
			// archivesMeterType 结算类型: 0-非贸易结算表 1-贸易结算表
			return recordState === 1 && useAmount && archivesMeterType === 1
		},
		handleSelectChange(data) {
			this.selectedData = data
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.getList(1)
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		async getList(curPage) {
			this.loading = true

			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { meterReadingTaskId, date } = this.$route.query
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getReviewDetailList4({
					type: this.type,
					current,
					size,
					meterReadingTaskId,
					date,
					...this.formData,
				})
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},

		// 全部生成账单
		async handleCreateBillByMeterTask() {
			this.$confirm('确定要整册开账吗?').then(async () => {
				const { meterReadingTaskId, taskYear: year } = this.$route.query
				await createMeterTaskBill({
					meterReadingTaskId,
					year,
				})
				this.$message.success('整册开账成功')
				this.getList(1)
			})
		},

		// 生成账单
		async handleCreateBill(data = []) {
			this.$confirm('确定要账单开账吗?').then(async () => {
				await createBill({
					meterReadingRecordIdList: data.map(item => item.meterReadingRecordId),
					year: this.$route.query.taskYear,
				})
				this.$message.success('账单开账成功')
				this.getList(1)
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	flex: 1;
	height: 0;
	display: flex;
	flex-direction: column;
	padding: 20px;
}
.container-search {
	display: flex;
	justify-content: space-between;
}
.table-container {
	flex: 1;
	height: 0;
}
</style>
