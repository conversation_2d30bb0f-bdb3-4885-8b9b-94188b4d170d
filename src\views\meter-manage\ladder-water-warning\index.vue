<template>
	<div class="wrapper">
		<div class="search-wrapper">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" @click="handleSearch">
						<i class="iconfontCis icon-small-search"></i>
						筛选
					</el-button>
					<el-button @click="handleReset">
						<i class="iconfontCis icon-reset"></i>
						重置
					</el-button>
				</el-form-item>
			</GcFormSimple>
			<el-button
				v-has="'cpm_report_water-amount-warning_export_excel'"
				type="primary"
				:disabled="!tableData.length"
				@click="handleExport"
			>
				预警报表导出
			</el-button>
		</div>
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			@current-page-change="handlePageChange"
		>
			<template v-slot:priceDesc="{ row }">
				<div v-html="row.priceDesc"></div>
			</template>
			<template v-slot:ladderAmountLimit="{ row }">
				<div v-html="row.ladderAmountLimit"></div>
			</template>
		</GcTable>
	</div>
</template>

<script>
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams, exportBlob } from '@/utils/index.js'
import { apiGetPriceList_all, apiGetWaterAmountWarning, apiWaterAmountWarningExport } from '@/api/meterManage.api.js'
export default {
	name: 'LadderWaterWarning',
	data() {
		return {
			formData: {
				orgCode: '',
				archivesIdentity: '',
				priceId: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
				},
				{
					type: 'el-select',
					label: '价格',
					prop: 'priceId',
					options: [],
				},
			],
			formAttrs: {
				inline: true,
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
				},
			},
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'addressFullName',
					name: '地址',
					tooltip: true,
				},
				{
					key: 'priceCode',
					name: '价格编号',
					tooltip: true,
				},
				{
					key: 'priceDesc',
					name: '价格',
					tooltip: true,
					minWidth: 220,
				},
				{
					key: 'ladder',
					name: '当前阶梯',
					tooltip: true,
				},
				{
					key: 'ladderAmount',
					name: '当前阶梯水量',
					tooltip: true,
				},
				{
					key: 'ladderAmountLimit',
					name: '预警阈值',
					tooltip: true,
					minWidth: 220,
				},
				{
					key: 'cycleQuantity',
					name: '当前周期水量',
					tooltip: true,
				},
				{
					key: 'lastSettleTime',
					name: '抄表时间',
					tooltip: true,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			priceList: [],
		}
	},
	created() {
		this._apiGetPriceList_all()
	},
	computed: {
		orgOptions() {
			return this.formItems[0]?.options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.$nextTick(() => {
						this.handleSearch()
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		async _apiGetPriceList_all() {
			try {
				const { records } = await apiGetPriceList_all()

				this.priceList = records
				if (records && records.length > 0) {
					this.formItems[2].options = records.map(item => {
						return {
							label: item.priceName,
							value: item.priceId,
						}
					})
				}
			} catch (error) {
				console.log(error)
			}
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleReset() {
			this.$refs.formRef.clearValidate()
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.handlePageChange({ page: 1 })
		},
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
					orgCode: [formParams.orgCode],
				})
				const { total = 0, records = [] } = await apiGetWaterAmountWarning(formParams)

				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleExport() {
			const { current, size } = this.pageData
			const formParams = trimParams(removeNullParams(this.formData))
			Object.assign(formParams, {
				current,
				size,
				orgCode: [formParams.orgCode],
			})
			apiWaterAmountWarningExport(formParams).then(res => {
				exportBlob(res, '预警报表')
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	padding: 20px;
}
.search-wrapper {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}
</style>
