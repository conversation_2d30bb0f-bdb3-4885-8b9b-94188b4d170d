<template>
	<div class="container">
		<div class="container-title">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" :loading="mainLoading" @click="handleSearch">
						<i class="iconfontCis icon-small-search"></i>
						筛选
					</el-button>
					<el-button @click="handleReset">
						<i class="iconfontCis icon-reset"></i>
						重置
					</el-button>
				</el-form-item>
			</GcFormSimple>
		</div>
		<div class="flex">
			<StatisticsList :statisticData="billStatistics" @year-data-select="handleYearQuery" />
			<div class="bill-container">
				<el-tabs v-model="tableDataQuery.type" type="border-card">
					<el-tab-pane
						v-for="(tab, index) in tabs"
						:key="index"
						:label="tab.label"
						:name="tab.name"
					></el-tab-pane>
				</el-tabs>
				<div class="container-search">
					<GcFormSimple
						v-model="tableDataQuery"
						:formItems="listFilterFormItems"
						:formAttrs="listFilterFormAttrs"
					>
						<el-form-item>
							<el-button type="primary" :loading="loading" @click="handleSubQuery">
								<i class="iconfontCis icon-small-search"></i>
								筛选
							</el-button>
							<el-button @click="handleListFilterReset">
								<i class="iconfontCis icon-reset"></i>
								重置
							</el-button>
						</el-form-item>
					</GcFormSimple>
					<div class="btn-group">
						<el-button
							v-has="'billing_unionCollection_closeUnionBill'"
							v-if="billClosable && +tableDataQuery.type !== 2"
							type="primary"
							:disabled="!selectedBillList.length"
							@click="handleClearList"
						>
							手动销账
						</el-button>
						<el-button
							v-has="'billing_unionCollection_exportBill'"
							type="primary"
							:disabled="!tableData.length"
							@click="handleExport"
						>
							导出对账记录
						</el-button>
					</div>
				</div>
				<component
					ref="listTable"
					:is="listTable"
					:loading="loading"
					:tableData="tableData"
					:billSum="billSum"
					:billClosable="billClosable"
					@page-change="handlePageChange"
					@select-change="handleSelectChange"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import PushBillList from './push-bill-list'
import ClearBillList from './clear-bill-list'
import AbnormalBillList from './abnormal-bill-list'
import StatisticsList from './statistics-list'
import { ruleRequired } from '@/utils/rules.js'
import {
	queryUnionBillStatistics,
	queryUnionBillList,
	closeUnionBill,
	unionCollectionExportBill,
} from '@/api/costManage.api'
export default {
	name: '',
	components: {
		PushBillList,
		ClearBillList,
		AbnormalBillList,
		StatisticsList,
	},
	data() {
		return {
			tabs: [
				{ label: '已推送账单', name: '1' },
				{ label: '已销账账单', name: '2' },
				{ label: '异常账单', name: '3' },
			],
			loading: false,
			mainLoading: false,
			formData: {
				orgCode: '',
				fileMonth: '',
				fileDay: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
				},
				{
					type: 'el-date-picker',
					label: '送盘文件生成月',
					prop: 'fileMonth',
					attrs: {
						type: 'month',
						valueFormat: 'yyyy-MM',
					},
				},
				{
					type: 'el-date-picker',
					label: '回盘日期',
					prop: 'fileDay',
					attrs: {
						type: 'date',
						valueFormat: 'yyyy-MM-dd',
					},
				},
			],
			formAttrs: {
				inline: true,
				rules: {
					fileMonth: [ruleRequired('请选择送盘文件生成月')],
				},
			},
			listFilterFormItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
						width: 150,
					},
				},
			],
			listFilterFormAttrs: {
				inline: true,
				labelWidth: '80px',
			},
			tableDataQuery: {
				billYear: '',
				archivesIdentity: '',
				type: '1',
			},
			billStatistics: [],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			billSum: {},
			selectedBillList: [],
			billClosable: false,
			billFileMonth: '',
		}
	},
	computed: {
		listTable() {
			const type = +this.tableDataQuery.type
			return type === 1 ? 'PushBillList' : type === 2 ? 'ClearBillList' : 'AbnormalBillList'
		},
	},
	created() {
		const orgList = this.$store.getters.orgList || []
		const firstOrgItem = orgList[0] || {}
		this.formData.orgCode = firstOrgItem.value || ''
	},
	methods: {
		pageReset() {
			this.pageData.current = 1
			this.pageData.total = 0
			this.$refs.listTable.pageDataReset()
		},
		filterFormData(data) {
			const filteredData = {}
			for (const key in data) {
				if (data[key]) {
					filteredData[key] = data[key]
				}
			}
			return filteredData
		},
		clearTableData() {
			this.tableData = []
			this.billSum = {
				count: 0,
				amount: '0.00',
			}
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.billClosable = false
			this.billFileMonth = ''
			this.pageReset()
		},
		async queryUnionBillStatistics() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.mainLoading = true
			const params = this.filterFormData(this.formData)
			delete params.type
			queryUnionBillStatistics(params)
				.then(res => {
					this.billStatistics = res
					const hasYearData = res.some(item => item.billYear)
					if (!hasYearData) {
						this.clearTableData()
					}
				})
				.catch(err => {
					console.log(err)
					this.billStatistics = []
					this.clearTableData()
				})
				.finally(() => {
					this.mainLoading = false
				})
		},
		handleSubQuery() {
			this.pageReset()
			this.queryUnionBillList()
		},
		async queryUnionBillList() {
			const params = Object.assign(this.filterFormData(this.formData), this.tableDataQuery)
			if (!params.billYear || !params.fileMonth) return
			params.type = +params.type
			params.size = this.pageData.size
			params.current = this.pageData.current
			this.loading = true
			queryUnionBillList(params)
				.then(res => {
					this.tableData = res.billList.records
					this.pageData.total = res.billList.total
					this.billSum = {
						count: res.billNum,
						amount: (res.billAmount || 0).toFixed(2),
					}
					this.billClosable = res.status === 4
					this.billFileMonth = res.fileMonth
					this.$refs.listTable.pageData.total = res.billList.total
				})
				.catch(err => {
					console.log(err)
					this.clearTableData()
				})
				.finally(() => {
					this.loading = false
				})
		},
		handlePageChange(pageData) {
			this.pageData = pageData
			this.queryUnionBillList()
		},
		handleSelectChange(arr) {
			this.selectedBillList = arr
		},
		handleYearQuery(yearBill) {
			this.tableDataQuery.billYear = yearBill.billYear
			this.pageReset()
			this.queryUnionBillList()
		},
		handleSearch() {
			this.pageReset()
			this.queryUnionBillStatistics()
			this.tableData.length = 0
		},
		handleListFilterReset() {
			this.tableDataQuery.archivesIdentity = ''
			this.pageReset()
			this.queryUnionBillList()
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.tableDataQuery.type = '1'
			this.tableDataQuery.billYear = ''
			this.tableDataQuery.archivesIdentity = ''
			this.handleSearch()
		},
		handleClearList() {
			const totalAmount = this.selectedBillList.reduce((total, item) => total + +item.receivableAmount, 0)
			const label = `确定要对选中的表卡进行销账? 总金额 ${totalAmount.toFixed(2)} 元`
			this.$confirm(label, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
				center: true,
			})
				.then(() => {
					const data = {
						fileMonth: this.billFileMonth,
						sendFileRefIds: this.selectedBillList.map(item => item.id),
					}
					this._apiClearAccount(data)
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消销账',
					})
				})
		},
		async _apiClearAccount(params) {
			const result = await closeUnionBill(params).catch(err => {
				console.log(err)
				this.$message.error(err.message || '销账失败！')
			})
			if (result !== undefined) {
				this.$message.success('销账成功')
				this.pageReset()
				this.queryUnionBillStatistics()
			}
		},
		handleExport() {
			const params = Object.assign(this.filterFormData(this.formData), this.tableDataQuery)
			if (!params.billYear || !params.fileMonth) return
			params.type = +params.type
			this.loading = true
			unionCollectionExportBill(params)
				.then(() => {
					this.$message.success('导出成功！')
				})
				.catch(err => {
					console.log(err)
					this.$message.error('导出失败！')
				})
				.finally(() => {
					this.loading = false
				})
		},
	},
	watch: {
		'tableDataQuery.type'() {
			this.billSum = {
				count: 0,
				amount: '0.00',
			}
			this.tableData = []
			this.selectedBillList = []
			this.queryUnionBillList()
		},
	},
	activated() {
		if (this.$route.query.fileMonth) {
			this.formData.fileMonth = this.$route.query.fileMonth
			this.formData.fileDay = this.$route.query.fileDay || ''
			this.formData.orgCode = this.$route.query.orgCode || ''
			this.handleSearch()
		}
	},
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	.container-title {
		padding: 20px 20px 0 20px;
		display: flex;
		align-items: center;
		border-bottom: 10px solid #eceff8;
	}
}
.flex {
	display: flex;
	flex: 1;
	height: 0;
	.bill-container {
		flex: 1;
		max-width: calc(100% - 450px);
		padding: 0;
		.el-radio-group {
			padding: 0 20px;
		}
	}
}
::v-deep {
	.el-tabs {
		width: 100%;
		box-shadow: none;
		border: none;
		border-radius: 4px 4px 0 0;
		overflow: hidden;
		.el-tabs__content {
			padding: 0;
		}
	}
	.el-tabs--border-card > .el-tabs__header {
		border: none;
		.el-tabs__nav {
			.el-tabs__item {
				border: none;
				font-size: 14px;
				color: #6d7480;
				&.is-active {
					background: #ffffff;
					font-weight: 500;
					color: #2f87fe;
				}
			}
		}
	}
	.el-tabs__item:focus.is-active.is-focus:not(:active) {
		-webkit-box-shadow: none;
		box-shadow: none;
	}
}
.container-search {
	display: flex;
	justify-content: space-between;
	padding: 20px 20px 0 20px;
	::v-deep {
		.el-form-item--small.el-form-item {
			margin-bottom: 15px;
		}
	}
}
</style>
