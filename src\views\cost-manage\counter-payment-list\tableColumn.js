import { getfilterName } from '@/utils'
export function getColumn(_this) {
	return [
		{
			key: 'billNo',
			name: '账单编号',
			tooltip: true,
			minWidth: 230,
		},
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'userName',
			name: '用户名称',
		},
		{
			key: 'addressFullName',
			name: '地址',
			tooltip: true,
		},
		{
			key: 'billDate',
			name: '账期',
			tooltip: true,
		},
		{
			key: 'lastMeterReading',
			name: '上次指针',
			tooltip: true,
		},
		{
			key: 'curMeterReading',
			name: '本次指针',
			tooltip: true,
		},
		{
			key: 'useQty',
			name: '水量',
			tooltip: true,
		},
		{
			key: 'useAmt',
			name: '水费',
			tooltip: true,
		},
		{
			key: 'billItemAmt',
			name: '污水费',
			tooltip: true,
		},
		{
			key: 'billItemAmt2',
			name: '附加费',
			tooltip: true,
		},
		{
			key: 'paidAmount',
			name: '应缴金额',
			tooltip: true,
		},
		{
			key: 'paidAmount1',
			name: '缴费金额',
			tooltip: true,
			render: (h, row) => {
				return h('span', {}, row.paidAmount)
			},
		},
		{
			key: 'payTime',
			name: '缴费时间',
			tooltip: true,
			minWidth: 200,
		},
		{
			key: 'payRecordStatus',
			name: '缴费状态',
			tooltip: true,
			minWidth: 100,
			render: (h, row, total, scope) => {
				const valueStr = getfilterName(
					_this.$store.getters.dataList.payRecordStatus,
					row[scope.column.property],
					'sortValue',
					'sortName',
				)
				return h('span', {}, valueStr)
			},
		},
		{
			fixed: 'right',
			name: '操作',
			minWidth: 120,
			tooltip: true,
			render: (h, row) => {
				// 历史账单标识：0-非历史账单，1-历史账单
				// row.historicalBilling !== 1 判断不为历史账单
				const str =
					row.payRecordStatus == 1 && row.isCancel == 0 && row.historicalBilling !== 1
						? h('div', {}, [
								_this.$has('payment_bill-pay-record_revers') &&
									h(
										'el-button',
										{
											props: {
												type: 'text',
												size: 'medium',
											},
											on: {
												click: () => {
													_this.showAdjustLedger = true
													_this.$refs.adjustLedgerRef.formData.archivesIdentity =
														row.archivesIdentity
													_this.$refs.adjustLedgerRef.formData.billPayRecordId =
														row.billPayRecordId
													_this.$refs.adjustLedgerRef.formData.year = row.paymentYear
												},
											},
										},
										'销账调整',
									),
						  ])
						: ''

				return str
			},
			hide: !_this.$has(['payment_bill-pay-record_revers', 'payment_invoice_open-invoice4']),
		},
	]
}
