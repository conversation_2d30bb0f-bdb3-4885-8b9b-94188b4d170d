<template>
	<div class="detail" :class="{ readonly: !meterInfoEditable }" v-loading.fullscreen.lock="loading">
		<!-- 水表信息 -->
		<WaterMeterInfo :data="allData.meter" @refresh="getInfo" :editable="meterInfoEditable" />
		<!-- 详情tab切换区 -->
		<div class="content">
			<gc-detail-tab
				ref="detailTabRef"
				:tab-list="tabList"
				:default-active-name.sync="defaultActiveName"
				:data="allData"
				:editable="meterInfoEditable"
				@tab-change="handleTabChange"
				@refresh="getInfo"
			></gc-detail-tab>
		</div>
	</div>
</template>

<script>
import WaterMeterInfo from './water-meter-info' // 水表信息
import Summary from './summary' // 概览
import MeterRecord from './meter-record' // 表卡记录
import ReadingRecord from './reading-record' // 抄表记录
import StatusRecord from './status-record' // 状态变更记录
import { getMeterDetail } from '@/api/waterMeter.api'

export default {
	name: 'meterDetail',
	components: {
		WaterMeterInfo,
	},
	data() {
		return {
			loading: false,
			defaultActiveName: 'summary',

			allData: {
				meter: {
					meterNo: '--',
					meterWarehouseCode: '--',
					meterTypeName: '--',
					caliber: '--',
					ranges: '--',
					manufacturerName: '--',
					meterModel: '--',
					meterStatusDesc: '--',
					useTime: '--',
					useYears: '--',
				},
			},
		}
	},
	computed: {
		tabList() {
			let tabs = [
				{
					name: 'summary',
					label: '概览',
					component: Summary,
				},
			]
			if (!this.meterInfoEditable) return tabs
			this.$has('cpm_meter_install-meter-record') &&
				tabs.push({
					name: 'meterRecord',
					label: '表卡记录',
					component: MeterRecord,
				})
			this.$has('cpm_meterReadingTask_archivesId-record-list2') &&
				tabs.push({
					name: 'readingRecord',
					label: '抄表记录',
					component: ReadingRecord,
				})
			this.$has('cpm_archives_meter-modify-records2') &&
				tabs.push({
					name: 'statusRecord',
					label: '状态变更记录',
					component: StatusRecord,
				})
			return tabs
		},
		userOrgCode() {
			return this.$store.getters.userInfo?.orgCode
		},
		meterInfoEditable() {
			const archives = this.allData.archives
			if (!archives) return true
			return archives.orgCode.indexOf(this.userOrgCode) === 0
		},
	},
	activated() {
		this.getInfo()
	},
	mounted() {},
	methods: {
		handleTabChange(data) {
			const ref = this.$refs.detailTabRef.$refs?.componentRef[data.index]
			if (ref.getList) {
				ref.getList(1)
			}
		},

		async getInfo() {
			const meterId = this.$route.query.meterId
			if (!meterId) return
			try {
				const data = await getMeterDetail({
					meterId,
				})
				this.allData = data
				this.$nextTick(() => {
					if (!this.meterInfoEditable) {
						this.$message.warning(`非 ${data.archives.orgName} 营业分公司或管理人员，暂无操作权限`)
					}
				})
			} catch (error) {
				console.error(error)
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.detail {
	width: 100%;
	height: 100%;
	display: flex;
	.content {
		width: calc(100% - 290px);
	}
	&.readonly {
		&::v-deep {
			.el-tabs__active-bar {
				display: none;
			}
		}
	}
}
</style>
