<template>
	<div class="model-header">
		<div class="title-left">
			<img :src="icon" alt="" />
			<p :class="{ grey: isGrey }">{{ title }}</p>
			<slot name="left"></slot>
		</div>
		<div class="title-middle" v-if="$slots.middle">
			<slot name="middle"></slot>
		</div>
		<div class="title-right" v-if="$slots.right">
			<slot name="right"></slot>
		</div>
	</div>
</template>

<script>
export default {
	name: 'GcModelHeader',
	props: {
		icon: String,
		title: String,
		isGrey: <PERSON><PERSON><PERSON>,
	},
}
</script>

<style scoped lang="scss">
.model-header {
	height: 60px;
	display: flex;
	padding: 0 20px;
	align-items: center;
	justify-content: space-between;
	.title-left {
		flex: 1;
		display: flex;
		align-items: center;
		img {
			margin-right: 8px;
			width: 22px;
			height: 22px;
		}
		p {
			@include base-bold;
			&.grey {
				color: #c9c9c9;
			}
		}
	}
	.title-middle {
		flex: 1.5;
		text-align: right;
	}
}
</style>
