<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-10 16:12:11
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-12 10:59:53
-->
<template>
	<div class="right-container">
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			@current-page-change="handlePageChange"
		>
			<template v-slot:deal="{ row }">
				<!-- 已移交、任务状态为已抄表或已关闭 只展示'查看抄表记录'按钮 -->
				<el-button
					v-has="'plan-collection_meterReadingTask_getRecordList'"
					v-if="row.taskStatus === 2 || row.taskStatus === 3"
					type="text"
					size="medium"
					@click="
						handleTableBtnClick('/meterReading/meterReadingRecords', {
							taskYear: row.taskYear,
							bookNo: row.bookNo,
							orgCode: params.orgCode,
						})
					"
				>
					查看抄表记录
				</el-button>
				<div v-else class="inline-btn-box">
					<el-button
						v-if="row.pendingNum > 0"
						v-has="'plan-collection_meterReadingTask_getRecordList2'"
						type="text"
						size="medium"
						@click="
							handleTableBtnClick('/meterReading/meterReadingInput', {
								taskStatus: row.taskStatus,
								bookId: row.bookId,
								bookNo: row.bookNo,
								taskYear: row.taskYear,
								meterReadingTaskId: row.meterReadingTaskId,
								archivesIdentity: params.archivesIdentity,
							})
						"
					>
						抄表录入
					</el-button>
					<el-button
						v-if="row.pendingNum > 0"
						v-has="'plan-collection_meterReadingTask_autoReviewByTask2'"
						type="text"
						size="medium"
						@click="handleSendReview(row.meterReadingTaskId)"
					>
						送内复
					</el-button>
				</div>
				<!-- 非关闭状态 展示'关闭任务'按钮 -->
				<el-button
					v-has="'plan-collection_meterReadingTask_closeTask2'"
					v-if="row.taskStatus !== 3"
					type="text"
					size="medium"
					@click="handleCloseTask(row.meterReadingTaskId)"
				>
					关闭任务
				</el-button>
			</template>
		</GcTable>
	</div>
</template>

<script>
import { getTaskList2, autoReviewByTask2, closeTask2 } from '@/api/meterReading.api.js'

export default {
	name: '',
	components: {},
	props: {
		params: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			loading: false,
			columns: [
				{
					key: 'bookNo',
					name: '表册编号',
					tooltip: true,
				},
				{
					key: 'bookTypeDesc',
					name: '册本类型',
					tooltip: true,
				},
				{
					key: 'staffName',
					name: '抄表员',
					tooltip: true,
				},
				{
					key: 'handOverDesc',
					name: '是否全部移交',
					tooltip: true,
					width: 110,
				},
				{
					key: 'taskYear',
					name: '抄表年份',
					width: 100,
				},
				{
					key: 'taskMonth',
					name: '抄表月份',
					width: 100,
				},
				{
					key: 'taskStatusDesc',
					name: '任务状态',
					tooltip: true,
					width: 100,
				},
				{
					key: 'pendingNum',
					name: '待录入数',
					width: 100,
				},
				{
					key: 'auditedNum',
					name: '待审核数',
					width: 100,
				},
				{
					hide: !this.$has([
						'plan-collection_meterReadingTask_getRecordList',
						'plan-collection_meterReadingTask_getRecordList2',
						'plan-collection_meterReadingTask_autoReviewByTask2',
						'plan-collection_meterReadingTask_closeTask2',
					]),
					key: 'deal',
					name: '操作',
					fixed: 'right',
					minWidth: 200,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	created() {
		// this.getList();
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		async getList(curPage) {
			this.loading = true

			if (curPage) {
				this.pageData.current = curPage
			}

			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getTaskList2({
					current,
					size,
					...this.params,
				})
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleTableBtnClick(path, query) {
			if (path) {
				this.$router.push({
					path,
					query,
				})
			}
		},
		// 送内复
		async handleSendReview(meterReadingTaskId) {
			this.$confirm('确定送内复吗?').then(async () => {
				await autoReviewByTask2({
					meterReadingTaskId,
				})
				this.$message.success('送内复成功')
				this.getList(1)
			})
		},
		// 关闭任务
		async handleCloseTask(taskId) {
			this.$confirm('任务关闭后，将无法继续抄表录入，请确认是否继续关闭？', '关闭抄表任务').then(async () => {
				await closeTask2({ taskId })
				this.$message.success('关闭成功')
				this.getList(1)
			})
		},
		resetTableData() {
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.right-container {
	width: 0;
	flex: 1;
	padding: 20px;
	background-color: #fff;
}
.inline-btn-box {
	display: inline-flex;
	margin-right: 10px;
}
</style>
