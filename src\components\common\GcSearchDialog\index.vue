<template>
	<el-dialog
		:custom-class="isGlobal ? 'gc-global-search' : ''"
		:top="top"
		:title="title"
		:visible.sync="innerVisible"
		width="647px"
		class="search-dialog"
		:class="{ 'show-list': innerMenuList.length }"
		:append-to-body="appendToBody"
		:close-on-click-modal="isGlobal ? true : false"
		:show-close="isGlobal ? false : true"
		@close="publicSearch('close')"
	>
		<div v-if="isGlobal" slot="title" class="box-tab">
			<div
				class="tab-item"
				v-for="(item, index) in searchKeys"
				:key="index"
				:class="[item.key == activeTab ? 'active' : '']"
				@click="changeActiveCondition(item.key)"
			>
				{{ item.label }}
			</div>
		</div>
		<div class="content">
			<div class="tab-switch" v-if="!isGlobal && searchCondition.length > 1">
				<div
					class="tab-item"
					v-for="(item, index) in searchCondition"
					:key="index"
					:class="[item.key == activeTab ? 'active' : '']"
					@click="changeActiveCondition(item.key)"
				>
					{{ item.label }}
				</div>
			</div>
			<el-form ref="searchInputForm" :model="searchInputForm" @submit.native.prevent>
				<div class="content-operation" v-if="activeTab != 'addressName'">
					<el-form-item prop="input" :rules="formRules">
						<el-input
							v-model="searchInputForm.input"
							:placeholder="activePlaceholder"
							@keyup.enter.native="publicSearch"
						>
							<i slot="suffix" @click="publicSearch">搜索</i>
							<i slot="prefix" class="el-input__icon el-icon-search"></i>
						</el-input>
					</el-form-item>
				</div>
				<div class="content-operation" v-else>
					<div class="address-special">
						<div class="select">
							<el-select
								v-model="district"
								filterable
								placeholder="区/县"
								@change="getStreetList('street', district)"
							>
								<el-option
									v-for="item in districtList"
									:key="item.regionCode"
									:label="item.regionName"
									:value="item.regionCode"
								></el-option>
							</el-select>
							<el-select v-model="street" filterable placeholder="请输入街道/小区/乡镇/村庄">
								<el-option
									v-for="item in streetList"
									:key="item.addressAreaCode"
									:label="item.streetArea"
									:value="item.addressAreaCode"
								></el-option>
							</el-select>
						</div>
						<el-form-item prop="addressDetail" :rules="formRules">
							<el-input
								v-model="searchInputForm.addressDetail"
								placeholder="请输入详细地址"
								@keyup.enter.native="publicSearch"
							>
								<i slot="suffix" @click="publicSearch">搜索</i>
							</el-input>
						</el-form-item>
					</div>
				</div>
			</el-form>
			<div class="filter-condition" v-if="filterConditionVisible">
				<div class="all">
					<span>筛选条件</span>
					<el-button type="text" @click="emptyCondition" :disabled="filterConditionDisabled">清空</el-button>
				</div>
				<div class="collection-one">
					<div v-if="selectedCondition.length > 0">
						<span v-for="item in selectedCondition" :key="item.key">
							{{ item.value }}
							<i v-show="!item.hideClear" @click="deleteOne(item.key)">×</i>
						</span>
					</div>
					<div v-else class="collection-empty">暂无筛选条件</div>
				</div>
			</div>
			<div class="archives-menu" v-if="innerMenuVisible" v-loading="innerloading">
				<div class="menu-list" v-if="innerMenuList.length && isGlobal">
					<div class="menu-item" v-for="(item, index) in innerMenuList" :key="index">
						<archive-temp-card
							:curTab="activeTab"
							:item="item"
							@close="publicSearch('close')"
						></archive-temp-card>
					</div>
				</div>
				<div class="menu-list" id="menuListBox" v-if="innerMenuList.length && !isGlobal">
					<div
						class="menu-item pointer"
						v-for="(item, index) in innerMenuList"
						:key="index"
						@click="goArchives(item.archivesId)"
					>
						<div
							v-for="(el, i) in innerMenuLabel"
							:key="i"
							:class="[el == activeTab ? 'archives-search' : 'detail-item']"
						>
							<div v-if="el == activeTab">{{ item[el] }}</div>
							<div v-else>
								<span>{{ menuLabel[el] }}：</span>
								<!--档案状态- 0：已建档 1：在用 2：已停用 3：已销档 4：待完善 -->
								<span
									:class="{
										status0: item[el] === 0 || item[el] == 1 || item[el] == 2,
										status1: item[el] == 3 || item[el] == 4,
									}"
								>
									{{
										el != 'archivesStatus'
											? item[el] || '--'
											: nameConversion(item[el], archiveState)
									}}
								</span>
							</div>
						</div>
					</div>
				</div>
				<div class="page-turn" v-if="innerMenuList.length > 0">
					<gc-pagination
						:total="page.total"
						:page-size="page.size"
						:current-page="page.current"
						:show-page-sizes="false"
						@current-page-change="pageChange"
						isRightAlign
					></gc-pagination>
				</div>
				<div class="menu-empty" v-if="!innerloading && innerMenuList.length == 0">
					<span>搜索无结果！</span>
				</div>
			</div>
		</div>
	</el-dialog>
</template>

<script>
import addressMix from '@/components/common/GcAddress/index.js' //区县/小区
import { searchKeys } from '@/consts/search'
import ArchiveTempCard from './ArchiveTempCard'
import { nameConversion, joinAddress } from '@/utils/index.js'

export default {
	name: 'GcSearchDialog',
	mixins: [addressMix],
	components: { ArchiveTempCard },
	props: {
		// 弹窗的显示/关闭
		dialogVisible: {
			type: Boolean,
			required: true,
			default: false,
		},
		//弹窗名称
		title: {
			type: String,
			required: true,
			default: '搜索',
		},
		// 弹窗内的搜索条件
		searchCondition: {
			type: Array,
			required: true,
			default: () => [],
		},
		// 弹窗当前所处的tab
		activeCondition: {
			type: String,
			required: true,
			default: () => '',
		},
		// 弹窗外选中的条件
		selectedCondition: {
			type: Array,
			default: () => [],
		},
		// 筛选条件是否显示
		filterConditionVisible: {
			type: Boolean,
			default: true,
		},
		// 是否在搜索弹窗内展示菜单项
		menuVisible: {
			type: Boolean,
			default: false,
		},
		// 菜单项
		menuList: {
			type: Array,
			default: () => [],
		},
		// 当需要显示菜单项的时候，传值页数信息
		page: {
			type: Object,
			default: () => {
				return {}
			},
		},
		// 是否是全局搜索
		isGlobal: {
			type: Boolean,
			default: false,
		},
		// 传值
		valueObj: {
			type: Object,
			default: () => {
				return {}
			},
		},
		appendToBody: {
			type: Boolean,
			default: false,
		},
		loading: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			menuLabel: {
				archivesNo: '档案编号',
				userName: '用户名称',
				archivesStatus: '档案状态',
				userMobile: '手机号码',
				meterNo: '表具编号',
				addressName: '表具地址',
			}, //菜单项的label显示问题
			searchKeys,
			searchInputForm: {
				input: '', //输入框内容
				addressDetail: '', //详细地址
			},
		}
	},
	computed: {
		formRules() {
			const current = this.searchCondition.find(item => item.key === this.activeCondition)
			return current?.rule || {}
		},
		// 控制弹窗的显示/隐藏
		innerVisible: {
			get: function () {
				return this.dialogVisible
			},
			set: function (val) {
				this.$emit('update:dialogVisible', val)
			},
		},
		// placeholder的值
		activePlaceholder() {
			let str = ''
			if (this.searchCondition) {
				this.searchCondition.map(item => {
					if (item.key == this.activeTab) {
						str += '请输入' + item.label
					}
				})
			}
			return str
		},
		// 整理菜单项label的顺序，菜单的显示
		innerMenuLabel() {
			let middleArr = []
			this.menuList.map((item, index) => {
				if (index == 0) {
					middleArr.push(this.activeTab)
					for (let key in item) {
						if (
							key != this.activeTab &&
							key != 'archivesId' &&
							key != 'archivesIdentity' &&
							key != 'certificateNo'
						) {
							middleArr.push(key)
						}
					}
				}
			})
			return middleArr
		},
		innerMenuList: {
			get: function () {
				if (!this.isGlobal) return this.menuList
				let result = []
				this.menuList &&
					this.menuList.map(it => {
						const { address = {}, archives = {}, meter = {}, user = {} } = it
						result.push({
							archivesNo: archives.archivesNo,
							userName: user?.userName,
							addressName: joinAddress(address),
							meterNo: meter?.meterNo,
							userMobile: user?.userMobile,
							archivesIdentity: archives.archivesIdentity,
							archivesStatus: archives.archivesStatus,
							archivesId: archives.archivesId,
							certificateNo: user?.certificateNo,
						})
					})
				return result
			},
			set: function (val) {
				this.$emit('update:menu-list', val)
			},
		},
		activeTab: {
			get: function () {
				return this.activeCondition
			},
			set: function (val) {
				this.$emit('update:active-condition', val)
			},
		},
		top() {
			if (this.isGlobal) return '0'
			return '120px'
		},
		innerMenuVisible: {
			get: function () {
				return this.menuVisible
			},
			set: function (val) {
				this.$emit('update:menuVisible', val)
			},
		},
		innerloading: {
			get: function () {
				return this.loading
			},
			set: function (val) {
				this.$emit('update:loading', val)
			},
		},
		archiveState() {
			return this.$store.getters.dataList.archiveState || []
		},
		// 筛选条件清空按钮是否置灰
		filterConditionDisabled() {
			const arr = this.selectedCondition.filter(item => !item.hideClear)
			return !arr.length
		},
	},
	watch: {
		activeCondition(newVal) {
			this.activeTab = newVal
		},
		activeTab() {
			// 当切换到不同的tab时候，清空数据
			this.resetfn()
			this.innerMenuList = []
			this.innerMenuVisible = false
		},
		valueObj(newVal) {
			// 清空数据
			if (Object.keys(newVal).length == 0) {
				this.resetfn()
			}
		},
		menuList(newVal) {
			if (newVal) {
				this.$nextTick(() => {
					const menuListBox = document.querySelector('#menuListBox')
					if (!menuListBox) return
					// 累加top、header、tab、search、padding、pagination等高度
					let height = 120 * 2 + 48 + 52 * 2 + 20 * 2 + 50
					if (this.activeCondition === 'addressName') height += 62
					menuListBox.style.cssText = `max-height: calc(100vh - ${height}px); min-height: 95px; overflow: auto;`
				})
			}
		},
	},
	methods: {
		nameConversion,
		resetfn() {
			this.searchInputForm.input = ''
			this.district = ''
			this.street = ''
			this.searchInputForm.addressDetail = ''
			this.innerMenuVisible = false
		},
		// 关闭弹窗
		handleClose() {
			this.innerVisible = false
		},
		// 切换所在tab
		changeActiveCondition(val) {
			this.activeTab = val
		},
		// 清空
		emptyCondition() {
			this.$emit('empty')
		},
		// 删除单个的筛选条件
		deleteOne(val) {
			this.$emit('delete-one', val)
		},
		//搜索
		publicSearch(flag, page = 1) {
			if (flag !== 'page' && flag !== 'close') {
				let error = false
				this.$refs.searchInputForm.validate(valid => {
					error = !valid
				})
				if (error) {
					this.$message.error('请修正错误')
					return
				}
			}
			let val = null
			let currentLabel = null
			this.searchCondition.map(item => {
				if (item.key == this.activeTab) {
					currentLabel = item.label
				}
			})
			if (this.activeTab != 'addressName') {
				val = {
					key: this.activeTab,
					label: currentLabel,
					value: this.searchInputForm.input,
				}
			} else {
				val = []
				val.push({
					key: 'regionCode',
					label: '区/县',
					value: this.district,
					desc: this.districtList.filter(item => item.regionCode === this.district)[0]?.regionName || '',
				})
				val.push({
					key: 'addressAreaCode',
					label: '街道/小区',
					value: this.street,
					desc: this.streetList.filter(item => item.addressAreaCode === this.street)[0]?.streetArea || '',
				})
				val.push({
					key: 'addressName',
					label: '详细地址',
					value: this.searchInputForm.addressDetail,
					desc: this.searchInputForm.addressDetail,
				})
			}
			if (flag == 'page') {
				this.$emit('public-search', { ...val, page })
			} else if (flag == 'close') {
				// this.resetfn();
				this.$emit('dialog-close', val)
				this.innerVisible = false
			} else {
				this.$emit('public-search', val)
				this.innerVisible = false
			}
		},
		pageChange({ page }) {
			this.publicSearch('page', page)
		},
		// 上一页
		goPrevious() {
			this.$emit('go-up')
		},
		// 下一页
		goNext() {
			this.$emit('go-down')
		},
		goArchives(val) {
			this.$emit('go-archives', val)
			this.innerVisible = false
		},
	},
}
</script>
<style lang="scss" scoped>
@import './index.scss';
</style>
