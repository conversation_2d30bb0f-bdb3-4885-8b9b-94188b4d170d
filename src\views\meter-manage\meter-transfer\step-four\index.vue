<template>
	<div class="step-four-wrapper">
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:taxpayerIdentity>
				<el-autocomplete
					style="width: 100%"
					v-model="formData.taxpayerIdentity"
					:fetch-suggestions="queryTaxpayerIdentity"
					placeholder="请输入"
					@select="handleTaxpayerIdentitySelect"
					@change="handleTaxpayerIdentityChange"
				>
					<template slot-scope="{ item }">
						<div class="billing-information-item">
							<p class="billing-information-item-row">纳税人识别号：{{ item.taxpayerIdentity }}</p>
							<p class="billing-information-item-row">开票抬头：{{ item.userName }}</p>
						</div>
					</template>
				</el-autocomplete>
			</template>
		</GcFormRow>
	</div>
</template>

<script>
import _ from 'lodash'
import getFormItems from './form.js'
import { RULE_INTEGERONLY, ruleMaxLength, RULE_INT_ENGLISH } from '@/utils/rules'
import { apiQueryInvoiceBuyer } from '@/api/userManage.api'
export default {
	props: {
		userType: {
			type: Number,
			default: 3,
		},
	},
	data() {
		return {
			formData: {
				invoiceType: '',
				taxpayerIdentity: '',
				openBank: '',
				bankAccount: '',
				buyerName: '',
			},
			formAttrs: {
				labelWidth: '115px',
				labelPosition: 'right',
				rules: {
					openBank: [ruleMaxLength(32)],
					taxpayerIdentity: [RULE_INT_ENGLISH, ruleMaxLength(20)],
					bankAccount: [RULE_INTEGERONLY, ruleMaxLength(32)],
					buyerName: [ruleMaxLength(32)],
				},
			},
			billingInfoDisabled: false,
		}
	},
	created() {
		this.queryTaxpayerIdentity = _.debounce(this.queryTaxpayerIdentity, 250)
	},
	computed: {
		formItems() {
			return getFormItems(this)[this.userType]
		},
	},
	watch: {
		formData: {
			handler: 'validateForm',
			deep: true,
		},
	},
	methods: {
		assignForm(obj) {
			this.formData = Object.assign(this.formData, obj)
		},
		handleReset() {
			this.$refs.formRef.resetFields()
		},
		async validateForm() {
			const valid = await this.$refs.formRef.validate()
			return valid
		},
		async queryTaxpayerIdentity(taxpayerIdentity, callback) {
			taxpayerIdentity = taxpayerIdentity || ''
			try {
				let result = await apiQueryInvoiceBuyer({
					taxpayerIdentity,
				})
				result = result || []
				const list = result.map(item => {
					const { taxpayerIdentity } = item
					return { ...item, value: taxpayerIdentity }
				})

				callback(list)
			} catch (e) {
				console.log(e)
				callback([])
			}
		},
		handleTaxpayerIdentitySelect(item) {
			const { userName, openBank, bankAccount } = item
			this.formData.openBank = openBank
			this.formData.buyerName = userName
			this.formData.bankAccount = bankAccount
			this.billingInfoDisabled = true
		},
		handleTaxpayerIdentityChange() {
			this.billingInfoDisabled = false
		},
	},
}
</script>

<style lang="scss" scoped>
.step-four-wrapper {
	flex: 1;
	width: 800px;
	::v-deep {
		.el-row {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		}
	}
}
</style>
