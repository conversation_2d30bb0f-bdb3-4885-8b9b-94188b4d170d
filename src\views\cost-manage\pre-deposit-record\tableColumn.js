export function getColumn(instance) {
	const costOperationType = instance.$store.getters.dataList.costOperationType || []
	const payChannel = instance.$store.getters.dataList.payChannel || []
	let columns = []
	const baseColumns = [
		{
			key: 'costOperationType',
			name: '操作方式',
			tooltip: true,
			render: (h, row) => {
				return h(
					'span',
					(costOperationType.find(item => item.sortValue == row.costOperationType) || {}).sortName || '--',
				)
			},
		},
		{
			key: 'payTime',
			name: '操作时间',
			tooltip: true,
			width: '180px',
		},
		{
			key: 'payChannel',
			name: '渠道',
			tooltip: true,
			render: (h, row) => {
				return h('span', (payChannel.find(item => item.sortValue == row.payChannel) || {}).sortName || '--')
			},
		},
		{
			key: 'paySerialNo',
			name: '交易流水号',
			tooltip: true,
			width: '180px',
		},
		{
			key: 'previouusBalance',
			name: '交易前余额',
			tooltip: true,
			align: 'right',
			render: function (h, row) {
				const previouusBalance = row.previouusBalance - 0
				return h('span', {}, previouusBalance.toFixed(2))
			},
		},
		{
			key: 'paidAmount',
			name: '交易金额',
			tooltip: true,
			align: 'right',
			render: function (h, row) {
				const paidAmount = row.paidAmount - 0
				return h('span', {}, paidAmount.toFixed(2))
			},
		},
		{
			key: 'billAmount',
			name: '账单总金额',
			tooltip: true,
			align: 'right',
			render: function (h, row) {
				const billAmount = row.billAmount - 0
				return h('span', {}, billAmount.toFixed(2))
			},
		},
		{
			key: 'balance',
			name: '交易后余额',
			tooltip: true,
			align: 'right',
			render: function (h, row) {
				const balance = row.balance - 0
				return h('span', {}, balance.toFixed(2))
			},
		},
	]
	if (instance.activeTab === 'resident') {
		columns = baseColumns.concat([
			{
				key: 'userName',
				name: '用户名称',
				tooltip: true,
			},
			{
				key: 'addressFullName',
				name: '地址',
				tooltip: true,
				width: '300px',
			},
		])
		columns.splice(3, 0, {
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
		})
	} else {
		columns = baseColumns.concat([
			{
				key: 'userName',
				name: '企业名称',
				tooltip: true,
			},
			{
				key: 'companyAddress',
				name: '企业地址',
				tooltip: true,
				width: '300px',
			},
		])
		columns.splice(3, 0, {
			key: 'enterpriseNumber',
			name: '企业编号',
			tooltip: true,
		})
	}
	columns = columns.concat([
		{
			key: 'operate',
			name: '操作',
			fixed: 'right',
		},
	])
	return columns
}
