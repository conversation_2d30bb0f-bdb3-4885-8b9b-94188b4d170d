<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />

			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			/>
		</div>
	</div>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getColumn } from './tableColumn'
import { getFormItems } from './formItem.js'
import {
	apiGetRegion,
	apiGetAddressAreaMap,
	apiQueryRecordPage,
	apiQueryStreetRecordPage,
	apiQueryAddressRecordPage,
	apiQueryAreaStaffList,
	apiQueryRegionRecordPage,
} from '@/api/addressManage.api.js'
export default {
	data() {
		return {
			// 左侧查询
			formData: {
				orgCode: '',
				archivesIdentityNew: '',
				archivesIdentityOld: '',
				cancelTime: '',
			},
			formItems: [],
			formAttrs: {
				rules: {
					orgCode: {
						required: true,
						message: '请选择营业分公司',
						trigger: 'change',
					},
				},
			},
			// 右侧列表
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	computed: {
		columns() {
			const { type } = this.$route.query
			if (type === 'community') {
				return getColumn(this).filter(item =>
					[
						'oldAddressAreaName',
						'newAddressAreaName',
						'operationTypeDesc',
						'createStaffName',
						'createTime',
					].includes(item.key),
				)
			} else if (type === 'street') {
				return getColumn(this).filter(item =>
					[
						'oldAddressAreaName',
						'newAddressAreaName',
						'operationTypeDesc',
						'createStaffName',
						'createTime',
					].includes(item.key),
				)
			} else if (type === 'region') {
				return getColumn(this).filter(item =>
					['oldRegionName', 'newRegionName', 'operationTypeDesc', 'createStaffName', 'createTime'].includes(
						item.key,
					),
				)
			} else {
				return getColumn(this).filter(item =>
					['oldAddressName', 'newAddressName', 'operationTypeDesc', 'createStaffName', 'createTime'].includes(
						item.key,
					),
				)
			}
		},
	},
	activated() {
		this.initFormData()
	},
	beforeRouteEnter(to, from, next) {
		const { type } = to.query
		if (type) {
			const enums = {
				community: '小区操作记录',
				street: '街道/乡镇操作记录',
				region: '区县操作记录',
				address: '地址操作记录',
			}
			to.meta.title = enums[type]
		}
		next()
	},
	methods: {
		// 获取区县数据
		async _getRegionData() {
			const { records } = await apiGetRegion({
				regionCode: 2102,
			})
			const regionObj = this.formItems.find(item => item.prop === 'regionCode')
			if (!regionObj) return
			regionObj.options = records.map(item => {
				return {
					value: item.regionCode,
					label: item.regionName,
				}
			})
		},
		// 获取街道、小区  key:streetCode, neighbourhoodCode
		async _getAddressAreaMap(value, key) {
			const data = await apiGetAddressAreaMap({
				parentCode: value,
			})
			const obj = this.formItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = data.map(item => {
				return {
					value: item.addressAreaCode,
					label: item.addressAreaName,
				}
			})
		},
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const params = trimParams(removeNullParams(this.formData))
				Object.assign(params, {
					current,
					size,
				})
				const { type } = this.$route.query
				const apiMethods = {
					community: params => apiQueryRecordPage({ ...params, level: 6 }),
					street: params => apiQueryStreetRecordPage({ ...params, level: 4 }),
					region: params => apiQueryRegionRecordPage({ ...params, level: 3 }),
					default: params => apiQueryAddressRecordPage(params),
				}
				const apiMethod = apiMethods[type] || apiMethods.default
				const { total = 0, records = [] } = await apiMethod(params)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				this.tableData = [{}]
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		async _apiQueryAreaStaffList() {
			const data = await apiQueryAreaStaffList()
			const obj = this.formItems.find(item => item.prop === 'createStaffId')
			if (!obj) return
			obj.options = data.map(item => {
				return {
					value: item.staffId,
					label: item.staffName,
				}
			})
		},
		initFormData() {
			const { type } = this.$route.query
			if (type === 'community') {
				this.formItems = getFormItems(this).filter(item =>
					['newAddressAreaName', 'createStaffId'].includes(item.prop),
				)
			} else if (type === 'street') {
				this.formItems = getFormItems(this).filter(item =>
					['newAddressAreaName', 'createStaffId'].includes(item.prop),
				)
			} else if (type === 'region') {
				this.formItems = getFormItems(this).filter(item =>
					['newRegionName', 'createStaffId'].includes(item.prop),
				)
			} else {
				this.formItems = getFormItems(this).filter(item =>
					['regionCode', 'streetCode', 'neighbourhoodCode', 'createStaffId'].includes(item.prop),
				)
				this._getRegionData()
			}
			this._apiQueryAreaStaffList()
			this.formData = {}
			this.formItems.forEach(item => {
				this.$set(this.formData, item.prop, '')
			})
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleReset() {
			this.initFormData()
			this.handleSearch()
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.handlePageChange({ page: 1 })
		},
		filterFormData(data) {
			const filteredData = {}
			for (const key in data) {
				if (data[key] !== '' && data[key] !== null) {
					filteredData[key] = data[key]
				}
			}
			return filteredData
		},
	},
	mounted() {
		this.handleSearch()
	},
}
</script>
