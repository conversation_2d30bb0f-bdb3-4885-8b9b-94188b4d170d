<template>
	<div class="chart-wrapper">
		<div
			class="title-wrapper"
			:style="{
				background: `linear-gradient(92.14deg, rgba(151, 203, 255, 0.4) -1.61%, rgba(191, 219, 255, 0.35) 96.3%),url(${cardDetail.bg}) no-repeat right bottom`,
			}"
		>
			<div class="title-top">
				<div class="top-name">{{ cardDetail.name }}</div>
				<el-menu
					:default-active="cardDetail.activeTab"
					class="el-menu-custom"
					mode="horizontal"
					@select="handleSelect"
				>
					<el-menu-item
						v-for="(tabItem, tabIndex) in cardDetail.tabList"
						:index="tabItem.value"
						:key="tabIndex"
					>
						{{ tabItem.name }}
					</el-menu-item>
				</el-menu>
			</div>
			<div class="title-content">
				<div v-for="(subItem, subIndex) in cardDetail.titleList" :key="subIndex">
					<div class="title-name">{{ subItem.label }}</div>
					<div class="title-value">
						{{ isBlank(subItem.value) ? '--' : subItem.value }}
						<span v-if="!isBlank(subItem.value)">{{ subItem.unit }}</span>
					</div>
				</div>
			</div>
		</div>
		<div class="chart-box">
			<slot />
		</div>
	</div>
</template>

<script>
import { isBlank } from '@/utils/validate.js'
export default {
	name: 'CardContainer',

	props: {
		cardDetail: Object,
	},
	data() {
		return {}
	},

	methods: {
		handleSelect(v) {
			this.cardDetail.activeTab = v
		},
		isBlank(v) {
			return isBlank(v)
		},
	},
}
</script>

<style lang="scss" scoped>
.user-water {
	height: 100%;
	display: flex;
	flex-direction: column;
}
.top {
	display: flex;
	justify-content: space-between;
}
.bottom {
	flex: 1;
}
</style>
