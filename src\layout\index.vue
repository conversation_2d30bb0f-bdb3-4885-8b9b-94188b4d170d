<template>
	<div class="layout-wrapper" :class="classObj">
		<component :is="'layout-' + theme.layout" :collapse="collapse" :device="device" />
		<gc-theme-drawer />
	</div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'

export default {
	name: 'Layouts',
	data() {
		return {
			isMobile: false,
			oldLayout: '',
		}
	},
	computed: {
		...mapGetters({
			device: 'settings/device',
			collapse: 'settings/collapse',
			theme: 'settings/theme',
		}),
		classObj() {
			return {
				mobile: this.device === 'mobile',
			}
		},
	},
	beforeMount() {
		this.oldLayout = this.theme.layout
		// 移除resize 判断
		// window.addEventListener('resize', this.handleLayouts)
		// this.handleLayouts()
	},
	methods: {
		...mapActions({
			toggleDevice: 'settings/toggleDevice',
			foldSideBar: 'settings/foldSideBar',
			openSideBar: 'settings/openSideBar',
		}),
		handleLayouts() {
			const isMobile = document.body.getBoundingClientRect().width - 1 < 992
			if (this.isMobile !== isMobile) {
				if (isMobile) {
					this.oldLayout = this.theme.layout
					this.foldSideBar()
				} else {
					this.openSideBar()
				}
				this.toggleDevice(isMobile ? 'mobile' : 'desktop')
				this.theme.layout = isMobile ? 'vertical' : this.oldLayout
				this.isMobile = isMobile
			}
		},
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.handleLayouts)
		if (this.isMobile) this.theme.layout = this.oldLayout
	},
}
</script>

<style lang="scss" scoped>
.layout-wrapper {
	position: relative;
	width: 100%;
	height: 100%;

	[class*='gc-layout-'] {
		position: relative;

		::v-deep {
			.gc-layout-header {
				box-shadow: $base-box-shadow;
			}
		}

		&.fixed {
			padding-top: $base-nav-height + $base-tabs-height;
		}

		&.gc-layout-horizontal.fixed {
			padding-top: $base-top-bar-height + $base-tabs-height;
		}
	}

	::v-deep {
		.fixed-header {
			position: fixed;
			top: 0;
			right: 0;
			left: 0;
			z-index: $base-z-index - 1;
			width: 100%;
		}

		.gc-main {
			position: relative;
			width: auto;
			min-height: 100%;
			margin-left: $base-left-menu-width + 4;

			&.is-collapse-main {
				margin-left: $base-left-menu-width-min;

				.fixed-header {
					left: $base-left-menu-width-min;
					width: calc(100% - #{$base-left-menu-width-min});
				}
			}
		}
	}

	/* 手机端开始 */
	&.mobile {
		::v-deep {
			.gc-layout-vertical {
				.el-scrollbar.gc-side-bar.is-collapse {
					width: 0;
				}

				.gc-main {
					.fixed-header {
						left: 0;
						width: 100%;
					}

					width: 100%;
					margin-left: 0;
				}
			}
		}
	}

	/* 手机端结束 */
}
</style>
