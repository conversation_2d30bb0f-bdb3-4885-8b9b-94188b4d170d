import Layout from '@/layout'

export default [
	{
		path: '/statisticsManage',
		name: 'StatisticsManage',
		component: Layout,
		redirect: '/statisticsManage/reportConfig',
		meta: {
			title: '统计',
			icon: 'icon-cis_yj_tongji',
			permissions: [
				'cpm_report_config_queryPage',
				'cpm_report_config_add',
				'cpm_report_config_update',
				'cpm_report_config_delete',
				'cpm_report_category_add',
				'cpm_report_category_update',
				'cpm_report_category_delete',
				'cpm_report_config_permissionList',
				'cpm_report_category_queryCategoryReportList',
				'cpm_report_role_add',
				'cpm_report_role_delete',
				'cpm_report_role_update',
				'cpm_report_roleResource_savePermission',
				'cpm_report_roleUserRef_add',
				'cpm_report_roleUserRef_update',
				'cpm_report_roleUserRef_delete',
			],
		},
		children: [
			{
				path: 'reportConfig',
				name: 'ReportConfig',
				component: () => import('@/views/statistics-manage/report-config/index.vue'),
				meta: {
					title: '报表配置',
					keepAlive: true,
					icon: 'icon-cis_ej_baobiaopeizhi',
					permissions: [
						'cpm_report_config_queryPage',
						'cpm_report_config_add',
						'cpm_report_config_update',
						'cpm_report_config_delete',
						'cpm_report_category_add',
						'cpm_report_category_update',
						'cpm_report_category_delete',
					],
				},
			},
			{
				path: 'reportShow',
				name: 'ReportShow',
				component: () => import('@/views/statistics-manage/report-show/index.vue'),
				meta: {
					title: '报表展示',
					keepAlive: true,
					icon: 'icon-cis_ej_baobiaozhanshi',
					permissions: ['cpm_report_config_permissionList'],
				},
			},
			{
				path: 'reportPermission',
				name: 'ReportPermission',
				component: () => import('@/views/statistics-manage/report-permission/index.vue'),
				meta: {
					title: '报表权限',
					keepAlive: true,
					icon: 'icon-cis_ej_baobiaoquanxian',
					permissions: [
						'cpm_report_category_queryCategoryReportList',
						'cpm_report_role_add',
						'cpm_report_role_delete',
						'cpm_report_role_update',
						'cpm_report_roleResource_savePermission',
						'cpm_report_roleUserRef_add',
						'cpm_report_roleUserRef_delete',
					],
				},
			},
			{
				path: 'combinationQuery',
				name: 'combinationQuery',
				component: () => import('@/views/statistics-manage/combination-query/index.vue'),
				meta: {
					title: '组合查询',
					keepAlive: true,
					icon: 'icon-cis_ej_baobiaozhanshi',
					permissions: [
						'cpm_report_unionTemplate_queryUnionTemplate',
						'cpm_report_unionTemplate_add',
						'cpm_report_unionTemplate_update',
						'cpm_report_unionTemplate_delete',
						'cpm_report_unionTemplate_updateName',
						'cpm_report_unionTemplate_export_union-data-record-excel',
						'cpm_report_unionTemplate_queryUnionTemplateData',
					],
				},
			},
		],
	},
]
