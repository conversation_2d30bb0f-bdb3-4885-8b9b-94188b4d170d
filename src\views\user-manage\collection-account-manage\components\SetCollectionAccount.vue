<template>
	<GcElDialog
		:show="isShow"
		:title="title"
		width="800px"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:collectionAgreementNumber>
				<el-input
					v-show="editType === 'add'"
					v-model="formData.collectionAgreementNumber"
					class="account-more"
					readonly
				>
					<img slot="append" src="@/assets/images/icon/get-num.svg" @click="_apiGetAgreementNumber" />
				</el-input>
				<el-input v-show="editType === 'edit'" v-model="formData.collectionAgreementNumber"></el-input>
			</template>
		</GcFormRow>
	</GcElDialog>
</template>

<script>
import {
	ruleRequired,
	ruleMaxLength,
	RULE_INTEGERONLY,
	RULE_INCORRECTEMAIL,
	RULE_PHONE,
	RULE_POSTALCODE,
} from '@/utils/rules'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { isBlank } from '@/utils/validate.js'
import {
	apiQueryCollectionBankList,
	apiQueryConsignBankCodeList,
	apiCreateCollectionAccount,
	apiSetCollectionAccount,
	apiGetAgreementNum,
} from '@/api/userManage.api'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		data: {
			type: Object,
			default: () => ({}),
		},
		editType: {
			type: String,
			default: 'add',
		},
	},
	watch: {
		show(val) {
			if (val) {
				this.getBankList()
			}
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		title() {
			return this.editType === 'add' ? '新增托收账户' : '编辑托收账户'
		},
	},
	data() {
		return {
			formData: {
				orgCode: '',
				collectionAgreementNumber: '',
				bankCode: '',
				bankNo: '',
				bankBranchCode: '',
				depositBank: '',
				bankAcctName: '',
				bankAcctCode: '',
				bankAddres: '',
				contactPeople: '',
				contactPhone: '',
				email: '',
				mailingAddress: '',
				remarks: '',
				zipCode: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
				},
				{
					type: 'slot',
					label: '托收协议号',
					prop: 'collectionAgreementNumber',
					slotName: 'collectionAgreementNumber',
				},
				{
					type: 'el-select',
					label: '银行名称',
					prop: 'bankCode',
					options: [],
					events: {
						change: value => {
							this.formData.bankNo = ''
							this.formData.bankBranchCode = ''
							this.formItems[3].options = []
							if (value) {
								this.getConsignBankList(value)
							}
						},
					},
				},
				{
					type: 'el-select',
					label: '银行支行行名',
					prop: 'bankNo',
					options: [],
					events: {
						change: value => {
							this.formData.bankBranchCode = value
						},
					},
				},
				{
					type: 'el-input',
					label: '银行支行行号',
					prop: 'bankBranchCode',
					attrs: {
						disabled: true,
					},
				},
				{
					type: 'el-input',
					label: '开户银行',
					prop: 'depositBank',
				},
				{
					type: 'el-input',
					label: '开户户名',
					prop: 'bankAcctName',
				},
				{
					type: 'el-input',
					label: '开户账号',
					prop: 'bankAcctCode',
				},
				{
					type: 'el-input',
					label: '开户地址',
					prop: 'bankAddres',
				},
				{
					type: 'el-input',
					label: '联系人',
					prop: 'contactPeople',
				},
				{
					type: 'el-input',
					label: '联系电话',
					prop: 'contactPhone',
				},
				{
					type: 'el-input',
					label: '电子邮箱',
					prop: 'email',
				},
				{
					type: 'el-input',
					label: '邮寄地址',
					prop: 'mailingAddress',
				},
				{
					type: 'el-input',
					label: '备注',
					prop: 'remarks',
				},
				{
					type: 'el-input',
					label: '邮编',
					prop: 'zipCode',
				},
			],
			formAttrs: {
				displayItem: 'block',
				labelPosition: 'top',
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
					collectionAgreementNumber: [ruleRequired('必填'), ruleMaxLength(30), RULE_INTEGERONLY],
					bankCode: [ruleRequired('必填')],
					bankNo: [ruleRequired('必填'), ruleMaxLength(64)],
					bankBranchCode: [ruleRequired('必填')],
					depositBank: [ruleRequired('必填'), ruleMaxLength(64)],
					bankAcctName: [ruleRequired('必填'), ruleMaxLength(64)],
					bankAcctCode: [ruleRequired('必填'), ruleMaxLength(64)],
					bankAddres: [ruleMaxLength(64)],
					contactPeople: [ruleMaxLength(64)],
					contactPhone: [RULE_PHONE],
					email: [RULE_INCORRECTEMAIL],
					mailingAddress: [ruleMaxLength(64)],
					remarks: [ruleMaxLength(64)],
					zipCode: [RULE_POSTALCODE],
				},
			},
		}
	},
	methods: {
		async _apiGetAgreementNumber() {
			const { orgCode } = this.formData
			if (isBlank(orgCode)) {
				this.$message.error('请选择营业分公司')
				return
			}

			try {
				const { collectionAgreementNumber = '' } = await apiGetAgreementNum({
					orgCode,
				})
				const message = collectionAgreementNumber ? '获取托收协议号成功' : '未查询到数据'
				this.formData.collectionAgreementNumber = collectionAgreementNumber
				this.$message.success(message)
			} catch (error) {
				console.error(error)
			}
		},
		async getBankList() {
			const data = await apiQueryCollectionBankList()
			this.formItems[2].options = data.map(item => {
				return {
					label: item.bankName,
					value: item.bankCode,
				}
			})
		},
		async getConsignBankList(value) {
			const data = await apiQueryConsignBankCodeList({
				bankType: value,
			})
			this.formItems[3].options = data.map(item => {
				return {
					label: item.bankName,
					value: item.bankCode,
				}
			})
		},
		assignForm(obj) {
			const keys = Object.keys(this.formData)
			if (obj.bankCode) {
				this.getConsignBankList(obj.bankCode)
			}

			keys.forEach(key => {
				if (obj && Object.prototype.hasOwnProperty.call(obj, key)) {
					this.formData[key] = obj[key]
					if (key === 'bankNo') {
						this.formData.bankBranchCode = obj[key]
					}
				}
			})
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				const formParams = trimParams(removeNullParams(this.formData))
				if (this.editType === 'add') {
					await apiCreateCollectionAccount(formParams)
				}
				if (this.editType === 'edit') {
					Object.assign(formParams, {
						collectionAccountId: this.data.collectionAccountId,
					})
					await apiSetCollectionAccount(formParams)
				}
				this.$message.success(this.title + '成功')
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetForm()
			this.$nextTick(() => {
				this.$refs.formRef.clearValidate()
			})
			this.isShow = false
		},
	},
}
</script>
<style lang="scss" scoped>
::v-deep {
	.el-form {
		padding: 0 20px;
	}

	.el-input-group__append {
		padding: 0;
		img {
			display: block;
			padding: 0 10px;
			height: 30px;
			line-height: 30px;
			object-fit: none;
			cursor: pointer;
		}
	}
	.account-more .el-input__inner {
		background-color: #f7f7f7;
	}
}
</style>
