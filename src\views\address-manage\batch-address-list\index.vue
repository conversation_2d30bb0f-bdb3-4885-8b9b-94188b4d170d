<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
			<div class="btn-group">
				<el-button style="width: 50%" round @click="initFormData">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:operate="{ row }">
					<el-button
						type="text"
						size="mini"
						@click="handleModify(row)"
						v-has="'cpm_newAddress_updateAddress'"
					>
						修改
					</el-button>
					<el-button
						type="text"
						size="mini"
						@click="handleView"
						v-has="'cpm_newAddress_queryAddressRecordPage'"
					>
						查看操作记录
					</el-button>
				</template>
			</GcTable>
		</div>
		<!-- 修改地址弹窗 -->
		<AddressModify ref="addressResetRef" :show.sync="showDialog" :data="rowData" @success="handleSearch" />
	</div>
</template>

<script>
import { getColumn } from './tableColumn'
import { getFormItems } from './formItem.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiGetRegion, apiGetAddressAreaMap, apiQueryAddressPage } from '@/api/addressManage.api.js'
import AddressModify from './components/AddressModify.vue'
export default {
	components: { AddressModify },
	data() {
		return {
			formData: {},
			formItems: [],
			formAttrs: {
				rules: {},
			},
			// 右侧列表
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			showDialog: false,
			rowData: null,
		}
	},
	computed: {
		columns() {
			return getColumn(this)
		},
	},
	async activated() {
		const { city, region, street, community } = this.$route.query
		const promises = []
		this.formItems = getFormItems(this)

		await this._getCityOriRegionData(21, 'cityCode')
		if (city) {
			this.formData.cityCode = this.formItems[0].options[0]?.value
			promises.push(this._getCityOriRegionData(this.formData.cityCode, 'regionCode'))
		}
		if (region) {
			promises.push(this._getAddressAreaMap(region, 'streetCode'))
		}
		if (street) {
			promises.push(this._getAddressAreaMap(street, 'neighbourhoodCode'))
		}
		if (community) {
			promises.push(this._getAddressAreaMap(community, 'buildingCode'))
		}
		await Promise.all(promises)
		const cityIndex = this.formItems.findIndex(item => item.prop === 'cityCode')
		const regionIndex = this.formItems.findIndex(item => item.prop === 'regionCode')
		const streetIndex = this.formItems.findIndex(item => item.prop === 'streetCode')
		const communityIndex = this.formItems.findIndex(item => item.prop === 'neighbourhoodCode')
		const buildingIndex = this.formItems.findIndex(item => item.prop === 'buildingCode')
		if (city) {
			const ifExist = this.formItems[cityIndex].options.some(item => item.value === city)
			if (!ifExist) {
				this.formData.cityCode = ''
				this.formItems[regionIndex].options = []
				this.formItems[streetIndex].options = []
				this.formItems[communityIndex].options = []
				this.formItems[buildingIndex].options = []
			} else {
				this.formData.cityCode = city
			}
		}
		if (region) {
			const ifExist = this.formItems[regionIndex].options.some(item => item.value === region)
			if (!ifExist) {
				this.formData.regionCode = ''
				this.formItems[streetIndex].options = []
				this.formItems[communityIndex].options = []
				this.formItems[buildingIndex].options = []
			} else {
				this.formData.regionCode = region
			}
		}
		if (street) {
			const ifExist = this.formItems[streetIndex].options.some(item => item.value === street)
			if (!ifExist) {
				this.formData.streetCode = ''
				this.formItems[communityIndex].options = []
				this.formItems[buildingIndex].options = []
			} else {
				this.formData.streetCode = street
			}
		}

		if (community) {
			const ifExist = this.formItems[communityIndex].options.some(item => item.value === community)
			if (!ifExist) {
				this.formData.neighbourhoodCode = ''
				this.formItems[buildingIndex].options = []
			} else {
				this.formData.neighbourhoodCode = community
			}
		}

		this.handleSearch()
	},
	methods: {
		// 获取市、区县数据
		async _getCityOriRegionData(value, key) {
			const { records } = await apiGetRegion({ regionCode: value })
			const obj = this.formItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = records.map(item => {
				return {
					value: item.regionCode,
					label: item.regionName,
				}
			})
		},
		// 获取街道、小区、楼栋数据
		async _getAddressAreaMap(value, key) {
			const data = await apiGetAddressAreaMap({
				parentCode: value,
			})
			const obj = this.formItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = data.map(item => {
				let label = item.addressAreaName
				if (key === 'buildingCode') {
					label = label + item.unit
				}
				return {
					value: item.addressAreaCode,
					label,
				}
			})
		},
		async getList() {
			this.loading = true
			try {
				const formParams = trimParams(removeNullParams(this.formData))
				const { current, size } = this.pageData
				Object.assign(formParams, {
					current,
					size,
				})
				const { total = 0, records } = await apiQueryAddressPage(formParams)

				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 改变地址
		async handleChangeAddress(value, type) {
			const regionIndex = this.formItems.findIndex(item => item.prop === 'regionCode')
			const streetIndex = this.formItems.findIndex(item => item.prop === 'streetCode')
			const neighbourhoodIndex = this.formItems.findIndex(item => item.prop === 'neighbourhoodCode')
			const buildingIndex = this.formItems.findIndex(item => item.prop === 'buildingCode')
			if (type === 'cityCode') {
				this.formItems[regionIndex].options = []
				this.formItems[streetIndex].options = []
				this.formItems[neighbourhoodIndex].options = []
				this.formItems[buildingIndex].options = []
				this.formData.regionCode = ''
				this.formData.streetCode = ''
				this.formData.neighbourhoodCode = ''
				this.formData.buildingCode = ''
				if (value) {
					this._getCityOriRegionData(value, 'regionCode')
				}
			} else if (type === 'regionCode') {
				this.formItems[streetIndex].options = []
				this.formItems[neighbourhoodIndex].options = []
				this.formItems[buildingIndex].options = []
				this.formData.streetCode = ''
				this.formData.neighbourhoodCode = ''
				this.formData.buildingCode = ''

				if (value) {
					this._getAddressAreaMap(value, 'streetCode')
				}
			} else if (type === 'streetCode') {
				this.formItems[buildingIndex].options = []
				this.formItems[neighbourhoodIndex].options = []
				this.formData.buildingCode = ''
				this.formData.neighbourhoodCode = ''
				if (value) {
					this._getAddressAreaMap(value, 'neighbourhoodCode')
				}
			} else if (type === 'neighbourhoodCode') {
				this.formItems[buildingIndex].options = []
				this.formData.buildingCode = ''
				if (value) {
					this._getAddressAreaMap(value, 'buildingCode')
				}
			}
		},
		// 初始化form表单
		initFormData() {
			this.formItems = getFormItems(this)
			this._getCityOriRegionData(21, 'cityCode')
			this.$nextTick(() => {
				this.$refs.formRef.$refs.formRef.resetFields()
				this.handleSearch()
			})
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.handlePageChange({ page: 1 })
		},
		// 修改
		handleModify(row) {
			this.showDialog = true
			this.rowData = row
		},
		// 操作记录
		handleView() {
			this.$router.push({
				path: '/addressManage/operateRecord',
				query: {
					type: 'address',
				},
			})
		},
		handlePageChange({ page, size  }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
	},
}
</script>
