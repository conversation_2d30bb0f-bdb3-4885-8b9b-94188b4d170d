[{"permissionId": 1, "subPath": "/auth/aggregation/password/sys/manage/update", "permissionCode": "auth_password_sys_manage_update", "actionName": "post"}, {"permissionId": 2, "subPath": "/v1/tos/orgstrutemplate/detail", "permissionCode": "tos_orgstrutemplate_detail", "actionName": "get"}, {"permissionId": 3, "subPath": "/v1/tos/orgstrutemplate/tree", "permissionCode": "tos_orgstrutemplate_tree", "actionName": "get"}, {"permissionId": 4, "subPath": "/v1/tos/templaterole/create", "permissionCode": "tos_templaterole_create", "actionName": "post"}, {"permissionId": 5, "subPath": "/v1/tos/templaterole/delete", "permissionCode": "tos_templaterole_delete", "actionName": "delete"}, {"permissionId": 6, "subPath": "/v1/tos/templaterole/modify", "permissionCode": "tos_templaterole_modify", "actionName": "put"}, {"permissionId": 7, "subPath": "/v1/tos/templaterole/detail", "permissionCode": "tos_templaterole_detail", "actionName": "get"}, {"permissionId": 8, "subPath": "/v1/tos/templaterole/list", "permissionCode": "tos_templaterole_list", "actionName": "get"}, {"permissionId": 9, "subPath": "/v1/tos/roletemplate/create", "permissionCode": "tos_roletemplate_create", "actionName": "post"}, {"permissionId": 10, "subPath": "/v1/tos/roletemplate/delete", "permissionCode": "tos_roletemplate_delete", "actionName": "delete"}, {"permissionId": 11, "subPath": "/v1/tos/roletemplate/modify", "permissionCode": "tos_roletemplate_modify", "actionName": "put"}, {"permissionId": 12, "subPath": "/v1/tos/roletemplate/detail", "permissionCode": "tos_roletemplate_detail", "actionName": "get"}, {"permissionId": 13, "subPath": "/v1/tos/roletemplate/list", "permissionCode": "tos_roletemplate_list", "actionName": "get"}, {"permissionId": 14, "subPath": "/v1/tos/orgstruture/choose", "permissionCode": "tos_orgstruture_choose", "actionName": "put"}, {"permissionId": 15, "subPath": "/v1/tos/orgstruture/list", "permissionCode": "tos_orgstruture_list", "actionName": "get"}, {"permissionId": 16, "subPath": "/v1/tos/orgstruture/sys/list", "permissionCode": "tos_orgstruture_sys_list", "actionName": "get"}, {"permissionId": 17, "subPath": "/v1/tos/orgstruture/tree", "permissionCode": "tos_orgstruture_tree", "actionName": "get"}, {"permissionId": 18, "subPath": "/v1/tos/tenant/create", "permissionCode": "tos_tenant_create", "actionName": "post"}, {"permissionId": 19, "subPath": "/v1/tos/tenant/modify", "permissionCode": "tos_tenant_modify", "actionName": "put"}, {"permissionId": 20, "subPath": "/v1/tos/tenant/disable", "permissionCode": "tos_tenant_disable", "actionName": "put"}, {"permissionId": 21, "subPath": "/v1/tos/tenant/enable", "permissionCode": "tos_tenant_enable", "actionName": "put"}, {"permissionId": 22, "subPath": "/v1/tos/tenant/detail", "permissionCode": "tos_tenant_detail", "actionName": "get"}, {"permissionId": 23, "subPath": "/v1/tos/tenant/sys/detail", "permissionCode": "tos_tenant_sys_detail", "actionName": "get"}, {"permissionId": 24, "subPath": "/v1/tos/tenant/list", "permissionCode": "tos_tenant_list", "actionName": "get"}, {"permissionId": 25, "subPath": "/v1/tos/organization/create", "permissionCode": "tos_organization_create", "actionName": "post"}, {"permissionId": 26, "subPath": "/v1/tos/organization/delete", "permissionCode": "tos_organization_delete", "actionName": "delete"}, {"permissionId": 27, "subPath": "/v1/tos/organization/modify", "permissionCode": "tos_organization_modify", "actionName": "put"}, {"permissionId": 28, "subPath": "/v1/tos/organization/lock", "permissionCode": "tos_organization_lock", "actionName": "put"}, {"permissionId": 29, "subPath": "/v1/tos/organization/unlock", "permissionCode": "tos_organization_unlock", "actionName": "put"}, {"permissionId": 30, "subPath": "/v1/tos/organization/setsubtenant", "permissionCode": "tos_organization_subtenant_set", "actionName": "put"}, {"permissionId": 31, "subPath": "/v1/tos/organization/cancelsubtenant", "permissionCode": "tos_organization_subtenant_cancel", "actionName": "put"}, {"permissionId": 32, "subPath": "/v1/tos/organization/detail", "permissionCode": "tos_organization_detail", "actionName": "get"}, {"permissionId": 33, "subPath": "/v1/tos/organization/tree", "permissionCode": "tos_organization_tree", "actionName": "get"}, {"permissionId": 34, "subPath": "/v1/tos/organization/sys/detail", "permissionCode": "tos_organization_sys_detail", "actionName": "get"}, {"permissionId": 35, "subPath": "/v1/tos/organization/sys/tree", "permissionCode": "tos_organization_sys_tree", "actionName": "get"}, {"permissionId": 36, "subPath": "/v1/tos/role/roletemplate/apply", "permissionCode": "tos_role_roletemplate_apply", "actionName": "post"}, {"permissionId": 37, "subPath": "/v1/tos/role/templateroles/apply", "permissionCode": "tos_role_templateroles_apply", "actionName": "post"}, {"permissionId": 38, "subPath": "/v1/tos/role/create", "permissionCode": "tos_role_create", "actionName": "post"}, {"permissionId": 39, "subPath": "/v1/tos/role/delete", "permissionCode": "tos_role_delete", "actionName": "delete"}, {"permissionId": 40, "subPath": "/v1/tos/role/modify", "permissionCode": "tos_role_modify", "actionName": "put"}, {"permissionId": 41, "subPath": "/v1/tos/role/detail", "permissionCode": "tos_role_detail", "actionName": "get"}, {"permissionId": 42, "subPath": "/v1/tos/role/list", "permissionCode": "tos_role_list", "actionName": "get"}, {"permissionId": 43, "subPath": "/v1/tos/staff/createadmin", "permissionCode": "tos_staff_admin_create", "actionName": "post"}, {"permissionId": 44, "subPath": "/v1/tos/staff/create", "permissionCode": "tos_staff_create", "actionName": "post"}, {"permissionId": 45, "subPath": "/v1/tos/staff/modify", "permissionCode": "tos_staff_modify", "actionName": "put"}, {"permissionId": 46, "subPath": "/v1/tos/staff/detail", "permissionCode": "tos_staff_detail", "actionName": "get"}, {"permissionId": 47, "subPath": "/v1/tos/staff/list", "permissionCode": "tos_staff_list", "actionName": "get"}, {"permissionId": 48, "subPath": "/v1/tos/changelog/list", "permissionCode": "tos_changelog_list", "actionName": "get"}, {"permissionId": 49, "subPath": "/v1/tos/changelog/detail", "permissionCode": "tos_changelog_detail", "actionName": "get"}, {"permissionId": 50, "subPath": "/cpm/archives", "permissionCode": "cpm_archives_create", "actionName": "post"}, {"permissionId": 51, "subPath": "/cpm/archives/detail", "permissionCode": "cpm_archives_detail", "actionName": "get"}, {"permissionId": 52, "subPath": "/cpm/archives", "permissionCode": "cpm_archives_modify", "actionName": "put"}, {"permissionId": 53, "subPath": "/cpm/archives/list", "permissionCode": "cpm_archives_list", "actionName": "post"}, {"permissionId": 54, "subPath": "/cpm/archives-batch/record/list", "permissionCode": "cpm_archives_batch_record_list", "actionName": "post"}, {"permissionId": 55, "subPath": "/cpm/archives-batch/update-credit", "permissionCode": "cpm_archives_batch_update_credit", "actionName": "put"}, {"permissionId": 56, "subPath": "/cpm/archives/affect", "permissionCode": "cpm_archives_affect", "actionName": "get"}, {"permissionId": 57, "subPath": "/cpm/archives/by-address", "permissionCode": "cpm_archives_by_address", "actionName": "get"}, {"permissionId": 58, "subPath": "/cpm/report/archives-list/export/excel", "permissionCode": "cpm_report_archives_list_excel", "actionName": "post"}, {"permissionId": 59, "subPath": "/cpm/report/archives-list/export/pdf", "permissionCode": "cpm_report_archives_list_pdf", "actionName": "post"}, {"permissionId": 60, "subPath": "/cpm/prices/effective", "permissionCode": "cpm_prices_effective", "actionName": "get"}, {"permissionId": 61, "subPath": "/sns/announcement", "permissionCode": "sns_announcement_create", "actionName": "post"}, {"permissionId": 62, "subPath": "/sns/announcement", "permissionCode": "sns_announcement_delete", "actionName": "delete"}, {"permissionId": 63, "subPath": "/sns/announcement", "permissionCode": "sns_announcement_query", "actionName": "get"}, {"permissionId": 64, "subPath": "/cpm/device-type/tenant/dtu-type", "permissionCode": "cpm_device_type_tenant_dtu_type", "actionName": "get"}, {"permissionId": 65, "subPath": "/cpm/device-type/tenant/meter-type", "permissionCode": "cpm_device_type_tenant_meter_type", "actionName": "get"}, {"permissionId": 66, "subPath": "/cpm/dtu/list", "permissionCode": "cpm_dtu_list", "actionName": "get"}, {"permissionId": 67, "subPath": "/sns/message/sign", "permissionCode": "sns_message_sign_create", "actionName": "post"}, {"permissionId": 68, "subPath": "/sns/message/sign", "permissionCode": "sns_message_sign_delete", "actionName": "delete"}, {"permissionId": 69, "subPath": "/sns/message/sign/list", "permissionCode": "sns_message_sign_list", "actionName": "post"}, {"permissionId": 70, "subPath": "/sns/message/template", "permissionCode": "sns_message_template_create", "actionName": "post"}, {"permissionId": 71, "subPath": "/sns/message/template", "permissionCode": "sns_message_template_delete", "actionName": "delete"}, {"permissionId": 72, "subPath": "/sns/message/template/list", "permissionCode": "sns_message_template_list", "actionName": "post"}, {"permissionId": 73, "subPath": "/sns/message/tenant/notify-biz-list", "permissionCode": "sns_message_tenant_notify_biz_list", "actionName": "post"}, {"permissionId": 74, "subPath": "/sns/message/tenant/notify-biz-setting", "permissionCode": "sns_message_tenant_notify_biz_setting", "actionName": "post"}, {"permissionId": 75, "subPath": "/sns/message/biz-msg-template", "permissionCode": "sns_message_biz_msg_template_exe", "actionName": "post"}, {"permissionId": 76, "subPath": "/sns/message/biz-msg-template/list", "permissionCode": "sns_message_biz_msg_template_list", "actionName": "post"}, {"permissionId": 77, "subPath": "/sns/message/biz/list", "permissionCode": "sns_message_biz_list", "actionName": "post"}, {"permissionId": 78, "subPath": "/adaptor/pay/recharge", "permissionCode": "adaptor_pay_recharge_exe", "actionName": "post"}, {"permissionId": 79, "subPath": "/adaptor/pay/getUserInfo", "permissionCode": "adaptor_pay_userinfo_query", "actionName": "post"}, {"permissionId": 80, "subPath": "/adaptor/pay/getRechargeDetail", "permissionCode": "adaptor_pay_recharge_detail_query", "actionName": "post"}, {"permissionId": 81, "subPath": "/rest/v1/query/queryAlarmInfo", "permissionCode": "rest_alarminfo_query", "actionName": "post"}, {"permissionId": 82, "subPath": "/rest/v1/query/customerInfo", "permissionCode": "rest_customerinfo_query", "actionName": "post"}, {"permissionId": 83, "subPath": "/rest/v1/query/queryEquipmentStatus", "permissionCode": "rest_equipment_status_query", "actionName": "post"}, {"permissionId": 84, "subPath": "/rest/v1/query/querydayrecord", "permissionCode": "rest_dayrecord_query", "actionName": "post"}, {"permissionId": 85, "subPath": "/rest/v1/query/querymonthrecord", "permissionCode": "rest_monthrecord_query", "actionName": "post"}, {"permissionId": 86, "subPath": "/cpm/address-areas", "permissionCode": "cpm_address_areas_modify", "actionName": "put"}, {"permissionId": 87, "subPath": "/cpm/address-areas/list", "permissionCode": "cpm_address_areas_list", "actionName": "post"}, {"permissionId": 88, "subPath": "/cpm/address-areas/info", "permissionCode": "cpm_address_areas_info", "actionName": "get"}, {"permissionId": 89, "subPath": "/cpm/region/children", "permissionCode": "cpm_region_children", "actionName": "get"}, {"permissionId": 90, "subPath": "/cpm/archives-batch/record", "permissionCode": "cpm_archives_batch_record", "actionName": "post"}, {"permissionId": 91, "subPath": "/cpm/archives-batch", "permissionCode": "cpm_archives_batch_exe", "actionName": "post"}, {"permissionId": 92, "subPath": "/cpm/archives-batch/fail", "permissionCode": "cpm_archives_batch_fail", "actionName": "get"}, {"permissionId": 93, "subPath": "/cpm/prices", "permissionCode": "cpm_prices_create", "actionName": "post"}, {"permissionId": 94, "subPath": "/cpm/prices", "permissionCode": "cpm_prices_modify", "actionName": "put"}, {"permissionId": 95, "subPath": "/cpm/dtu/search", "permissionCode": "cpm_dtu_search", "actionName": "get"}, {"permissionId": 96, "subPath": "/cpm/user/search", "permissionCode": "cpm_user_search", "actionName": "post"}, {"permissionId": 97, "subPath": "/cpm/address-polls/search", "permissionCode": "cpm_address_polls_search", "actionName": "get"}, {"permissionId": 98, "subPath": "/cpm/archives/alarmrecords", "permissionCode": "cpm_archives_alarmrecords", "actionName": "post"}, {"permissionId": 99, "subPath": "/cpm/archives/costrecords", "permissionCode": "cpm_archives_costrecords", "actionName": "post"}, {"permissionId": 100, "subPath": "/cpm/archives/modifyrecords", "permissionCode": "cpm_archives_modifyrecords", "actionName": "post"}, {"permissionId": 101, "subPath": "/cpm/archives/settle-detail", "permissionCode": "cpm_archives_settle_detail", "actionName": "post"}, {"permissionId": 102, "subPath": "/cpm/charge/recharge", "permissionCode": "cpm_charge_recharge", "actionName": "post"}, {"permissionId": 103, "subPath": "/cpm/charge/reissue", "permissionCode": "cpm_charge_reissue", "actionName": "post"}, {"permissionId": 104, "subPath": "/cpm/charge/cancel-recharge", "permissionCode": "cpm_charge_cancel_recharge", "actionName": "post"}, {"permissionId": 105, "subPath": "/cpm/charge/cancel-reissue", "permissionCode": "cpm_charge_cancel_reissue", "actionName": "post"}, {"permissionId": 106, "subPath": "/cpm/charge-batch/fail", "permissionCode": "cpm_charge_batch_fail", "actionName": "get"}, {"permissionId": 107, "subPath": "/cpm/charge-batch/recharge", "permissionCode": "cpm_charge_batch_recharge", "actionName": "post"}, {"permissionId": 108, "subPath": "/cpm/charge-batch/record", "permissionCode": "cpm_charge_batch_record", "actionName": "post"}, {"permissionId": 109, "subPath": "/cpm/charge-batch/record/list", "permissionCode": "cpm_charge_batch_record_list", "actionName": "post"}, {"permissionId": 110, "subPath": "/cpm/charge-batch/reissue", "permissionCode": "cpm_charge_batch_reissue", "actionName": "post"}, {"permissionId": 111, "subPath": "/cpm/other-cost/charge", "permissionCode": "cpm_other_cost_charge", "actionName": "post"}, {"permissionId": 112, "subPath": "/cpm/other-cost/cancel", "permissionCode": "cpm_other_cost_cancel", "actionName": "post"}, {"permissionId": 113, "subPath": "/cpm/other-cost/modify", "permissionCode": "cpm_other_cost_modify", "actionName": "post"}, {"permissionId": 114, "subPath": "/cpm/other-cost/register", "permissionCode": "cpm_other_cost_register", "actionName": "post"}, {"permissionId": 115, "subPath": "/cpm/other-cost/undo", "permissionCode": "cpm_other_cost_undo", "actionName": "post"}, {"permissionId": 116, "subPath": "/cpm/other-cost/list", "permissionCode": "cpm_other_cost_list", "actionName": "post"}, {"permissionId": 117, "subPath": "/cpm/charge/refund-balance", "permissionCode": "cpm_charge_refund_balance", "actionName": "post"}, {"permissionId": 118, "subPath": "/cpm/charge/charge-detail", "permissionCode": "cpm_charge_charge_detail", "actionName": "post"}, {"permissionId": 119, "subPath": "/cpm/charge/charge-print-info", "permissionCode": "cpm_charge_charge_print_info", "actionName": "post"}, {"permissionId": 120, "subPath": "/cpm/charge/cost-records", "permissionCode": "cpm_charge_cost_records", "actionName": "get"}, {"permissionId": 121, "subPath": "/cpm/user/getUserDatailsById", "permissionCode": "cpm_user_getuserdatailsbyid", "actionName": "get"}, {"permissionId": 122, "subPath": "/cpm/user/getUserInfoByPhoneOrUserName", "permissionCode": "cpm_user_getuserinfobyphoneorusername", "actionName": "post"}, {"permissionId": 123, "subPath": "/cpm/user", "permissionCode": "cpm_user_blacklist_create", "actionName": "post"}, {"permissionId": 124, "subPath": "/cpm/user", "permissionCode": "cpm_user_blacklist_delete", "actionName": "put"}, {"permissionId": 125, "subPath": "/cpm/icCard/icCardArchives", "permissionCode": "cpm_iccard_iccardarchives", "actionName": "post"}, {"permissionId": 126, "subPath": "/cpm/icCard/getIcCardDetails", "permissionCode": "cpm_iccard_geticcarddetails", "actionName": "get"}, {"permissionId": 127, "subPath": "/cpm/icCard/openCard", "permissionCode": "cpm_iccard_opencard", "actionName": "post"}, {"permissionId": 128, "subPath": "/cpm/icCard/patchCard", "permissionCode": "cpm_iccard_patchcard", "actionName": "post"}, {"permissionId": 129, "subPath": "/cpm/icCard/clearCard", "permissionCode": "cpm_iccard_clearcard", "actionName": "post"}, {"permissionId": 130, "subPath": "/cpm/icCard/infos", "permissionCode": "cpm_iccard_infos", "actionName": "get"}, {"permissionId": 131, "subPath": "/cpm/icCard/priceCard", "permissionCode": "cpm_iccard_pricecard", "actionName": "post"}, {"permissionId": 132, "subPath": "/cpm/icCard/doubleWayPriceCard", "permissionCode": "cpm_iccard_doublewaypricecard", "actionName": "post"}, {"permissionId": 133, "subPath": "/cpm/icCard/doubleWayPriceCardInfo", "permissionCode": "cpm_iccard_doublewaypricecardinfo", "actionName": "get"}, {"permissionId": 134, "subPath": "/cpm/icCard/readChangePriceNum", "permissionCode": "cpm_iccard_readchangepricenum", "actionName": "get"}, {"permissionId": 135, "subPath": "/cpm/icCard/getIcCardNo", "permissionCode": "cpm_iccard_get<PERSON>no", "actionName": "post"}, {"permissionId": 136, "subPath": "/cpm/icCharge/icChargeConfirm", "permissionCode": "cpm_iccharge_icchargeconfirm", "actionName": "post"}, {"permissionId": 137, "subPath": "/cpm/icCharge/icCancelChargeConfirm", "permissionCode": "cpm_iccharge_iccancelchargeconfirm", "actionName": "post"}, {"permissionId": 138, "subPath": "/cpm/icCharge/icReissueGasConfirm", "permissionCode": "cpm_iccharge_icreissuegasconfirm", "actionName": "post"}, {"permissionId": 139, "subPath": "/cpm/taxRate/taxRateAdd", "permissionCode": "cpm_taxrate_taxrateadd", "actionName": "post"}, {"permissionId": 140, "subPath": "/cpm/taxRate/taxRateModify", "permissionCode": "cpm_taxrate_taxratemodify", "actionName": "post"}, {"permissionId": 141, "subPath": "/cpm/taxRate", "permissionCode": "cpm_taxrate_delete", "actionName": "delete"}, {"permissionId": 142, "subPath": "/cpm/taxRate/taxRateList", "permissionCode": "cpm_taxrate_taxratelist", "actionName": "get"}, {"permissionId": 143, "subPath": "/cpm/taxRate/taxRateInfo", "permissionCode": "cpm_taxrate_taxrateinfo", "actionName": "get"}, {"permissionId": 144, "subPath": "/cpm/invoiceSaler/details", "permissionCode": "cpm_invoicesaler_details", "actionName": "get"}, {"permissionId": 145, "subPath": "/cpm/invoiceSaler/save", "permissionCode": "cpm_invoicesaler_save", "actionName": "post"}, {"permissionId": 146, "subPath": "/cpm/meterType", "permissionCode": "cpm_metertype_create", "actionName": "post"}, {"permissionId": 147, "subPath": "/cpm/meterType", "permissionCode": "cpm_metertype_modify", "actionName": "put"}, {"permissionId": 148, "subPath": "/cpm/meterType", "permissionCode": "cpm_metertype_detail", "actionName": "get"}, {"permissionId": 149, "subPath": "/cpm/meterType/meterTypeList", "permissionCode": "cpm_metertype_metertype_list", "actionName": "post"}, {"permissionId": 150, "subPath": "/cpm/manufacturer", "permissionCode": "cpm_manufacturer_create", "actionName": "post"}, {"permissionId": 151, "subPath": "/cpm/manufacturer", "permissionCode": "cpm_manufacturer_modify", "actionName": "put"}, {"permissionId": 152, "subPath": "/cpm/manufacturer", "permissionCode": "cpm_manufacturer_query", "actionName": "get"}, {"permissionId": 153, "subPath": "/cpm/manufacturer/manufacturerList", "permissionCode": "cpm_manufacturer_manufacturer_list", "actionName": "post"}, {"permissionId": 154, "subPath": "/cpm/icCardType", "permissionCode": "cpm_iccardtype_create", "actionName": "post"}, {"permissionId": 155, "subPath": "/cpm/icCardType", "permissionCode": "cpm_iccardtype_modify", "actionName": "put"}, {"permissionId": 156, "subPath": "/cpm/icCardType", "permissionCode": "cpm_iccardtype_query", "actionName": "get"}, {"permissionId": 157, "subPath": "/cpm/icCardType/icCardTypeList", "permissionCode": "cpm_iccardtype_iccardtype_list", "actionName": "post"}, {"permissionId": 158, "subPath": "/cpm/protocol-mapping", "permissionCode": "cpm_protocol_mapping_create", "actionName": "post"}, {"permissionId": 159, "subPath": "/cpm/protocol-mapping", "permissionCode": "cpm_protocol_mapping_modify", "actionName": "put"}, {"permissionId": 160, "subPath": "/cpm/protocol-mapping/list", "permissionCode": "cpm_protocol_mapping_list", "actionName": "post"}, {"permissionId": 161, "subPath": "/cpm/code/command-record-list", "permissionCode": "cpm_code_command_record_list", "actionName": "post"}, {"permissionId": 162, "subPath": "/cpm/code/cancel-command", "permissionCode": "cpm_code_cancel_command", "actionName": "put"}, {"permissionId": 163, "subPath": "/cpm/code/resend-command", "permissionCode": "cpm_code_resend_command", "actionName": "put"}, {"permissionId": 164, "subPath": "/cpm/prices/enable", "permissionCode": "cpm_prices_enable", "actionName": "put"}, {"permissionId": 165, "subPath": "/cpm/prices/disable", "permissionCode": "cpm_prices_disable", "actionName": "put"}, {"permissionId": 166, "subPath": "/cpm/prices", "permissionCode": "cpm_prices_delete", "actionName": "delete"}, {"permissionId": 167, "subPath": "/cpm/prices/info", "permissionCode": "cpm_prices_info", "actionName": "get"}, {"permissionId": 168, "subPath": "/cpm/prices/list-by-id", "permissionCode": "cpm_prices_list_by_id", "actionName": "post"}, {"permissionId": 169, "subPath": "/cpm/prices/list", "permissionCode": "cpm_prices_list_last", "actionName": "post"}, {"permissionId": 170, "subPath": "/cpm/device-monitor/signal", "permissionCode": "cpm_device_monitor_signal", "actionName": "get"}, {"permissionId": 171, "subPath": "/cpm/device-monitor/voltage", "permissionCode": "cpm_device_monitor_voltage", "actionName": "get"}, {"permissionId": 172, "subPath": "/cpm/device-monitor/collection", "permissionCode": "cpm_device_monitor_collection", "actionName": "post"}, {"permissionId": 173, "subPath": "/cpm/device-monitor/detail-log", "permissionCode": "cpm_device_monitor_detail_log", "actionName": "post"}, {"permissionId": 174, "subPath": "/cpm/report/balance-detail/preview", "permissionCode": "cpm_report_balance_detail_preview", "actionName": "post"}, {"permissionId": 175, "subPath": "/cpm/report/balance-detail/export/pdf", "permissionCode": "cpm_report_balance_detail_pdf", "actionName": "post"}, {"permissionId": 176, "subPath": "/cpm/report/balance-detail/export/excel", "permissionCode": "cpm_report_balance_detail_excel", "actionName": "post"}, {"permissionId": 177, "subPath": "/cpm/report/balance-statistics/preview", "permissionCode": "cpm_report_balance_statistics_preview", "actionName": "post"}, {"permissionId": 178, "subPath": "/cpm/report/balance-statistics/export/pdf", "permissionCode": "cpm_report_balance_statistics_pdf", "actionName": "post"}, {"permissionId": 179, "subPath": "/cpm/report/balance-statistics/export/excel", "permissionCode": "cpm_report_balance_statistics_excel", "actionName": "post"}, {"permissionId": 180, "subPath": "/cpm/report/power-down/preview", "permissionCode": "cpm_report_power_down_preview", "actionName": "post"}, {"permissionId": 181, "subPath": "/cpm/report/power-down/export/pdf", "permissionCode": "cpm_report_power_down_pdf", "actionName": "post"}, {"permissionId": 182, "subPath": "/cpm/report/power-down/export/excel", "permissionCode": "cpm_report_power_down_excel", "actionName": "post"}, {"permissionId": 183, "subPath": "/cpm/report/interfere/preview", "permissionCode": "cpm_report_interfere_preview", "actionName": "post"}, {"permissionId": 184, "subPath": "/cpm/report/interfere/export/pdf", "permissionCode": "cpm_report_interfere_pdf", "actionName": "post"}, {"permissionId": 185, "subPath": "/cpm/report/interfere/export/excel", "permissionCode": "cpm_report_interfere_excel", "actionName": "post"}, {"permissionId": 186, "subPath": "/cpm/report/no-upload/preview", "permissionCode": "cpm_report_no_upload_preview", "actionName": "post"}, {"permissionId": 187, "subPath": "/cpm/report/no-upload/export/pdf", "permissionCode": "cpm_report_no_upload_pdf", "actionName": "post"}, {"permissionId": 188, "subPath": "/cpm/report/no-upload/export/excel", "permissionCode": "cpm_report_no_upload_excel", "actionName": "post"}, {"permissionId": 189, "subPath": "/etf/notify/sms/preview", "permissionCode": "etf_notify_sms_preview", "actionName": "post"}, {"permissionId": 190, "subPath": "/etf/notify/sms/export/pdf", "permissionCode": "etf_notify_sms_pdf", "actionName": "post"}, {"permissionId": 191, "subPath": "/etf/notify/sms/export/excel", "permissionCode": "etf_notify_sms_excel", "actionName": "post"}, {"permissionId": 192, "subPath": "/etf/notify/sms-system/preview", "permissionCode": "etf_notify_sms_system_preview", "actionName": "post"}, {"permissionId": 193, "subPath": "/etf/notify/sms-system/export/pdf", "permissionCode": "etf_notify_sms_system_pdf", "actionName": "post"}, {"permissionId": 194, "subPath": "/etf/notify/sms-system/export/excel", "permissionCode": "etf_notify_sms_system_excel", "actionName": "post"}, {"permissionId": 195, "subPath": "/etf/monthly-bill/preview", "permissionCode": "etf_monthly_bill_preview", "actionName": "post"}, {"permissionId": 196, "subPath": "/etf/monthly-bill/export/pdf", "permissionCode": "etf_monthly_bill_pdf", "actionName": "post"}, {"permissionId": 197, "subPath": "/etf/monthly-bill/export/excel", "permissionCode": "etf_monthly_bill_excel", "actionName": "post"}, {"permissionId": 198, "subPath": "/cpm/device-monitor/meter-detail", "permissionCode": "cpm_device_monitor_meter_detail", "actionName": "get"}, {"permissionId": 199, "subPath": "/cpm/device-monitor/meter-list", "permissionCode": "cpm_device_monitor_meter_list", "actionName": "post"}, {"permissionId": 200, "subPath": "/etf/gas/collection/preview", "permissionCode": "etf_gas_collection_preview", "actionName": "post"}, {"permissionId": 201, "subPath": "/etf/gas/collection/export/pdf", "permissionCode": "etf_gas_collection_pdf", "actionName": "post"}, {"permissionId": 202, "subPath": "/etf/gas/collection/export/excel", "permissionCode": "etf_gas_collection_excel", "actionName": "post"}, {"permissionId": 203, "subPath": "/cpm/report/meter-list/export/pdf", "permissionCode": "cpm_report_meter_list_pdf", "actionName": "post"}, {"permissionId": 204, "subPath": "/cpm/report/meter-list/export/excel", "permissionCode": "cpm_report_meter_list_excel", "actionName": "post"}, {"permissionId": 205, "subPath": "/cpm/code/archives/commandlist", "permissionCode": "cpm_code_archives_commandlist", "actionName": "get"}, {"permissionId": 206, "subPath": "/cpm/code/archives/save", "permissionCode": "cpm_code_archives_save", "actionName": "post"}, {"permissionId": 207, "subPath": "/cpm/code/web/commandlist", "permissionCode": "cpm_code_web_commandlist", "actionName": "get"}, {"permissionId": 208, "subPath": "/cpm/code/params/paramslist", "permissionCode": "cpm_code_params_paramslist", "actionName": "get"}, {"permissionId": 209, "subPath": "/cpm/code/web/send", "permissionCode": "cpm_code_web_send", "actionName": "post"}, {"permissionId": 210, "subPath": "/cpm/sysdata/add-datasort", "permissionCode": "cpm_sysdata_add_datasort_create", "actionName": "post"}, {"permissionId": 211, "subPath": "/cpm/sysdata/update-datasort", "permissionCode": "cpm_sysdata_update_datasort_modify", "actionName": "post"}, {"permissionId": 212, "subPath": "/cpm/sysdata/get-datasortlist", "permissionCode": "cpm_sysdata_get_datasort_list", "actionName": "post"}, {"permissionId": 213, "subPath": "/cpm/sysdata/add-data", "permissionCode": "cpm_sysdata_data_create", "actionName": "post"}, {"permissionId": 214, "subPath": "/cpm/sysdata/update-data", "permissionCode": "cpm_sysdata_data_modify", "actionName": "post"}, {"permissionId": 215, "subPath": "/cpm/sysdata/get-datalist", "permissionCode": "cpm_sysdata_get_datalist_query", "actionName": "post"}, {"permissionId": 216, "subPath": "/cpm/sysdata/getsysdatalist", "permissionCode": "cpm_sysdata_getsysdatalist", "actionName": "get"}, {"permissionId": 217, "subPath": "/cpm/sysdata/get-sysparalist", "permissionCode": "cpm_sysdata_get_sysparalist", "actionName": "get"}, {"permissionId": 218, "subPath": "/cpm/sysdata/create-syspara", "permissionCode": "cpm_sysdata_create_syspara", "actionName": "post"}, {"permissionId": 219, "subPath": "/cpm/alarm/list", "permissionCode": "cpm_alarm_list", "actionName": "get"}, {"permissionId": 220, "subPath": "/cpm/alarm/save-alarm", "permissionCode": "cpm_alarm_save_alarm", "actionName": "post"}, {"permissionId": 221, "subPath": "/cpm/code/command-detail-list", "permissionCode": "cpm_code_command_detail_list", "actionName": "post"}, {"permissionId": 222, "subPath": "/cpm/code/command-batch-list", "permissionCode": "cpm_code_command_batch_list", "actionName": "post"}, {"permissionId": 223, "subPath": "/cpm/code/command-batch-detail", "permissionCode": "cpm_code_command_batch_detail", "actionName": "post"}, {"permissionId": 224, "subPath": "/cpm/code/command-condition-list", "permissionCode": "cpm_code_command_condition_list", "actionName": "get"}, {"permissionId": 225, "subPath": "/cpm/alarm/get-alarmrecord", "permissionCode": "cpm_alarm_get_alarmrecord", "actionName": "post"}, {"permissionId": 226, "subPath": "/cpm/alarm/get-alarm-type", "permissionCode": "cpm_alarm_get_alarm_type", "actionName": "get"}, {"permissionId": 227, "subPath": "/cpm/dtu/meter-list", "permissionCode": "cpm_dtu_meter_list", "actionName": "get"}, {"permissionId": 228, "subPath": "/cpm/alarm/restore-alarm-record", "permissionCode": "cpm_alarm_get_restore_alarm_record", "actionName": "post"}, {"permissionId": 229, "subPath": "/cpm/archives/build", "permissionCode": "cpm_archives_build_exe", "actionName": "put"}, {"permissionId": 230, "subPath": "/cpm/archives/close", "permissionCode": "cpm_archives_close", "actionName": "put"}, {"permissionId": 231, "subPath": "/cpm/archives/disable", "permissionCode": "cpm_archives_disable", "actionName": "put"}, {"permissionId": 232, "subPath": "/cpm/archives/enable", "permissionCode": "cpm_archives_enable", "actionName": "put"}, {"permissionId": 233, "subPath": "/cpm/archives/change-meter", "permissionCode": "cpm_archives_change_meter_exe", "actionName": "put"}, {"permissionId": 234, "subPath": "/cpm/archives/change-meter-type", "permissionCode": "cpm_archives_change_meter_type", "actionName": "put"}, {"permissionId": 235, "subPath": "/cpm/archives/change-price", "permissionCode": "cpm_archives_change_price", "actionName": "put"}, {"permissionId": 236, "subPath": "/cpm/archives/change-user", "permissionCode": "cpm_archives_change_user", "actionName": "put"}, {"permissionId": 237, "subPath": "/cpm/archives/ic-recharge-record", "permissionCode": "cpm_archives_ic_recharge_record", "actionName": "post"}, {"permissionId": 238, "subPath": "/cpm/dtu-type", "permissionCode": "cpm_dtu_type_create", "actionName": "post"}, {"permissionId": 239, "subPath": "/cpm/dtu-type", "permissionCode": "cpm_dtu_type_modify", "actionName": "put"}, {"permissionId": 240, "subPath": "/cpm/dtu-type/list", "permissionCode": "cpm_dtu_type_list", "actionName": "post"}, {"permissionId": 241, "subPath": "/cpm/device-type/cancel", "permissionCode": "cpm_device_type_cancel", "actionName": "post"}, {"permissionId": 242, "subPath": "/cpm/device-type/grant", "permissionCode": "cpm_device_type_grant", "actionName": "post"}, {"permissionId": 243, "subPath": "/cpm/device-type/granted-device", "permissionCode": "cpm_device_type_granted_device", "actionName": "get"}, {"permissionId": 244, "subPath": "/cpm/device-type/ungrant-device", "permissionCode": "cpm_device_type_ungrant_device", "actionName": "post"}, {"permissionId": 245, "subPath": "/cpm/prices/adjust", "permissionCode": "cpm_prices_adjust_exe", "actionName": "post"}, {"permissionId": 246, "subPath": "/cpm/prices/adjust-list", "permissionCode": "cpm_prices_adjust_list", "actionName": "post"}, {"permissionId": 247, "subPath": "/cpm/prices/adjust-detail", "permissionCode": "cpm_prices_adjust_detail", "actionName": "post"}, {"permissionId": 248, "subPath": "/cpm/archives/change-dtu", "permissionCode": "cpm_archives_change_dtu", "actionName": "put"}, {"permissionId": 249, "subPath": "/cpm/archives/history-price", "permissionCode": "cpm_archives_history_price", "actionName": "get"}, {"permissionId": 250, "subPath": "/cpm/archives/last-disable-reason", "permissionCode": "cpm_archives_last_disable_reason", "actionName": "get"}, {"permissionId": 251, "subPath": "/cpm/prices/list-price", "permissionCode": "cpm_prices_list_price", "actionName": "get"}, {"permissionId": 252, "subPath": "/cpm/device-type/list", "permissionCode": "cpm_device_type_list", "actionName": "get"}, {"permissionId": 253, "subPath": "/cpm/invoice/opening-invoice", "permissionCode": "cpm_invoice_opening_invoice", "actionName": "post"}, {"permissionId": 254, "subPath": "/cpm/invoice/redhash-invoice", "permissionCode": "cpm_invoice_redhash_invoice", "actionName": "post"}, {"permissionId": 255, "subPath": "/cpm/invoice/getPDF", "permissionCode": "cpm_invoice_getpdf", "actionName": "get"}, {"permissionId": 256, "subPath": "/cpm/invoice/query-invoice-detail", "permissionCode": "cpm_invoice_query_invoice_detail", "actionName": "get"}, {"permissionId": 257, "subPath": "/cpm/invoice-buyer/query-invoicebuyer-info", "permissionCode": "cpm_invoice_buyer_query_invoicebuyer_info", "actionName": "get"}, {"permissionId": 258, "subPath": "/cpm/icCard/getIcCardLog", "permissionCode": "cpm_iccard_geticcardlog", "actionName": "post"}, {"permissionId": 259, "subPath": "/cpm/report/valve-closed/preview", "permissionCode": "cpm_report_valve_closed_preview", "actionName": "post"}, {"permissionId": 260, "subPath": "/cpm/report/valve-closed/export/pdf", "permissionCode": "cpm_report_valve_closed_pdf", "actionName": "post"}, {"permissionId": 261, "subPath": "/cpm/report/valve-closed/export/excel", "permissionCode": "cpm_report_valve_closed_excel", "actionName": "post"}, {"permissionId": 262, "subPath": "/cpm/report/arrears/preview", "permissionCode": "cpm_report_arrears_preview", "actionName": "post"}, {"permissionId": 263, "subPath": "/cpm/report/arrears/export/pdf", "permissionCode": "cpm_report_arrears_pdf", "actionName": "post"}, {"permissionId": 264, "subPath": "/cpm/report/arrears/export/excel", "permissionCode": "cpm_report_arrears_excel", "actionName": "post"}, {"permissionId": 265, "subPath": "/cpm/report/low-battery/preview", "permissionCode": "cpm_report_low_battery_preview", "actionName": "post"}, {"permissionId": 266, "subPath": "/cpm/report/low-battery/export/pdf", "permissionCode": "cpm_report_low_battery_pdf", "actionName": "post"}, {"permissionId": 267, "subPath": "/cpm/report/low-battery/export/excel", "permissionCode": "cpm_report_low_battery_excel", "actionName": "post"}, {"permissionId": 268, "subPath": "/cpm/report/unused-gas/preview", "permissionCode": "cpm_report_unused_gas_preview", "actionName": "post"}, {"permissionId": 269, "subPath": "/cpm/report/unused-gas/export/pdf", "permissionCode": "cpm_report_unused_gas_pdf", "actionName": "post"}, {"permissionId": 270, "subPath": "/cpm/report/unused-gas/export/excel", "permissionCode": "cpm_report_unused_gas_excel", "actionName": "post"}, {"permissionId": 271, "subPath": "/etf/expense/recharge-detail/preview", "permissionCode": "etf_expense_recharge_detail_preview", "actionName": "post"}, {"permissionId": 272, "subPath": "/etf/expense/recharge-detail/export/pdf", "permissionCode": "etf_expense_recharge_detail_pdf", "actionName": "post"}, {"permissionId": 273, "subPath": "/etf/expense/recharge-detail/export/excel", "permissionCode": "etf_expense_recharge_detail_excel", "actionName": "post"}, {"permissionId": 274, "subPath": "/etf/expense/recharge-summary/preview", "permissionCode": "etf_expense_recharge_summary_preview", "actionName": "post"}, {"permissionId": 275, "subPath": "/etf/expense/recharge-summary/export/pdf", "permissionCode": "etf_expense_recharge_summary_pdf", "actionName": "post"}, {"permissionId": 276, "subPath": "/etf/expense/recharge-summary/export/excel", "permissionCode": "etf_expense_recharge_summary_excel", "actionName": "post"}, {"permissionId": 277, "subPath": "/cpm/chart/open-account", "permissionCode": "cpm_chart_open_account", "actionName": "post"}, {"permissionId": 278, "subPath": "/cpm/report/open-account/preview", "permissionCode": "cpm_report_open_account_preview", "actionName": "post"}, {"permissionId": 279, "subPath": "/cpm/report/open-account/export/pdf", "permissionCode": "cpm_report_open_account_pdf", "actionName": "post"}, {"permissionId": 280, "subPath": "/cpm/report/open-account/export/excel", "permissionCode": "cpm_report_open_account_excel", "actionName": "post"}, {"permissionId": 281, "subPath": "/cpm/chart/reading-meter", "permissionCode": "cpm_chart_reading_meter", "actionName": "post"}, {"permissionId": 282, "subPath": "/etf/reading/meter-reading/export/pdf", "permissionCode": "etf_reading_meter_reading_pdf", "actionName": "post"}, {"permissionId": 283, "subPath": "/etf/reading/meter-reading/export/excel", "permissionCode": "etf_reading_meter_reading_excel", "actionName": "post"}, {"permissionId": 284, "subPath": "/etf/reading/meter-reading/preview", "permissionCode": "etf_reading_meter_reading_preview", "actionName": "post"}, {"permissionId": 285, "subPath": "/etf/gas/usage-quantity/chart", "permissionCode": "etf_gas_usage_quantity_chart", "actionName": "post"}, {"permissionId": 286, "subPath": "/etf/gas/usage-quantity/price/preview", "permissionCode": "etf_gas_usage_quantity_price_preview", "actionName": "post"}, {"permissionId": 287, "subPath": "/etf/gas/usage-quantity/price/export/excel", "permissionCode": "etf_gas_usage_quantity_price_excel", "actionName": "post"}, {"permissionId": 288, "subPath": "/etf/gas/usage-quantity/price/export/pdf", "permissionCode": "etf_gas_usage_quantity_price_pdf", "actionName": "post"}, {"permissionId": 289, "subPath": "/etf/gas/usage-quantity/region/preview", "permissionCode": "etf_gas_usage_quantity_region_preview", "actionName": "post"}, {"permissionId": 290, "subPath": "/etf/gas/usage-quantity/region/export/excel", "permissionCode": "etf_gas_usage_quantity_region_excel", "actionName": "post"}, {"permissionId": 291, "subPath": "/etf/gas/usage-quantity/region/export/pdf", "permissionCode": "etf_gas_usage_quantity_region_pdf", "actionName": "post"}, {"permissionId": 292, "subPath": "/etf/gas/usage-quantity/archives/preview", "permissionCode": "etf_gas_usage_quantity_archives_preview", "actionName": "post"}, {"permissionId": 293, "subPath": "/etf/gas/usage-quantity/archives/export/excel", "permissionCode": "etf_gas_usage_quantity_archives_excel", "actionName": "post"}, {"permissionId": 294, "subPath": "/etf/gas/usage-quantity/archives/export/pdf", "permissionCode": "etf_gas_usage_quantity_archives_pdf", "actionName": "post"}, {"permissionId": 295, "subPath": "/etf/gas/usage-quantity/detail/preview", "permissionCode": "etf_gas_usage_quantity_detail_preview", "actionName": "post"}, {"permissionId": 296, "subPath": "/etf/gas/usage-quantity/detail/export/excel", "permissionCode": "etf_gas_usage_quantity_detail_excel", "actionName": "post"}, {"permissionId": 297, "subPath": "/etf/gas/usage-quantity/detail/export/pdf", "permissionCode": "etf_gas_usage_quantity_detail_pdf", "actionName": "post"}, {"permissionId": 298, "subPath": "/cpm/archives/settle-records-group", "permissionCode": "cpm_archives_settle_records_group", "actionName": "post"}, {"permissionId": 299, "subPath": "/cpm/report/collection/export/pdf", "permissionCode": "cpm_report_collection_pdf", "actionName": "post"}, {"permissionId": 300, "subPath": "/cpm/report/collection/export/excel", "permissionCode": "cpm_report_collection_excel", "actionName": "post"}, {"permissionId": 301, "subPath": "/rest/v1/query/userArchivesInfo", "permissionCode": "rest_userarchivesinfo_query", "actionName": "get"}, {"permissionId": 302, "subPath": "/rest/v1/query/costRecordInfo", "permissionCode": "rest_costrecordinfo_query", "actionName": "get"}, {"permissionId": 303, "subPath": "/cpm/icCharge/icCancelReissueGas", "permissionCode": "cpm_iccharge_iccancelreissuegas", "actionName": "post"}, {"permissionId": 304, "subPath": "/express-report/recharge/rest/v1/query/queryPingYaoDataSource", "permissionCode": "express_querypingyaodatasource", "actionName": "post"}, {"permissionId": 305, "subPath": "/adaptor/jinka-pay/recharge", "permissionCode": "adaptor_jinka_pay_recharge", "actionName": "post"}, {"permissionId": 306, "subPath": "/adaptor/jinka-pay/getArchivesInfo", "permissionCode": "adaptor_jinka_pay_getarchivesinfo", "actionName": "post"}, {"permissionId": 307, "subPath": "/adaptor/jinka-pay/archivesInfoList", "permissionCode": "adaptor_jinka_pay_archivesinfolist", "actionName": "post"}, {"permissionId": 308, "subPath": "/adaptor/jinka-pay/payRecordList", "permissionCode": "adaptor_jinka_pay_payrecordlist", "actionName": "post"}, {"permissionId": 309, "subPath": "/adaptor/jinka-pay/last-twelve-month-monthly-bill", "permissionCode": "adaptor_jinka_pay_last_twelve_month_monthly_bill", "actionName": "get"}, {"permissionId": 310, "subPath": "/adaptor/jinka-pay/settlement-info-from-price-view", "permissionCode": "adaptor_jinka_pay_settlement_info_from_price_view", "actionName": "get"}, {"permissionId": 311, "subPath": "/adaptor/jinka-pay/monthly-bill", "permissionCode": "adaptor_jinka_pay_monthly_bill", "actionName": "get"}, {"permissionId": 312, "subPath": "/adaptor/jinka-pay/getUserDetail", "permissionCode": "adaptor_jinka_pay_getuserdetail", "actionName": "post"}, {"permissionId": 313, "subPath": "/cpm/device-monitor/long-connect/list", "permissionCode": "cpm_device_monitor_long_connect_list", "actionName": "post"}, {"permissionId": 314, "subPath": "/cpm/device-type/long-connect/list", "permissionCode": "cpm_device_type_long_connect_list", "actionName": "get"}, {"permissionId": 315, "subPath": "/etf/gas/usage-quantity/archives-day/preview", "permissionCode": "etf_gas_usage_quantity_archives_day_preview", "actionName": "post"}, {"permissionId": 316, "subPath": "/etf/gas/usage-quantity/archives-day/export/excel", "permissionCode": "etf_gas_usage_quantity_archives_day_excel", "actionName": "post"}, {"permissionId": 317, "subPath": "/etf/gas/usage-quantity/archives-day/export/pdf", "permissionCode": "etf_gas_usage_quantity_archives_day_pdf", "actionName": "post"}, {"permissionId": 318, "subPath": "/cpm/device-monitor/duplicate-meter/list", "permissionCode": "cpm_device_monitor_duplicate_meter_list", "actionName": "get"}, {"permissionId": 319, "subPath": "/cpm/device-monitor/del-duplicate-meter", "permissionCode": "cpm_device_monitor_del_duplicate_meter", "actionName": "delete"}, {"permissionId": 320, "subPath": "/rest/v1/query/collectionInfo", "permissionCode": "rest_collectioninfo_query", "actionName": "post"}, {"permissionId": 321, "subPath": "/adaptor/jinka-pay/realm", "permissionCode": "adaptor_jinka_pay_realm", "actionName": "get"}, {"permissionId": 322, "subPath": "/v1/tos/loginverify/create", "permissionCode": "tos_loginverify_create", "actionName": "post"}, {"permissionId": 323, "subPath": "/v1/tos/loginverify/delete", "permissionCode": "tos_loginverify_delete", "actionName": "delete"}, {"permissionId": 324, "subPath": "/v1/tos/loginverify/list", "permissionCode": "tos_loginverify_list", "actionName": "get"}, {"permissionId": 325, "subPath": "/v1/tos/loginverify/detail", "permissionCode": "tos_loginverify_detail", "actionName": "get"}, {"permissionId": 326, "subPath": "/v1/tos/loginverify/enable", "permissionCode": "tos_loginverify_enable", "actionName": "put"}, {"permissionId": 327, "subPath": "/v1/tos/loginverify/disable", "permissionCode": "tos_loginverify_disable", "actionName": "put"}, {"permissionId": 328, "subPath": "/adaptor/v1/security-check/archives/list", "permissionCode": "adaptor_security_check_archives_list", "actionName": "post"}, {"permissionId": 329, "subPath": "/adaptor/v1/security-check/meter/list", "permissionCode": "adaptor_security_check_meter_list", "actionName": "post"}, {"permissionId": 330, "subPath": "/cpm/report/archives-operator/change-meter/preview", "permissionCode": "cpm_report_archives_operator_change_meter_preview", "actionName": "post"}, {"permissionId": 331, "subPath": "/cpm/report/archives-operator/change-meter/export/pdf", "permissionCode": "cpm_report_archives_operator_change_meter_pdf", "actionName": "post"}, {"permissionId": 332, "subPath": "/cpm/report/archives-operator/change-meter/export/excel", "permissionCode": "cpm_report_archives_operator_change_meter_excel", "actionName": "post"}, {"permissionId": 333, "subPath": "/cpm/report/archives-operator/change-user/preview", "permissionCode": "cpm_report_archives_operator_change_user_preview", "actionName": "post"}, {"permissionId": 334, "subPath": "/cpm/report/archives-operator/change-user/export/pdf", "permissionCode": "cpm_report_archives_operator_change_user_pdf", "actionName": "post"}, {"permissionId": 335, "subPath": "/cpm/report/archives-operator/change-user/export/excel", "permissionCode": "cpm_report_archives_operator_change_user_excel", "actionName": "post"}, {"permissionId": 336, "subPath": "/cpm/report/archives-operator/close/preview", "permissionCode": "cpm_report_archives_operator_close_preview", "actionName": "post"}, {"permissionId": 337, "subPath": "/cpm/report/archives-operator/close/export/pdf", "permissionCode": "cpm_report_archives_operator_close_pdf", "actionName": "post"}, {"permissionId": 338, "subPath": "/cpm/report/archives-operator/close/export/excel", "permissionCode": "cpm_report_archives_operator_close_excel", "actionName": "post"}, {"permissionId": 339, "subPath": "/adaptor/jinka-pay/wx-monthly-bill", "permissionCode": "adaptor_jinka_pay_wx_monthly_bill", "actionName": "post"}, {"permissionId": 340, "subPath": "/cpm/charge-batch/register", "permissionCode": "cpm_charge_batch_register", "actionName": "post"}, {"permissionId": 341, "subPath": "/adaptor/pay/getMeterInfo", "permissionCode": "adaptor_pay_getmeterinfo", "actionName": "post"}, {"permissionId": 342, "subPath": "/cpm/report/archives-operator/statistics/preview", "permissionCode": "cpm_report_archives_operator_statistics_preview", "actionName": "post"}, {"permissionId": 343, "subPath": "/cpm/report/archives-operator/statistics/export/pdf", "permissionCode": "cpm_report_archives_operator_statistics_pdf", "actionName": "post"}, {"permissionId": 344, "subPath": "/cpm/report/archives-operator/statistics/export/excel", "permissionCode": "cpm_report_archives_operator_statistics_excel", "actionName": "post"}, {"permissionId": 345, "subPath": "/cpm/report/archives-operator/change-price/preview", "permissionCode": "cpm_report_archives_operator_change_price_preview", "actionName": "post"}, {"permissionId": 346, "subPath": "/cpm/report/archives-operator/change-price/export/pdf", "permissionCode": "cpm_report_archives_operator_change_price_pdf", "actionName": "post"}, {"permissionId": 347, "subPath": "/cpm/report/archives-operator/change-price/export/excel", "permissionCode": "cpm_report_archives_operator_change_price_excel", "actionName": "post"}, {"permissionId": 348, "subPath": "/cpm/report/archives-disable/preview", "permissionCode": "cpm_report_archives_disable_preview", "actionName": "post"}, {"permissionId": 349, "subPath": "/cpm/report/archives-disable/export/pdf", "permissionCode": "cpm_report_archives_disable_pdf", "actionName": "post"}, {"permissionId": 350, "subPath": "/cpm/report/archives-disable/export/excel", "permissionCode": "cpm_report_archives_disable_excel", "actionName": "post"}, {"permissionId": 351, "subPath": "/cpm/report/archives-enable/preview", "permissionCode": "cpm_report_archives_enable_preview", "actionName": "post"}, {"permissionId": 352, "subPath": "/cpm/report/archives-enable/export/pdf", "permissionCode": "cpm_report_archives_enable_pdf", "actionName": "post"}, {"permissionId": 353, "subPath": "/cpm/report/archives-enable/export/excel", "permissionCode": "cpm_report_archives_enable_excel", "actionName": "post"}, {"permissionId": 354, "subPath": "/cpm/report/open-valve/preview", "permissionCode": "cpm_report_open_valve_preview", "actionName": "post"}, {"permissionId": 355, "subPath": "/cpm/report/open-valve/export/pdf", "permissionCode": "cpm_report_open_valve_pdf", "actionName": "post"}, {"permissionId": 356, "subPath": "/cpm/report/open-valve/export/excel", "permissionCode": "cpm_report_open_valve_excel", "actionName": "post"}, {"permissionId": 357, "subPath": "/cpm/report/close-valve/preview", "permissionCode": "cpm_report_close_valve_preview", "actionName": "post"}, {"permissionId": 358, "subPath": "/cpm/report/close-valve/export/pdf", "permissionCode": "cpm_report_close_valve_pdf", "actionName": "post"}, {"permissionId": 359, "subPath": "/cpm/report/close-valve/export/excel", "permissionCode": "cpm_report_close_valve_excel", "actionName": "post"}, {"permissionId": 360, "subPath": "/cpm/report/external-trigger/preview", "permissionCode": "cpm_report_external_trigger_preview", "actionName": "post"}, {"permissionId": 361, "subPath": "/cpm/report/external-trigger/export/pdf", "permissionCode": "cpm_report_external_trigger_pdf", "actionName": "post"}, {"permissionId": 362, "subPath": "/cpm/report/external-trigger/export/excel", "permissionCode": "cpm_report_external_trigger_excel", "actionName": "post"}, {"permissionId": 363, "subPath": "/cpm/report/pipeline-tamper/preview", "permissionCode": "cpm_report_pipeline_tamper_preview", "actionName": "post"}, {"permissionId": 364, "subPath": "/cpm/report/pipeline-tamper/export/pdf", "permissionCode": "cpm_report_pipeline_tamper_pdf", "actionName": "post"}, {"permissionId": 365, "subPath": "/cpm/report/pipeline-tamper/export/excel", "permissionCode": "cpm_report_pipeline_tamper_excel", "actionName": "post"}, {"permissionId": 366, "subPath": "/cpm/report/insufficient-balance/preview", "permissionCode": "cpm_report_insufficient_balance_preview", "actionName": "post"}, {"permissionId": 367, "subPath": "/cpm/report/insufficient-balance/export/pdf", "permissionCode": "cpm_report_insufficient_balance_pdf", "actionName": "post"}, {"permissionId": 368, "subPath": "/cpm/report/insufficient-balance/export/excel", "permissionCode": "cpm_report_insufficient_balance_excel", "actionName": "post"}, {"permissionId": 369, "subPath": "/cpm/report/pressure-exception/preview", "permissionCode": "cpm_report_pressure_exception_preview", "actionName": "post"}, {"permissionId": 370, "subPath": "/cpm/report/pressure-exception/export/pdf", "permissionCode": "cpm_report_pressure_exception_pdf", "actionName": "post"}, {"permissionId": 371, "subPath": "/cpm/report/pressure-exception/export/excel", "permissionCode": "cpm_report_pressure_exception_excel", "actionName": "post"}, {"permissionId": 372, "subPath": "/cpm/report/temperature-exception/preview", "permissionCode": "cpm_report_temperature_exception_preview", "actionName": "post"}, {"permissionId": 373, "subPath": "/cpm/report/temperature-exception/export/pdf", "permissionCode": "cpm_report_temperature_exception_pdf", "actionName": "post"}, {"permissionId": 374, "subPath": "/cpm/report/temperature-exception/export/excel", "permissionCode": "cpm_report_temperature_exception_excel", "actionName": "post"}, {"permissionId": 375, "subPath": "/cpm/report/directReading-exception/preview", "permissionCode": "cpm_report_directreading_exception_preview", "actionName": "post"}, {"permissionId": 376, "subPath": "/cpm/report/directReading-exception/export/pdf", "permissionCode": "cpm_report_directreading_exception_pdf", "actionName": "post"}, {"permissionId": 377, "subPath": "/cpm/report/directReading-exception/export/excel", "permissionCode": "cpm_report_directreading_exception_excel", "actionName": "post"}, {"permissionId": 378, "subPath": "/cpm/report/case-tamper/preview", "permissionCode": "cpm_report_case_tamper_preview", "actionName": "post"}, {"permissionId": 379, "subPath": "/cpm/report/case-tamper/export/pdf", "permissionCode": "cpm_report_case_tamper_pdf", "actionName": "post"}, {"permissionId": 380, "subPath": "/cpm/report/case-tamper/export/excel", "permissionCode": "cpm_report_case_tamper_excel", "actionName": "post"}, {"permissionId": 381, "subPath": "/cpm/report/flow-exception/preview", "permissionCode": "cpm_report_flow_exception_preview", "actionName": "post"}, {"permissionId": 382, "subPath": "/cpm/report/flow-exception/export/pdf", "permissionCode": "cpm_report_flow_exception_pdf", "actionName": "post"}, {"permissionId": 383, "subPath": "/cpm/report/flow-exception/export/excel", "permissionCode": "cpm_report_flow_exception_excel", "actionName": "post"}, {"permissionId": 384, "subPath": "/cpm/report/valve-through-exception/preview", "permissionCode": "cpm_report_valve_through_exception_preview", "actionName": "post"}, {"permissionId": 385, "subPath": "/cpm/report/valve-through-exception/export/pdf", "permissionCode": "cpm_report_valve_through_exception_pdf", "actionName": "post"}, {"permissionId": 386, "subPath": "/cpm/report/valve-through-exception/export/excel", "permissionCode": "cpm_report_valve_through_exception_excel", "actionName": "post"}, {"permissionId": 387, "subPath": "/cpm/chart/meter-install-tendency", "permissionCode": "cpm_chart_meter_install_tendency", "actionName": "post"}, {"permissionId": 388, "subPath": "/cpm/chart/kpi-monitor", "permissionCode": "cpm_chart_kpi_monitor", "actionName": "post"}, {"permissionId": 389, "subPath": "/cpm/chart/month-open-account", "permissionCode": "cpm_chart_month_open_account", "actionName": "post"}, {"permissionId": 390, "subPath": "/etf/gas/month-usage-quantity/chart", "permissionCode": "etf_gas_month_usage_quantity_chart", "actionName": "post"}, {"permissionId": 391, "subPath": "/cpm/code/command-batch-info", "permissionCode": "cpm_code_command_batch_info", "actionName": "post"}, {"permissionId": 392, "subPath": "/cpm/code/web/batch/send", "permissionCode": "cpm_code_web_batch_send", "actionName": "post"}, {"permissionId": 393, "subPath": "/cpm/chart/today-income", "permissionCode": "cpm_chart_today_income", "actionName": "post"}, {"permissionId": 394, "subPath": "/adaptor/black-user/get-archives", "permissionCode": "adaptor_black_user_get_archives", "actionName": "post"}, {"permissionId": 395, "subPath": "/adaptor/black-user/add", "permissionCode": "adaptor_black_user_add", "actionName": "post"}, {"permissionId": 396, "subPath": "/adaptor/black-user/remove", "permissionCode": "adaptor_black_user_remove", "actionName": "post"}, {"permissionId": 397, "subPath": "/cpm/chart/ip-to-weather", "permissionCode": "cpm_chart_ip_to_weather", "actionName": "get"}]