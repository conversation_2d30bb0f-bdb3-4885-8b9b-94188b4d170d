import { ruleMaxLength } from '@/utils/rules'
export const getFormItems = function (_this) {
	const arr = [
		{
			type: 'slot',
			slotName: 'dividLine',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'span',
			label: '表卡编号',
			prop: 'currentArchivesIdentity',
			attrs: {
				col: 5,
			},
		},
		{
			type: 'span',
			label: '水表编号',
			prop: 'currentMeterNo',
			attrs: {
				col: 5,
			},
		},
		{
			type: 'span',
			label: '换表操作人员',
			prop: 'operatorName',
			attrs: {
				col: 7,
			},
		},
		{
			type: 'el-input',
			label: '当前水表指针',
			prop: 'meterReading',
			attrs: {
				col: 7,
				maxlength: 8,
			},
			events: {
				input: val => {
					console.log('input ==>', val)
					// 只允许输入数字，实时过滤非数字字符
					const numericValue = val.replace(/[^0-9]/g, '')
					// 限制最大值
					const maxValue = 99999999
					const finalValue = numericValue ? Math.min(parseInt(numericValue), maxValue).toString() : ''

					if (finalValue !== val) {
						this.formData.meterReading = finalValue
					}
				},
			},
		},
		{
			type: 'slot',
			slotName: 'dividLine',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'slot',
			label: '新水表信息',
			slotName: 'newMeterInfo',
			attrs: {
				col: 24,
				className: 'title',
			},
		},
		{
			type: 'el-input',
			label: '水表编号',
			prop: 'meterNo',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '水库仓库编号',
			prop: 'meterWarehouseCode',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-select',
			label: '水表类型',
			prop: 'meterTypeId',
			options: [],
			attrs: {
				col: 8,
			},
			events: {
				change: value => {
					const meterTypeObj = _this.formItems.find(item => item.prop === 'meterTypeId')
					const obj = meterTypeObj.options.find(item => item.value === value)
					if (obj) {
						_this.formData.manufacturerName = obj.manufacturerName
						_this.formData.ranges = obj.meterRange
						_this.formData.useYears = obj.useYears
						_this.formAttrs.rules.meterNo = [
							{ required: true, message: '必填', trigger: 'blur' },
							{
								pattern: obj.ruleCode,
								message: '格式不正确',
								trigger: '',
							},
							ruleMaxLength(32),
						]
					} else {
						_this.formData.manufacturerName = ''
						_this.formData.ranges = ''
						_this.formData.useYears = ''
					}
				},
			},
		},
		{
			type: 'el-input',
			label: '水表厂商',
			prop: 'manufacturerName',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '防盗编号',
			prop: 'antiTheftCode',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '量程',
			prop: 'ranges',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '水表型号',
			prop: 'meterModel',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '口径',
			prop: 'caliber',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-date-picker',
			label: '装表时间',
			prop: 'installationDate',
			attrs: {
				col: 8,
				type: 'date',
				placeholder: '选择日期',
				valueFormat: 'yyyy-MM-dd',
			},
		},
		{
			type: 'el-input',
			label: '初始指针数',
			prop: 'startMeterReading',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '水表标号',
			prop: 'baseMeterNo',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '服役年限',
			prop: 'useYears',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
	]
	return arr
}
