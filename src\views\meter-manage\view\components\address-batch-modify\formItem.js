export function getFormItems(_this) {
	const region = [
		{
			type: 'el-select',
			label: '区/县',
			prop: 'regionCode',
			options: [],
			attrs: {
				col: 12,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'regionCode'),
			},
		},
		{
			type: 'el-select',
			label: '街道/乡镇',
			prop: 'streetCode',
			options: [],
			attrs: {
				col: 12,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'streetCode'),
			},
		},
		{
			type: 'el-select',
			label: '小区/村庄',
			prop: 'communityCode',
			options: [],
			attrs: {
				col: 12,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'communityCode'),
			},
		},
	]
	const desc = [
		{
			type: 'el-input',
			label: '替换文字',
			prop: 'oldText',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '新文字',
			prop: 'newText',
			attrs: {
				col: 12,
			},
		},
	]
	return {
		region,
		desc,
	}
}
