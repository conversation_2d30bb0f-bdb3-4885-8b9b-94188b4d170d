export const getFormItems = function (_this) {
	return [
		{
			type: 'el-select',
			label: '价格名称',
			prop: 'priceId',
			options: [],
			events: {
				change: _this.changePrice,
			},
		},
		{
			type: 'el-input',
			label: '计费类型',
			prop: 'billingTypeName',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '用水性质',
			prop: 'natureName',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '生效日期',
			prop: 'effectiveTime',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '价格信息',
			prop: 'priceInfo',
			attrs: {
				type: 'textarea',
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '附加费',
			prop: 'bilItemList',
			attrs: {
				type: 'textarea',
				disabled: true,
			},
		},
		{
			type: 'el-radio',
			label: '是否重置余量',
			prop: 'isReset',
			options: [
				{
					label: '是',
					value: '0',
				},
				{
					label: '否',
					value: '1',
				},
			],
			hide: true,
		},
	]
}
