<template>
	<div class="page-layout">
		<div class="page-left">
			<GcModelHeader title="角色列表" :icon="require('@/assets/images/icon/title-list.png')">
				<template v-slot:right>
					<div class="operate-group">
						<span
							v-has="'v1_tos_role_create'"
							title="新增角色"
							class="iconfontCis icon-add-role"
							@click="handleCreate"
						></span>
					</div>
				</template>
			</GcModelHeader>
			<div class="left-list" v-loading="loading">
				<vue-scroll :ops="{ bar: { background: '#e3e3e3' } }" v-if="roleList.length">
					<ul>
						<li
							:class="{ _active: selectRole.id === item.id }"
							v-for="item in roleList"
							:key="item.id"
							@click="selectTargetRole(item)"
						>
							<div class="l">
								<p>{{ item.noEdit ? '新角色' : item.name }}</p>
							</div>
							<div v-has="'v1_tos_role_delete'" class="r" v-show="!item.noEdit">
								<span class="iconfontCis icon-delete" title="删除角色" @click="deleteRole"></span>
							</div>
						</li>
					</ul>
				</vue-scroll>
				<GcEmpty v-else></GcEmpty>
			</div>
		</div>
		<div class="page-right">
			<GcModelHeader title="角色权限" :icon="require('@/assets/images/icon/title-jurisdiction.png')" />
			<RoleAuthority v-if="selectRole.id" @operate-success="getRoleList" :selectRole="selectRole" />
			<GcEmpty v-else></GcEmpty>
		</div>
	</div>
</template>

<script>
import { apiRoleList, apiDeleteRole } from '@/api/organizationManage.api.js'
import identity from '@/mixin/identity.js'
import RoleAuthority from './components/RoleAuthority.vue'

export default {
	mixins: [identity],
	components: { RoleAuthority },
	data() {
		return {
			loading: false,
			roleList: [],
			selectRole: {},
		}
	},
	created() {
		this.getRoleList()
	},
	methods: {
		async getRoleList() {
			try {
				const { data } = await apiRoleList({
					paged: false,
					tid: this.tenantId,
				})
				this.roleList = data
				if (this.roleList.length) {
					this.selectTargetRole(this.roleList[0])
				}
			} catch (error) {
				console.log(error)
			}
		},
		selectTargetRole(roleItem) {
			if (this.roleList.find(o => o.noEdit)) {
				this.roleList.splice(this.roleList.length - 1, 1)
			}
			this.selectRole = roleItem
		},
		deleteRole() {
			this.$confirm('删除后数据将无法恢复', '确定删除角色吗？', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(() => {
				apiDeleteRole({
					id: this.selectRole.id,
					tid: this.selectRole.tenant_id,
				}).then(() => {
					this.$message({
						type: 'success',
						message: '删除角色成功',
					})
					this.getRoleList()
				})
			})
		},
		handleCreate() {
			if (this.roleList.find(o => o.noEdit)) {
				this.roleList.splice(this.roleList.length - 1, 1)
			}
			const createRoleItem = {
				noEdit: true,
				id: 999999,
				copyId: 0,
				tenant_id: JSON.parse(localStorage.getItem('ui_Tenant'))?.value.id || '',
			}
			this.roleList.push(createRoleItem)
			this.selectRole = createRoleItem
		},
	},
}
</script>

<style lang="scss" scoped>
.page-left,
.page-right {
	padding-top: 0;
	.model-header {
		padding: 0;
	}
}
.page-left {
	flex: 0 0 380px;
	padding-top: 0;
	.model-header {
		padding: 0;
	}
	.operate-group {
		line-height: 60px;
		span {
			color: #2f87fe;
			margin-left: 12px;
			cursor: pointer;
		}
	}
	.left-list {
		height: calc(100% - 60px);
		box-sizing: border-box;
		padding: 10px 0 0;
		ul {
			li {
				line-height: 40px;
				cursor: pointer;
				display: flex;
				justify-content: space-between;
				padding: 0 20px 0 40px;
				&._active {
					background: #dce8ff;
				}
				.l {
					display: flex;
					align-items: center;
					p {
						font-size: 14px;
						color: #4e4e4e;
						letter-spacing: 1px;
					}
				}
				.r {
					span {
						color: #2f87fe;
						font-size: 14px;
						margin-left: 12px;
						cursor: pointer;
					}
				}
			}
		}
	}
}
</style>
