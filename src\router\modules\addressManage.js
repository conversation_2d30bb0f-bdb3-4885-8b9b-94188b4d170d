import Layout from '@/layout'

export default [
	{
		path: '/addressManage',
		name: 'AddressManage',
		component: Layout,
		redirect: '/addressManage/summaryAddressManage',
		meta: {
			title: '地址',
			icon: 'icon-cis_yj_dizhi',
			permissions: [
				'cpm_area_queryRecordPage',
				'cpm_region_queryRecordPage',
				'cpm_area_queryStreetRecordPage',
				'cpm_newAddress_queryAddressRecordPage',
				'cpm_area_queryNeighbourhood',
				'cpm_area_queryBuilding',
				'cpm_area_addBuilding',
				'cpm_area_batchAddBuilding',
				'cpm_area_batchDeleteBuilding',
				'cpm_area_deleteBuilding',
				'cpm_area_updateBuilding',
				'cpm_newAddress_queryAddressPage',
				'cpm_newAddress_updateAddress',
				'cpm_newAddress_batchCreateAddress',
			],
		},
		children: [
			{
				path: 'summaryAddressManage',
				name: 'SummaryAddressManage',
				component: () => import('@/views/address-manage/summary-address-manage/index.vue'),
				meta: {
					title: '地址管理',
					keepAlive: true,
					icon: 'icon-cis_ej_dizhiguanli',
					permissions: ['cpm_area_queryNeighbourhood'],
				},
			},
			{
				path: 'operateRecord',
				name: 'OperateRecord',
				component: () => import('@/views/address-manage/operate-record/index.vue'),
				meta: {
					title: '操作记录',
					keepAlive: true,
					icon: 'icon-erji-xiaoquguanli',
					permissions: [
						'cpm_area_queryRecordPage',
						'cpm_region_queryRecordPage',
						'cpm_area_queryStreetRecordPage',
						'cpm_newAddress_queryAddressRecordPage',
					],
				},
				hidden: true,
			},
			{
				path: 'buildingMaintenance',
				name: 'BuildingMaintenance',
				component: () => import('@/views/address-manage/building-maintenance/index.vue'),
				meta: {
					title: '楼栋维护',
					keepAlive: true,
					icon: 'icon-erji-xiaoquguanli',
					permissions: [
						'cpm_area_queryBuilding',
						'cpm_area_addBuilding',
						'cpm_area_batchAddBuilding',
						'cpm_area_batchDeleteBuilding',
						'cpm_area_deleteBuilding',
						'cpm_area_updateBuilding',
					],
				},
				hidden: true,
			},
			{
				path: 'batchAddressList',
				name: 'BatchAddressList',
				component: () => import('@/views/address-manage/batch-address-list/index.vue'),
				meta: {
					title: '地址列表',
					keepAlive: true,
					icon: 'icon-cis_ej_dizhiliebiao',
					permissions: ['cpm_newAddress_queryAddressPage', 'cpm_newAddress_updateAddress'],
				},
			},
			{
				path: 'batchAddressCreate',
				name: 'BatchAddressCreate',
				component: () => import('@/views/address-manage/batch-address-create/index.vue'),
				meta: {
					title: '地址批量创建',
					keepAlive: true,
					icon: 'icon-cis_ej_dizhipiliang',
					permissions: ['cpm_newAddress_batchCreateAddress'],
				},
			},
		],
	},
]
