import service from './request'
import { CPM, PAYMENT } from '@/consts/moduleNames'

//水表类型
export function apiGetMeterType(params) {
	return service({
		url: `${CPM}/device-type/tenant/meter-type`,
		method: 'GET',
		params,
	})
}
// 获取包含禁用价格
export const apiGetPriceList_all = params => {
	return service({
		url: `${CPM}/prices/list-price`,
		method: 'get',
		params,
	})
}

//获取生效的价格
export const apiEffectivePrice = params => {
	return service({
		url: `${CPM}/prices/effective`,
		method: 'GET',
		params,
	})
}

// 新增建档
export const apiArchives = data => {
	return service({
		url: `${CPM}/archives/add`,
		method: 'POST',
		data,
	})
}
// 企业建档
export const apiArchivesAdd2 = data => {
	return service({
		url: `${CPM}/archives/add2`,
		method: 'POST',
		data,
	})
}
// 更新建档
export const apiUpdateArchives = data => {
	return service({
		url: `${CPM}/archives`,
		method: 'PUT',
		data,
	})
}
// 表卡修改
export const apiArchivesUpdate = data => {
	return service({
		url: `${CPM}/archives/update`,
		method: 'PUT',
		data,
	})
}
// 表卡修改2
export const apiArchivesUpdate1 = data => {
	return service({
		url: `${CPM}/archives/update1`,
		method: 'PUT',
		data,
	})
}

// 查询居民表卡
export const apiGetMeterCard = data => {
	return service({
		url: `${CPM}/archives/list`,
		method: 'POST',
		data,
	})
}
// 查询企业表卡
export const apiGetMeterCard2 = data => {
	return service({
		url: `${CPM}/archives/list2`,
		method: 'POST',
		data,
	})
}
//  居民表卡导出
export function apiExportArchivesList(data) {
	return service({
		url: `${CPM}/archives/export/archive-list-excel`,
		method: 'POST',
		data,
		responseType: 'blob',
	})
}
// 居民表卡编辑列导出
export function apiExportEditArchivesList() {
	return service({
		url: `${CPM}/archives/export/archive-list-edit-excel`,
		method: 'POST',
		data,
		responseType: 'blob',
	})
}
// 企业表卡导出
export function apiExport2ArchivesList(data) {
	return service({
		url: `${CPM}/archives/export/archive-list-excel2`,
		method: 'POST',
		data,
		responseType: 'blob',
	})
}

// 表卡详情查询
export const apiGetArchivesDetail = params => {
	return service({
		url: `${CPM}/archives/detail`,
		method: 'GET',
		params,
	})
}
// 表卡详情查询2
export const apiGetArchivesDetail5 = params => {
	return service({
		url: `${CPM}/archives/detail5`,
		method: 'GET',
		params,
	})
}
// 虚表拆分：表卡详情查询
export const apiGetArchivesDetail2 = params => {
	return service({
		url: `${CPM}/archives/detail2`,
		method: 'GET',
		params,
	})
}
// 册本视图 表卡打印
export const apiGetArchivesDetail3 = params => {
	return service({
		url: `${CPM}/archives/detail3`,
		method: 'GET',
		params,
	})
}

// 册本视图 表卡批量打印
export const apiGetArchivesDetailList3 = data => {
	return service({
		url: `${CPM}/archives/detail-list`,
		method: 'POST',
		data,
	})
}

// 表卡视图 表卡打印
export const apiGetArchivesDetail4 = params => {
	return service({
		url: `${CPM}/archives/detail4`,
		method: 'GET',
		params,
	})
}
// 企业表卡视图 表卡打印
export const apiGetArchivesDetail6 = params => {
	return service({
		url: `${CPM}/archives/detail6`,
		method: 'GET',
		params,
	})
}

// 提交拆分虚表
export const apiUpdateVirtualRecords = data => {
	return service({
		url: `${CPM}/archives/update-virtual-records`,
		method: 'POST',
		data,
	})
}

// 查询虚表列表
export const apiGetVirtualArchivesList = data => {
	return service({
		url: `${CPM}/archives/virtual-archives-list`,
		method: 'POST',
		data,
	})
}
// 查询虚表详情
export const apiGetVirtualArchivesDetail = params => {
	return service({
		url: `${CPM}/archives/virtual-archives`,
		method: 'GET',
		params,
	})
}
// 查询虚表详情2
export const apiGetVirtualArchivesDetail2 = params => {
	return service({
		url: `${CPM}/archives/virtual-archives2`,
		method: 'GET',
		params,
	})
}

// 转正常表
export const apiConvertNormalArchives = params => {
	return service({
		url: `${CPM}/archives/convert-normal-archives`,
		method: 'GET',
		params,
	})
}
// 转正常表前调用
export const apiQueryConvertNormalArchives = params => {
	return service({
		url: `${CPM}/archives/query-convert-normal-archives`,
		method: 'GET',
		params,
	})
}

// 查询档案水表记录
export const apiGetMeterModifyRecords = data => {
	return service({
		url: `${CPM}/archives/meter-modify-records`,
		method: 'POST',
		data,
	})
}
// 查询档案水表记录
export const apiGetMeterModifyRecords3 = data => {
	return service({
		url: `${CPM}/archives/meter-modify-records3`,
		method: 'POST',
		data,
	})
}
//查询档案变更记录
export const apiGetModifyrecords = data => {
	return service({
		url: `${CPM}/archives/modifyrecords`,
		method: 'POST',
		data,
	})
}
//查询档案变更记录
export const apiGetModifyrecords2 = data => {
	return service({
		url: `${CPM}/archives/modifyrecords2`,
		method: 'POST',
		data,
	})
}
// 抄表记录列表查询
export const apiGetMeterReadingList = data => {
	return service({
		url: `${CPM}/meterReadingTask/archivesId-record-list`,
		method: 'POST',
		data,
	})
}
// 抄表记录列表查询
export const apiGetMeterReadingList3 = data => {
	return service({
		url: `${CPM}/meterReadingTask/archivesId-record-list3`,
		method: 'POST',
		data,
	})
}
// 档案缴费记录
export const apiGetBillPayRecordList = data => {
	return service({
		url: `${CPM}/archives/billPay-record-list`,
		method: 'POST',
		data,
	})
}
// 档案缴费记录
export const apiGetBillPayRecordList2 = data => {
	return service({
		url: `${CPM}/archives/billPay-record-list2`,
		method: 'POST',
		data,
	})
}
// 用户缴费记录
export const apiGetBillPayRecordListUser = data => {
	return service({
		url: `${CPM}/archives/billPay-record-list/user`,
		method: 'POST',
		data,
	})
}
// 预存记录
export const apiGetPayrecords = data => {
	return service({
		url: `${CPM}/archives/payrecords`,
		method: 'POST',
		data,
	})
}
// 档案账单记录
export const apiGetBillRecordList = data => {
	return service({
		url: `${CPM}/archives/bill-record-list`,
		method: 'POST',
		data,
	})
}
// 档案账单记录
export const apiGetBillRecordList1 = data => {
	return service({
		url: `${CPM}/archives/bill-record-list1`,
		method: 'POST',
		data,
	})
}
// 账单记录导出
export const apiBillRecordExportExcel = data => {
	return service({
		url: `${CPM}/archives/export/bill-record-list-excel`,
		method: 'POST',
		data,
		responseType: 'blob',
	})
}
// 开票记录
export const apiGetInvoiceRecordList = data => {
	return service({
		url: `${PAYMENT}/invoice/record-list1`,
		method: 'POST',
		data,
	})
}
// 开票记录
export const apiGetInvoiceRecordList3 = data => {
	return service({
		url: `${PAYMENT}/invoice/record-list3`,
		method: 'POST',
		data,
	})
}
// 查看开票信息
export function getInvoicePdf(params) {
	return service({
		url: `${PAYMENT}/invoice/pdf3`,
		method: 'GET',
		params,
	})
}
// 价格记录
export const apiGetHistoryPrice = params => {
	return service({
		url: `${CPM}/archives/history-price`,
		method: 'GET',
		params,
	})
}
// 价格记录
export const apiGetHistoryPrice1 = params => {
	return service({
		url: `${CPM}/archives/history-price1`,
		method: 'GET',
		params,
	})
}
// 档案变更记录
export const apiGetMobileModifyRecords = data => {
	return service({
		url: `${CPM}/archives/mobile-modify-records`,
		method: 'POST',
		data,
	})
}
// 档案变更记录
export const apiGetMobileModifyRecords1 = data => {
	return service({
		url: `${CPM}/archives/mobile-modify-records1`,
		method: 'POST',
		data,
	})
}
// 获取表卡编号
export const apiGetArchivesIdentity = data => {
	return service({
		url: `${CPM}/archives/get-archivesIdentity`,
		method: 'POST',
		data,
	})
}

// 获取企业编号
export const apiGetEnterpriseNumber = params => {
	return service({
		url: `${CPM}/archives/get-enterpriseNumber`,
		method: 'GET',
		params,
	})
}

// 获取表册内序号 待后端实现
export const apiGetRecordSeq = params => {
	return service({
		url: `${CPM}/archives/max-seq`,
		method: 'GET',
		params,
	})
}

// 停用档案记录
export const apiGetStopUsingList = data => {
	return service({
		url: `${CPM}/archives/stop-using-list`,
		method: 'POST',
		data,
	})
}
// 恢复用水
export function apiEnableArchives(data) {
	return service({
		url: `${CPM}/archives/enable`,
		method: 'PUT',
		data,
	})
}
// 停水表卡管理：恢复用水
export function apiEnableArchives2(data) {
	return service({
		url: `${CPM}/archives/enable2`,
		method: 'PUT',
		data,
	})
}
// 企业表卡视图：恢复用水
export function apiEnableArchives4(data) {
	return service({
		url: `${CPM}/archives/enable4`,
		method: 'PUT',
		data,
	})
}
// 停水
export function apiDisableArchives(data) {
	return service({
		url: `${CPM}/archives/disable`,
		method: 'PUT',
		data,
	})
}
// 停水
export function apiDisableArchives4(data) {
	return service({
		url: `${CPM}/archives/disable4`,
		method: 'PUT',
		data,
	})
}
// 销卡
export function apiArchivesClose(data) {
	return service({
		url: `${CPM}/archives/close`,
		method: 'PUT',
		data,
	})
}
// 销卡
export function apiArchivesClose1(data) {
	return service({
		url: `${CPM}/archives/close1`,
		method: 'PUT',
		data,
	})
}
// 销卡- 编辑虚表拆分使用
export function apiArchivesClose2(data) {
	return service({
		url: `${CPM}/archives/close2`,
		method: 'PUT',
		data,
	})
}
// cpm/archives/close2
// 销户档案记录
export const apiGetCancellationList = data => {
	return service({
		url: `${CPM}/archives/cancellation-list`,
		method: 'POST',
		data,
	})
}
// 表卡恢复
export function apiResumeArchives(data) {
	return service({
		url: `${CPM}/archives/resume-archives`,
		method: 'PUT',
		data,
	})
}
// 表卡恢复3
export function apiResumeArchives3(data) {
	return service({
		url: `${CPM}/archives/resume-archives3`,
		method: 'PUT',
		data,
	})
}

// 表卡恢复-编辑虚表模块使用
export function apiResumeArchives4(data) {
	return service({
		url: `${CPM}/archives/resume-archives4`,
		method: 'PUT',
		data,
	})
}

// 表卡转换记录
export const apiGetChangeModifyrecords = data => {
	return service({
		url: `${CPM}/archives/change-modifyrecords`,
		method: 'POST',
		data,
	})
}
// 导出表卡转换记录
export const apiExportChangeModifyRecordsExcel = data => {
	return service({
		url: `${CPM}/archives/export/change-modifyrecords-excel`,
		method: 'POST',
		data,
		responseType: 'blob',
	})
}
// 水量预警
export function apiGetWaterAmountWarning(data) {
	return service({
		url: `${CPM}/report/water-amount-warning/preview`,
		method: 'POST',
		data,
	})
}
// 水量预警导出
export function apiWaterAmountWarningExport(data) {
	return service({
		url: `${CPM}/report/water-amount-warning/export/excel`,
		method: 'POST',
		data,
		responseType: 'blob',
	})
}

//选择分表档案列表
export function apiGetDmaArchivesList(data) {
	return service({
		url: `${CPM}/dma-archives/list`,
		method: 'POST',
		data,
	})
}

// 保存总分关系记录
export function apiSaveSubArchivesRecord(data) {
	return service({
		url: `${CPM}/dma-archives/save-sub-archives-record`,
		method: 'POST',
		data,
	})
}

// 查询总分表关系记录
export function apiGetListSubArchivesRecord(data) {
	return service({
		url: `${CPM}/dma-archives/list-sub-archives-record`,
		method: 'POST',
		data,
	})
}
// 查询总分表关系记录
export function apiGetListSubArchivesRecord2(data) {
	return service({
		url: `${CPM}/dma-archives/list-sub-archives-record2`,
		method: 'POST',
		data,
	})
}
// 删除总分表关系
export function apiDeleteSubArchivesRecord(data) {
	return service({
		url: `${CPM}/dma-archives/delete-sub-archives-record/?recordId=${data}`,
		method: 'DELETE',
	})
}
// 查询DMA档案信息
export function apiGetDmaArchivesDetail(params) {
	return service({
		url: `${CPM}/dma-archives/detail`,
		method: 'GET',
		params,
	})
}
// DMA总分表管理
export function apiGetListDmaArchives(data) {
	return service({
		url: `${CPM}/dma-archives/list-dma-archives`,
		method: 'POST',
		data,
	})
}

// DMA档案建档|更新
export function apiCreateDmaArchives(data) {
	return service({
		url: `${CPM}/dma-archives`,
		method: 'POST',
		data,
	})
}

// 根据手机号或者用户名查询用户信息
export function apiGetUserInfoByPhoneOrUserName(data) {
	return service({
		url: `${CPM}/user/getUserInfoByPhoneOrUserName`,
		method: 'POST',
		data,
	})
}
// 居民表卡视图：过户
export function apiArchivesTransferUser(data) {
	return service({
		url: `${CPM}/archives/transfer-user`,
		method: 'PUT',
		data,
	})
}
// 企业表卡视图：过户
export function apiArchivesTransferUser1(data) {
	return service({
		url: `${CPM}/archives/transfer-user1`,
		method: 'PUT',
		data,
	})
}
// 表卡转换
export function apiArchivesTransferTablecard(data) {
	return service({
		url: `${CPM}/archives/transfer-tablecard`,
		method: 'PUT',
		data,
	})
}
// 表卡转换
export function apiArchivesTransferTablecard2(data) {
	return service({
		url: `${CPM}/archives/transfer-tablecard2`,
		method: 'PUT',
		data,
	})
}
// 拆表
export function apiArchivesRemoveMeter(data) {
	return service({
		url: `${CPM}/archives/remove-meter`,
		method: 'PUT',
		data,
	})
}
// 拆表
export function apiArchivesRemoveMeter3(data) {
	return service({
		url: `${CPM}/archives/remove-meter3`,
		method: 'PUT',
		data,
	})
}
// 换表
export function apiArchivesSwitchMeter(data) {
	return service({
		url: `${CPM}/archives/switching-meter`,
		method: 'PUT',
		data,
	})
}
// 换表
export function apiArchivesSwitchMeter1(data) {
	return service({
		url: `${CPM}/archives/switching-meter1`,
		method: 'PUT',
		data,
	})
}
// 装表
export function apiArchivesIntallMeter(data) {
	return service({
		url: `${CPM}/archives/intallMeter`,
		method: 'POST',
		data,
	})
}
// 装表
export function apiArchivesIntallMeter1(data) {
	return service({
		url: `${CPM}/archives/intallMeter1`,
		method: 'POST',
		data,
	})
}

// 根据水表编号查询水表信息
export function apiGetMeterInfoByMeterNo(params) {
	return service({
		url: `${CPM}/meter/waitInstallMeterList`,
		method: 'GET',
		params,
	})
}

// 换价
export function apiArchivesChangePrice(data) {
	return service({
		url: `${CPM}/archives/change-price`,
		method: 'PUT',
		data,
	})
}
// 换价1
export function apiArchivesChangePrice1(data) {
	return service({
		url: `${CPM}/archives/change-price1`,
		method: 'PUT',
		data,
	})
}
// 小区接管情况
export function apiGetRecordArchivesList(data) {
	return service({
		url: `${CPM}/address-areas/record-archives-list`,
		method: 'POST',
		data,
	})
}
// 下载小区基本信息
export function apiExportArea(data) {
	return service({
		url: `${CPM}/report/area/export/excel`,
		method: 'POST',
		data,
		responseType: 'blob',
	})
}
// 下载用户表卡
export function apiExportAreaArchives(data) {
	return service({
		url: `${CPM}/report/area/archives/export/excel`,
		method: 'POST',
		data,
		responseType: 'blob',
	})
}

// 小区接管记录
export function apiGetRecordTakeOver(data) {
	return service({
		url: `${CPM}/address-areas/record-take-over`,
		method: 'POST',
		data,
	})
}
// 小区接管登记
export function apiRecordTakeOver(data) {
	return service({
		url: `${CPM}/address-areas/take-over`,
		method: 'POST',
		data,
	})
}
// 查询小区详情
export function apiGetAddressAreasInfo(params) {
	return service({
		url: `${CPM}/address-areas/info`,
		method: 'GET',
		params,
	})
}
// 小区总表信息
export function apiGetSummaryArchives(data) {
	return service({
		url: `${CPM}/archives/summary-archives`,
		method: 'POST',
		data,
	})
}
// 小区用户表卡信息
export function apiGetBuildingArchives(data) {
	return service({
		url: `${CPM}/archives/building-archives`,
		method: 'POST',
		data,
	})
}
// 小区表卡首抄记录列表
export function apiGetReadingFirstRecord(data) {
	return service({
		url: `${CPM}/archives/reading-first-record`,
		method: 'POST',
		data,
	})
}
// 小区指针修正
export function apiSaveOrUpdateReadingFirstRecords(data) {
	return service({
		url: `${CPM}/archives/saveOrUpdate-reading-first-record`,
		method: 'POST',
		data,
	})
}
//搜索详细地址
export function apiGetAddressBydb(params) {
	return service({
		url: `${CPM}/archives/by-address-db`,
		method: 'GET',
		params,
	})
}

// 查询联系人列表
export function apiGetContactList(params) {
	return service({
		url: `${CPM}/archives/contact/list`,
		method: 'GET',
		params,
	})
}
// 新增联系人
export function apiAddContact(data) {
	return service({
		url: `${CPM}/archives/contact/add`,
		method: 'POST',
		data,
	})
}
// 修改联系人
export function apiModifyContact(data) {
	return service({
		url: `${CPM}/archives/contact/modify`,
		method: 'POST',
		data,
	})
}
// 删除联系人
export function apiDeleteContact(params) {
	return service({
		url: `${CPM}/archives/contact/remove`,
		method: 'GET',
		params,
	})
}

// 批量修改地址信息
export function apiBatchUpdateAddress(data) {
	return service({
		url: `${CPM}/archives/modify/addressInfo`,
		method: 'POST',
		data,
	})
}
// 批量修改地址信息2
export function apiBatchUpdateAddress2(data) {
	return service({
		url: `${CPM}/archives/modify/addressInfo2`,
		method: 'POST',
		data,
	})
}

// 表卡编号校验
export function apiVerifyArchives(params) {
	return service({
		url: `${CPM}/archives/verify/archivesIdentity`,
		method: 'GET',
		params,
	})
}
