<template>
	<gc-el-dialog
		:show="isShow"
		title="检修登记"
		custom-top="120px"
		width="600px"
		@open="handleOpen"
		@close="handleClose"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { ruleMaxLength } from '@/utils/rules'
import { repairMeter } from '@/api/waterMeter.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		data: {
			type: Object,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				meterNo: '',

				operatorDate: '',
				repairResult: '',
				operatorPerson: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '水表编号',
					prop: 'meterNo',
					attrs: {
						col: 24,
						disabled: true,
						placeholder: '',
					},
				},
				{
					type: 'el-date-picker',
					label: '检修时间',
					prop: 'operatorDate',
					attrs: {
						col: 24,
						valueFormat: 'yyyy-MM-dd',
						placeholder: '请选择检修时间',
					},
				},
				{
					type: 'el-select',
					label: '检修结果',
					prop: 'repairResult',
					options: [
						{
							label: '合格',
							value: true,
						},
						{
							label: '不合格',
							value: false,
						},
					],
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择检修结果',
					},
				},
				{
					type: 'el-input',
					label: '操作人员',
					prop: 'operatorPerson',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入操作人员',
					},
				},
			],
			formAttrs: {
				labelWidth: '110px',
				labelPosition: 'top',
				rules: {
					operatorDate: [{ required: true, message: '请选择报废时间', trigger: 'change' }],
					repairResult: [{ required: true, message: '请选择检修结果', trigger: 'change' }],
					operatorPerson: [ruleMaxLength(30, '操作人员')],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				await repairMeter({
					meterId: this.data.meterId,
					...this.formData,
				})
				this.$message.success(`检修成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleOpen() {
			this.formData.meterNo = this.data?.meterNo ?? ''
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.el-form-item--small.el-form-item {
		margin-bottom: 12px;
	}
}
</style>
