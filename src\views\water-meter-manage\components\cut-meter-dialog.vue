<template>
	<gc-el-dialog
		:show="isShow"
		:title="typeText"
		custom-top="120px"
		width="600px"
		@open="handleOpen"
		@close="handleClose"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { ruleMaxLength } from '@/utils/rules'
import { batchDisableMeter, disableMeter } from '@/api/waterMeter.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		isBatch: {
			type: Boolean,
			default: false,
		},
		// 批量：meterIds集合
		// 非批量：当前水表信息对象
		data: {
			type: [Array, Object],
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		typeText() {
			return this.isBatch ? '批量停水' : '停水登记'
		},
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
				meterNo: '',
				disableDate: '',
				disablePerson: '',
				disableReason: '',
			},
			formItems: [
				{
					hide: this.isBatch,
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						col: 24,
						disabled: true,
					},
				},
				{
					hide: this.isBatch,
					type: 'el-input',
					label: '水表编号',
					prop: 'meterNo',
					attrs: {
						col: 24,
						disabled: true,
					},
				},
				{
					type: 'el-date-picker',
					label: '停水时间',
					prop: 'disableDate',
					attrs: {
						col: 24,
						valueFormat: 'yyyy-MM-dd',
						placeholder: '请选择停水时间',
					},
				},
				{
					type: 'el-input',
					label: '停水操作人',
					prop: 'disablePerson',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入停水操作人',
					},
				},
				{
					type: 'el-input',
					label: '停水原因',
					prop: 'disableReason',
					attrs: {
						col: 24,
						type: 'textarea',
						clearable: true,
						placeholder: '请输入停水原因',
						maxlength: '64',
						showWordLimit: true,
						autosize: {
							minRows: 4,
							maxRows: 8,
						},
					},
				},
			],
			formAttrs: {
				labelWidth: '110px',
				labelPosition: 'top',
				rules: {
					disableDate: [{ required: true, message: '请选择停水时间', trigger: 'change' }],
					disablePerson: [
						{ required: true, message: '请输入停水操作人', trigger: 'blur' },
						ruleMaxLength(30, '操作人员'),
					],
					disableReason: [{ required: true, message: '请输入停水原因', trigger: 'blur' }],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				if (this.isBatch) {
					await batchDisableMeter({
						meterIds: this.data,
						...this.formData,
					})
				} else {
					await disableMeter({
						meterId: this.data.meterId,
						archivesId: this.data.archivesId,
						...this.formData,
					})
				}
				this.$message.success(`${this.typeText}成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleOpen() {
			if (this.isBatch) return
			this.formData.archivesIdentity = this.data?.archivesIdentity ?? ''
			this.formData.meterNo = this.data?.meterNo ?? ''
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}

::v-deep {
	.el-form-item--small.el-form-item {
		margin-bottom: 12px;
	}
}
</style>
