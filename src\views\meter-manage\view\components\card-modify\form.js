export function getFormItems(_this) {
	const resident = [
		{
			type: 'el-input',
			label: '表卡编号',
			prop: 'archivesIdentity',
			attrs: {
				col: 12,
				disabled: true,
			},
		},
		{
			type: 'el-radio',
			label: '小区总表',
			prop: 'summaryArchives',
			options: [
				{ label: '否', value: 0 },
				{ label: '是', value: 1 },
			],
			attrs: {
				col: 12,
			},
			events: {
				input: value => {
					_this.changeSummaryArchives(value)
				},
			},
		},
		{
			type: 'el-input',
			label: '账号',
			prop: 'accountNumber',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-select',
			label: '区/县',
			prop: 'regionCode',
			options: [],
			attrs: {
				col: 12,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'regionCode'),
			},
		},
		{
			type: 'el-select',
			label: '街道/乡镇',
			prop: 'streetCode',
			options: [],
			attrs: {
				col: 12,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'streetCode'),
			},
		},
		{
			type: 'el-select',
			label: '小区/村庄',
			prop: 'communityCode',
			options: [],
			attrs: {
				col: 12,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'communityCode'),
			},
		},
		{
			type: 'el-select',
			label: '楼栋',
			prop: 'buildingCode',
			options: [],
			attrs: {
				col: 12,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'buildingCode'),
			},
		},
		{
			type: 'slot',
			slotName: 'fullAddress',
			prop: 'addressName',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-input',
			label: '自来水编号',
			prop: 'tapWaterNo',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '房屋建设年代',
			prop: 'houseYear',
			attrs: {
				placeholder: '请输入',
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '层数',
			prop: 'floorNum',
			attrs: {
				placeholder: '请输入',
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '压力区',
			prop: 'pressureZone',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: 'GIS编号',
			prop: 'gisCode',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '管网编号',
			prop: 'pipeNetworkCode',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '户数',
			prop: 'households',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '人口数',
			prop: 'resiPopulation',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '产权人名称',
			prop: 'propertyOwner',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '合同编号',
			prop: 'contractNum',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'slot',
			slotName: 'purchaseContractUrl',
			label: '购房合同',
			prop: 'purchaseContractUrl',
			attrs: {
				col: 8,
			},
		},
	]
	const company = [
		{
			type: 'el-input',
			label: '表卡编号',
			prop: 'archivesIdentity',
			attrs: {
				col: 12,
				disabled: true,
			},
		},
		{
			type: 'el-radio',
			label: '小区总表',
			prop: 'summaryArchives',
			options: [
				{ label: '否', value: 0 },
				{ label: '是', value: 1 },
			],
			attrs: {
				col: 12,
			},
			events: {
				input: value => {
					_this.changeSummaryArchives(value)
				},
			},
		},
		{
			type: 'el-input',
			label: '账号',
			prop: 'accountNumber',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-select',
			label: '区/县',
			prop: 'regionCode',
			options: [],
			attrs: {
				col: 12,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'regionCode'),
			},
		},
		{
			type: 'el-select',
			label: '街道/乡镇',
			prop: 'streetCode',
			options: [],
			attrs: {
				col: 12,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'streetCode'),
			},
		},
		{
			type: 'el-select',
			label: '小区/村庄',
			prop: 'communityCode',
			options: [],
			attrs: {
				col: 12,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'communityCode'),
			},
		},
		{
			type: 'el-select',
			label: '楼栋',
			prop: 'buildingCode',
			options: [],
			attrs: {
				col: 12,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'buildingCode'),
			},
		},
		{
			type: 'slot',
			prop: 'addressName',
			slotName: 'fullAddress',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-input',
			label: '自来水编号',
			prop: 'tapWaterNo',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '房屋建设年代',
			prop: 'houseYear',
			attrs: {
				placeholder: '请输入',
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '层数',
			prop: 'floorNum',
			attrs: {
				placeholder: '请输入',
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '压力区',
			prop: 'pressureZone',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: 'GIS编号',
			prop: 'gisCode',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '管网编号',
			prop: 'pipeNetworkCode',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '站点号',
			prop: 'stationNo',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '合同编号',
			prop: 'contractNum',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'slot',
			slotName: 'purchaseContractUrl',
			label: '购房合同',
			prop: 'purchaseContractUrl',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'slot',
			slotName: 'businessLicenseUrl',
			label: '营业执照合同',
			prop: 'businessLicenseUrl',
			attrs: {
				col: 12,
			},
		},
	]

	return {
		company,
		resident,
	}
}
