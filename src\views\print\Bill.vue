<template>
	<gc-el-dialog
		:show.sync="innerVisible"
		title="打印票据"
		width="480px"
		@close="resetForm"
		class="bill"
		@open="getPrinterList"
	>
		<el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="form-layout">
			<el-form-item label="选择打印机" prop="printId" class="adjust-length">
				<el-select v-model="ruleForm.printId" filterable placeholder="请选择打印机">
					<el-option label="默认打印机" value=""></el-option>
					<el-option
						v-for="item in printList"
						:key="item.value"
						:label="item.name"
						:value="item.value"
					></el-option>
				</el-select>
			</el-form-item>
		</el-form>
		<template #footer>
			<button class="gc-button gc-button-two" @click="preview">预览</button>
			<button class="gc-button gc-button-one" @click="print">打印</button>
		</template>
	</gc-el-dialog>
</template>

<script>
import resetData from './resetData'
import { getLodop } from './lodop/LodopFuncs'
import { apiBill } from '@/api/print.api'
import { printBill, previewBill } from './lodop/lodop.js'

export default {
	name: 'Bill',
	mixins: [resetData],
	props: {
		// 当前选中的元素的具体信息
		selectedItem: {
			type: [Object, String],
			default: '',
		},
		// 是否是补打票据 入口为缴费明细则=2 入口为缴费=1
		reprintFlag: {
			type: [Number, String],
			default: 2,
		},
	},
	data() {
		return {
			ruleForm: {
				printId: '', //打印机id
			},
			rules: {},
			printList: [],
		}
	},
	created() {},
	mounted() {},
	methods: {
		// 获取打印机列表
		getPrinterList() {
			let LODOP = getLodop()
			if (!LODOP) return
			let count = 0
			try {
				count = LODOP.GET_PRINTER_COUNT() //获取本机打印机数量
				let middleArr = []
				for (let i = 0; i < count; i++) {
					let name = LODOP.GET_PRINTER_NAME(i) //获取打印机的名字
					middleArr.push({
						value: i,
						name: name,
					})
				}
				this.printList = middleArr
			} catch {
				this.$message.error('打印机程序出错，请刷新重试')
				this.$nextTick(() => {
					this.innerVisible = false
				})
			}
		},
		//预览
		preview() {
			this.handleRequest('preview')
		},
		// 打印
		print() {
			this.handleRequest('print')
		},
		// 处理数据请求
		handleRequest(flag) {
			//flag-预览(preview)还是打印(print)
			this.$listeners.controlLoading(true)
			let params = {}
			params['businessNo'] =
				this.selectedItem.costOperationType == 3 || this.selectedItem.costOperationType == 4
					? 'reissue'
					: 'charge'
			params['costRecordId'] = this.selectedItem.costRecordId
			params['reprintFlag'] = this.reprintFlag
			apiBill(params)
				.then(res => {
					this.$listeners.controlLoading(false)
					let afterChargeReceiptData = {}
					let afterTemplateStyle = ''
					try {
						let middleChargeReceiptData = res.chargeReceiptData || []
						let middleTemplateStyle = res.templateStyle || ''
						if (middleChargeReceiptData.length > 0) {
							middleChargeReceiptData = middleChargeReceiptData
								.replace('{', '')
								.replace('}', '')
								.split(', ')
							middleChargeReceiptData.forEach(item => {
								// 对接发票平台，二维码特殊处理
								if (item.indexOf('qr_code=') > -1) {
									afterChargeReceiptData['qr_code'] = item.slice(8)
								} else {
									let arr = item.split('=')
									afterChargeReceiptData[arr[0]] = arr[1]
								}
							})
						}
						if (middleTemplateStyle.length > 0) {
							afterTemplateStyle = middleTemplateStyle.replace(
								"if(type == 'pre')",
								"LODOP.SET_PRINTER_INDEX(printId);if(type == 'pre')",
							)
						}
						if (flag === 'preview') {
							// 预览
							previewBill(afterChargeReceiptData, this.ruleForm.printId, afterTemplateStyle)
						} else {
							// 打印
							printBill(afterChargeReceiptData, this.ruleForm.printId, afterTemplateStyle)
							this.innerVisible = false
						}
					} catch {
						this.$message.error('数据有问题，无法转化成正常数据')
					}
				})
				.catch(() => {
					this.$listeners.controlLoading(false)
				})
		},
	},
}
</script>
<style lang="scss" scoped>
@import './formLayout.scss';
.bill {
	button + button {
		margin-left: 10px;
	}
}
</style>
