<template>
	<div class="wrapper">
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			@current-page-change="handlePageChange"
		>
			<template #index="{ $index }">
				<span>{{ $index + 1 }}</span>
			</template>
			<template #deal="{ row }">
				<el-button
					v-has="'cpm_businessHall_queryBusinessHallList'"
					type="text"
					size="medium"
					@click="handleAdjustInfo(row)"
				>
					查看基本信息
				</el-button>
				<el-button
					v-has="'cpm_businessHall_updateBusinessHallCode'"
					type="text"
					size="medium"
					@click="handleAdjustCode(row)"
				>
					编码调整
				</el-button>
				<el-button
					v-has="'cpm_businessHall_updateBusinessHallDigits'"
					type="text"
					size="medium"
					@click="handleAdjustDigit(row)"
				>
					册本位数调整
				</el-button>
				<el-button
					v-has="'cpm_businessHall_updateAccountOpeningDate'"
					type="text"
					size="medium"
					@click="handleAdjustIncvoiceDate(row)"
				>
					推送缴费日管理
				</el-button>
				<el-button
					v-has="'cpm_businessHall_updateAccountOpeningDate'"
					type="text"
					size="medium"
					@click="configUseMonitorParams(row)"
				>
					用水监控配置
				</el-button>
			</template>
		</GcTable>

		<!-- 查看、编辑基本信息弹窗 -->
		<AdjustInfoDialog ref="adjustInfoDialogRef" :show.sync="showAdjustInfo" @success="getList(1)" />
		<!-- 编码调整弹窗 -->
		<AdjustCodeDialog ref="adjustCodeDialogRef" :show.sync="showAdjustCode" @success="getList(1)" />
		<!-- 册本位数调整弹窗 -->
		<AdjustDigitDialog ref="adjustDigitDialogRef" :show.sync="showAdjustDigit" @success="getList(1)" />
		<!-- 开账日管理弹窗 -->
		<AdjustIncvoiceDateDialog
			ref="adjustIncvoiceDateDialogRef"
			:show.sync="showAdjustIncvoiceDate"
			@success="getList(1)"
		/>
		<!-- 开账日管理弹窗 -->
		<UseMonitorParamsConfigDialog
			ref="UseMonitorParamsConfigDialogRef"
			:show.sync="showUseMonitorParamsDialog"
			@success="getList(1)"
		/>
	</div>
</template>

<script>
import AdjustInfoDialog from './components/AdjustInfoDialog.vue'
import AdjustCodeDialog from './components/AdjustCodeDialog.vue'
import AdjustDigitDialog from './components/AdjustDigitDialog.vue'
import AdjustIncvoiceDateDialog from './components/AdjustIncvoiceDateDialog.vue'
import UseMonitorParamsConfigDialog from './components/UseMonitorParamsConfigDialog.vue'
import { queryBusinessHallList } from '@/api/basicConfig.api'

export default {
	name: 'FilialeManage',
	components: {
		AdjustInfoDialog,
		AdjustCodeDialog,
		AdjustDigitDialog,
		AdjustIncvoiceDateDialog,
		UseMonitorParamsConfigDialog,
	},
	data() {
		return {
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'index',
					name: '序号',
					width: 50,
				},
				{
					key: 'orgName',
					name: '营业分公司名称',
					tooltip: true,
				},
				{
					key: 'businessAdress',
					name: '营业分公司地址',
					tooltip: true,
				},
				{
					key: 'businessCode',
					name: '营业分公司编码',
					tooltip: true,
				},
				{
					key: 'collectionBusinessCode',
					name: '营业分公司托收协议编码',
					tooltip: true,
				},
				{
					key: 'residentsDigits',
					name: '居民册本位数',
					tooltip: true,
				},
				{
					key: 'enterpriseDigits',
					name: '企业册本位数',
					tooltip: true,
				},
				{
					hide: !this.$has([
						'cpm_businessHall_queryBusinessHallList',
						'cpm_businessHall_updateBusinessHallCode',
						'cpm_businessHall_updateBusinessHallDigits',
						'cpm_businessHall_updateAccountOpeningDate',
					]),
					key: 'deal',
					name: '操作',
					width: 450,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 查看、编辑基本信息弹窗
			showAdjustInfo: false,
			// 编码调整弹窗
			showAdjustCode: false,
			// 册本位数调整弹窗
			showAdjustDigit: false,
			// 开账日管理弹窗
			showAdjustIncvoiceDate: false,
			showUseMonitorParamsDialog: false,
		}
	},

	created() {
		this.getList()
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.getList(1)
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await queryBusinessHallList({
					size,
					current,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},

		// 查看基本信息
		handleAdjustInfo(data) {
			this.$refs.adjustInfoDialogRef.setFormData(data)
			this.showAdjustInfo = true
		},
		// 编码调整弹窗
		handleAdjustCode(data) {
			this.$refs.adjustCodeDialogRef.setFormData(data)
			this.showAdjustCode = true
		},
		// 册本位数调整弹窗
		handleAdjustDigit(data) {
			this.$refs.adjustDigitDialogRef.setFormData(data)
			this.showAdjustDigit = true
		},
		// 开账日管理弹窗
		handleAdjustIncvoiceDate(data) {
			this.$refs.adjustIncvoiceDateDialogRef.setOrgCode(data.orgCode)
			this.showAdjustIncvoiceDate = true
		},
		configUseMonitorParams(data) {
			this.$refs.UseMonitorParamsConfigDialogRef.setFormData(data)
			this.showUseMonitorParamsDialog = true
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	padding: 20px;
}
</style>
