<template>
	<div class="page-layout">
		<div class="page-left">
			<GcModelHeader title="部门层级结构列表" :icon="require('@/assets/images/icon/title-structure.png')">
				<template #right>
					<el-button v-show="!showClearStaffParamsBtn" type="text" size="medium" @click="handleFilterStaff">
						<span class="filter-button">
							<img src="@/assets/images/icon/title-filter.svg" />
							<i>员工筛选</i>
						</span>
					</el-button>
					<el-button v-show="showClearStaffParamsBtn" type="primary" @click="clearStaffParams">
						退出筛选
					</el-button>
				</template>
			</GcModelHeader>
			<div class="tree-list" v-loading="treeLoading">
				<vue-scroll v-if="dataTree.length" :ops="{ bar: { background: '#e3e3e3' } }">
					<el-tree
						ref="departmentRef"
						:data="dataTree"
						default-expand-all
						highlight-current
						node-key="node_key"
						:props="{ label: 'name' }"
						:expand-on-click-node="false"
						@node-click="handleNodeClick"
					>
						<div class="custom-tree-node" slot-scope="{ node, data }">
							<div class="left-text">
								<span style="margin-right: 6px">{{ data.name }}</span>
								<el-tag v-show="data.disabled" size="mini">
									{{ getOrgStructureName(data.org_stru_code) }}
								</el-tag>
							</div>
							<div class="right-operate">
								<el-dropdown>
									<span class="el-icon-more"></span>
									<el-dropdown-menu slot="dropdown">
										<el-dropdown-item
											v-has="'v1_tos_department_addDepartment'"
											v-show="data.disabled"
											@click.native="handleChooseDepartment(data, 'add')"
										>
											新增部门
										</el-dropdown-item>
										<el-dropdown-item
											v-has="'v1_tos_department_updateDepartment'"
											v-show="!data.disabled"
											@click.native="handleChooseDepartment(data, 'edit')"
										>
											编辑部门
										</el-dropdown-item>
										<el-dropdown-item
											v-has="'v1_tos_department_deleteDepartment'"
											v-show="!data.disabled"
											@click.native="handleDeleteDepartment(data)"
										>
											删除部门
										</el-dropdown-item>
									</el-dropdown-menu>
								</el-dropdown>
							</div>
						</div>
					</el-tree>
				</vue-scroll>
				<div style="height: 100%" v-else>
					<GcEmpty></GcEmpty>
				</div>
			</div>
		</div>
		<div class="page-right" v-show="currentDepartment.disabled === false">
			<GcModelHeader
				:title="currentDepartment.name"
				:icon="require('@/assets/images/icon/title-service-hall.png')"
			>
				<template v-slot:right>
					<el-button
						v-has="'v1_tos_department_updateDepartment'"
						type="primary"
						plain
						@click="handleSetDepartment('edit')"
					>
						编辑部门信息
					</el-button>
					<el-button v-has="'v1_tos_staff_create'" type="primary" plain @click="handleSetStaff('add')">
						新增员工
					</el-button>
				</template>
			</GcModelHeader>
			<div class="store-info">
				<GcGroupDetail :data="businessHallInfo"></GcGroupDetail>
			</div>
			<div class="staff-list">
				<div class="staff-search">
					<GcSearchInput v-model="search.value" @keyupEnter="getStaffList" @search="getStaffList">
						<div class="search_prepend" slot="prepend">
							<el-dropdown placement="bottom-start">
								<span class="el-dropdown-link">
									{{ search.type.label }}
									<i class="el-icon-arrow-down el-icon--right"></i>
								</span>
								<el-dropdown-menu slot="dropdown">
									<el-dropdown-item
										@click.native="
											search.type = item
											search.value = ''
										"
										v-for="item in $option.searchTypeOptions"
										:key="item.value"
									>
										{{ item.label }}
									</el-dropdown-item>
								</el-dropdown-menu>
							</el-dropdown>
						</div>
					</GcSearchInput>
				</div>
				<div class="list-block" ref="staffListBlock" v-if="staffList.length">
					<vue-scroll :ops="{ bar: { background: '#e3e3e3' } }">
						<div
							class="model"
							:class="{ resign: item.is_resigned }"
							:style="`background-image: url(${item.bg})`"
							v-for="item in staffList"
							:key="item.id"
						>
							<div class="info">
								<div class="l">
									<img :src="item.genderImg" alt="" />
								</div>
								<div class="r">
									<h5>{{ item.name }}</h5>
									<p>工号 {{ item.basic.staff_no || '--' }}</p>
								</div>
							</div>
							<div class="status">
								<div>
									<span class="iconfontCis icon-zhiwei"></span>
									<em>{{ item.role_name }}</em>
								</div>
								<div style="flex: 1; flex-shrink: 0">
									<span class="iconfontCis icon-gangweizhuangtai"></span>
									<em>{{ getResignedTypeDesc(item.is_resigned) }}</em>
								</div>
							</div>
							<div class="top_shadow">
								<div @click="handleSetStaff('view', item)">查看详情</div>
								<div
									v-has="[
										'v1_tos_staff_modify',
										'v1_tos_staff_modify2',
										'auth_aggregation_password_tenant_manage_update',
									]"
								>
									<span v-has="'v1_tos_staff_modify'" @click="handleSetStaff('edit', item)">
										修改信息
									</span>
									<em v-has="'v1_tos_staff_modify'">|</em>
									<span
										v-has="[
											'v1_tos_staff_modify2',
											'auth_aggregation_password_tenant_manage_update',
										]"
										@click="handleSetAccount(item)"
									>
										修改账户
									</span>
								</div>
							</div>
						</div>
					</vue-scroll>
				</div>
			</div>
		</div>
		<div class="page-right" v-show="currentDepartment.disabled !== false">
			<GcEmpty></GcEmpty>
		</div>
		<!-- 新增编辑部门 -->
		<DepartmentSetting
			:show.sync="showDepartment"
			:editType="editType"
			:editData="currentDepartment"
			@success="editSuccessDepartment"
		/>
		<!-- 新增编辑员工 -->
		<StaffSetting
			:show.sync="showStaff"
			:editType.sync="editStaffType"
			:editData="editStaffData"
			:departmentData="currentDepartment"
			@success="editSuccessDepartment"
		/>
		<!-- 修改账户 -->
		<PasswordSetting :show.sync="showPassword" :accountInfo="editStaffData" @success="editSuccessDepartment" />
		<!-- 筛选员工 -->
		<FilterStaff ref="filterStaffRef" :show.sync="showFilterStaff" @search="handleSearchStaff" />
	</div>
</template>

<script>
import { resignedTypeOptions } from '@/consts/optionList.js'
import { getfilterName } from '@/utils'
import { isValidObj } from '@/utils/validate'
import identity from '@/mixin/identity.js'
import { apiGetRegion } from '@/api/addressManage.api.js'
import { apiGetDepartmentTree, apiDeleteDepartment, apiStaffList } from '@/api/organizationManage.api.js'
import DepartmentSetting from './components/DepartmentSetting.vue'
import StaffSetting from './components/StaffSetting.vue'
import PasswordSetting from './components/PasswordSetting.vue'
import FilterStaff from './components/FilterStaff.vue'

export default {
	mixins: [identity],
	components: { DepartmentSetting, StaffSetting, PasswordSetting, FilterStaff },
	data() {
		return {
			treeLoading: false,
			dataTree: [],
			search: {
				type: { value: 'name', label: '员工姓名' },
				value: '',
			},
			currentDepartment: {},
			// 部门编辑、新增
			showDepartment: false,
			editType: 'add', // 编辑和新增部门区分
			// 员工编辑、新增
			showStaff: false,
			editStaffType: 'add', // 编辑和新增部门区分
			editStaffData: {},
			staffList: [],
			// 账户编辑
			showPassword: false,
			// 员工筛选
			showFilterStaff: false,
			staffParams: {},
		}
	},
	computed: {
		businessHallInfo() {
			const { parent_org_code = '', principal, phone, provinceDesc, cityDesc, location } =
				this.currentDepartment || {}
			const parentObj = this.findDeepTreeByKey(this.dataTree, 'org_code', parent_org_code)
			return {
				row: 4,
				list: [
					{
						key: '所属组织',
						value: parentObj?.name || '--',
					},
					{
						key: '负责人',
						value: principal || '--',
					},
					{
						key: '手机号码',
						value: phone || '--',
					},
					{
						key: '所在省份',
						value: provinceDesc || '--',
					},
					{
						key: '所在市',
						value: cityDesc || '--',
					},
					{
						key: '地址',
						value: location || '--',
					},
				],
			}
		},
		showClearStaffParamsBtn() {
			return isValidObj(this.staffParams)
		},
	},
	methods: {
		getResignedTypeDesc(val) {
			return getfilterName(resignedTypeOptions, val)
		},
		/* 查询部门所有人员列表信息 */
		async getStaffList() {
			try {
				const searchParams = {
					paged: false,
					includeResigned: true,
					departmentCode: this.currentDepartment.department_code,
					includeSubOrg: false,
					tid: this.tenantId,
					loginAccount: this.staffParams.loginAccount,
				}
				searchParams[this.search.type.value] = this.search.value
				const { data } = await apiStaffList(searchParams)
				this.staffList = (data || []).map(o => {
					o.bg = o.is_resigned
						? require('@/assets/images/bg/staff-bg-d.png')
						: require('@/assets/images/bg/staff-bg.png')
					o.genderImg =
						o.basic.gender === 0
							? require('@/assets/images/pic/h-men.png')
							: o.basic.gender === 1
							? require('@/assets/images/pic/h-women.png')
							: require('@/assets/images/pic/nogender.png')
					return {
						...o,
						orgName: this.currentDepartment.name,
						basic: {
							...o.basic,
							gender: o.basic.gender === undefined ? 2 : o.basic.gender,
						},
					}
				})
			} catch (error) {
				console.log(error)
			}
		},
		/**
		 * 根据provinceCode和cityCode获取当前省份和市区中文名称
		 * @return { Promise }
		 */
		findRegionName() {
			const provinceCode = this.currentDepartment.province
			const cityCode = this.currentDepartment.city
			return new Promise(resolve => {
				if (!provinceCode) {
					return resolve({
						proviceName: '--',
						cityName: '--',
					})
				}
				apiGetRegion({
					regionCode: provinceCode,
				})
					.then(res => {
						const city = (res.records || []).find(o => o.regionCode === cityCode)
						resolve({
							proviceName: res.parent.regionName || '--',
							cityName: city ? city.regionName : '--',
						})
					})
					.catch(() => {
						resolve({
							proviceName: '--',
							cityName: '--',
						})
					})
			})
		},
		async _apiGetDepartmentTree() {
			const { name, loginAccount } = this.staffParams
			const res = await apiGetDepartmentTree({
				name,
				loginAccount,
			})
			this.dataTree = res || []
			this.recursionOrganizeTree(this.dataTree)
		},
		/**
		 * 递归层级部门树数据结构修改返回数据格式
		 * @param { Array } data 需要处理数据list
		 */
		recursionOrganizeTree(data) {
			data.forEach(o => {
				for (let key in o.tenant_organization) {
					this.$set(o, key, o.tenant_organization[key])
				}

				if (o.department && o.department.length) {
					o.children = o.department.concat(o.children)
				}
				if (o.department_code) {
					this.$set(o, 'name', o.department_name)
					this.$set(o, 'tree_code', o.department_code)
					this.$set(o, 'node_key', o.department_code)

					this.$set(o, 'disabled', false)
				} else {
					this.$set(o, 'disabled', true)
					this.$set(o, 'tree_code', o.org_code)
					this.$set(o, 'node_key', o.uid + '_' + o.org_code)
				}

				if (o.children && o.children.length) {
					this.recursionOrganizeTree(o.children)
				} else {
					o.children = null
				}
			})
		},
		/**
		 * 根据指定keyName值找到指定对象object
		 * @param { Array } data 需要指定寻找数据的源数据
		 * @param { String } keyName 指定数据key值 支持object.key.key
		 * @param { Number | String } value 指定寻找数据值
		 */
		findDeepTreeByKey(data, keyName, value) {
			for (let i = 0; i < data.length; i++) {
				const kList = keyName.split('.')
				const targetValue = kList.reduce((prev, cur) => {
					return prev[cur]
				}, data[i])
				if (targetValue === value) {
					return data[i]
				}
				if (data[i].children && data[i].children.length) {
					if (this.findDeepTreeByKey(data[i].children, keyName, value)) {
						return this.findDeepTreeByKey(data[i].children, keyName, value)
					}
				}
			}
		},
		// 层级树：层级类型名称
		getOrgStructureName(value) {
			const obj = this.$store.getters.dataList?.orgStructureMap.find(item => item.sortValue === value)
			if (obj) {
				return obj.sortName
			} else {
				return ''
			}
		},
		// 点击树节点
		handleNodeClick(data) {
			this.currentDepartment = data
			this.$nextTick(() => {
				if (data.disabled === false) {
					this.getStaffList()
					this.$refs.departmentRef.setCurrentKey(data.node_key)
					this.findRegionName().then(res => {
						this.$set(this.currentDepartment, 'provinceDesc', res.proviceName)
						this.$set(this.currentDepartment, 'cityDesc', res.cityName)
					})
				} else {
					this.$refs.departmentRef.setCurrentKey(null)
				}
			})
		},
		handleChooseDepartment(data, type) {
			if (data) {
				this.handleNodeClick(data)
			}
			this.handleSetDepartment(type)
		},
		// 修改部门
		handleSetDepartment(type) {
			this.showDepartment = true
			this.editType = type
		},
		// 删除部门
		async handleDeleteDepartment(data) {
			this.$confirm('删除后数据将无法恢复', '此操作将删除部门信息, 是否继续？', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(() => {
				apiDeleteDepartment({ id: data.id }).then(() => {
					this.$message.success('删除组织部门成功')
					this._apiGetDepartmentTree()
					this.handleNodeClick({})
				})
			})
		},
		// 新增、编辑员工
		handleSetStaff(type, data) {
			this.showStaff = true
			this.editStaffType = type

			if (type === 'edit' || type === 'view') {
				this.editStaffData = Object.assign({}, data, data.basic)
			}
		},
		// 修改账户
		handleSetAccount(data) {
			this.showPassword = true
			this.editStaffData = Object.assign({}, data, data.basic)
		},
		editSuccessDepartment() {
			this._apiGetDepartmentTree().then(() => {
				// 更新当前部门信息
				this.currentDepartment = this.findDeepTreeByKey(
					this.dataTree,
					'node_key',
					this.currentDepartment.node_key,
				)
				this.handleNodeClick(this.currentDepartment)
				this.$refs.departmentRef.setCurrentKey(this.currentDepartment.node_key)
			})
		},
		// 员工筛选
		handleFilterStaff() {
			this.showFilterStaff = true
		},
		handleSearchStaff(params) {
			this.currentDepartment = {}
			this.staffList = []
			this.staffParams = params
			if (this.search.type.value !== 'name') {
				this.search.type = this.$option.searchTypeOptions.find(item => item.value === 'name')
			}
			this.search.value = params.name
			this._apiGetDepartmentTree()
		},
		clearStaffParams() {
			this.staffParams = {}
			this.search.value = ''
			this.currentDepartment = {}
			this.staffList = []
			this._apiGetDepartmentTree()
			this.$refs.filterStaffRef.reset()
		},
	},
	mounted() {
		this._apiGetDepartmentTree()
	},
}
</script>

<style lang="scss" scoped>
.page-left,
.page-right {
	padding-top: 0;
	.model-header {
		padding: 0;
	}
}
.page-left {
	flex: 0 0 380px;
	.filter-button {
		display: flex;
		align-items: center;
		img {
			width: 18px;
			margin-right: 2px;
		}
	}
	.tree-list {
		width: 340px;
		height: calc(100% - 60px);
		overflow-x: auto;
		::v-deep .el-tree {
			padding: 0 12px;
			.el-tree-node:focus > .el-tree-node__content {
				background-color: #fff;
			}

			.is-current {
				& > .el-tree-node__content {
					background-color: #eef5ff !important;
					& > .el-tree-node__label {
						color: #2080f7;
					}
				}
			}
			.el-tree-node__content {
				line-height: 34px;
				height: 34px;
				.el-checkbox__inner {
					border-radius: 0;
				}
				.el-tree-node__expand-icon {
					&:not(.is-leaf) {
						color: #666;
					}
				}
				&:hover {
					.el-icon-more {
						display: block !important;
						color: #2f87fe;
					}
				}
			}
		}
		.custom-tree-node {
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			gap: 20px;
			.left-disabled {
				cursor: not-allowed;
				span {
					color: #c9c9c9;
				}
			}
			.left-text {
				display: flex;
				align-items: center;
				img {
					margin-left: 2px;
					width: 12px;
				}
			}
			.right-operate {
				position: relative;
				.el-icon-more {
					transform: rotate(90deg);
					color: #999;
					font-size: 14px;
					cursor: pointer;
					display: none;
					margin-right: 8px;
				}
			}
		}
	}
}
.page-right {
	.store-info {
		position: relative;
		padding: 5px 0;
		&:after {
			content: '';
			position: absolute;
			left: 26px;
			right: 26px;
			bottom: 0;
			border-top: 1px dashed #d9d9d9;
		}
	}
	.staff-search {
		.search_prepend {
			margin-top: 8px;
			cursor: pointer;
		}
	}
	.staff-list {
		margin-top: 30px;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}
	.list-block {
		margin-top: 15px;
		flex: 1;
		// height: 0;
		overflow: auto;
		::v-deep.__view {
			display: flex;
			flex-wrap: wrap;
			min-height: auto !important;
		}
		.model {
			width: 190px;
			height: 106px;
			margin: 20px 20px 0 0;
			box-sizing: border-box;
			background-repeat: no-repeat;
			background-position: center;
			background-size: 100% 100%;
			box-shadow: 0px 2px 25px 0px rgba(0, 0, 0, 0.1);
			border-radius: 4px;
			opacity: 0.9;
			padding: 16px 10px 10px 10px;
			position: relative;
			&:hover {
				.top_shadow {
					opacity: 1;
				}
			}
			.top_shadow {
				position: absolute;
				width: 100%;
				height: 100%;
				background: rgba(0, 0, 0, 0.01);
				top: 0;
				left: 0;
				opacity: 0;
				transition: 0.3s;
				padding: 14px 0;
				box-sizing: border-box;
				display: flex;
				flex-wrap: wrap;
				& > div {
					height: 36px;
					background: rgba(0, 0, 0, 0.75);
					border-radius: 4px;
					font-size: 12px;
					width: 80%;
					margin: 0 auto;
					letter-spacing: 1px;
					cursor: pointer;
					color: #fff;
					text-align: center;
					line-height: 36px;
					&:last-child {
						margin-top: 8px;
					}
					em {
						margin: 0 10px;
						font-style: normal;
					}
				}
			}
			.info {
				display: flex;
				.l {
					margin-right: 10px;
					img {
						width: 40px;
						display: block;
					}
				}
				.r {
					flex: 1;
					width: 0;
					h5 {
						color: hsl(173, 67%, 33%);
						font-weight: 600;
						font-size: 16px;
						margin: 3px 0 4px 0;
						text-overflow: ellipsis;
						white-space: nowrap;
						overflow: hidden;
						height: 20px;
					}
					p {
						color: #1c8d7f;
						font-size: 12px;
					}
				}
			}
			.status {
				display: flex;
				margin-top: 14px;
				& > div {
					text-align: center;
					position: relative;
					&:first-child {
						max-width: 66%;
						min-width: 50%;
						padding-right: 8px;
						&:after {
							content: '';
							width: 1px;
							height: 12px;
							border-right: 1px solid #448981;
							position: absolute;
							right: -2px;
							top: 4px;
						}
					}
					em {
						font-size: 12px;
						color: #448981;
						font-style: normal;
					}
					span {
						color: #448981;
						position: relative;
						top: 2px;
						margin-right: 2px;
					}
				}
			}
			&.resign {
				.info {
					opacity: 0.6;
				}
				.status {
					opacity: 0.6;
				}
			}
		}
	}
}
</style>
