<template>
	<gc-el-dialog :show="isShow" title="导入抄表数据" custom-top="120px" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template #readMeterExcel>
				<el-upload
					ref="uploadRef"
					action=" "
					:limit="1"
					accept=".xls,.xlsx"
					:auto-upload="false"
					:on-change="handleUploadChange"
					:on-remove="handleUploadRemove"
					:on-exceed="handleExceed"
				>
					<el-button size="small" type="primary">上传文件</el-button>
					<div slot="tip" class="el-upload__tip">
						<el-button type="text" @click="handleDownloadTemplate">
							下载模版
						</el-button>
					</div>
				</el-upload>
			</template>
		</GcFormSimple>
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button :loading="saveLoading" :isDisabled="saveLoading" @click.native="handleSave">确定导入</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { exportBlob } from '@/utils/index.js'
import {
	downloadMeterReadingRecordTemplate,
	importMeterReadingRecordExcel,
	autoReviewByRecord,
} from '@/api/meterReading.api'
export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		params: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				readMeterExcel: null,
			},
			formItems: [
				{
					type: 'slot',
					slotName: 'readMeterExcel',
					label: '抄表文件',
					prop: 'readMeterExcel',
					attrs: {
						col: 24,
						placeholder: '',
					},
				},
			],
			saveLoading: false,
			formAttrs: {
				labelPosition: 'right',
				labelWidth: '110px',
				rules: {
					readMeterExcel: [{ required: true, message: '请上传文件', trigger: 'change' }],
				},
			},
		}
	},
	methods: {
		// 选择文件
		handleUploadChange(file) {
			this.formData.readMeterExcel = file.raw
			this.$refs.formRef.validateField('readMeterExcel')
		},
		// 移除文件
		handleUploadRemove() {
			this.formData.readMeterExcel = null
			this.$refs.formRef.validateField('readMeterExcel')
		},
		handleExceed() {
			this.$message.error('最多只能选择一个文件')
		},
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.saveLoading = true
				const { readMeterExcel } = this.formData
				const { taskYear, taskMonth, bookId, meterReadingTaskId } = this.params
				const formData = new FormData()
				formData.append('excel', readMeterExcel)
				formData.append('taskMonth', taskMonth)
				formData.append('taskYear', taskYear)
				formData.append('bookId', bookId)
				formData.append('meterReadingTaskId', meterReadingTaskId)
				try {
					const res = await importMeterReadingRecordExcel(formData)
					const contentType = res.headers['content-type']
					const mainContentType = contentType.split(';')[0].trim()

					if (mainContentType === 'application/json') {
						// Content-Type 是 application/json; 上传成功
						try {
							// 由于 responseType 是 'blob'，需要将 Blob 转换为文本
							const reader = new FileReader()
							reader.onload = () => {
								const response = JSON.parse(reader.result)
								if (response.code !== 0) {
									this.$message.error(response.message)
									return
								} else {
									this.$emit('success')
									this.isShow = false
									this.importSuccessBack(response.data)
								}
							}
							reader.readAsText(res.data)
						} catch (error) {
							this.$message.error(error)
						}
					} else {
						// Content-Type 不是 application/json，代表部分导入失败，下载文件
						exportBlob(res.data, '导入失败', 'xlsx')
						this.$emit('success')
						this.isShow = false
					}
				} catch (error) {
					const reader = new FileReader()
					reader.onload = () => {
						const response = JSON.parse(reader.result)
						this.$message.error(response.message)
					}
					reader.readAsText(error.response.data)
				} finally {
					this.saveLoading = false
				}
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.$refs.uploadRef.clearFiles()
			this.saveLoading = false
			this.isShow = false
		},
		// 下载导入模版
		async handleDownloadTemplate() {
			const res = await downloadMeterReadingRecordTemplate(this.params)
			exportBlob(res, '抄表数据模版', 'xlsx')
		},
		importSuccessBack(meterReadingRecordIdList) {
			const label = '抄表数据导入成功，是否直接送内复？'
			this.$confirm(label, '导入成功', {
				confirmButtonText: '是',
				cancelButtonText: '否',
				type: 'success',
				center: true,
			})
				.then(async () => {
					const { meterReadingTaskId, bookNo, taskYear = '', taskMonth = '', taskStatus = '' } = this.params
					await autoReviewByRecord({
						meterReadingRecordIdList,
						taskYear,
					})
					this.$message.success('送内复成功')
					this.$router.push({
						path: '/meterReading/meterReadingVerificationDetail',
						query: {
							meterReadingTaskId,
							bookNo,
							taskYear,
							taskMonth,
							date:
								taskYear && taskMonth
									? `${taskYear}-${taskMonth <= 9 ? `0${taskMonth}` : taskMonth}`
									: '--',
							taskStatus,
						},
					})
				})
				.catch(() => {})
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
</style>
