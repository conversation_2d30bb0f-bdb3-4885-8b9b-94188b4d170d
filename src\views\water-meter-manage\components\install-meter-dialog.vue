<template>
	<gc-el-dialog
		:show="isShow"
		title="装表登记"
		custom-top="120px"
		width="600px"
		@open="handleOpen"
		@close="handleClose"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { ruleMaxLength } from '@/utils/rules'
import { installMeter } from '@/api/waterMeter.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		data: Object,
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				meterNo: '',

				meterReading: undefined,
				operatorDate: '',
				operatorPerson: '',

				archivesIdentity: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '水表编号',
					prop: 'meterNo',
					attrs: {
						col: 24,
						disabled: true,
					},
				},
				{
					type: 'el-input',
					label: '指针数',
					prop: 'meterReading',
					attrs: {
						col: 24,
						placeholder: '请输入指针数',
						maxlength: 8,
					},
					events: {
						input: val => {
							// 只允许输入数字，实时过滤非数字字符
							const numericValue = val.replace(/[^0-9]/g, '')
							// 限制最大值
							const maxValue = 99999999
							const finalValue = numericValue ? Math.min(parseInt(numericValue), maxValue).toString() : ''

							if (finalValue !== val) {
								this.formData.meterReading = finalValue
							}
						},
					},
				},
				{
					type: 'el-date-picker',
					label: '装表时间',
					prop: 'operatorDate',
					attrs: {
						col: 24,
						valueFormat: 'yyyy-MM-dd',
						placeholder: '请选择装表时间',
					},
				},
				{
					type: 'el-input',
					label: '装表操作人员',
					prop: ' operatorPerson',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入装表操作人员',
					},
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
			],
			formAttrs: {
				labelWidth: '100px',
				labelPosition: 'top',
				rules: {
					meterReading: [{ required: true, message: '请输入指针数', trigger: 'blur' }],
					operatorDate: [{ required: true, message: '请选择装表时间', trigger: 'blur' }],
					operatorPerson: [ruleMaxLength(30, '装表操作人员')],
					archivesIdentity: [{ required: true, message: '请输入表卡编号', trigger: 'blur' }],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				await installMeter({
					meterId: this.data.meterId,
					archivesId: this.data.archivesId,
					...this.formData,
				})
				this.$message.success(`装表成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleOpen() {
			this.formData.archivesIdentity = this.data?.archivesIdentity ?? ''
			this.formData.meterNo = this.data?.meterNo ?? ''
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.formData.meterReading = undefined
			this.isShow = false
		},
		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.el-form-item--small.el-form-item {
		margin-bottom: 12px;
	}
	.el-input-number {
		width: 100%;
		.el-input__inner {
			text-align: left;
		}
	}
}
</style>
