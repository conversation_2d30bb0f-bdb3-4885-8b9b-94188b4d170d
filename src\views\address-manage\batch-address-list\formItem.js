export function getFormItems(_this) {
	const arr = [
		{
			type: 'el-select',
			label: '市',
			prop: 'cityCode',
			options: [],
			events: {
				change: value => _this.handleChangeAddress(value, 'cityCode'),
			},
		},
		{
			type: 'el-select',
			label: '区/县',
			prop: 'regionCode',
			options: [],
			events: {
				change: value => _this.handleChangeAddress(value, 'regionCode'),
			},
		},
		{
			type: 'el-select',
			label: '街道/乡镇',
			prop: 'streetCode',
			options: [],
			events: {
				change: value => _this.handleChangeAddress(value, 'streetCode'),
			},
		},
		{
			type: 'el-select',
			label: '小区/村庄',
			prop: 'neighbourhoodCode',
			options: [],
			events: {
				change: value => _this.handleChangeAddress(value, 'neighbourhoodCode'),
			},
		},
		{
			type: 'el-select',
			label: '楼栋',
			prop: 'buildingCode',
			options: [],
		},
		{
			type: 'el-input',
			label: '地址',
			prop: 'addressFullName',
		},
	]
	arr.forEach(item => {
		_this.$set(_this.formData, item.prop, '')
	})
	return arr
}
