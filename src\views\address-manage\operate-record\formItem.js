export function getFormItems(_this) {
	return [
		{
			type: 'el-input',
			label: _this.$route.query.type === 'street' ? '街道/乡镇（新）' : '地址（新）',
			prop: 'newAddressAreaName',
			attrs: {
				placeholder: _this.$route.query.type === 'street' ? '请输入街道/乡镇名' : '请输入',
			},
		},
		{
			type: 'el-input',
			label: '区/县（新）',
			prop: 'newRegionName',
			attrs: {
				placeholder: '请输入区/县名',
			},
		},
		{
			type: 'el-select',
			label: '区/县（新）',
			prop: 'regionCode',
			options: [],
			events: {
				change: value => {
					const streetIndex = _this.formItems.findIndex(item => item.prop === 'streetCode')
					const communityIndex = _this.formItems.findIndex(item => item.prop === 'neighbourhoodCode')
					_this.formItems[streetIndex].options = []
					_this.formItems[communityIndex].options = []
					_this.formData.streetCode = ''
					_this.formData.neighbourhoodCode = ''
					if (value) {
						_this._getAddressAreaMap(value, 'streetCode')
					}
				},
			},
		},
		{
			type: 'el-select',
			label: '街道/乡镇（新）',
			prop: 'streetCode',
			options: [],
			events: {
				change: value => {
					const communityIndex = _this.formItems.findIndex(item => item.prop === 'neighbourhoodCode')
					_this.formData.neighbourhoodCode = ''
					_this.formItems[communityIndex].options = []
					if (value) {
						_this._getAddressAreaMap(value, 'neighbourhoodCode')
					}
				},
			},
		},
		{
			type: 'el-select',
			label: '小区/村庄（新）',
			prop: 'neighbourhoodCode',
			options: [],
		},
		{
			type: 'el-select',
			label: '操作人',
			prop: 'createStaffId',
			options: [],
		},
	]
}
