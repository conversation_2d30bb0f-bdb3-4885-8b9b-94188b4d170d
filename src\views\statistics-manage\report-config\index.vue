<template>
	<div class="page-layout">
		<div class="page-left">
			<gc-model-header
				class="info-title"
				title="报表分类列表"
				:icon="require(`@/assets/images/icon/title-multi-check.png`)"
			>
				<template #right>
					<el-button v-has="'cpm_report_category_add'" type="primary" size="small" @click="handleAddType">
						新增分类
					</el-button>
				</template>
			</gc-model-header>
			<div class="list-box" v-loading="treeLoading">
				<div
					v-show="reportTypeList.length > 0"
					class="list-item"
					v-for="(item, index) in reportTypeList"
					:key="item.id"
					:class="{ active: listActive === index }"
					@click="handleTypeClick(index)"
				>
					<div class="label">{{ item.categoryName }}</div>
					<el-dropdown v-has="['cpm_report_category_update', 'cpm_report_category_delete']" @click.stop>
						<i class="icon-more el-icon-more" @click.stop></i>
						<el-dropdown-menu slot="dropdown">
							<el-dropdown-item v-has="'cpm_report_category_update'" @click.native="handleRename(item)">
								重命名
							</el-dropdown-item>
							<el-dropdown-item
								v-has="'cpm_report_category_delete'"
								@click.native="handleDeleteType(item)"
							>
								删除
							</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</div>
				<gc-empty v-show="reportTypeList.length === 0" />
			</div>
		</div>
		<div class="page-right">
			<div v-has="'cpm_report_config_add'" class="right-top">
				<el-button
					v-has="'cpm_report_config_add'"
					type="primary"
					:disabled="!currentReportCategoryId"
					@click="handleAdd"
				>
					新增报表
				</el-button>
			</div>
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:deal="{ row }">
					<el-button v-has="'cpm_report_config_update'" type="text" size="medium" @click="handleEdit(row)">
						修改
					</el-button>
					<el-button v-has="'cpm_report_config_delete'" type="text" size="medium" @click="handleDelete(row)">
						删除
					</el-button>
				</template>
			</GcTable>
		</div>

		<!-- 新增、编辑弹窗 -->
		<UpdateDialog
			ref="updateDialogRef"
			:show.sync="showUpdate"
			:editType="editType"
			:report-category-id="currentReportCategoryId"
			@success="getList(1)"
		/>
	</div>
</template>

<script>
import { isBlank } from '@/utils/validate.js'
import UpdateDialog from './components/UpdateDialog.vue'
import {
	queryReportCategoryList,
	addReportCategory,
	editReportCategory,
	deleteReportCategory,
	queryReportPage,
	deleteReport,
} from '@/api/statisticsManage.api'

export default {
	name: 'ReportConfig',
	components: { UpdateDialog },
	data() {
		return {
			// 左侧列表
			treeLoading: false,
			listActive: 0,
			reportTypeList: [],
			// 右侧列表
			loading: false,
			columns: [
				{
					key: 'reportName',
					name: '报表名称',
					tooltip: true,
				},
				{
					key: 'reportUrl',
					name: '报表路径',
					tooltip: true,
				},
				{
					hide: !this.$has(['cpm_report_config_update', 'cpm_report_config_delete']),
					key: 'deal',
					name: '操作',
					fixed: 'right',
					width: 140,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 新增报表弹窗
			editType: 'add',
			showUpdate: false,
		}
	},
	computed: {
		currentReportCategoryId() {
			return this.reportTypeList.length > 0 ? this.reportTypeList[this.listActive].id : ''
		},
	},
	beforeRouteEnter(to, from, next) {
		next(vm => {
			vm.$nextTick(() => {
				vm.getTypeList()
			})
		})
	},
	methods: {
		handleTypeClick(index) {
			if (this.listActive !== index) {
				this.listActive = index
				this.getList(1)
			}
		},
		typeInputValidator(value) {
			if (isBlank(value)) return '请输入报表分类名称'
			if (value.length > 16) return '分类名称不能超过16位字符'
		},
		// 新增报表分类
		handleAddType() {
			this.$prompt('报表分类名称', '新增报表分类', {
				customClass: 'type-message-box',
				closeOnClickModal: false,
				inputPlaceholder: '请输入报表分类名称',
				inputValidator: this.typeInputValidator,
			}).then(async ({ value }) => {
				await addReportCategory({
					categoryName: value,
				})
				this.$message.success('新增成功')
				this.getTypeList()
			})
		},
		// 重命名
		handleRename(data) {
			this.$prompt('报表分类名称', '重命名', {
				customClass: 'type-message-box',
				inputValue: data.categoryName,
				closeOnClickModal: false,
				inputPlaceholder: '请输入报表分类名称',
				inputValidator: this.typeInputValidator,
			}).then(async ({ value }) => {
				await editReportCategory({
					id: this.currentReportCategoryId,
					categoryName: value,
				})
				this.$message.success('重命名成功')
				this.getTypeList(this.listActive)
			})
		},
		// 删除报表分类
		handleDeleteType(data) {
			this.$confirm('确定要删除该报表分类吗？').then(async () => {
				await deleteReportCategory({ id: data.id })
				this.$message.success('删除成功')
				this.getTypeList()
			})
		},
		// 获取左侧报表分类列表
		async getTypeList(active = 0) {
			this.treeLoading = true
			try {
				const data = await queryReportCategoryList()
				this.reportTypeList = data || []
				this.listActive = active
				if (this.reportTypeList.length > 0) {
					this.getList(1)
				}
			} catch (error) {
				console.error(error)
				this.reportTypeList = []
				this.listActive = 0
			} finally {
				this.treeLoading = false
			}
		},

		// 右侧列表
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await queryReportPage({
					size,
					current,
					categoryId: this.currentReportCategoryId,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		// 编辑
		handleEdit(data) {
			this.editType = 'edit'
			this.showUpdate = true
			this.$nextTick(() => {
				this.$refs.updateDialogRef.setFormData(data)
			})
		},
		// 删除
		handleDelete(data) {
			this.$confirm('确定要删除该报表吗？').then(async () => {
				await deleteReport({ id: data.id })
				this.$message.success('删除成功')
				this.getList(1)
			})
		},
		// 新增报表
		handleAdd() {
			this.editType = 'add'
			this.showUpdate = true
		},
	},
}
</script>

<style lang="scss">
.type-message-box {
	position: absolute;
	top: 120px;
	left: 50%;
	transform: translateX(-50%);
	padding-bottom: 20px;
	.el-message-box__message {
		display: flex;
		align-items: center;
		&::before {
			content: '*';
			color: #ec6b60;
			margin-right: 4px;
		}
	}
	.el-message-box__input {
		padding-top: 6px;
	}
}
</style>
<style lang="scss" scoped>
.page-left {
	overflow: auto;
}
.right-top {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-bottom: 12px;
}
.info-title {
	height: auto;
	padding: 0;
	margin-bottom: 12px;
}
.icon-more {
	transform: rotate(90deg);
}
.list-box {
	flex: 1;
	overflow: auto;
	.list-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 42px;
		padding: 0 12px;
		cursor: pointer;
		.label {
			flex: 1;
			margin-right: 12px;
			@include text-overflow;
		}
		&.active {
			color: #2f87fe;
			background-color: rgba(196, 221, 255, 0.5);
			.el-dropdown {
				display: block;
			}
		}
	}
	.el-dropdown {
		display: none;
	}
}
::v-deep {
	.el-dropdown-menu__item {
		min-width: auto;
	}
}
</style>
