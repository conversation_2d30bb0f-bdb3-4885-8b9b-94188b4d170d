<template>
	<div class="user-detail" v-loading.fullscreen.lock="loading">
		<!-- 用户信息 -->
		<UserInfo :userDetail="userDetail" @success="_apiGetUserDatailsById" />
		<!-- 详情tab切换区 -->
		<div class="user-content">
			<GcDetailTab
				ref="detailTabRef"
				:tab-list="tabList"
				:default-active-name.sync="defaultActiveName"
				@controlLoading="controlLoading"
				@refresh="_apiGetUserDatailsById"
				@tab-change="handleTabChange"
			></GcDetailTab>
		</div>
	</div>
</template>

<script>
import { apiGetUserDatailsById } from '@/api/userManage.api.js'
import UserInfo from './user-info' // 用户信息
import Overview from './overview' // 概览
import PaymentRecords from './payment-records' // 缴费记录
import ArrearageRecords from './arrearage-records' // 欠费记录
import RenameRecords from './rename-records' // 更名记录
import TransferRecords from './transfer-records' // 迁移记录
import InvoiceRecords from './invoice-records' // 开票记录
import { mergeOpenInvoice4 } from '@/api/costManage.api'
export default {
	name: 'UserView',
	components: {
		UserInfo,
	},
	computed: {
		tabList() {
			const arr = [
				{
					name: 'Overview',
					label: '概览',
					component: Overview,
					data: this.userDetail,
				},
			]
			this.$has('cpm_archives_billPay-record-list_user') &&
				arr.push({
					name: 'PaymentRecords',
					label: '缴费记录',
					component: PaymentRecords,
				})
			this.$has('billing_bill-arrears_list1') &&
				arr.push({
					name: 'ArrearageRecords',
					label: '欠费信息',
					component: ArrearageRecords,
				})
			this.$has('cpm_archives_modify-user-name-record') &&
				arr.push({
					name: 'RenameRecords',
					label: '更名记录',
					component: RenameRecords,
				})
			this.$has('cpm_archives_change-user-record') &&
				arr.push({
					name: 'TransferRecords',
					label: '过户记录',
					component: TransferRecords,
				})
			this.$has('payment_invoice_record-list2') &&
				arr.push({
					name: 'InvoiceRecords',
					label: '开票记录',
					component: InvoiceRecords,
				})
			return arr
		},
	},
	data() {
		this.invoiceApi = mergeOpenInvoice4
		return {
			loading: false,
			defaultActiveName: 'Overview',
			controlLoading: false,
			userDetail: {},
			openInvoiceType: '',
			billList: [],
			openInvoiceDialogShow: false,
			onInvoiceOpenDone: null,
		}
	},
	activated() {
		if (this.$route.query.userId) {
			this._apiGetUserDatailsById()
		}
	},
	methods: {
		// 用户信息详情
		async _apiGetUserDatailsById() {
			try {
				const data = await apiGetUserDatailsById({
					userId: this.$route.query.userId,
				})
				this.userDetail = data
			} catch (error) {
				console.log(error)
			}
		},
		handleTabChange(data) {
			this.$refs.detailTabRef.$refs.componentRef[data.index].handleSearch()
		},
	},
}
</script>
<style lang="scss" scoped>
.user-detail {
	width: 100%;
	height: 100%;
	display: flex;
	.user-content {
		width: calc(100% - 290px);
	}
}
::v-deep {
	.container {
		background-color: #fff;
		padding: 20px;
		display: flex;
		flex-direction: column;
		height: 100%;
		.table-container {
			flex: 1;
			overflow: auto;
		}
	}
}
</style>
