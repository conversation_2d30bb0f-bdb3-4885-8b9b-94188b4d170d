<template>
	<gc-el-dialog
		:show.sync="innerVisible"
		title="开具发票"
		width="480px"
		@close="resetForm"
		class="print-invoice"
		@open="getInitInfo"
	>
		<el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="form-layout">
			<div class="form-item-area">
				<el-form-item label="开票类型" prop="buyerType" class="adjust-length" ref="buyerType">
					<el-radio-group v-model="ruleForm.buyerType" @change="buyerTypeChange">
						<el-radio label="1">企业单位</el-radio>
						<el-radio label="2">个人/非企业单位</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="发票抬头" prop="userName" class="adjust-length" ref="userName">
					<el-input v-model="ruleForm.userName" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item
					label="身份证号"
					prop="taxpayerIdentity"
					class="adjust-length"
					key="taxpayerIdentity-0"
					ref="taxpayerIdentity"
					v-if="ruleForm.buyerType == '2'"
					:rules="[RULE_CASE_NUM, ruleMaxLength(18), ruleMinLength(6)]"
				>
					<el-input v-model="ruleForm.taxpayerIdentity" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item
					label="纳税人识别号"
					prop="taxpayerIdentity"
					class="adjust-length"
					key="taxpayerIdentity-1"
					ref="taxpayerIdentity"
					v-if="ruleForm.buyerType == '1'"
					:rules="[ruleRequired('请输入纳税人识别号'), RULE_CASE_NUM, ruleMaxLength(20), ruleMinLength(6)]"
				>
					<el-input v-model="ruleForm.taxpayerIdentity" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="电子邮箱" prop="email" class="adjust-length" ref="email">
					<el-input v-model="ruleForm.email" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="地址、电话" prop="address" class="adjust-length" ref="address">
					<el-input v-model="ruleForm.address" placeholder="例：上城区测试小区测试地址***********"></el-input>
				</el-form-item>
				<el-form-item label="开户行" prop="openBank" class="adjust-length" ref="openBank">
					<el-input v-model="ruleForm.openBank" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="开户账户" prop="bankAccount" class="adjust-length" ref="bankAccount">
					<el-input v-model="ruleForm.bankAccount" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="推送手机号" prop="phoneNumber" class="adjust-length" ref="phoneNumber">
					<el-input v-model="ruleForm.phoneNumber" placeholder="请输入"></el-input>
				</el-form-item>
				<el-form-item label="同步更新开票信息" prop="sync" class="adjust-length" ref="sync">
					<el-radio-group v-model="ruleForm.sync">
						<el-radio :label="1">同步</el-radio>
						<el-radio :label="0">不同步</el-radio>
					</el-radio-group>
				</el-form-item>
				<!-- <el-form-item
          label="开票人"
          prop="drawer"
          class="adjust-width"
          ref="drawer"
        >
          <el-input v-model="ruleForm.drawer" placeholder="请输入"></el-input>
        </el-form-item> -->
			</div>
			<!-- v-if="source !== 'bill'" -->
			<div class="expense-details" v-if="false">
				<div class="title">费用明细</div>
				<el-table
					ref="multipleTable"
					:data="tableData"
					tooltip-effect="dark"
					style="width: 100%"
					v-if="tableData.length > 0"
				>
					<el-table-column label="费用类型">
						<template slot-scope="scope">
							{{ nameConversion(scope.row.costType, costTypeArr) }}
						</template>
					</el-table-column>
					<el-table-column prop="taxRate" label="税率"></el-table-column>
					<el-table-column prop="taxCode" label="税率编码"></el-table-column>
					<el-table-column prop="paidAmount" label="金额（元）"></el-table-column>
				</el-table>
				<div class="empty" v-else>暂无费用</div>
				<div class="date">
					<span>开票日期：</span>
					<span>{{ todayDate }}</span>
				</div>
			</div>
		</el-form>
		<template #footer>
			<button class="gc-button gc-button-two" @click="submitForm('ruleForm', 'code')">开具发票</button>
		</template>
	</gc-el-dialog>
</template>

<script>
import resetData from './resetData'
import { ruleMaxLength, ruleRequired, ruleMinLength, RULE_CASE_NUM } from '@/utils/rules.js'
import { apiInvoiceBuyerInfo, apiTaxRateInfo, apiCostRecords, apiOpeningInvoice } from '@/api/print.api.js'
import { apiMonthBillingOpeningInvoice } from '@/api/archives.api.js'
import { isBlank, isString } from '@/utils/validate.js'
import { nameConversion, joinAddress } from '@/utils/index.js'
import dictionaryValue from '@/utils/dictionaryValue'

export default {
	name: 'Invoice',
	mixins: [resetData, dictionaryValue],
	props: {
		// 当前选中元素的具体信息
		selectedItem: {
			type: [Object, String],
			default: '',
		},
		// 入口 缴费明细=payment 缴费=recharge
		source: {
			type: [String],
			required: true,
			default: '',
		},
		// 档案编号
		archivesNo: {
			type: [String],
			required: true,
			default: '',
		},
		//支付ID（缴费页面需传递）
		payRecordId: {
			type: [Number, String],
			default: '',
		},
		//costRecordIdStr 费用明细IDstr
		costRecordIdStr: {
			type: [String, Number],
			default: '',
		},
		detailData: {}, //档案信息
	},
	data() {
		return {
			ruleForm: {
				buyerType: '1', //开票类型
				address: '', //购方联系地址
				bankAccount: '', //开户账号
				// drawer: "", //开票人
				invoiceBuyerId: '', //发票购方信息ID
				openBank: '', //开户行
				phoneNumber: '', //购方联系电话
				taxpayerIdentity: '', //纳税人识别号
				userName: '', //购方客户名称
				email: '', //电子邮箱
				sync: 1, //是否同步
			},
			rules: {
				buyerType: [ruleRequired('请选择开票类型')],
				// drawer: [ruleRequired("请输入开票人"), ruleMaxLength(8)],
				userName: [ruleRequired('请输入发票抬头'), ruleMaxLength(32)],
				address: [ruleMaxLength(50)],
				openBank: [ruleMaxLength(32)],
				bankAccount: [ruleMaxLength(32)],
				phoneNumber: [ruleMaxLength(11)],
				email: [ruleMaxLength(50)],
			},
			tableData: [],
			multipleSelection: [],
			RULE_CASE_NUM,
		}
	},
	computed: {
		todayDate() {
			let date = new Date()
			let year = date.getFullYear()
			let month = date.getMonth() + 1
			let day = date.getDate()
			return year + '-' + month + '-' + day
		},
		invoiceSet() {
			return this.$store.state.archives.invoiceSet || {}
		},
	},
	mounted() {},
	methods: {
		nameConversion,
		ruleRequired,
		ruleMaxLength,
		ruleMinLength,
		submitForm(formName, flag) {
			//flag标识 识别是二维码开票(code)还是PDF(pdf)
			this.$refs[formName].validate((valid, object) => {
				if (valid) {
					this.$listeners.controlLoading(true)
					let params = {}
					params['archivesNo'] = this.archivesNo
					if (this.source === 'bill') {
						params['monthlyBillId'] = this.selectedItem.monthlyBillId
					} else {
						params['costRecordId'] = this.costRecordIdStr
					}
					for (let key in this.ruleForm) {
						if (!isBlank(this.ruleForm[key])) {
							if (isString(this.ruleForm[key])) {
								params[key] = this.ruleForm[key].trim()
							} else {
								params[key] = this.ruleForm[key]
							}
						}
					}
					let api = apiOpeningInvoice
					if (this.source === 'bill') {
						api = apiMonthBillingOpeningInvoice
					}
					api(params)
						.then(res => {
							this.$message.success('开票成功')
							if (this.source === 'payment' || this.source === 'bill') {
								this.$emit('updateList')
							} else {
								this.$listeners.controlLoading(false)
							}
							this.innerVisible = false
						})
						.catch(err => {
							console.log(err, 'err')
							this.$listeners.controlLoading(false)
						})
				} else {
					for (let i in object) {
						let dom = this.$refs[i]
						if (Object.prototype.toString.call(dom) !== '[object Object]') {
							//这里是针对遍历的情况（多个输入框），取值为数组
							dom = dom[0]
						}
						dom.$el.scrollIntoView({
							//滚动到指定节点
							block: 'center', //值有start,center,end，nearest，当前显示在视图区域中间
							behavior: 'smooth', //值有auto、instant,smooth，缓动动画（当前是慢速的）
						})
						break
					}
					return false
				}
			})
		},
		// 获取初始化信息
		getInitInfo() {
			// 获取购方信息
			if (this.archivesNo) {
				this.getBuyerInfo()
			}
			// 获取费用明细列表
			// 入口为缴费明细列表项
			// if (this.source === "payment") {
			//   // 请求税率详情
			//   this.getTaxRateInfo();
			// }
			// 入口为缴费
			// if (this.payRecordId || this.payRecordId === 0) {
			//   if (this.source === "recharge") {
			//     // 请求费用列表
			//     this.getCostRecordsList();
			//   }
			// }
		},
		// 获取购方信息
		getBuyerInfo() {
			apiInvoiceBuyerInfo({
				archivesNo: this.archivesNo,
			}).then(res => {
				if (res) {
					let middleObj = res || {}
					if (Object.keys(middleObj).length > 0) {
						for (let key in middleObj) {
							if (Object.prototype.hasOwnProperty.call(this.ruleForm, key) && !isBlank(middleObj[key])) {
								this.ruleForm[key] = middleObj[key]
							}
						}
					}
				} else {
					const {
						user: { userType = null, userName = null, certificateNo = null, userMobile = null },
						address = {},
					} = this.detailData
					// 如果民用档案且用户在发票设置中默认档案，则默认回填
					if ([2, 3].includes(+userType) && this.invoiceSet?.resident?.residentDefaultHeader == 1) {
						this.ruleForm.buyerType = '2'
						this.ruleForm.userName = !isBlank(userName) ? userName : ''
						this.ruleForm.taxpayerIdentity = !isBlank(certificateNo) ? certificateNo : ''
						this.ruleForm.phoneNumber = !isBlank(userMobile) ? userMobile : ''
						this.ruleForm.address = joinAddress(address) != '--' ? joinAddress(address) : ''
					}
				}
			})
		},
		// 获取税率详情
		getTaxRateInfo() {
			apiTaxRateInfo({
				costItemType: this.selectedItem.costType,
			}).then((res = {}) => {
				let middleArr = []
				let obj = {
					costType: '--',
					paidAmount: '--',
					taxCode: '--',
					taxRate: '--',
				}
				let { costType = '--', paidAmount = '--' } = this.selectedItem
				costType !== '--' ? (obj['costType'] = costType) : null
				paidAmount !== '--' ? (obj['paidAmount'] = paidAmount.toFixed(2)) : null
				let { taxCode, taxRate } = res
				!isBlank(taxCode) ? (obj['taxCode'] = taxCode) : null
				!isBlank(taxRate) ? (obj['taxRate'] = taxRate) : null
				middleArr.push(obj)
				this.tableData = middleArr
			})
		},
		// 获取费用列表
		getCostRecordsList() {
			apiCostRecords({
				payRecordId: this.payRecordId,
			}).then(res => {
				this.tableData = res || []
			})
		},
		// 开票类型切换
		buyerTypeChange() {
			this.ruleForm.address = ''
			this.ruleForm.bankAccount = ''
			this.ruleForm.invoiceBuyerId = ''
			this.ruleForm.openBank = ''
			this.ruleForm.phoneNumber = ''
			this.ruleForm.taxpayerIdentity = ''
			this.ruleForm.userName = ''
			this.ruleForm.email = ''
			this.$nextTick(() => {
				this.$refs.ruleForm.clearValidate()
			})
		},
	},
}
</script>
<style lang="scss" scoped>
@import './formLayout.scss';
.print-invoice {
	::v-deep {
		.el-dialog {
			margin-top: 60px !important;
		}
		.el-dialog__body {
			padding: 0;
			.form-item-area {
				padding: 15px 24px 0 24px;
				display: flex;
				flex-wrap: wrap;
			}
		}
		.adjust-width {
			width: 50%;
			.el-input__inner {
				width: 204px !important;
			}
		}
	}

	.expense-details {
		width: 100%;
		background: #f5f8ff;
		padding: 20px 24px;
		.title {
			color: $base-color-2;
			font-weight: 500;
		}
		::v-deep {
			.el-table {
				th {
					background: #dde7fa;
					color: $base-color-2;
					font-weight: 500;
				}
			}
		}
		.empty,
		.date {
			display: flex;
			font-size: $base-font-size-default;
		}
		.empty {
			justify-content: center;
			color: $base-color-4;
			padding: 10px 0;
		}
		.date {
			justify-content: flex-end;
			color: $base-color-6;
			padding-top: 10px;
		}
	}
	button + button {
		margin-left: 10px;
	}
}
</style>
