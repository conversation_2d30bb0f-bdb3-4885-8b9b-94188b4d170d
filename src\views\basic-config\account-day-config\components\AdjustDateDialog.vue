<template>
	<gc-el-dialog :show="isShow" title="结账日修改" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { updateBalanceSheetDate } from '@/api/basicConfig.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		prevDate: String,
		nextDate: String,
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				dateYear: '',
				dateMonth: '',
				balanceSheetDate: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '年份',
					prop: 'dateYear',
					attrs: {
						col: 24,
						disabled: true,
						placeholder: '请输入年份',
					},
				},
				{
					type: 'el-input',
					label: '月份',
					prop: 'dateMonth',
					attrs: {
						col: 24,
						disabled: true,
						placeholder: '请输入月份',
					},
				},
				{
					type: 'el-date-picker',
					label: '结账日',
					prop: 'balanceSheetDate',
					attrs: {
						col: 24,
						type: 'date',
						valueFormat: 'yyyy-MM-dd',
						placeholder: '请选择结账日',
						pickerOptions: {
							disabledDate: time => {
								const { dateYear, dateMonth } = this.formData
								if (!dateYear || !dateMonth) return false

								const timestamp = time.getTime()
								const currentYearMonth = this.dayjs(`${dateYear}-${dateMonth}`)

								// 小于上月的结账日
								const less = this.prevDate ? timestamp <= this.dayjs(this.prevDate).valueOf() : false
								// 大于下月的结账日
								const more = this.nextDate ? timestamp >= this.dayjs(this.nextDate).valueOf() : false
								return (
									timestamp > currentYearMonth.add(1, 'month').endOf('month').valueOf() ||
									timestamp < currentYearMonth.startOf('month').valueOf() ||
									less ||
									more
								)
							},
						},
					},
				},
			],
			formAttrs: {
				rules: {
					dateYear: [
						{
							required: true,
							message: '请输入年份',
							trigger: 'blur',
						},
					],
					dateMonth: [
						{
							required: true,
							message: '请输入月份',
							trigger: 'blur',
						},
					],
					balanceSheetDate: [
						{
							required: true,
							message: '请选择结账日',
							trigger: 'change',
						},
					],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				await updateBalanceSheetDate({
					id: this.formData.id,
					balanceSheetDate: this.formData.balanceSheetDate,
				})
				this.$message.success('结账日修改成功')
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
</style>
