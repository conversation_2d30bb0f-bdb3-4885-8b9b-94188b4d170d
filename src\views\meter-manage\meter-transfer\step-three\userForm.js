export default function (_this) {
	const resident = [
		{
			type: 'el-input',
			label: '用户名称',
			prop: 'userName',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-select',
			label: '用户类型',
			prop: 'userSubType',
			options: _this.$store.getters.dataList.resident
				? _this.$store.getters.dataList.resident.map(item => {
						return {
							label: item.sortName,
							value: Number(item.sortValue),
						}
				  })
				: [],
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '手机号',
			prop: 'userMobile',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '曾用名',
			prop: 'nameUsedBefore',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '合同编号',
			prop: 'contractNum',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '联系人',
			prop: 'contactPeople',
			attrs: {
				col: 6,
			},
		},

		{
			type: 'el-input',
			label: '电话',
			prop: 'contactPhone',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '户数',
			prop: 'households',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '人口数',
			prop: 'resiPopulation',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '产权人名称',
			prop: 'propertyOwner',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '邮编',
			prop: 'zipCode',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '身份证号',
			prop: 'certificateNo',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-select',
			label: '其他证件',
			prop: 'certificateType',
			options: _this.$store.getters.dataList.certificateType
				? _this.$store.getters.dataList.certificateType.map(item => {
						return {
							label: item.sortName,
							value: Number(item.sortValue),
						}
				  })
				: [],
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '证件号码',
			prop: 'otherCertificateNo',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '电子邮箱',
			prop: 'email',
			attrs: {
				col: 6,
			},
		},

		{
			type: 'el-select',
			label: '收费方式',
			prop: 'chargingMethod',
			options: _this.$store.getters.dataList.chargingMethod
				? _this.$store.getters.dataList.chargingMethod.map(item => {
						return {
							label: item.sortName,
							value: Number(item.sortValue),
						}
				  })
				: [],
			attrs: {
				col: 6,
			},
		},

		{
			type: 'el-input',
			label: '邮寄地址',
			prop: 'mailingAddress',
			attrs: {
				col: 12,
				type: 'textarea',
				maxlength: '64',
				showWordLimit: true,
				autosize: {
					minRows: 2,
					maxRows: 2,
				},
			},
		},
		// 其他手机
		{
			type: 'slot',
			slotName: 'otherMobile',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'slot',
			slotName: 'contract',
			label: '购房合同',
			prop: 'purchaseContractUrl',
			attrs: {
				col: 6,
			},
		},
	]
	const company = [
		{
			type: 'el-select',
			label: '所属企业',
			prop: 'enterpriseNumber',
			options: _this.companyList,
			attrs: {
				col: 6,
				remote: true,
				filterable: true,
				loading: false,
				placeholder: '请输入企业编号进行查询',
				remoteMethod: _this.apiGetCompanyBySelect,
			},
			events: {
				change: _this.handleChangeEnterprise,
			},
		},
		{
			type: 'el-input',
			label: '企业名称',
			prop: 'enterpriseName',
			attrs: {
				col: 6,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '用户名称',
			prop: 'userName',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '联系人',
			prop: 'contactPeople',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '手机号',
			prop: 'userMobile',
			attrs: {
				col: 6,
			},
		},

		{
			type: 'el-input',
			label: '电话',
			prop: 'contactPhone',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '邮编',
			prop: 'zipCode',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '合同编号',
			prop: 'contractNum',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-select',
			label: '用户类型',
			prop: 'userSubType',
			options:
				_this.$store.getters?.dataList?.business?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '电子邮箱',
			prop: 'email',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '邮寄地址',
			prop: 'mailingAddress',
			attrs: {
				col: 12,
				type: 'textarea',
				maxlength: '64',
				showWordLimit: true,
				autosize: {
					minRows: 2,
					maxRows: 2,
				},
			},
		},
		{
			type: 'slot',
			slotName: 'businessLicenseUrl',
			label: '营业执照合同',
			prop: 'businessLicenseUrl',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'slot',
			slotName: 'purchaseContractUrl',
			label: '购房合同',
			prop: 'purchaseContractUrl',
			attrs: {
				col: 6,
			},
		},
		// 其他手机
		{
			type: 'slot',
			slotName: 'otherMobile',
			attrs: {
				col: 6,
			},
		},
	]

	return {
		3: resident,
		4: company,
	}
}
