<template>
	<div class="gc-app-main">
		<section>
			<transition mode="out-in" name="fade-transform">
				<gc-keep-alive />
			</transition>
		</section>
		<!-- 下载IC卡组件弹窗 -->
		<ic-download-dialog />
	</div>
</template>

<script>
import IcDownloadDialog from '@/components/IcDownloadDialog'

export default {
	name: 'GcAppMain',
	components: { IcDownloadDialog },
	data() {
		return {
			routerView: true,
		}
	},
	computed: {
		cachedViews() {
			const { tags } = this.$store.state.tagsView
			const result = []
			tags.map(item => {
				if (item.meta && item.meta.keepAlive) {
					result.push(item.fullPath || item.path)
				}
			})
			return result
		},
	},
}
</script>
