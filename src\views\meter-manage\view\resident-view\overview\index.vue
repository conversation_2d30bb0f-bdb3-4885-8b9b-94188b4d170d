<template>
	<div class="tab-content overview">
		<div class="bg-overflow">
			<div class="layout-overview">
				<div class="left">
					<!-- 用户信息 -->
					<div class="data-container fn-flex">
						<div class="user-info">
							<GcModelHeader title="用户信息" :icon="require('@/assets/images/icon/title-user.png')">
								<template slot="right">
									<div v-show="archivesStatus" class="title-btn">
										<span v-has="'cpm_user_modify-user2'" @click="showResidentModify = true">
											<img src="@/assets/images/icon/view-rename.png" />
											修改
										</span>

										<span
											v-has="'cpm_archives_transfer-user'"
											v-show="archivesStatus !== 3"
											@click="showUserInfoTransfer = true"
										>
											<img src="@/assets/images/icon/view-transfer.png" />
											过户
										</span>
									</div>
								</template>
							</GcModelHeader>
							<GcGroupDetail :data="userData"></GcGroupDetail>
							<div class="user-footer footer">
								<div class="footer-left">
									<span class="color-blue" @click="showUserInfoMore = true">
										<i class="iconfontCis icon-read"></i>
										查看更多
									</span>
									<span class="color-blue" @click="showUserInfoInvoice = true">
										<i class="iconfontCis icon-read"></i>
										查看开票信息
									</span>
									<span
										v-show="tabData.user && tabData.user.userId"
										class="color-blue"
										@click="handleView('user')"
									>
										<i class="iconfontCis icon-read"></i>
										查看用户视图
									</span>
								</div>
							</div>
						</div>
						<div class="user-info">
							<GcModelHeader title="联系人" :icon="require('@/assets/images/icon/title-user.png')">
								<template slot="right">
									<div
										class="title-btn"
										v-has="'cpm_archives_contact_add'"
										v-show="tabData && tabData.archives"
									>
										<span @click="handleEditContact('add')">
											<img src="@/assets/images/icon/view-transfer.png" />
											新增联系人
										</span>
									</div>
								</template>
							</GcModelHeader>
							<div style="padding: 10px 20px; height: 160px">
								<GcTable :columns="contactColumns" :table-data="contactTable">
									<template v-slot:invoiceInfo="{ row }">
										<el-button type="text" @click="handleViewContactInvoice(row)">
											查看开票信息
										</el-button>
									</template>
									<template v-slot:operate="{ row }">
										<el-button
											v-has="'cpm_archives_contact_modify'"
											type="text"
											@click="handleEditContact('edit', row)"
										>
											修改
										</el-button>
										<el-button
											v-has="'cpm_archives_contact_remove'"
											type="text"
											@click="handleEditContact('delete', row)"
										>
											删除
										</el-button>
									</template>
								</GcTable>
							</div>
						</div>
					</div>
					<!-- 表具信息 -->
					<div class="data-container fn-flex">
						<div class="meter-info">
							<GcModelHeader
								title="表具信息"
								:icon="require('@/assets/images/icon/title-multi-check.png')"
							>
								<template slot="right" v-if="virtualMeterType !== 1 && archivesStatus">
									<span
										v-has="'cpm_archives_intallMeter'"
										v-show="!tabData.meter || !tabData.meter.meterId"
										@click="showMeterInfoInstall = true"
									>
										<img src="@/assets/images/icon/iconPark-add-mode.png" />
										装表
									</span>
									<span
										v-has="'cpm_archives_switching-meter'"
										v-show="tabData.meter && tabData.meter.meterId"
										@click="showMeterInfoChange = true"
									>
										<img src="@/assets/images/icon/view-change.png" />
										换表
									</span>
									<span
										v-has="'cpm_archives_remove-meter'"
										v-show="tabData.meter && tabData.meter.meterId"
										@click="showMeterInfoSplit = true"
									>
										<img src="@/assets/images/icon/view-split.png" />
										拆表
									</span>
									<span
										v-has="'cpm_archives_disable'"
										v-show="archivesStatus == 1"
										@click="showMeterInfoWaterOutage = true"
									>
										<img src="@/assets/images/icon/view-stop.png" />
										停水
									</span>
									<span
										v-has="'cpm_archives_enable'"
										v-show="archivesStatus == 2"
										@click="showMeterInfoWaterRecover = true"
									>
										<img src="@/assets/images/icon/view-recover.png" />
										恢复用水
									</span>
								</template>
							</GcModelHeader>
							<GcGroupDetail :data="meterData"></GcGroupDetail>
							<div class="footer">
								<div class="footer-left">
									<span class="color-blue" @click="showMeterInfoMore = true">
										<i class="iconfontCis icon-read"></i>
										查看更多
									</span>
									<span
										v-show="tabData.meter && tabData.meter.meterId"
										style="cursor: pointer; color: #2080f7; margin-left: 15px"
										@click="
											$router.push({
												path: '/waterMeterManage/waterMeterView',
												query: {
													meterId: tabData.meter && tabData.meter.meterId,
												},
											})
										"
									>
										<i class="iconfontCis icon-read"></i>
										查看表具视图
									</span>
									<span
										v-has="'cpm_meter_modify-meter2'"
										v-show="
											virtualMeterType !== 1 &&
											archivesStatus &&
											tabData.meter &&
											tabData.meter.meterId
										"
										@click="showMeterInfoModify = true"
										style="cursor: pointer; color: #2080f7; margin-left: 15px"
									>
										<i class="iconfontCis icon-modify"></i>
										修改
									</span>
								</div>
								<div class="footer-right"></div>
							</div>
						</div>
						<div class="invoice-info">
							<GcModelHeader
								title="默认开票信息"
								:icon="require('@/assets/images/icon/title-multi-check.png')"
							></GcModelHeader>
							<GcGroupDetail :data="invoiceData"></GcGroupDetail>
							<div class="footer" v-has="'cpm_invoice-buyer_buyerList2'">
								<div class="footer-right">
									<span @click="setDefaultInvoiceInfo()">
										<i class="iconfontCis icon-modify"></i>
										设置默认开票信息
									</span>
								</div>
							</div>
						</div>
					</div>
					<!-- 册本信息、价格信息 -->
					<div class="data-container fn-flex">
						<div class="records-info">
							<GcModelHeader
								title="册本信息"
								:icon="require('@/assets/images/icon/title-common-parameters.png')"
							></GcModelHeader>
							<GcGroupDetail :data="recordsData"></GcGroupDetail>
							<div class="footer">
								<div class="footer-left"></div>
								<div v-show="virtualMeterType !== 1 && archivesStatus" class="footer-right">
									<span
										v-has="'plan-collection_meterReadingTask_createMeterReadingCollection'"
										v-show="archivesStatus !== 3 && tabData.meter && tabData.meter.meterId"
										@click="
											$router.push(
												`/meterReading/meterReadingCollect?archivesIdentity=${tabData.archives.archivesIdentity}`,
											)
										"
									>
										<img src="@/assets/images/icon/view-collect.png" />
										追收抄表
									</span>
									<span
										v-has="'plan-collection_meterReadingBook_updateArchivesSeq1'"
										@click="handleRevise"
									>
										<i class="iconfontCis icon-modify"></i>
										修改册内序号
									</span>
								</div>
							</div>
						</div>
						<div class="price-info">
							<GcModelHeader
								title="价格信息"
								:icon="require('@/assets/images/icon/title-cash.png')"
							></GcModelHeader>
							<GcGroupDetail :data="priceData">
								<template #currentPrice>
									<div class="price-show">
										<p v-for="(item, index) in levelPrice" :key="index">
											<span>{{ item.key }}</span>
											<span v-show="item.value">{{ item.value }}</span>
										</p>
									</div>
								</template>
								<template #surcharge>
									<div class="price-show">
										<p v-for="(item, index) in surchangePrice" :key="index">
											<span>{{ item.key }}</span>
											<span>{{ item.value }}</span>
										</p>
									</div>
								</template>
							</GcGroupDetail>
							<div class="footer">
								<div class="footer-left"></div>
								<div class="footer-right" v-show="archivesStatus">
									<span v-has="'cpm_archives_change-price'" @click="showPriceExchange = true">
										<i class="iconfontCis icon-modify"></i>
										换价
									</span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- 缴费信息 -->
				<div class="right">
					<GcModelHeader title="缴费信息" :icon="require('@/assets/images/icon/title-ic-card.png')">
						<span
							v-has="'billing_bill-adjust_adjustment1'"
							v-show="
								selectBillData.length === 1 &&
								selectBillData[0].invoiceStatus === 0 &&
								[0, 2, 5].includes(selectBillData[0].billStatus) &&
								selectBillData[0].pushFlag !== 1
							"
							slot="right"
							style="color: #ff9d57; cursor: pointer"
							@click="goToAdjustment"
						>
							<i class="iconfontCis icon-feiyongtiaozheng"></i>
							费用调整
						</span>
					</GcModelHeader>
					<div class="table-container">
						<GcTable
							:columns="columns"
							:table-data="tableData"
							needType="selection"
							@selectChange="selectChange"
							row-key="billId"
						/>
					</div>
					<!-- 分割线 -->
					<div class="devide"></div>
					<div class="recharge-field" v-show="tabData.price && tabData.price.levelPrice">
						<div class="field-title">各阶余量</div>
						<div class="field-value allowance">{{ cycSurplus }}</div>
					</div>
					<div class="recharge-field" v-if="tabData.archives">
						<div class="field-title">表卡余额</div>
						<div class="field-value balance">
							￥ {{ Number(tabData.archives.meterBalanceAmount || 0).toFixed(2) }}
						</div>
					</div>
					<div class="recharge-field" v-for="(value, name, index) in getTotalAmount" :key="index">
						<div class="field-title">{{ name }}</div>
						<div class="field-value money">{{ value }}</div>
					</div>
					<div class="recharge-ops" v-show="archivesStatus">
						<el-button
							size="small"
							class="recharge"
							v-click-blur
							:disabled="isPayButtonDisabled"
							@click="handleSubmit"
						>
							缴 费
						</el-button>
					</div>
					<div class="flex">
						<div class="footer-left"></div>
						<div class="footer-right charge-panel-actions" v-show="archivesStatus">
							<span v-has="'cpm_urge_payment_urgeRegister2'" @click="handleUrge">
								<img src="@/assets/images/icon/view-urge.png" />
								催缴登记
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 弹窗-->
		<!-- 用户信息更多-->
		<UserInfoMore
			:tabData="{
				archives: tabData.archives,
				user: tabData.user,
			}"
			:show.sync="showUserInfoMore"
		/>
		<!-- 开票信息 -->
		<UserInfoInvoice
			:tabData="{
				user: tabData.user,
			}"
			:show.sync="showUserInfoInvoice"
		/>
		<!-- 修改居民用户信息 -->
		<UserInfoResidentModify
			:detailData="{
				archives: tabData.archives,
				user: tabData.user,
				invoiceBuyer: tabData.invoiceBuyer,
			}"
			:show.sync="showResidentModify"
			@success="$emit('refresh')"
		/>
		<!-- 联系人设置 -->
		<ContactPeopleSetting
			ref="contactPeopleSettingRef"
			:show.sync="showContactPeople"
			@success="$emit('refresh')"
		/>
		<!-- 联系人开票信息 -->
		<ContactInvoice ref="contactInvoiceRef" :show.sync="showContactInvoice" />
		<!-- 过户 -->
		<UserInfoTransfer
			:show.sync="showUserInfoTransfer"
			:detailData="tabData"
			permissionCode="cpm_archives_transfer-user"
			@success="$emit('refresh')"
		/>
		<!-- 表具信息更多 -->
		<MeterInfoMore :tabData="tabData" :show.sync="showMeterInfoMore" />
		<!-- 修改水表 -->
		<MeterInfoModify
			:show.sync="showMeterInfoModify"
			:detailData="tabData"
			permissionCode="cpm_meter_modify-meter2"
			@refresh="$emit('refresh')"
		/>
		<!-- 装表 -->
		<MeterInfoInstall
			:show.sync="showMeterInfoInstall"
			:detailData="tabData"
			permissionCode="cpm_archives_intallMeter"
			@refresh="$emit('refresh')"
		/>
		<!-- 换表 -->
		<MeterInfoChange
			:show.sync="showMeterInfoChange"
			:detailData="{
				archivesIdentity: tabData.archives && tabData.archives.archivesIdentity,
				archivesId: tabData.archives && tabData.archives.archivesId,
				meterNo: tabData.meter && tabData.meter.meterNo,
				meterReading: tabData.meter && tabData.meter.meterReading,
			}"
			@refresh="$emit('refresh')"
		/>

		<!-- 拆表 -->
		<MeterInfoSplit
			:detailData="{
				archivesIdentity: tabData.archives && tabData.archives.archivesIdentity,
				archivesId: tabData.archives && tabData.archives.archivesId,
				meterNo: tabData.meter && tabData.meter.meterNo,
			}"
			:show.sync="showMeterInfoSplit"
			@refresh="$emit('refresh')"
		/>
		<!-- 停水 -->
		<MeterInfoWaterOutage
			:show.sync="showMeterInfoWaterOutage"
			:detailData="{
				archivesIdentity: tabData.archives && tabData.archives.archivesIdentity,
				archivesId: tabData.archives && tabData.archives.archivesId,
				meterNo: tabData.meter && tabData.meter.meterNo,
			}"
			@refresh="$emit('refresh')"
		/>
		<!-- 恢复用水 -->
		<MeterInfoWaterRecover
			:show.sync="showMeterInfoWaterRecover"
			:detailData="{
				archivesIdentity: tabData.archives && tabData.archives.archivesIdentity,
				archivesId: tabData.archives && tabData.archives.archivesId,
				meterNo: tabData.meter && tabData.meter.meterNo,
			}"
			@refresh="$emit('refresh')"
		/>
		<!-- 修改册内序号 -->
		<RecordsUpdateSeq
			ref="updateSeqDialogRef"
			:show.sync="showRecordsSeq"
			permissionCode="plan-collection_meterReadingBook_updateArchivesSeq1"
			@success="$emit('refresh')"
		/>
		<!-- 换价 -->
		<PriceExchange
			:show.sync="showPriceExchange"
			:detailData="{
				archivesId: tabData.archives && tabData.archives.archivesId,
				priceId: tabData.price && tabData.price.priceId,
				levelPrice: tabData.price && tabData.price.levelPrice,
			}"
			permissionCode="cpm_archives_change-price"
			@refresh="$emit('refresh')"
		/>
		<!-- 催缴登记 -->
		<CheckInDialog
			permissionCode="cpm_urge_payment_urgeRegister2"
			:show.sync="showPaymentRemind"
			:data="selectBillData"
			@success="$emit('refresh')"
		/>

		<GcElDialog
			ref="setDefaultInvoiceInfoDialogRef"
			:show="setDefaultInvoiceInfoDialogShow"
			:class="{ 'ok-disabled': defaultInvoiceInfoDisabled }"
			title="选择开票信息"
			width="700px"
			okText="设为默认"
			class="invoice-dialog"
			@close="setDefaultInvoiceInfoDialogShow = false"
			@cancel="setDefaultInvoiceInfoDialogShow = false"
			@ok="handleDefaultInvoiceInfoSet"
		>
			<InvoiceInfoSelect
				:is-show="setDefaultInvoiceInfoDialogShow"
				:archivesIds="tabData.archives && tabData.archives.archivesId"
				:type="'resident'"
				@error="handleError"
				@select="handleInvoiceSelect"
			/>
		</GcElDialog>
	</div>
</template>

<script>
import { apiGetBillArrearsList } from '@/api/costManage.api'
import { apiGetContactList, apiDeleteContact } from '@/api/meterManage.api'
import { setDefaultInvoiceInfo } from '@/api/print.api'
import { getfilterName } from '@/utils'
import { accAdd } from '@/utils/calc.js'
import { isBlank } from '@/utils/validate.js'
import CheckInDialog from '@/views/arrearage-manage/components/checkInDialog.vue'
import UserInfoMore from '../../components/userInfo-more/index.vue'
import UserInfoInvoice from '../../components/userInfo-invoice/index.vue'
import UserInfoTransfer from '../../components/userInfo-transfer/index.vue'
import UserInfoResidentModify from '../../components/userInfo-resident-modify/index.vue'
import ContactPeopleSetting from '../../components/contact-people-setting/index.vue'
import ContactInvoice from '../../components/contact-invoice/index.vue'
import MeterInfoMore from '../../components/meterInfo-more/index.vue'
import MeterInfoChange from '../../components/meterInfo-change/index.vue'
import MeterInfoSplit from '../../components/meterInfo-split/index.vue'
import MeterInfoWaterOutage from '../../components/meterInfo-water-outage/index.vue'
import MeterInfoWaterRecover from '../../components/meterInfo-water-recover/index.vue'
import MeterInfoModify from '../../components/meterInfo-modify/index.vue'
import MeterInfoInstall from '../../components/meterInfo-install/index.vue'
import RecordsUpdateSeq from '../../components/records-update-seq/index.vue'
import PriceExchange from '../../components/price-exchange/index.vue'
import InvoiceInfoSelect from '@/components/PaymentInvoiceDialog/InvoiceInfoSelect.vue'

export default {
	name: '',
	components: {
		UserInfoMore,
		UserInfoInvoice,
		UserInfoTransfer,
		UserInfoResidentModify,
		ContactInvoice,
		ContactPeopleSetting,
		MeterInfoMore,
		MeterInfoChange,
		MeterInfoSplit,
		MeterInfoWaterOutage,
		MeterInfoWaterRecover,
		MeterInfoModify,
		MeterInfoInstall,
		RecordsUpdateSeq,
		PriceExchange,
		CheckInDialog,
		InvoiceInfoSelect,
	},
	props: {
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	watch: {
		tabData: {
			handler() {
				this.handleSearch()
			},
			deep: true,
		},
	},
	data() {
		return {
			// 用户信息弹窗
			showUserInfoMore: false,
			showUserInfoInvoice: false,
			showUserInfoTransfer: false,
			showResidentModify: false,

			// 表具信息弹窗
			showMeterInfoMore: false,
			showMeterInfoChange: false,
			showMeterInfoSplit: false,
			showMeterInfoWaterOutage: false,
			showMeterInfoWaterRecover: false,
			showMeterInfoModify: false,
			showMeterInfoInstall: false,
			//  册本信息弹窗
			showRecordsSeq: false,
			showPriceExchange: false,
			showPaymentRemind: false,
			showPaymentInvoice: false,
			// 联系人表格
			showContactInvoice: false,
			showContactPeople: false,
			// 开票信息
			setDefaultInvoiceInfoDialogShow: false,
			selectedInvoiceInfo: null,
			defaultInvoiceInfoDisabled: true,
			contactColumns: [
				{
					key: 'contactName',
					name: '联系人',
					tooltip: true,
				},
				{
					key: 'contactMobile',
					name: '手机号码',
					tooltip: true,
				},
				{
					key: 'invoiceInfo',
					name: '开票信息',
				},
				{
					key: 'operate',
					name: '操作',
				},
			],
			contactTable: [],
			// 缴费信息表格
			columns: [
				{
					key: 'billDate',
					name: '账期',
					width: 85,
					align: 'center',
					tooltip: true,
				},
				{
					key: 'receivableAmount',
					name: '应缴金额(元)',
					width: 120,
					align: 'center',
					tooltip: true,
				},
				{
					key: 'useAmount',
					name: '用水量(吨)',
					width: 100,
					align: 'center',
					tooltip: true,
				},
			],
			tableData: [],
			totalAmount: 0,
			billNos: [],
			selectBillData: [],
		}
	},
	computed: {
		// 用户信息
		userData() {
			const list = [
				{
					key: '用户名称',
					value: '--',
					field: 'userName',
				},
				{
					key: '手机号',
					value: '--',
					field: 'userMobile',
				},
				{
					key: '用户类型',
					value: '--',
					field: 'userSubType',
				},
				{
					key: '收费方式',
					value: '--',
					field: 'chargingMethod',
				},
			]

			const extractedData = this.tabData?.user || {}
			const getValue = (field, value) => {
				const { chargingMethod = [], resident = [] } = this.$store.getters.dataList || {}
				switch (field) {
					case 'chargingMethod':
						return getfilterName(chargingMethod, value, 'sortValue', 'sortName')
					case 'userSubType':
						return getfilterName(resident, value, 'sortValue', 'sortName')
					default:
						return value
				}
			}
			list.forEach(item => {
				item.value = getValue(item.field, extractedData[item.field])
			})
			return {
				list,
				row: 2,
			}
		},
		// 表具信息
		meterData() {
			const list = [
				{
					key: '水表编号',
					value: '--',
					field: 'meterNo',
				},
				{
					key: '水表标号',
					value: '--',
					field: 'baseMeterNo',
				},
				{
					key: '表具类型',
					value: '--',
					field: 'meterTypeName',
				},
				{
					key: '水表状态',
					value: '--',
					field: 'meterStatus',
				},
				{
					key: '指针数',
					value: '--',
					field: 'meterReading',
				},
				{
					key: '上次抄表时间',
					value: '--',
					field: 'meterReadingDate',
				},
			]
			const extractedData = Object.assign({}, ...Object.values(this.tabData))
			const getValue = (field, value) => {
				const { meterStatus = [] } = this.$store.getters.dataList || {}

				switch (field) {
					case 'meterStatus':
						return getfilterName(meterStatus, value, 'sortValue', 'sortName')
					default:
						return value
				}
			}
			list.forEach(item => {
				item.value = getValue(item.field, extractedData[item.field])
			})
			return {
				list,
				row: 3,
			}
		},
		// 开票信息
		invoiceData() {
			const list = [
				{
					key: '开票类型',
					value: '--',
					field: 'invoiceType',
				},
				{
					key: '所有者名称',
					value: '--',
					field: 'userName',
				},
				{
					key: '银行账户',
					value: '--',
					field: 'bankAccount',
				},
				{
					key: '纳税人识别号',
					value: '--',
					field: 'taxpayerIdentity',
				},
				{
					key: '电子邮箱',
					value: '--',
					field: 'email',
				},
				{
					key: '手机号码',
					value: '--',
					field: 'phoneNumber',
				},
			]
			const { invoiceType = [] } = this.$store.getters.dataList || {}
			const extractedData = this.tabData.invoiceBuyer || {}

			list.forEach(item => {
				item.value = extractedData[item.field]
				if (item.field === 'invoiceType') {
					item.value = getfilterName(invoiceType, item.value, 'sortValue', 'sortName')
				}
			})
			return {
				list,
				row: 3,
			}
		},
		// 册本信息
		recordsData() {
			const list = [
				{
					key: '表册编号',
					value: '--',
					field: 'bookNo',
				},
				{
					key: '册本类型',
					value: '--',
					field: 'bookTypeDesc',
				},
				{
					key: '坊别',
					value: '--',
					field: 'alleyCode',
				},
				{
					key: '抄表员',
					value: '--',
					field: 'meterReadingStaffName',
				},
				{
					key: '抄表周期',
					value: '--',
					field: 'meterReadingCycleDesc',
				},
				{
					key: '抄表范围',
					value: '--',
					field: 'archivesNoRange',
				},
				{
					key: '表卡数',
					value: '--',
					field: 'meterNum',
				},
				{
					key: '册内序号',
					value: '--',
					field: 'recordSeq',
				},
			]
			const extractedData = Object.assign({}, ...Object.values(this.tabData))
			list.forEach(item => {
				item.value = extractedData[item.field]
			})
			return {
				list,
				row: 3,
			}
		},
		// 价格信息
		priceData() {
			const extractedData = Object.assign({}, ...Object.values(this.tabData))
			const list = [
				{
					key: '价格编号',
					value: '--',
					field: 'priceCode',
				},
				{
					key: '价格名称',
					value: '--',
					field: 'priceName',
				},
				{
					key: '计费类型',
					field: 'billingTypeId',
				},
				{
					key: '当前价格',
					slot: 'currentPrice',
					col: extractedData.levelPrice ? 2 : 1,
				},
				{
					key: '附加费',
					slot: 'surcharge',
					col: extractedData.levelPrice ? 1 : 2,
				},
			]

			const getValue = (field, value) => {
				const { billingType = [] } = this.$store.getters.dataList || {}

				switch (field) {
					case 'billingTypeId':
						return getfilterName(billingType, value, 'sortValue', 'sortName')
					default:
						return value
				}
			}
			list.forEach(item => {
				item.value = getValue(item.field, extractedData[item.field])
			})
			return {
				list,
				row: 3,
			}
		},
		// 当前价格
		levelPrice() {
			const str = this.tabData.price && this.tabData.price.priceDesc
			const arr =
				str &&
				str.split('<br/>').map(item => {
					const [key, value] = item.split(' ')
					return { key, value }
				})
			return arr
		},
		// 附加费
		surchangePrice() {
			const list = this.tabData.price && this.tabData.price.priceBillItemList
			const arr =
				list &&
				list.map(item => {
					return { key: item.itemName, value: item.billItemPrice + '元/吨' }
				})
			return arr
		},
		// 各阶余量
		cycSurplus() {
			let str = '--'
			const cycSurplusValue = this.tabData.archives && this.tabData.archives.cycSurplus
			if (Object.keys(this.tabData).length > 0) {
				if (!isBlank(cycSurplusValue)) {
					let middleArr = cycSurplusValue.split('|')
					middleArr.map((item, index) => {
						if (index == middleArr.length - 1) {
							middleArr[index] = '∞'
						}
					})
					str = middleArr.join(' | ') + ` （吨）`
				}
			}
			return str
		},
		getTotalAmount() {
			let arr = { 总应缴金额: '￥ 0.00' }
			if (!isBlank(this.totalAmount)) {
				let num = 0
				let money = this.totalAmount.toFixed(2)
				// 解决保留两位小数后为-0.00的显示问题
				if (money == 0) {
					num = num.toFixed(2)
				} else {
					num = money
				}
				arr = {
					总应缴金额: '￥ ' + num,
				}
			} else {
				arr = { 总应缴金额: '￥ 0.00' }
			}
			return arr
		},
		// 缴费按钮是否禁用
		isPayButtonDisabled() {
			return !this.billNos.length
		},
		archivesStatus() {
			return this.tabData.archives && this.tabData.archives.archivesStatus
		},
		virtualMeterType() {
			return this.tabData.archives && this.tabData.archives.virtualMeterType
		},
	},
	methods: {
		// 获取联系人列表
		async _apiGetContactList() {
			const { archivesId } = this.tabData.archives || {}
			try {
				const data = await apiGetContactList({ archivesId })
				this.contactTable = data
			} catch (error) {
				console.log(error)
			}
		},
		setDefaultInvoiceInfo() {
			this.setDefaultInvoiceInfoDialogShow = true
		},
		async handleDefaultInvoiceInfoSet() {
			const invoiceInfo = this.selectedInvoiceInfo
			if (!invoiceInfo) {
				this.$message.error('请选择开票信息')
				return
			}
			const loading = this.$loading({
				target: this.$refs.setDefaultInvoiceInfoDialogRef.$el.querySelector('.el-dialog__body'),
				lock: true,
				text: '设置中...',
				spinner: 'el-icon-loading',
				background: 'rgba(255, 255, 255, 0.5)',
			})
			const result = await setDefaultInvoiceInfo({
				archivesId: this.tabData.archives.archivesId,
				invoiceBuyerId: invoiceInfo.invoiceBuyerId,
			}).catch(e => {
				console.error(e)
				this.$message.error(e.message || '默认开票信息设置失败')
			})
			loading.close()
			if (result === null) {
				this.setDefaultInvoiceInfoDialogShow = false
				this.$message.success('设置成功')
				this.tabData.invoiceBuyer = { ...invoiceInfo }
			}
		},
		handleInvoiceSelect(invoiceInfo) {
			if (invoiceInfo && !invoiceInfo.defualt) {
				this.defaultInvoiceInfoDisabled = false
			} else {
				this.defaultInvoiceInfoDisabled = true
			}
			this.selectedInvoiceInfo = invoiceInfo
		},
		// 编辑联系人
		handleEditContact(type, row) {
			if (type === 'edit') {
				this.showContactPeople = true
				this.$refs.contactPeopleSettingRef.assignForm(row)
				this.$refs.contactPeopleSettingRef.setOtherInfo({
					archivesId: this.tabData?.archives?.archivesId || '',
					contactId: row.contactId,
				})
			} else if (type === 'add') {
				this.showContactPeople = true
				this.$refs.contactPeopleSettingRef.setOtherInfo({
					archivesId: this.tabData?.archives?.archivesId || '',
					contactId: '',
				})
			} else {
				const message = `正在对${row.contactName}联系人移除，请确认是否继续?`
				this.$confirm(message, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning',
				}).then(async () => {
					await apiDeleteContact({ archivesId: this.tabData?.archives?.archivesId, contactId: row.contactId })
					this.$message.success('删除成功')
					this.handleSearch()
				})
			}
		},
		handleError(message) {
			this.$message.error(message)
			this.setDefaultInvoiceInfoDialogShow = false
		},
		// 查看联系人开票信息
		handleViewContactInvoice(row) {
			this.$refs.contactInvoiceRef.setUserInfo(row)
			this.showContactInvoice = true
		},
		// 查看视图
		handleView(type) {
			if (type === 'user') {
				const { userId } = this.tabData.user
				this.$router.push({
					path: '/userManage/userView',
					query: {
						userId,
					},
				})
			}
		},
		// 修改册内序号
		handleRevise() {
			const data = Object.assign({}, ...Object.values(this.tabData))
			this.$refs.updateSeqDialogRef.setFormData(data)
			this.showRecordsSeq = true
		},
		// 费用调整
		goToAdjustment() {
			const obj = this.selectBillData[0]
			const year = obj.year

			this.$router.push({
				path: '/costManage/feeAdjustment',
				query: {
					id: obj.billId,
					year,
					code: 'billing_bill-adjust_adjustment1',
				},
			})
		},
		// 待缴费列表
		async _apiGetBillArrearsList() {
			this.tableData = []
			const { archivesIdentity = '' } = this.tabData.archives || {}
			const params = {
				current: 1,
				size: 99999,
				archivesIdentity,
			}
			const { records } = await apiGetBillArrearsList(params)
			this.tableData = records.map(item => {
				return {
					...item,
					archivesNo: archivesIdentity,
					arrearsAmount: item.receivableAmount,
					billYear: item.year,
				}
			})
		},
		selectChange(arr) {
			const amountList = arr.map(item => item.receivableAmount)
			this.selectBillData = arr.map(item => {
				return {
					...item,
					waterAmount: item.useAmount,
				}
			})
			this.billNos = arr.map(item => item.billNo)
			this.totalAmount = this.accAddMultiple(amountList)
		},
		accAddMultiple(args) {
			return args.reduce((acc, curr) => accAdd(acc, curr), 0)
		},
		handleSubmit() {
			this.$router.push({
				path: '/costManage/paymentPage',
				query: {
					billNos: this.billNos?.length ? this.billNos.join(',') : '',
				},
			})
		},
		handleSearch() {
			this._apiGetBillArrearsList()
			this._apiGetContactList()
			this.billNos = []
			this.selectBillData = []
		},
		// 催缴登记
		handleUrge() {
			if (!this.selectBillData.length) {
				this.$message.error('请选择账单')
				return
			}
			this.showPaymentRemind = true
		},
	},
}
</script>

<style lang="scss" scoped>
@import '../style/common.scss';
.left {
	// width: 70% !important;
	flex: 1 !important;
	width: auto !important;
	.data-container {
		margin-top: 15px !important;
	}
	.data-container:nth-of-type(1) {
		margin-top: 0 !important;
	}
}
.right {
	flex: none !important;
	padding: 0 20px;
	width: 390px;
	.model-header {
		padding: 0 0;
	}
	.table-container {
		height: 300px;
	}
	// 分割线
	.devide {
		border-bottom: 1px dashed #cccccc;
		margin: $base-margin 0;
	}
	.recharge-field {
		margin-bottom: $base-margin;
		.field-title {
			color: $base-color-9;
			font-size: $base-font-size-small;
		}
		.field-value {
			margin-top: 10px;
		}
		.field-value.allowance {
			color: $base-color-4;
			font-size: $base-font-size-bigger;
		}
		.field-value.money {
			color: $base-color-red;
			font-size: 20px;
		}
		.field-value.balance {
			color: $base-color-blue;
			font-size: $base-font-size-bigger;
		}
	}
	// 充值按钮
	.recharge-ops {
		padding-top: $base-padding;
		display: flex;
		.el-button {
			flex: 1;
			border-radius: 17px;
		}
		.recharge {
			background: linear-gradient(180deg, #789fff 0%, #3565df 100%);
			color: #ffffff;
			&:hover {
				background: linear-gradient(180deg, #93b2ff 0%, #5e84e6 100%);
			}
			&:focus {
				background: linear-gradient(180deg, #6c8fe6 0%, #2e59c4 100%);
			}
			&.is-disabled {
				background: linear-gradient(180deg, #aec5ff 0%, #86a3ec 100%);
			}
		}
	}
	.flex {
		margin-top: 20px;
		display: flex;
		justify-content: space-between;
		span {
			display: flex;
			align-items: center;
			cursor: pointer;
			color: #ff9d57;
			margin-right: 20px;
		}
	}
}
.title-btn {
	display: flex;
	gap: 20px;
	img {
		width: 14px;
		height: 14px;
		margin-right: 3px;
	}
	span {
		display: flex;
		align-items: center;
		cursor: pointer;
		color: #ff9d57;
	}
}
.footer,
.footer-right,
.meter-info .title-right {
	img {
		width: 14px;
		height: 14px;
		margin-right: 3px;
	}
}
.footer-right {
	margin-left: auto;
}
.meter-info ::v-deep {
	.title-right {
		display: flex;
		align-items: center;
		cursor: pointer;
		color: #ff9d57;
		gap: 15px;
		span {
			display: inline-flex;
			align-items: center;
		}
	}
}
.charge-panel-actions {
	display: flex;
	gap: 10px;
}
.fn-flex {
	display: flex;
	gap: 20px;
	background: #eceff8;
	.user-info,
	.meter-info,
	.invoice-info,
	.records-info {
		min-width: calc(50% - 10px);
		max-width: calc(50% - 10px);
		background: #fff;
	}
	.company-user-info,
	.price-info {
		width: 50%;
		background: #fff;
		.price-show {
			p {
				display: flex;
				gap: 4px;
				span {
					flex: 100%;
				}
			}
			p + p {
				padding-top: 12px;
			}
		}
	}
}
.invoice-dialog {
	&::v-deep {
		.el-dialog {
			max-height: calc(100vh - 200px);
		}
		.el-dialog__body {
			padding-top: 0;
		}
	}
	&.ok-disabled {
		::v-deep {
			.gc-button-one {
				filter: grayscale(100%);
				opacity: 0.5;
				pointer-events: none;
			}
		}
	}
}
</style>
