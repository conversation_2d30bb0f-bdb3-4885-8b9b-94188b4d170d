<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handleChangePage"
			/>
			<!-- 弹窗 -->
			<AdjustLedger
				ref="adjustLedgerRef"
				:show.sync="showAdjustLedger"
				@success="handleChangePage({ page: 1 })"
			/>
		</div>
	</div>
</template>

<script>
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiGetCounterList } from '@/api/costManage.api'
import { getColumn } from './tableColumn'
import AdjustLedger from './adjust-ledger'
export default {
	components: { AdjustLedger },
	data() {
		return {
			formData: {
				orgCode: '',
				archivesIdentity: '',
				userName: '',
				enterpriseNumber: '',
				collectionAgreementNumber: '',
				payMode: '',
				payRecordStatus: '',
				payTime: '',
				year: this.dayjs().format('YYYY'),
				payChannel: '1',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
				},
				{
					type: 'el-date-picker',
					label: '年份',
					prop: 'year',
					attrs: {
						type: 'year',
						valueFormat: 'yyyy',
						clearable: false,
						pickerOptions: {
							disabledDate(time) {
								const currentYear = new Date().getFullYear()
								const minYear = 2007
								return time.getFullYear() < minYear || time.getFullYear() > currentYear + 1
							},
						},
					},
				},
				{
					type: 'el-select',
					label: '缴费方式',
					prop: 'payChannel',
					options: this.$store.getters.dataList.payChannel
						? this.$store.getters.dataList.payChannel
								.filter(item => !['联合收费', '人民银行托收', '工商银行托收'].includes(item.sortName))
								.map(item => {
									return {
										label: item.sortName,
										value: item.sortValue,
									}
								})
						: [],
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
				},
				{
					type: 'el-input',
					label: '用户名称',
					prop: 'userName',
				},
				{
					type: 'el-input',
					label: '企业编号',
					prop: 'enterpriseNumber',
				},
				{
					type: 'el-input',
					label: '托收协议号',
					prop: 'collectionAgreementNumber',
				},
				{
					type: 'el-select',
					label: '付款方式',
					prop: 'payMode',
					options:
						this.$store.getters?.dataList?.payMode?.map(item => {
							return {
								label: item.sortName,
								value: item.sortValue,
							}
						}) || [],
				},
				{
					type: 'el-select',
					label: '缴费状态',
					prop: 'payRecordStatus',
					options:
						this.$store.getters?.dataList?.payRecordStatus?.map(item => {
							return {
								label: item.sortName,
								value: item.sortValue,
							}
						}) || [],
				},
				{
					type: 'el-date-picker',
					label: '缴费时间',
					prop: 'payTime',
					attrs: {
						type: 'datetimerange',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						defaultTime: ['00:00:00', '23:59:59'],
						rangeSeparator: 'L',
					},
				},
			],
			formAttrs: {
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
					year: [ruleRequired('必填')],
					payTime: [{ validator: this.validatePayTime, trigger: 'change' }],
				},
			},
			columns: getColumn(this),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			showAdjustLedger: false,
			loading: false,
		}
	},
	computed: {
		orgOptions() {
			return this.formItems[0].options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.$nextTick(() => {
						this.handleSearch()
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
				})
				if (formParams.payTime && formParams.payTime.length > 1) {
					formParams.payTimeStart = this.dayjs(formParams.payTime[0]).format('YYYY-MM-DD HH:mm:ss')
					formParams.payTimeEnd = this.dayjs(formParams.payTime[1]).format('YYYY-MM-DD HH:mm:ss')
				}
				delete formParams.payTime
				const { records, total } = await apiGetCounterList(formParams)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		validatePayTime(rule, value, callback) {
			if (value && value.length === 2) {
				const startYear = new Date(value[0]).getFullYear()
				const endYear = new Date(value[1]).getFullYear()
				if (startYear != this.formData.year) {
					callback(new Error('缴费时间必须在所选年份内'))
					return
				}
				if (startYear !== endYear) {
					callback(new Error('缴费时间不能跨年'))
					return
				}
			}
			callback()
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.handleChangePage({ page: 1 })
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
	},
}
</script>

<style lang="scss" scoped>
.page-left {
	flex: 0 310px;
}
.el-form {
	flex: 1;
	padding: 0 10px;
	overflow: auto;
}
::v-deep {
	.el-range-editor.el-input__inner {
		height: auto !important;
		flex-wrap: wrap;
		width: 100%;
		.el-range-input {
			float: left;
			width: 192px;
			line-height: 32px;
			height: 32px;
			text-align: left;
			box-sizing: border-box;
			&:first-child {
				flex: 0 0 190px;
			}
		}
		.el-range-separator {
			margin-left: -5px;
			width: 25px;
			font-size: 14px;
			color: #c0c4cc;
			box-sizing: border-box;
		}
		.el-range__close-icon {
			margin-left: 90%;
			margin-top: -60px;
			float: left;
			box-sizing: border-box;
		}
	}
}
</style>
