export function getFormItems(_this) {
	const commonFormItems = [
		{
			type: 'slot',
			slotName: 'dividLine',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'span',
			label: '表卡编号',
			prop: 'archivesIdentity',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'span',
			label: '当前用户名称',
			prop: 'currentUserName',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'span',
			label: '表卡欠费金额',
			prop: 'arrearsTotalAmount',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'span',
			label: '表卡账户余额',
			prop: 'meterBalanceAmount',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'span',
			label: '过户操作人员',
			prop: 'operatorPerson',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'slot',
			slotName: 'dividLine',
			attrs: {
				col: 24,
			},
		},
	]
	const residentFormItems = [
		{
			type: 'slot',
			slotName: 'userInfo',
			label: '基本信息',
			attrs: {
				col: 24,
				className: 'title',
			},
		},
		{
			type: 'el-select',
			label: '用户分类',
			prop: 'userType',
			options:
				_this.$store.getters?.dataList?.userType?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 8,
				disabled: true,
			},
			events: {
				change: _this.handleChangeUserType,
			},
		},
		{
			type: 'el-input',
			label: '用户名称',
			prop: 'userName',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-select',
			label: '用户类型',
			prop: 'userSubType',
			options:
				_this.$store.getters?.dataList?.resident?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '手机号',
			prop: 'userMobile',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '曾用名',
			prop: 'nameUsedBefore',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '合同编号',
			prop: 'contractNum',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '联系人',
			prop: 'contactPeople',
			attrs: {
				col: 8,
			},
		},

		{
			type: 'el-input',
			label: '电话',
			prop: 'contactPhone',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '户数',
			prop: 'households',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '人口数',
			prop: 'resiPopulation',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '产权人名称',
			prop: 'propertyOwner',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '邮编',
			prop: 'zipCode',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '身份证号',
			prop: 'certificateNo',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-select',
			label: '其他证件',
			prop: 'certificateType',
			options: _this.$store.getters.dataList.certificateType
				? _this.$store.getters.dataList.certificateType.map(item => {
						return {
							label: item.sortName,
							value: Number(item.sortValue),
						}
				  })
				: [],
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '证件号码',
			prop: 'otherCertificateNo',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '电子邮箱',
			prop: 'email',
			attrs: {
				col: 8,
			},
		},

		{
			type: 'el-select',
			label: '收费方式',
			prop: 'chargingMethod',
			options: _this.$store.getters.dataList.chargingMethod
				? _this.$store.getters.dataList.chargingMethod.map(item => {
						return {
							label: item.sortName,
							value: Number(item.sortValue),
						}
				  })
				: [],
			attrs: {
				col: 8,
			},
		},

		{
			type: 'el-input',
			label: '邮寄地址',
			prop: 'mailingAddress',
			attrs: {
				col: 8,
				type: 'textarea',
				maxlength: '64',
				showWordLimit: true,
				autosize: {
					minRows: 2,
					maxRows: 4,
				},
			},
		},
		// 其他手机
		{
			type: 'slot',
			slotName: 'otherMobile',
			prop: 'otherMobile',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'slot',
			slotName: 'purchaseContractUrl',
			label: '购房合同',
			prop: 'purchaseContractUrl',
			attrs: {
				col: 16,
			},
		},
		{
			type: 'slot',
			slotName: 'otherInfo',
			label: '开票信息',
			attrs: {
				col: 24,
				className: 'title',
			},
		},
		{
			type: 'slot',
			label: '纳税人识别号',
			slotName: 'taxpayerIdentity',
			prop: 'taxpayerIdentity',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '开票抬头',
			prop: 'buyerName',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '开户银行',
			prop: 'openBank',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '银行账户',
			prop: 'bankAccount',
			attrs: {
				col: 8,
			},
		},
	]
	const companyFormItems = [
		{
			type: 'slot',
			slotName: 'userInfo',
			label: '基本信息',
			attrs: {
				col: 24,
				className: 'title',
			},
		},
		{
			type: 'el-select',
			label: '用户分类',
			prop: 'userType',
			options:
				_this.$store.getters?.dataList?.userType?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 8,
				disabled: true,
			},
			events: {
				change: _this.handleChangeUserType,
			},
		},
		{
			type: 'el-select',
			label: '所属企业',
			prop: 'enterpriseNumber',
			options: [],
			attrs: {
				col: 8,
				placeholder: '请输入企业编号',
				disabled: true,
			},
			events: {
				change: _this.handleChangeEnterprise,
			},
		},
		{
			type: 'el-input',
			label: '企业名称',
			prop: 'enterpriseName',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '用户名称',
			prop: 'userName',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '联系人',
			prop: 'contactPeople',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '手机号',
			prop: 'userMobile',
			attrs: {
				col: 8,
			},
		},

		{
			type: 'el-input',
			label: '电话',
			prop: 'contactPhone',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '邮编',
			prop: 'zipCode',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '合同编号',
			prop: 'contractNum',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '电子邮箱',
			prop: 'email',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '邮寄地址',
			prop: 'mailingAddress',
			attrs: {
				col: 16,
				type: 'textarea',
				maxlength: '64',
				showWordLimit: true,
				autosize: {
					minRows: 2,
					maxRows: 4,
				},
			},
		},
		{
			type: 'el-select',
			label: '用户类型',
			prop: 'userSubType',
			options:
				_this.$store.getters?.dataList?.business?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 8,
			},
		},
		// 其他手机
		{
			type: 'slot',
			slotName: 'otherMobile',
			prop: 'otherMobile',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'slot',
			slotName: 'businessLicenseUrl',
			label: '营业执照合同',
			prop: 'businessLicenseUrl',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'slot',
			slotName: 'purchaseContractUrl',
			label: '购房合同',
			prop: 'purchaseContractUrl',
			attrs: {
				col: 8,
			},
		},

		{
			type: 'slot',
			slotName: 'otherInfo',
			label: '开票信息',
			attrs: {
				col: 24,
				className: 'title',
			},
		},
		{
			type: 'el-select',
			label: '开票类型',
			prop: 'invoiceType',
			options:
				_this.$store.getters?.dataList?.invoiceType?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 8,
			},
		},
		{
			type: 'slot',
			label: '纳税人识别号',
			slotName: 'taxpayerIdentity',
			prop: 'taxpayerIdentity',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '开票抬头',
			prop: 'buyerName',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '开户银行',
			prop: 'openBank',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '银行账户',
			prop: 'bankAccount',
			attrs: {
				col: 8,
			},
		},
	]
	const priceFormItems = [
		{
			type: 'slot',
			label: '价格信息',
			attrs: {
				col: 24,
				className: 'title',
			},
		},
		{
			type: 'el-select',
			label: '价格名称',
			prop: 'priceName',
			options: _this.priceNameList,
			attrs: {
				col: 8,
			},
			events: {
				change: _this.handleChangePrice,
			},
		},
		{
			type: 'el-select',
			label: '价格编号',
			prop: 'priceCode',
			options: _this.priceCodeList,
			attrs: {
				filterable: true,
				col: 8,
			},
			events: {
				change: _this.handleChangePrice,
			},
		},
	]

	const arr =
		_this.formData?.userType === 4
			? commonFormItems.concat(companyFormItems, priceFormItems)
			: commonFormItems.concat(residentFormItems, priceFormItems)
	return arr
}
