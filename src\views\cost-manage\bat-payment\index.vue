<template>
	<batch-ops-layout :tabArr="tabArr" @tab-switch="tabSwitch">
		<template #batch-ops>
			<gc-model-header
				title="上传文件"
				:icon="require('@/assets/images/icon/title-uploading.png')"
			></gc-model-header>
			<div class="ops-content">
				<!-- 提醒 -->
				<upload-tip v-if="uploadStep == 1 || uploadStep == 2"></upload-tip>
				<!-- 模板下载 -->
				<template-download
					:templateList="templateList"
					v-if="uploadStep == 1 || uploadStep == 2"
				></template-download>
				<div class="upload-file-box">
					<!-- 上传文件 -->
					<upload-file
						:select-text-inital.sync="selectTextInital"
						:upload-step.sync="uploadStep"
						:msg="msg"
						:loading-text.sync="loadingText"
						:loading.sync="loading"
						@before-upload-file="beforeUploadFile"
						@upload-down-fail="uploadDownFail"
						@upload-confirm="uploadConfirm"
					></upload-file>
				</div>
			</div>
		</template>
		<template #batch-record>
			<div class="table-show">
				<gc-table
					:columns="columns"
					:table-data="recordList"
					:show-page="true"
					:total="page.total"
					@current-page-change="currentPageChange"
					:current-page.sync="page.current"
					:page-size="page.size"
				></gc-table>
			</div>
		</template>
	</batch-ops-layout>
</template>

<script>
import uploadMix from '@/components/UploadFile/index.js'
import BatchOpsLayout from '@/components/BatchOpsLayout/index.vue'
import UploadTip from '@/components/UploadTip/index.vue'
import TemplateDownload from '@/components/TemplateDownload/index.vue'
import UploadFile from '@/components/UploadFile/index.vue'
import identity from '@/mixin/identity.js'
import { excelVerList, excelVerListWater } from '@/consts/templateVersion.js'

export default {
	name: 'BatRecharge',
	mixins: [uploadMix, identity],
	components: {
		BatchOpsLayout,
		UploadTip,
		TemplateDownload,
		UploadFile,
	},
	data() {
		return {
			tabArr: [
				{
					key: 'recharge',
					value: '批量充值',
				},
				{
					key: 'rechargeRecord',
					value: '批量充值记录',
				},
			],
			selectTextInital: '选择文件',
		}
	},
	computed: {
		templateList() {
			let arr = [
				{
					title: '批量充值',
					url: `excel/批量充值模板-${this.version}.xlsx`,
				},
			]
			if (this.realm === 'water') {
				arr = [
					{
						title: '批量充值',
						url: `excel-water/批量充值模板-${this.version}.xlsx`,
					},
				]
			}
			return arr
		},
		version() {
			if (this.realm === 'water') {
				return excelVerListWater['bat-recharge']
			} else {
				return excelVerList['bat-recharge']
			}
		},
	},
	mounted() {
		// 获取其他费用数据字典
		this.getCostType()
	},
	methods: {
		// tab切换
		tabSwitch(flag) {
			if (flag === 'rechargeRecord') {
				this.getRecordList(
					{
						page: this.page.current,
					},
					'bat-recharge',
				)
			}
		},
		// 选择文件前校验
		beforeUploadFile(data, cb) {
			cb(true)
		},
		// 上传
		uploadConfirm(val) {
			let excelObj = val
			const sourceData = excelObj['list']
			if (sourceData.length > 500) {
				this.$message.error('一次性最多可导入 500 条记录，请拆分文档！')
				return
			}
			this.uploadLogic(sourceData, excelObj['type'])
		},
		//翻页
		currentPageChange({ page, size }) {
			this.page.current = page
			this.page.size = size
			this.getRecordList({ page, size }, 'bat-recharge')
		},
	},
	deactivated() {
		if (this.uploadStep == 3 || this.uploadStep == 4) {
			this.uploadStep = 1
			this.selectTextInital = '上传文件'
		}
	},
}
</script>
<style lang="scss" scoped>
.ops-content {
	padding: 10px 24px 0 24px;
	display: flex;
	flex-direction: column;
	height: calc(100% - 60px);
	align-items: flex-start;
	.upload-file-box {
		flex: 1;
		width: 100%;
	}
}
.table-show {
	height: 100%;
	::v-deep {
		.status0::before,
		.status1::before {
			content: '';
			display: inline-block;
			width: 6px;
			height: 6px;
			border-radius: 50%;
			margin-right: 5px;
			vertical-align: middle;
		}
		.status0::before {
			background: #ec6b60;
		}
		.status1::before {
			background: #12b3c7;
		}
		.no-ops {
			color: #cccccc;
		}
		.down-fail-record {
			color: #ec6b60;
			font-size: 14px;
		}
	}
}
</style>
