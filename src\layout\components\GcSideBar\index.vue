<template>
	<el-scrollbar class="gc-side-bar" :class="{ 'is-collapse': collapse }">
		<gc-logo v-if="layout === 'vertical'" />
		<el-menu
			:collapse="collapse"
			:collapse-transition="false"
			:default-active="activeMenu"
			menu-trigger="click"
			mode="vertical"
			:unique-opened="uniqueOpened"
		>
			<template v-for="route in handleRoutes">
				<gc-menu class="menu-item" v-if="!route.hidden" :key="route.fullPath" :item="route" />
			</template>
		</el-menu>
	</el-scrollbar>
</template>

<script>
import variables from '@/styles/variables/variables.scss'
import { mapGetters } from 'vuex'
import { uniqueOpened } from '@/config'
import { handleActivePath } from '@/utils/routes'

export default {
	name: 'GcSideBar',
	props: {
		layout: {
			type: String,
			default: 'vertical',
		},
	},
	data() {
		return {
			activeMenu: '',
			uniqueOpened,
			variables,
		}
	},
	computed: {
		...mapGetters({
			collapse: 'settings/collapse',
			extra: 'settings/extra',
			routes: 'routes/routes',
		}),
		handleRoutes() {
			return this.routes.flatMap(route => (route.menuHidden === true && route.children ? route.children : route))
		},
		handlePartialRoutes() {
			const activeMenu = this.routes.find(_ => _.name === this.extra.first)
			return activeMenu ? activeMenu.children : []
		},
	},
	watch: {
		$route: {
			handler(route) {
				this.activeMenu = handleActivePath(route)
			},
			immediate: true,
		},
	},
}
</script>

<style lang="scss" scoped>
@mixin active {
	&:hover {
		color: $base-color-white;
		background-color: $base-menu-background-active !important;
	}

	&.is-active {
		color: $base-color-white;
		background-color: $base-menu-background-active !important;
	}
}

::v-deep {
	.el-submenu .el-menu-item {
		min-width: initial;
		color: rgba(255, 255, 255, 0.7);
	}
	.el-menu--collapse .el-menu .el-submenu {
		min-width: initial;
	}
}

.gc-side-bar {
	position: fixed;
	top: 0;
	bottom: 0;
	left: 0;
	z-index: $base-z-index + 1;
	width: $base-left-menu-width;
	height: 100vh;
	overflow: hidden;
	background: $base-menu-background;
	box-shadow: $base-box-shadow;
	transition: $base-transition;

	&.is-collapse {
		width: $base-left-menu-width-min;
		border-right: 0;

		::v-deep {
			.el-menu--collapse.el-menu {
				> .el-menu-item,
				> .el-submenu {
					text-align: center;
				}

				> .el-submenu .el-menu {
					padding: 0;
					margin-left: 2px;
					min-width: 146px;
				}
			}

			.el-submenu .el-menu-item {
				min-width: none;
			}

			.el-menu-item,
			.el-submenu {
				text-align: left;
			}

			.el-menu--collapse {
				padding-top: 30px;
				border-right: 0;

				.el-submenu__icon-arrow {
					right: 10px;
					margin-top: -3px;
				}
			}
		}
	}

	::v-deep {
		.el-scrollbar__wrap {
			overflow-x: hidden;
		}

		.el-menu {
			border: 0;
		}

		.el-menu-item,
		.el-submenu__title {
			height: $base-menu-item-height;
			overflow: hidden;
			line-height: $base-menu-item-height;
			text-overflow: ellipsis;
			white-space: nowrap;
			vertical-align: middle;

			i {
				color: inherit;
			}
		}

		.el-menu-item {
			@include active;
		}
	}
}
</style>
