import router from '@/router'
import store from '@/store'
import Vue from 'vue'
import { hasPermission, passRouterValidate } from '@/utils/routes'
import { getToken } from '@/utils/storage'

// 路由白名单，不需要token校验的路由
const routesWhiteList = ['/login', '/om-login']

router.beforeEach(async (to, from, next) => {
	Vue.prototype.$progress.start()
	let hasToken = getToken()
	if (hasToken) {
		if (store.getters.routes.length) {
			// 禁止已登录用户返回登录页
			if (to.path === '/login') {
				// next({ path: "/" });
				// Vue.prototype.$progress.done();
				handleNoPermission(from, next)
			} else {
				if (to.matched.length && to.matched.every(item => hasPermission(store.getters.permissions, item))) {
					// 处理跳转路由需要权限的页面

					// 需要先选择租户或组织机构的路由，条件不成立时进行阻断
					if (!passRouterValidate(to)) {
						store.commit('routes/SET_TARGET_ROUTER_PATH', to.path)
						Vue.prototype.$baseEventBus.$emit('render-router')
						next(false)
						Vue.prototype.$progress.done()
					} else {
						next()
					}
				} else {
					handleNoPermission(from, next, to.matched.length)
				}
			}
		} else {
			if (to.path === '/login') {
				next()
			} else {
				handleNoPermission(from, next)
			}
		}
	} else if (routesWhiteList.includes(to.path)) {
		next()
		Vue.prototype.$progress.done()
	} else {
		next({ path: '/login', replace: true })
		Vue.prototype.$progress.done()
	}
})
const handleNoPermission = (from, next, showNotification = false) => {
	if (showNotification) {
		Vue.prototype.$notify({
			message: '暂无权限访问，请联系管理员！',
			title: '提示',
			offset: 60,
			type: 'warning',
		})
	}
	next({ ...from, replace: true })
	Vue.prototype.$progress.done()
}

router.afterEach(() => {
	if (Vue.prototype.$progress.status) Vue.prototype.$progress.done()
})
