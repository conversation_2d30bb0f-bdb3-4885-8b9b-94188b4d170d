<template>
	<div class="wrapper">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch(false)">查询</el-button>
				<el-button @click="handleReset">重置</el-button>
			</el-form-item>
		</GcFormSimple>
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			@current-page-change="handlePageChange"
		/>
	</div>
</template>

<script>
import { getMeterModifyRecord } from '@/api/waterMeter.api'

export default {
	name: 'StatusRecord',
	components: {},
	data() {
		return {
			formData: {
				dateRange: [],
				operatorType: '',
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '变更日期',
					prop: 'dateRange',
					attrs: {
						type: 'daterange',
						clearable: true,
						valueFormat: 'yyyy-MM-dd',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
					},
				},
				{
					type: 'el-select',
					label: '操作类型',
					prop: 'operatorType',
					options:
						this.$store.getters?.dataList?.operatorMeterType?.map(item => {
							return {
								label: item.sortName,
								value: Number(item.sortValue),
							}
						}) || [],
					attrs: {
						clearable: true,
						placeholder: '请选择操作类型',
					},
				},
			],
			formAttrs: { inline: true, labelWidth: '90px' },

			loading: false,
			tableData: [],
			columns: [
				{
					key: 'createTime',
					name: '变更时间',
					tooltip: true,
				},
				{
					key: 'createStaffName',
					name: '操作人员',
					tooltip: true,
				},
				{
					key: 'operatorTypeName',
					name: '操作类型',
					tooltip: true,
				},
				{
					key: 'meterStatusDesc',
					name: '操作后水表状态',
					tooltip: true,
				},
				{
					key: 'meterReading',
					name: '指针数',
					tooltip: true,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},

	created() {
		// this.getList();
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.getList(1)
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { dateRange, ...rest } = this.formData
				const { total = 0, records = [] } = await getMeterModifyRecord({
					meterId: this.$route.query.meterId,
					size,
					current,
					...rest,
					modifyTimeStart: dateRange && dateRange.length > 0 ? dateRange[0] : '',
					modifyTimeEnd: dateRange && dateRange.length > 0 ? dateRange[1] : '',
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	padding: 20px;
}
</style>
