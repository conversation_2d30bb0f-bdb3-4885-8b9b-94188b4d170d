<template>
	<GcDetailCard :detail-card-info="detailCardInfo" :header-num="headerNum">
		<template #tag>
			<span :class="`tag status${archivesStatus}`">
				<i></i>
				{{ archivesStatusStr }}
			</span>
		</template>
		<template #header-button>
			<el-button type="primary" size="small" class="more" @click="showCardMore = true">查看更多</el-button>
		</template>
		<template #card-content>
			<div class="card-content">
				<el-button
					v-has="'cpm_archives_print'"
					v-show="reqData && reqData.archives"
					type="text"
					class="print-btn"
					@click="handlePrintMeterCard"
				>
					<i class="el-icon-printer"></i>
					表卡打印
				</el-button>
				<div class="content-item" v-for="(item, index) in displayList" :key="index">
					<p class="field">{{ item.key }}</p>
					<p class="value">
						{{ item.value }}
						<el-button
							v-if="
								(item.field === 'isVirtual' && item.value == '是') ||
								(item.field === 'virtualMeterType' && item.value == '虚表')
							"
							type="text"
							@click="showPreview = true"
						>
							<i class="iconfontCis icon-read"></i>
							查看水量分配
						</el-button>
					</p>
				</div>
			</div>
		</template>
		<template #card-footer>
			<div class="card-footer">
				<el-button
					v-has="'cpm_archives_update'"
					v-show="archivesStatus !== 3"
					type="text"
					class="blue"
					@click="goModify"
				>
					<i class="iconfontCis icon-modify"></i>
					修改
				</el-button>
				<el-button
					v-has="'cpm_archives_transfer-tablecard'"
					v-show="
						[1, 2].includes(archivesStatus) && reqData.archives && reqData.archives.virtualMeterType === 0
					"
					type="text"
					@click="goTransfer"
				>
					<img src="@/assets/images/icon/view-return.png" />
					表卡转换
				</el-button>
				<el-button
					v-has="'cpm_archives_close'"
					v-show="archivesStatus !== 3"
					type="text"
					@click="showCardCancel = true"
				>
					<img src="@/assets/images/icon/view-cut.png" />
					销卡
				</el-button>
				<el-button
					v-has="'cpm_archives_resume-archives'"
					v-show="archivesStatus === 3"
					type="text"
					@click="showCardRecover = true"
				>
					<img src="@/assets/images/icon/view-back.png" />
					恢复表卡
				</el-button>
			</div>

			<!-- 弹窗 -->
			<!-- 虚表表卡 -->
			<VirtualPreview
				:show.sync="showPreview"
				:currentRow="{
					archivesId: reqData.archives && reqData.archives.archivesId,
					distributionMode: reqData.archives && reqData.archives.distributionMode,
				}"
			/>
			<!-- 更多 -->
			<CardMore :tabData="reqData" :show.sync="showCardMore" />
			<!-- 销卡 -->
			<CardCancel
				:show.sync="showCardCancel"
				:detailData="{
					archivesId: reqData.archives && reqData.archives.archivesId,
					archivesIdentity: reqData.archives && reqData.archives.archivesIdentity,
					virtualMeterType: reqData.archives && reqData.archives.virtualMeterType,
				}"
				permissionCode="cpm_archives_close"
				@refresh="$emit('refresh')"
			/>
			<!-- 恢复表卡 -->
			<CardRecover
				:show.sync="showCardRecover"
				:data="{
					archivesId: reqData.archives && reqData.archives.archivesId,
					archivesIdentity: reqData.archives && reqData.archives.archivesIdentity,
					meterNo: reqData.meter && reqData.meter.meterNo,
					meterId: reqData.meter && reqData.meter.meterId,
					virtualMeterType: reqData.archives && reqData.archives.virtualMeterType,
				}"
				permissionCode="cpm_archives_resume-archives"
				@refresh="$emit('refresh')"
			/>
			<!-- 表卡修改 -->
			<CardModify
				permissionCode="cpm_archives_update"
				:show.sync="showCardModify"
				:detailData="reqData"
				@refresh="$emit('refresh')"
			/>
		</template>
	</GcDetailCard>
</template>

<script>
import { getfilterName } from '@/utils'
import { yesOrNoEnum } from '@/consts/enums.js'
import { meterCardPrint } from '@/views/print/meter/index.js'
import CardMore from '../../components/card-more/index.vue'
import CardCancel from '../../components/card-cancel/index.vue'
import CardRecover from '../../components/card-recover/index.vue'
import CardModify from '../../components/card-modify/index.vue'
import VirtualPreview from '@/views/meter-manage/virtual-meter/manage-virtual-meter/virtual-preview/index.vue'
export default {
	components: {
		VirtualPreview,
		CardMore,
		CardCancel,
		CardRecover,
		CardModify,
	},
	props: {
		reqData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	computed: {
		headerNum() {
			const { archivesIdentity = '' } = this.reqData.archives || {}
			return {
				key: '表卡编号',
				value: archivesIdentity,
				field: 'archivesIdentity',
			}
		},
		// 表卡状态中文
		archivesStatusStr() {
			const { archiveState = [] } = this.$store.getters.dataList || {}
			const { archivesStatus } = this.reqData.archives || {}
			const valueStr = archivesStatus
				? getfilterName(archiveState, archivesStatus, 'sortValue', 'sortName')
				: '--'
			return valueStr
		},
		// 表卡状态
		archivesStatus() {
			return this.reqData.archives && this.reqData.archives.archivesStatus
		},
		//卡片展示字段
		displayList() {
			const list = [
				{
					key: '表卡类型',
					value: '--',
					field: 'virtualMeterType',
				},
				{
					key: '表卡状态',
					value: '--',
					field: 'archivesStatus',
				},
				{
					key: '建档时间',
					value: '--',
					field: 'archivesTime',
				},
				{
					key: '是否虚分',
					value: '--',
					field: 'isVirtual',
				},
				{
					key: '年累计量',
					value: '--',
					field: 'yearMeterCreditQuantity',
				},
				{
					key: '表具地址',
					value: '--',
					field: 'addressFullName',
				},
			]
			const extractedData = Object.assign({}, ...Object.values(this.reqData))
			const getValue = (field, value) => {
				const { archiveState = [], virtualMeterType = [] } = this.$store.getters.dataList || {}

				switch (field) {
					case 'archivesStatus':
						return getfilterName(archiveState, value, 'sortValue', 'sortName')
					case 'virtualMeterType':
						return getfilterName(virtualMeterType, value, 'sortValue', 'sortName')
					case 'isVirtual':
						return yesOrNoEnum[value]
					default:
						return value == undefined ? '--' : value
				}
			}

			list.forEach(item => {
				item.value = getValue(item.field, extractedData[item.field])
			})
			return list
		},
	},
	data() {
		return {
			detailCardInfo: {
				bgUrl: require('@/assets/images/bg/pic-file.png'), //背景图url
				signUrl: require('@/assets/images/icon/title-file.png'), //header中的标志的url
				cardName: '表卡信息',
			},
			showPreview: false,
			showCardMore: false,
			showCardCancel: false,
			showCardRecover: false,
			showCardModify: false,
		}
	},
	methods: {
		goModify() {
			this.showCardModify = true
		},
		goTransfer() {
			const isVirtual = this.reqData.archives.isVirtual
			if (isVirtual === 1) {
				this.$message.error('虚分表不能进行表卡转换')
				return
			}
			const { archivesId } = this.$route.query
			this.$router.push({
				path: '/meterManage/meterTransfer',
				query: {
					archivesId,
					code: 'transfer',
				},
			})
		},

		// 表卡打印
		handlePrintMeterCard() {
			meterCardPrint('cpm_archives_print', this.$route.query.archivesId, this)
		},
	},
}
</script>

<style lang="scss" scoped>
.detail-card {
	.more {
		background: linear-gradient(180deg, #789fff 0%, #3565df 100%);
		border: none;
	}
	.tag {
		background: #e9eef5;
		color: #a5b1c2;
		i {
			background: #a5b2c2;
		}
	}
	.tag.status0 {
		background: #ecfdff;
		color: #24a2b3;
		i {
			background: #12b3c7;
		}
	}
	.tag.status1 {
		background: #ecf4ff;
		color: $base-color-blue;
		i {
			background: $base-color-blue;
		}
	}
	.card-content {
		.print-btn {
			float: right;
			padding-right: 10px;
			i {
				margin-right: 2px;
			}
		}
		i {
			vertical-align: text-bottom;
		}
	}
	.card-footer {
		padding: 0 15px;
		display: flex;
		justify-content: flex-start;
		gap: 10px;
		::v-deep .el-button {
			font-size: 14px;
			color: $base-color-yellow;
			padding-bottom: 0;
			i {
				padding-right: 3px;
				font-size: 16px;
			}
			span {
				display: flex;
				align-items: center;
			}
		}
		.el-button + .el-button {
			margin-left: 2px;
			margin-right: 2px;
		}
		.blue {
			color: $base-color-blue;
		}
		img {
			width: 14px;
			height: 14px;
			margin-right: 3px;
		}
	}
}
</style>
