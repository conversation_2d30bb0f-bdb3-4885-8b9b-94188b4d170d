<template>
	<gc-el-dialog :show="isShow" title="导入新一年免污水费用表" custom-top="120px" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template #freeSewageExcel>
				<el-upload
					ref="uploadRef"
					action=" "
					:limit="1"
					accept=".xls,.xlsx"
					:auto-upload="false"
					:on-change="handleUploadChange"
					:on-remove="handleUploadRemove"
				>
					<el-button size="small" type="primary">上传文件</el-button>
					<div slot="tip" class="el-upload__tip">
						<el-button v-has="'cpm_planWaste_downloadExcel'" type="text" @click="handleDownloadTemplate">
							下载免污水费表卡模版
						</el-button>
					</div>
				</el-upload>
			</template>
		</GcFormSimple>
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button :loading="saveLoading" :isDisabled="saveLoading" @click.native="handleSave">确定导入</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { exportBlob } from '@/utils/index.js'
import { downloadPlanWasteTemplate, importPlanWaste } from '@/api/specialWater.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				orgCode: '',
				planYear: '',
				freeSewageExcel: null,
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						col: 24,
						placeholder: '请选择营业分公司',
					},
				},
				{
					type: 'el-date-picker',
					label: '计划年份',
					prop: 'planYear',
					attrs: {
						col: 24,
						type: 'year',
						valueFormat: 'yyyy',
						placeholder: '请选择计划年份',
						pickerOptions: {
							disabledDate: time => {
								const timestamp = time.getTime()
								const currentYear = this.dayjs().startOf('year').valueOf()
								return timestamp < currentYear
							},
						},
					},
				},
				{
					type: 'slot',
					slotName: 'freeSewageExcel',
					label: '免污水表卡',
					prop: 'freeSewageExcel',
					attrs: {
						col: 24,
						placeholder: '',
					},
				},
			],
			formAttrs: {
				labelPosition: 'right',
				labelWidth: '110px',
				rules: {
					orgCode: [{ required: true, message: '请选择营业分公司', trigger: 'change' }],
					planYear: [{ required: true, message: '请选择计划年份', trigger: 'change' }],
					freeSewageExcel: [{ required: true, message: '请上传文件', trigger: 'change' }],
				},
			},
			saveLoading: false,
		}
	},
	methods: {
		// 选择文件
		handleUploadChange(file) {
			this.formData.freeSewageExcel = file.raw
			this.$refs.formRef.validateField('freeSewageExcel')
		},
		// 移除文件
		handleUploadRemove() {
			this.formData.freeSewageExcel = null
			this.$refs.formRef.validateField('freeSewageExcel')
		},
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.saveLoading = true
				const { orgCode, planYear, freeSewageExcel } = this.formData
				const formData = new FormData()
				formData.append('freeSewageExcel', freeSewageExcel)
				formData.append('orgCode', orgCode)
				formData.append('planYear', planYear)
				try {
					const res = await importPlanWaste(formData)
					const contentType = res.headers['content-type']
					const mainContentType = contentType.split(';')[0].trim()

					if (mainContentType === 'application/json') {
						// Content-Type 是 application/json; 上传成功
						try {
							// 由于 responseType 是 'blob'，需要将 Blob 转换为文本
							const reader = new FileReader()
							reader.onload = () => {
								const response = JSON.parse(reader.result)
								if (response.code !== 0) {
									this.$message.error(response.message)
									return
								} else {
									this.$message.success(response.message)
									this.$emit('success')
									this.isShow = false
								}
							}
							reader.readAsText(res.data)
						} catch (error) {
							this.$message.error(error)
						}
					} else {
						// Content-Type 不是 application/json，代表部分导入失败，下载文件
						exportBlob(res.data, '导入失败', 'xlsx')
						this.$emit('success')
						this.isShow = false
					}
				} catch (error) {
					const reader = new FileReader()
					reader.onload = () => {
						const response = JSON.parse(reader.result)
						this.$message.error(response.message)
					}
					reader.readAsText(error.response.data)
				} finally {
					this.saveLoading = false
				}
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.$refs.uploadRef.clearFiles()
			this.saveLoading = false
			this.isShow = false
		},
		// 下载导入模版
		async handleDownloadTemplate() {
			const res = await downloadPlanWasteTemplate()
			exportBlob(res, '免污水费用表导入模版')
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
</style>
