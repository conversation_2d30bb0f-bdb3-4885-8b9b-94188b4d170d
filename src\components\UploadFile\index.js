// 说明 -此文件为抽取出来的批量操作的公共逻辑，请作为mixin引入
/*地址+表具模板-meter
	地址+用户模板-user
	批量补气模板-supplement
	批量充值模板-recharge
	批量登记其他费用-register
	完整档案模板-complete
*/
const { export_json_to_excel } = require('@/utils/excel/Export2Excel.js')
import {
	apiBatchUpload,
	apiBatchRecordAdd,
	apiDownFailRecord,
	apiBatchRecordList,
	apiBatchRechargeRecordAdd,
} from '@/api/archives.api.js'
import { apiGetMeterType } from '@/api/meterManage.api.js'
import { apiBatchRecharge, apiBatchReissue, apiBatchRegister } from '@/api/recharge.api.js'
import { batPayment, getBatPaymentRecords, getBatRechargeUploadFailRecords } from '@/api/costManage.api'
import { isBlank } from '@/utils/validate.js'
import dictionaryValue from '@/utils/dictionaryValue.js'
import { nameConversion } from '@/utils/index.js'
import { mapActions } from 'vuex'

export default {
	mixins: [dictionaryValue],
	data() {
		return {
			uploadStep: 1, //上传进度，1-未选择文件，初始状态，2-选择文件，3-上传成功，4-上传失败
			failDataList: [], //错误记录保存
			recordId: '', //生成记录ID
			recordList: [], //操作记录列表
			page: {
				current: 1, //当前页
				total: 0, //总条数
				size: 10, //一页多少条
				pages: '', //共多少页
			},
			msg: '', //提示语
			loadingText: '拼命加载中', //上传文件的loading加载文字
			loading: false, //上传文件的加载loading
			selectTextInital: '上传文件', //选择文件的按钮text
			columns: [
				{
					key: 'createTime',
					name: '操作时间',
					width: '220px',
				},
				{
					key: 'operatorType',
					name: '操作类型',
					width: '200px',
				},
				{
					key: 'createStaffName',
					name: '操作人员',
					width: '200px',
				},
				{
					key: 'fail',
					name: '上传状态',
					render: (h, row) => {
						return h(
							'span',
							{
								class: {
									status0: row.fail, //失败
									status1: !row.fail, //成功
								},
							},
							row.fail ? '上传失败' : '上传成功',
						)
					},
				},
				{
					key: 'ops',
					name: '操作',
					render: (h, row) => {
						return row.fail
							? h(
									'el-button',
									{
										props: {
											type: 'text',
										},
										class: 'down-fail-record',
										on: {
											click: () => {
												this.downloadFailRecord(row.recordId)
											},
										},
									},
									'下载错误记录',
							  )
							: h(
									'span',
									{
										class: 'no-ops',
									},
									'/',
							  )
					},
				},
			],
			meterTypeMap: [], //表具类型
		}
	},
	created() {
		apiGetMeterType({
			tenantId: this.$store.getters.userInfo.tenantId,
		})
			.then(res => {
				this.meterTypeMap = res.reduce((obj, item) => {
					obj[item.meterTypeName] = item.meterTypeId
					return obj
				}, {})
			})
			.catch(() => {})
	},
	computed: {
		// 支付方式-数据字典
		payModeArr() {
			let arr = this.$store.getters.dataList.payMode || []
			return arr
		},
		// 支付方式-数据字典
		chargingMethod() {
			let arr = this.$store.getters.dataList.chargingMethod || []
			return arr
		},
		// 补气方式-数据字典
		reissuePayModeArr() {
			let arr = this.$store.getters.dataList.reissuePayMode || []
			return arr
		},
		// 用户子类（全部）-数据字典
		userSubTypeArr() {
			let arr1 = this.$store.getters.dataList.business || []
			let arr2 = this.$store.getters.dataList.resident || []
			return arr1.concat(arr2)
		},
	},
	methods: {
		...mapActions({
			getCostType: 'apiCache/getCostType',
		}),
		// 处理数据并上传
		uploadLogic(list, type, others) {
			//list-解析后的列表 type-模板类型 others-其他公共参数（object）
			let result = this.dataGeneration(list, type, others)
			if (!result) {
				this.$message.error('文件数据解析错误！')
				return false
			}
			for (const item of result) {
				if (item.fail) {
					this.$message.error(item.reason)
					return false
				}
			}

			this.loadingText = '数据上传中'
			this.loading = true

			// ! 通过组件名判断是否为批量充值模块的方式不推荐，后续酌情优化
			const isBatRecharge = this.$options.name === 'BatRecharge'
			// 接口调用，上传
			this.interfaceUpload(result, type)
				.then(result => {
					this.failDataList = []
					if (result.records) {
						this.failDataList = result.records
					} else if (isBatRecharge) {
						// 新增批量充值上传无错误记录返回，不做处理
					} else {
						this.$message.error('记录保存失败-数据格式错误')
					}
					if (this.failDataList.length > 0) {
						this.uploadStep = 4
						// 提示语
						this.msg = `已上传${list.length}条数据：${list.length - this.failDataList.length}条成功， ${
							this.failDataList.length
						}条失败`
					} else {
						this.uploadStep = 3
						if (isBatRecharge) {
							this.msg = `${list.length}条数据已上传，请转至批量充值记录查看上传结果`
						} else {
							this.msg = `已上传${list.length}条数据：全部成功`
						}
					}
					this.bacthRecordAdd(list, type, others)
				})
				.catch(err => {
					this.loading = false
					this.$message.error(err.message ? err.message : '上传发生错误')
				})
		},
		// 上传数据生成
		dataGeneration(list, type, others) {
			// 缴费类模板
			if (['recharge', 'register', 'supplement'].includes(type)) {
				return this.rechargeTrans(list, type, others)
			}
			// 档案类模板
			else if (type.indexOf('archives-bat-create') === 0) {
				return this.archivesTrans(list, type, others)
			}
			// 批量充值
			else if ('bat-recharge' === type) {
				return this.batRechargeTrans(list, others)
			} else {
				return false
			}
		},
		// 缴费类模板字段转接口参数
		rechargeTrans(list, type, others) {
			let opsList = list
			let transList = [] //转换后的数据
			// 缴费字段转换
			const labelRecharge = {
				表具编号: 'meterNo',
				档案编号: 'archivesNo',
				用户名称: 'userName',
				档案标识: 'archivesIdentity',
				充值金额: 'rechargeAmount', //两位小数
				付款方式: 'payMode', //数据字典
				地址名称: 'addressName',
				支票号: 'checkNo',
				补气金额: 'rechargeAmount', //两位小数
				费用金额: 'rechargeAmount', //两位小数
				费用类型: 'costType', //数据字典
				补气方式: 'reissuePayMode', //数据字典
			}
			switch (type) {
				case 'supplement': //批量补气
				case 'recharge': //批量充值
					labelRecharge['备注'] = 'reason'
					break
				case 'register': //批量登记其他费用
					labelRecharge['备注'] = 'remark'
					break
			}
			try {
				opsList.forEach(item => {
					let obj = {}
					let keys = Object.keys(item)
					keys.map(el => {
						if (labelRecharge[el]) {
							if (!isBlank(item[el])) {
								switch (labelRecharge[el]) {
									// 小数位数处理
									case 'rechargeAmount':
										obj[labelRecharge[el]] = item[el].toFixed(2)
										break
									// 数据字典转换
									case 'payMode':
									case 'costType':
										obj[labelRecharge[el]] = this.transValue(item[el], labelRecharge[el])
										break
									// 补气方式字典取reissuePayMode，入参用payMode
									case 'reissuePayMode':
										obj['payMode'] = this.transValue(item[el], labelRecharge[el])
										break
									default:
										obj[labelRecharge[el]] = item[el]
										break
								}
							}
						}
					})
					obj.rowNum = item.rowNum
					if (
						others &&
						Object.prototype.toString.call(others) == '[object Object]' &&
						Object.keys(others).length > 0
					) {
						Object.assign(obj, others)
					}
					transList.push(obj)
				})
				return transList
			} catch (error) {
				console.log(error, '数据转换失败')
				return false
			}
		},
		// 批量充值模板字段转接口参数
		batRechargeTrans(list, others) {
			let opsList = list
			let transList = [] //转换后的数据
			// 缴费字段转换
			const labelRecharge = {
				表卡编号: 'archivesIdentity',
				充值金额: 'rechargeAmount',
				付款方式: 'payMode',
				备注: 'reason',
			}
			try {
				opsList.forEach(item => {
					let obj = {}
					let keys = Object.keys(item)
					keys.map(el => {
						if (labelRecharge[el]) {
							if (!isBlank(item[el])) {
								switch (labelRecharge[el]) {
									// 小数位数处理
									case 'rechargeAmount':
										obj[labelRecharge[el]] = item[el].toFixed(2)
										break
									// 数据字典转换
									case 'payMode':
										obj[labelRecharge[el]] = this.transValue(item[el], labelRecharge[el])
										break
									default:
										obj[labelRecharge[el]] = item[el]
										break
								}
							}
						}
					})
					obj.rowNum = item.rowNum
					if (
						others &&
						Object.prototype.toString.call(others) == '[object Object]' &&
						Object.keys(others).length > 0
					) {
						Object.assign(obj, others)
					}
					transList.push(obj)
				})
				return transList
			} catch (error) {
				console.log(error, '数据转换失败')
				return false
			}
		},
		// 档案类模板字段转接口参数
		archivesTrans(list, type, others) {
			let opsList = list
			let transList = [] //转换后的数据
			//批量建档字段转换（excel中的字段）
			const labelArchives = {
				// address
				详细地址: 'addressName',
				层数: 'floorNum',
				房屋建设年代: 'houseYear',
				压力区: 'pressureZone',
				GIS编号: 'gisCode',
				管网编号: 'pipeNetworkCode',
				// archives(计费方式外部传入)
				表卡编号: 'archivesIdentity',
				收费方式: 'chargingMethod', //数据字典
				常住人数: 'resiPopulation',
				户数: 'households',
				自来水编号: 'tapWaterNo',
				册内序号: 'recordSeq',
				小区总表: 'summaryArchives',
				//user
				身份证号: 'certificateNo',
				其他证件类型: 'certificateType', //数据字典
				其他证件号: 'otherCertificateNo',
				联系电话: 'contactPhone',
				手机号码: 'userMobile',
				用户名称: 'userName',
				用户类型: 'userType', //数据字典
				// meter（表类别（meterTypeId，frequencyId）外部传入）
				表具编号: 'meterNo',
				表具类型: 'meterTypeId',
				安装日期: 'installationDate', //日期格式转换
				出厂日期: 'manufactureDate', //日期格式转换
				启用表底: 'startMeterReading',
				基表表号: 'baseMeterNo',
				量程: 'ranges',
				表具口径: 'caliber',
				水表仓库编号: 'meterWarehouseCode',
				防盗编号: 'antiTheftCode',
			}
			try {
				opsList.forEach(item => {
					if (!item['初始周期累积量']) {
						item['初始周期累积量'] = 0
					}
					let obj = {}
					let keys = Object.keys(item)
					// 第一步转换，主要是转换key，进行数据的特殊处理
					keys.map(el => {
						if (labelArchives[el]) {
							if (!isBlank(item[el])) {
								obj[labelArchives[el]] = item[el]
								switch (labelArchives[el]) {
									// 小数位数处理
									case 'openMoney':
										obj[labelArchives[el]] = item[el].toFixed(2)
										break
									// 数据字典转换
									case 'chargingMethod':
									case 'certificateType':
									case 'userSubType':
									case 'userType':
									case 'gasDirection':
									case 'installPosition':
									case 'meterFlow':
										obj[labelArchives[el]] = this.transValue(item[el], labelArchives[el])
										break
									// 日期转换
									case 'openDate':
									case 'installationDate':
									case 'manufactureDate':
										if (typeof item[el] === 'number') {
											obj[labelArchives[el]] = this.transDate(item[el])
										} else {
											obj[labelArchives[el]] = item[el]
										}
										break
								}
							}
						}
					})
					obj.rowNum = item.rowNum

					//address中应传参的字段
					const addressKeys = [
						'addressName',
						'floorNum',
						'houseYear',
						'pressureZone',
						'gisCode',
						'pipeNetworkCode',
						'tapWaterNo',
					]
					//archives中应传参的字段
					const archivesKeys = [
						'archivesIdentity',
						'resiPopulation',
						'households',
						'recordSeq',
						'summaryArchives',
						'userType',
					]
					//user中应传参的字段
					const userKeys = [
						'certificateNo',
						'certificateType',
						'contactPhone',
						'otherCertificateNo',
						'userMobile',
						'userName',
						'userType',
						'chargingMethod',
					]
					const meterKeys = [
						'meterNo',
						'meterTypeId',
						'installationDate',
						'manufactureDate',
						'startMeterReading',
						'baseMeterNo',
						'ranges',
						'caliber',
						'meterWarehouseCode',
						'antiTheftCode',
					]
					// 第二部转换，处理数据到对应的内部对象中
					let secondObj = {
						address: {},
						archives: {},
						user: {},
						meter: {},
					}
					for (let i in obj) {
						if (addressKeys.indexOf(i) != -1) {
							secondObj.address[i] = obj[i]
							continue
						}
						if (archivesKeys.indexOf(i) != -1) {
							if (i === 'summaryArchives') {
								secondObj.archives[i] = obj[i] == '是' ? 1 : 0
								continue
							}
							secondObj.archives[i] = obj[i]
							continue
						}
						if (userKeys.indexOf(i) != -1) {
							secondObj.user[i] = obj[i]
							continue
						}
						if (meterKeys.indexOf(i) != -1) {
							if (i === 'meterTypeId') {
								if (!obj[i]) {
									continue
								}
								const meterTypeId = this.meterTypeMap[obj[i]]
								if (meterTypeId === undefined) {
									transList.push({
										fail: true,
										rowNum: obj.rowNum,
										reason: `第 ${item.rowNum} 行: 表具类型 ${obj[i]} 不存在，请核对文件数据！`,
									})
									return false
								}
								secondObj.meter[i] = meterTypeId
								continue
							}
							secondObj.meter[i] = obj[i]
							continue
						}
						if (
							addressKeys.indexOf(i) == -1 &&
							archivesKeys.indexOf(i) == -1 &&
							userKeys.indexOf(i) == -1 &&
							meterKeys.indexOf(i) == -1
						) {
							secondObj[i] = obj[i]
						}
					}

					secondObj.user.userType = obj.userType

					// 合并传入数据
					if (
						others &&
						Object.prototype.toString.call(others) == '[object Object]' &&
						Object.keys(others).length > 0
					) {
						let otherObj = JSON.parse(JSON.stringify(others))
						let othersKeys = Object.keys(otherObj)
						let secondObjKeys = Object.keys(secondObj)
						othersKeys.map(i => {
							if (secondObjKeys.indexOf(i) != -1) {
								secondObj[i] = Object.assign(secondObj[i], otherObj[i])
								if (i === 'address') {
									secondObj[i].addressFullName += secondObj[i].addressName
								}
							} else {
								secondObj[i] = otherObj[i]
							}
						})
					}

					// 根据模板置null
					if (type == 'archives-bat-create-without-user') {
						//地址+表具模板
						secondObj.user = null
					}
					if (type == 'archives-bat-create-without-meter') {
						//地址+用户模板
						secondObj.meter = null
					}
					if (type.indexOf('archives-bat-create') === 0) {
						secondObj.archives.archivesMeterType = 1
					}
					transList.push(secondObj)
				})
				return transList
			} catch (error) {
				console.log(error, '数据转换失败')
				return false
			}
		},
		// 接口调用，上传
		interfaceUpload(result, type) {
			/*地址+表具模板-meter
				地址+用户模板-user
				批量补气模板-supplement
				批量充值模板-recharge
				批量登记其他费用-register
				完整档案模板-complete
				批量充值-bat-recharge
				批量建表-archives-bat-create
			*/
			let obj = {
				records: result,
			}
			if (type.indexOf('archives-bat-create') === 0) {
				return apiBatchUpload(obj)
			} else if (type === 'recharge') {
				return apiBatchRecharge(obj)
			} else if (type === 'bat-recharge') {
				return batPayment(obj)
			} else if (type === 'register') {
				return apiBatchRegister(obj)
			} else if (type === 'supplement') {
				return apiBatchReissue(obj)
			}
		},
		// 记录入库
		bacthRecordAdd(list, type, others) {
			if (type === 'bat-recharge') {
				this.loading = false
				return
			}
			this.loadingText = '记录入库中'
			let obj = {}
			obj.records = this.failDataList
			obj.total = list.length //excel解析后的数组长度
			obj.fail = this.failDataList.length //失败的长度
			obj.success = list.length - this.failDataList.length //成功的长度
			//操作类型
			if (type.indexOf('archives-bat-create') === 0) {
				obj.operatorType = 1
			} else {
				obj.operatorType = null
			}
			// 建档传递的特殊参数
			if (type.indexOf('archives-bat-create') === 0 && others) {
				const { address, price, meter } = others
				if (address) {
					address.regionCode ? (obj.regionCode = address.regionCode) : null
					address.addressAreaName ? (obj.addressAreaName = address.addressAreaName) : null
					address.addressAreaCode ? (obj.addressAreaCode = address.addressAreaCode) : null
				}
				price && !isBlank(price.priceId) ? (obj.priceId = price.priceId) : null
				meter && meter.meterTypeId ? (obj.meterTypeId = meter.meterTypeId) : null
			}
			console.time('记录入库总耗时')
			if (type.indexOf('archives-bat-create') === 0) {
				apiBatchRecordAdd(obj).then(
					res => {
						console.timeEnd('记录入库总耗时')
						this.loading = false
						this.$message.success('记录保存成功')
						this.recordId = res.recordId
					},
					() => {
						this.loading = false
					},
				)
			} else if (['recharge', 'supplement', 'register'].includes(type)) {
				apiBatchRechargeRecordAdd(obj).then(
					res => {
						console.timeEnd('记录入库总耗时')
						this.loading = false
						this.$message.success('记录保存成功')
						this.recordId = res.recordId
					},
					() => {
						this.loading = false
					},
				)
			}
		},
		// 下载失败记录
		downloadFailRecord(id) {
			const instanceName = this.$options.name
			// 区分操作记录中的下载和操作的下载，不传或者传false的时候，取data存的recordId
			if (!id) {
				id = this.recordId
			}
			if (!id && id != 0) {
				this.$message.error('无法下载失败记录-recordId为空')
				return
			}
			// 表头标题
			const tHeader = ['错误行数', '错误原因']
			// 数据key
			const filterVal = ['rowNum', 'errorMsg']
			const downloadApi = instanceName === 'BatRecharge' ? getBatRechargeUploadFailRecords : apiDownFailRecord
			downloadApi({ recordId: id })
				.then(res => {
					// 数据集
					const list = res.records
					const data = this.formatJson(filterVal, list)
					let name = ''
					let date = ''
					if (res.createStaffName) {
						name = ' - ' + res.createStaffName
					}
					if (res.createTime) {
						date = ' - ' + res.createTime
					}
					// excel名称 后缀默认xlsx
					const excelName = `错误记录${name}${date}`
					// tHeader:表头数据 data:表数据 excelName:文件名
					export_json_to_excel(tHeader, data, excelName)
				})
				.catch(err => {
					console.log(err)
					this.$message.error('下载失败')
				})
		},
		// excel数据转换
		formatJson(filterVal, jsonData) {
			return jsonData.map(v => filterVal.map(j => v[j]))
		},
		// 查询操作记录列表
		getRecordList({ page, size }, flag) {
			// current-页数 flag-标志 archives(档案) recharge(缴费)
			this.page.current = page
			if (size) {
				this.page.size = size
			}
			let obj = {}
			obj.current = this.page.current
			obj.size = this.page.size
			if (flag === 'bat-recharge') {
				obj.operatorTypes = '4'
				getBatPaymentRecords(obj).then(res => {
					this.page.total = res.total
					this.page.pages = res.pages
					let middleArr = res.records || []
					if (middleArr.length > 0) {
						middleArr.map(item => {
							for (let key in item) {
								if (key === 'operatorType') {
									item[key] = nameConversion(item[key], this.operatorTypeArr)
								}
							}
						})
					}
					this.recordList = middleArr
				})
				return
			}
			if (flag == 'archives') {
				obj.operatorTypes = '1,2,3'
			} else if (flag == 'recharge') {
				obj.operatorTypes = '4,5,6'
			}
			apiBatchRecordList(obj).then(res => {
				this.page.total = res.total
				this.page.pages = res.pages
				let middleArr = res.records || []
				if (middleArr.length > 0) {
					middleArr.map(item => {
						for (let key in item) {
							if (key === 'operatorType') {
								item[key] = nameConversion(item[key], this.operatorTypeArr)
							}
						}
					})
				}
				this.recordList = middleArr
			})
		},
		// 数据字典值的转化
		transValue(val, key) {
			let arr = []
			switch (key) {
				case 'payMode':
					arr = this.payModeArr
					break
				case 'chargingMethod':
					arr = this.chargingMethod
					break
				case 'costType':
					arr = this.costTypeArr
					break
				case 'reissuePayMode':
					arr = this.reissuePayModeArr
					break
				case 'certificateType':
					arr = this.certificateTypeArr
					break
				case 'userSubType':
					arr = this.userSubTypeArr
					break
				case 'userType':
					arr = this.userTypeArr
					break
				case 'gasDirection':
					arr = this.gasDirectionArr
					break
				case 'installPosition':
					arr = this.installPositionArr
					break
				case 'meterFlow':
					arr = this.meterFlowArr
					break
			}
			let value = ''
			arr.map(item => {
				if (item.sortName == val) {
					value = item.sortValue
				}
			})
			return value
		},
		// 下载失败后下载错误记录
		uploadDownFail() {
			this.downloadFailRecord()
		},
		// excel解析出来的时间处理（数字-时间）
		transDate(numb) {
			if (numb > 0) {
				const time = new Date((numb - 1) * 24 * 3600000 + 1)
				let h = time.getHours() + 16
				let yeraData = new Date(1900, 0, numb - 1)
				let year = yeraData.getFullYear()
				let month = yeraData.getMonth() + 1
				month = month < 10 ? '0' + month : month
				let day = yeraData.getDate()
				day = day < 10 ? '0' + day : day
				if (h > 23) {
					h = h - 24
				}
				h < 10 ? (h = '0' + h) : null
				let m = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()
				let s = time.getSeconds() < 10 ? '0' + time.getSeconds() : time.getSeconds()
				if (h > 0 || m > 0 || s > 0) {
					return `${year}-${month}-${day} ${h}:${m}:${s}`
				} else {
					return `${year}-${month}-${day}`
				}
			} else {
				return '非法日期格式'
			}
		},
	},
}
