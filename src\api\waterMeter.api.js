import service from './request'
import { CPM } from '@/consts/moduleNames'

// 新增水表
export function addWaterMeter(data) {
	return service({
		url: `${CPM}/meter/create-meter`,
		method: 'post',
		data,
	})
}
// 查询水表列表
export function getWaterList(data) {
	return service({
		url: `${CPM}/report/meter/preview`,
		method: 'post',
		data,
	})
}
// 水表导入
export function importWaterMeter(data) {
	return service({
		url: `${CPM}/meter/batch-create-meter`,
		method: 'post',
		data,
	})
}
// 水表导出
export function exportWaterMeter(data) {
	return service({
		url: `${CPM}/report/meter/export/excel`,
		method: 'post',
		data,
		responseType: 'blob',
	})
}
// 批量报废
export function batchScrapMeter(data) {
	return service({
		url: `${CPM}/meter/batch-scrap-meter`,
		method: 'put',
		data,
	})
}
// 批量停水
export function batchDisableMeter(data) {
	return service({
		url: `${CPM}/archives/batch-disable`,
		method: 'put',
		data,
	})
}
// 批量恢复用水
export function batchEnableWater(data) {
	return service({
		url: `${CPM}/archives/batch-enable`,
		method: 'put',
		data,
	})
}
// 水表状态变更
export function modifyMeterStatus(data) {
	return service({
		url: `${CPM}/meter/modify-meter-status`,
		method: 'post',
		data,
	})
}

// 水表视图
// 查询水表详情（概况）
export function getMeterDetail(params) {
	return service({
		url: `${CPM}/archives/meter-detail`,
		method: 'get',
		params,
	})
}
// 修改水表信息
export function modifyMeterInfo(data) {
	return service({
		url: `${CPM}/meter/modify-meter`,
		method: 'put',
		data,
	})
}
// 修改水表信息2
export function modifyMeterInfo2(data) {
	return service({
		url: `${CPM}/meter/modify-meter2`,
		method: 'put',
		data,
	})
}
// 修改水表信息3
export function modifyMeterInfo3(data) {
	return service({
		url: `${CPM}/meter/modify-meter3`,
		method: 'put',
		data,
	})
}
// 检修
export function repairMeter(data) {
	return service({
		url: `${CPM}/meter/repair-meter`,
		method: 'put',
		data,
	})
}
// 报废
export function scrapMeter(data) {
	return service({
		url: `${CPM}/meter/scrap-meter`,
		method: 'put',
		data,
	})
}
// 装表
export function installMeter(data) {
	return service({
		url: `${CPM}/meter/install-meter`,
		method: 'post',
		data,
	})
}
// 拆表
export function removeMeter(data) {
	return service({
		url: `${CPM}/archives/remove-meter2`,
		method: 'put',
		data,
	})
}
// 停水
export function disableMeter(data) {
	return service({
		url: `${CPM}/archives/disable3`,
		method: 'put',
		data,
	})
}
// 恢复用水
export function enableMeter(data) {
	return service({
		url: `${CPM}/archives/enable3`,
		method: 'put',
		data,
	})
}

// 表卡记录
export function getMeterInstallRecord(data) {
	return service({
		url: `${CPM}/meter/install-meter-record`,
		method: 'post',
		data,
	})
}
// 抄表记录
export function getReadingMeterRecord(data) {
	return service({
		url: `${CPM}/meterReadingTask/archivesId-record-list2`,
		method: 'post',
		data,
	})
}
// 状态变更记录
export function getMeterModifyRecord(data) {
	return service({
		url: `${CPM}/archives/meter-modify-records2`,
		method: 'post',
		data,
	})
}
// 修改指针
export function modifyMeterReading(data) {
	return service({
		url: `${CPM}/meter/modify-meter-reading`,
		method: 'post',
		data,
	})
}
