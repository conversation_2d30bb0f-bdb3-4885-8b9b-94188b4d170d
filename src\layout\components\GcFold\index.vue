<template>
	<gc-icon
		class="fold-unfold"
		:class="{ 'fold-unfold-active': collapse }"
		icon="icon-navi-unfold"
		size="12"
		@click="toggleCollapse"
	/>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'

export default {
	name: 'GcFold',
	data() {
		return {}
	},
	computed: {
		...mapGetters({
			collapse: 'settings/collapse',
		}),
	},
	methods: {
		...mapActions({
			toggleCollapse: 'settings/toggleCollapse',
		}),
	},
}
</script>

<style lang="scss" scoped>
.fold-unfold {
	color: $base-color-blue;
	cursor: pointer;
	transform: rotateY(180deg);
	position: relative;
	top: 2px;
	&-active {
		transform: rotateY(0deg);
	}
}
</style>
