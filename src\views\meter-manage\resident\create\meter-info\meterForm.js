import { ruleMaxLength } from '@/utils/rules'
export function getFormItems(_this) {
	return [
		{
			type: 'el-input',
			label: '水表编号',
			prop: 'meterNo',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-select',
			label: '水表类型',
			prop: 'meterTypeId',
			options: [],
			attrs: {
				col: 8,
				clearable: true,
			},
			events: {
				change: value => {
					const obj = _this.formItems[1].options.find(item => item.value === value)
					if (obj) {
						_this.formData.manufacturerName = obj.manufacturerName
						_this.formData.ranges = obj.meterRange
						_this.formAttrs.rules.meterNo = [
							{ required: true, message: '必填', trigger: 'blur' },
							{
								pattern: obj.ruleCode,
								message: '格式不正确',
								trigger: '',
							},
							ruleMaxLength(32),
						]
					} else {
						_this.formData.manufacturerName = ''
						_this.formData.ranges = ''
					}
				},
			},
		},
		{
			type: 'el-date-picker',
			label: '装表时间',
			prop: 'installationDate',
			attrs: {
				col: 8,
				type: 'date',
				placeholder: '选择日期',
				valueFormat: 'yyyy-MM-dd',
			},
		},
		{
			type: 'el-input',
			label: '水表仓库编号',
			prop: 'meterWarehouseCode',
			attrs: {
				col: 8,
			},
		},

		{
			type: 'el-input',
			label: '水表厂商',
			prop: 'manufacturerName',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '服役年限',
			prop: 'useYears',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '防盗编号',
			prop: 'antiTheftCode',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '量程',
			prop: 'ranges',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '水表型号',
			prop: 'meterModel',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '口径',
			prop: 'caliber',
			attrs: {
				col: 8,
			},
		},

		{
			type: 'el-input',
			label: '初始指针数',
			prop: 'startMeterReading',
			attrs: {
				col: 8,
				disabled: Boolean(_this.$route.query.archivesId),
			},
		},
		{
			type: 'el-select',
			label: '装表位置',
			prop: 'installPosition',
			attrs: {
				col: 8,
			},
			options: _this.$store.getters.dataList.installPosition
				? _this.$store.getters.dataList.installPosition.map(item => {
						return {
							label: item.sortName,
							value: Number(item.sortValue),
						}
				  })
				: [],
		},

		{
			type: 'el-input',
			label: '表井位置',
			prop: 'tableWellLocation',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '水表标号',
			prop: 'baseMeterNo',
			attrs: {
				col: 8,
			},
		},

		{
			type: 'slot',
			slotName: 'otherInfo',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-input',
			label: '表册编号',
			prop: 'bookNo', // 前面带过来
			attrs: {
				col: 12,
				disabled: true,
			},
		},
		{
			type: 'slot',
			label: '册内序号',
			prop: 'recordSeq',
			slotName: 'recordSeq',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '备注',
			prop: 'remark',
			attrs: {
				col: 24,
				type: 'textarea',
				maxlength: '64',
				showWordLimit: true,
				autosize: {
					minRows: 2,
					maxRows: 4,
				},
			},
		},
	]
}
