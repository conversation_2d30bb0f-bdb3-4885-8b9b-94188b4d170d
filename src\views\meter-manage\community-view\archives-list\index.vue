<template>
	<div class="archives-list">
		<div class="data-container data-container1">
			<GcModelHeader title="总表表卡信息" :icon="require('@/assets/images/icon/title-meter.png')"></GcModelHeader>
			<div class="table-container table-container1">
				<GcTable
					:columns="columns1"
					:table-data="tableData1"
					:page-size="pageData1.size"
					:total="pageData1.total"
					:current-page="pageData1.current"
					showPage
					@current-page-change="handleChangePage1"
				/>
			</div>
		</div>
		<div class="data-container data-container2">
			<GcModelHeader title="用户表卡信息" :icon="require('@/assets/images/icon/title-meter.png')">
				<template slot="left">
					<span class="archives-num">表卡总数：{{ totalValue }}</span>
				</template>
			</GcModelHeader>
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" @click="handleSearch2">筛选</el-button>
					<el-button @click="handleReset2">重置</el-button>
				</el-form-item>
			</GcFormSimple>
			<div class="table-container table-container2">
				<GcTable
					:loading="loading"
					:columns="columns2"
					:table-data="tableData2"
					:page-size="pageData2.size"
					:total="pageData2.total"
					:current-page="pageData2.current"
					@current-page-change="handleChangePage2"
					showPage
				>
					<template v-slot:operate="{ row }">
						<el-button v-show="row.archivesStatus !== 3" type="text" @click="goModify(row)">
							档案维护
						</el-button>
					</template>
				</GcTable>
			</div>
		</div>
	</div>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiGetSummaryArchives, apiGetBuildingArchives } from '@/api/meterManage.api'
export default {
	props: {
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	watch: {
		tabData: {
			handler() {
				this.handleSearch()
			},
			deep: true,
		},
	},
	data() {
		return {
			columns1: [
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'addressFullName',
					name: '地址',
					tooltip: true,
				},
				{
					key: 'userName',
					name: '用户',
					tooltip: true,
				},
				{
					key: 'priceCode',
					name: '价格编号',
					tooltip: true,
				},
				{
					key: 'natureName',
					name: '用水性质',
					tooltip: true,
				},
				{
					key: 'meterTypeName',
					name: '水表类型',
					tooltip: true,
				},
				{
					key: 'createTime',
					name: '建档时间',
					tooltip: true,
				},
			],
			columns2: [
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'addressFullName',
					name: '地址',
					tooltip: true,
				},
				{
					key: 'userName',
					name: '用户',
					tooltip: true,
				},
				{
					key: 'priceCode',
					name: '价格编号',
					tooltip: true,
				},
				{
					key: 'natureName',
					name: '用水性质',
					tooltip: true,
				},
				{
					key: 'meterTypeName',
					name: '水表类型',
					tooltip: true,
				},
				{
					key: 'createTime',
					name: '建档时间',
					tooltip: true,
				},
				{
					key: 'operate',
					name: '操作',
				},
			],
			formData: {
				archivesIdentity: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						placeholder: '请输入',
						style: {
							width: '150px',
						},
					},
				},
			],
			formAttrs: {
				inline: true,
			},
			tableData1: [],
			tableData2: [],
			pageData1: {
				current: 1,
				size: 10,
				total: 0,
			},
			pageData2: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
			totalValue: '',
		}
	},
	methods: {
		async _getSummary() {
			const { current, size } = this.pageData1
			try {
				const { records, total } = await apiGetSummaryArchives({
					addressAreaCode: this.tabData.addressAreaCode,
					current,
					size,
				})
				this.tableData1 = records
				this.pageData1.total = total
			} catch (error) {
				console.log(error)
				this.tableData1 = []
				this.pageData1 = {
					current: 1,
					size: 10,
					total: 0,
				}
			}
		},
		async _getBuilding() {
			try {
				const { current, size } = this.pageData2
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					addressAreaCode: this.tabData.addressAreaCode,
					current,
					size,
				})
				const { pageRecord, userArchivesCount } = await apiGetBuildingArchives(formParams)
				const { records, total } = pageRecord
				this.totalValue = userArchivesCount
				this.tableData2 = records
				this.pageData2.total = total
			} catch (error) {
				console.log(error)
				this.tableData2 = []
				this.pageData2 = {
					current: 1,
					size: 10,
					total: 0,
				}
			}
		},
		handleSearch() {
			this.handleSearch1()
			this.handleSearch2()
		},
		handleSearch1() {
			this.handleChangePage1({ page: 1 })
		},
		handleChangePage1({ page, size }) {
			this.pageData1.current = page
			if (size) {
				this.pageData1.size = size
			}
			this._getSummary()
		},
		handleSearch2() {
			this.handleChangePage2({ page: 1 })
		},
		handleReset2() {
			this.$refs.formRef.resetFields()
			this.handleSearch2()
		},
		handleChangePage2({ page, size }) {
			this.pageData2.current = page
			if (size) {
				this.pageData2.size = size
			}
			this._getBuilding()
		},
		goModify(row) {
			const path = row.userType == 3 ? '/meterManage/residentMeterView' : '/meterManage/companyMeterView'

			const isPermission = row.isPermission
			if (!isPermission) {
				const orgName = this.$store.getters.userInfo.orgName
				this.$notify({
					message: `非${orgName}或管理人员，暂无操作权限`,
					title: '提示',
					offset: 60,
					type: 'warning',
				})
				return
			}

			if (!this.$has('cpm_archives_detail') && row.userType === 3) {
				this.$notify({
					message: '暂无权限访问，请联系管理员！',
					title: '提示',
					offset: 60,
					type: 'warning',
				})
				return
			}
			if (!this.$has('cpm_archives_detail5') && row.userType === 4) {
				this.$notify({
					message: '暂无权限访问，请联系管理员！',
					title: '提示',
					offset: 60,
					type: 'warning',
				})
				return
			}

			this.$router.push({
				path,
				query: {
					archivesId: row.archivesId,
				},
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.archives-list {
	display: flex;
	flex-direction: column;
	height: 100%;
	overflow: auto;
	.data-container {
		margin-bottom: 10px;
		background: #fff;
		border-radius: 4px;
		.table-container {
			padding: 20px;
		}
	}
	.data-container1 {
		display: flex;
		flex-direction: column;
		height: 300px;
		.table-container1 {
			flex: 1;
			overflow: hidden;
		}
	}
	.data-container2 {
		display: flex;
		flex-direction: column;
		flex: 1;
		min-height: 300px;
		overflow: hidden;
		.table-container2 {
			flex: 1;
			padding-top: 0;
			overflow: hidden;
		}
	}
	.archives-num {
		margin-left: 10px;
	}
	::v-deep {
		.el-form {
			padding: 0 20px;
		}
	}
}
</style>
