<template>
	<GcElDialog
		ref="dialog"
		:show="isShow"
		title="开票信息修改"
		width="700px"
		okText="修改"
		class="invoice-dialog"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSubmit"
		v-loading="loading"
	>
		<GcFormRow
			ref="formRowRef"
			v-model="formRowData"
			:formItems="formRowItems"
			:formAttrs="formRowAttrs"
		></GcFormRow>
	</GcElDialog>
</template>

<script>
import { RULE_INCORRECTEMAIL, RULE_PHONE } from '@/utils/rules.js'
import { apiUpdateInvoiceInfo } from '@/api/print.api'

export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		data: {
			type: Object,
			default() {
				return {}
			},
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formRowData: {
				userName: '',
				taxpayerIdentity: '',
				openBank: '',
				bankAccount: '',
				phoneNumber: '',
				email: '',
			},
			formRowItems: [
				{
					type: 'el-input',
					label: '发票抬头',
					prop: 'userName',
					attrs: {
						col: 24,
						disabled: true,
					},
				},
				{
					type: 'el-input',
					label: '纳税人识别号',
					prop: 'taxpayerIdentity',
					attrs: {
						col: 24,
						disabled: true,
					},
				},
				{
					type: 'el-input',
					label: '开户银行',
					prop: 'openBank',
					attrs: {
						col: 24,
						disabled: true,
					},
				},
				{
					type: 'el-input',
					label: '银行账户',
					prop: 'bankAccount',
					attrs: {
						col: 24,
						disabled: true,
					},
				},
				{
					type: 'el-input',
					label: '手机号码',
					prop: 'phoneNumber',
					attrs: {
						col: 24,
					},
				},
				{
					type: 'el-input',
					label: '电子邮箱',
					prop: 'email',
					attrs: {
						col: 24,
					},
				},
			],
			formRowAttrs: {
				rules: {
					phoneNumber: [RULE_PHONE],
					email: [RULE_INCORRECTEMAIL],
				},
			},
			loading: false,
		}
	},
	methods: {
		handleClose() {
			this.isShow = false
		},
		async handleSubmit() {
			const valid = await this.$refs.formRowRef.validate()
			if (valid) {
				try {
					const { formRowData } = this
					this.loading = true
					await apiUpdateInvoiceInfo(formRowData)
					this.$message.success('修改成功')
					this.$emit('refresh')
					this.isShow = false
				} catch (error) {
					console.log(error)
				} finally {
					this.loading = false
				}
			}
		},
	},
	watch: {
		data: {
			handler(data) {
				data = data || {}
				const { formRowItems } = this
				const newFormData = formRowItems.reduce((acc, item) => {
					const { prop } = item
					acc[prop] = data[prop] || ''
					return acc
				}, {})
				newFormData.invoiceBuyerId = data.invoiceBuyerId
				this.formRowData = newFormData
			},
			immediate: true,
		},
	},
}
</script>
<style lang="scss" scoped>
// .invoice-dialog {
// 	&::v-deep {
// 		.el-dialog {
// 			max-height: calc(100vh - 200px);
// 		}
// 		.el-dialog__body {
// 			padding-top: 0;
// 		}
// 	}
// }
</style>
