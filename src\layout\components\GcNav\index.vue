<template>
	<div class="gc-nav">
		<el-row :gutter="15">
			<el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="4">
				<div class="left-panel">
					<GcFold />
					<GcBreadcrumb class="hidden-xs-only" />
				</div>
			</el-col>
			<el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="20">
				<div class="right-panel">
					<GcSearch />
					<GcFullScreen />
					<GcTheme v-if="showTheme" />
					<GcAvatar />
				</div>
			</el-col>
		</el-row>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { openFirstMenu, showTheme } from '@/config'

export default {
	name: 'GcNav',
	props: {
		layout: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			firstMenu: '',
			showTheme,
		}
	},
	computed: {
		...mapGetters({
			extra: 'settings/extra',
			routes: 'routes/routes',
		}),
		handleRoutes() {
			return this.routes.filter(item => item.hidden !== true && item.meta)
		},
		handlePartialRoutes() {
			const activeMenu = this.routes.find(_ => _.name === this.extra.first)
			return activeMenu ? activeMenu.children : []
		},
	},
	watch: {
		$route: {
			handler(route) {
				const firstMenu = route.matched[0].name
				if (this.extra.first !== firstMenu) {
					this.extra.first = firstMenu
					this.handleTabClick(true)
				}
			},
			immediate: true,
		},
	},
	methods: {
		handleTabClick(handler) {
			if (handler !== true && openFirstMenu) this.$router.push(this.handlePartialRoutes[0])
		},
	},
}
</script>

<style lang="scss" scoped>
.gc-nav {
	position: relative;
	height: $base-nav-height;
	padding-right: $base-padding;
	padding-left: $base-padding;
	overflow: hidden;
	user-select: none;
	background: $base-color-white;
	box-shadow: $base-box-shadow;

	.left-panel {
		display: flex;
		align-items: center;
		justify-items: center;
		height: $base-nav-height;

		::v-deep {
			.gc-breadcrumb {
				margin-left: $base-margin;
			}

			.el-tabs {
				margin-left: $base-margin;

				.el-tabs__header {
					margin: 0;
				}

				.el-tabs__item {
					> div {
						display: flex;
						align-items: center;

						i {
							margin-right: 3px;
						}
					}
				}
			}

			.el-tabs__nav-wrap::after {
				display: none;
			}
		}
	}

	.right-panel {
		display: flex;
		align-content: center;
		align-items: center;
		justify-content: flex-end;
		height: $base-nav-height;

		::v-deep {
			[class*='ri-'] {
				margin-left: $base-margin;
				color: $base-color-grey;
				cursor: pointer;
			}

			button {
				[class*='ri-'] {
					margin-left: 0;
					color: $base-color-white;
					cursor: pointer;
				}
			}
		}
	}
}
</style>
