<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />

			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div class="right-top">
				<el-button v-has="'cpm_collectionAccount_add'" type="primary" @click="handleSetAccount('add')">
					新增托收账户
				</el-button>
			</div>
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handlePageChange"
			>
				<template v-slot:operate="{ row }">
					<el-button
						v-has="'cpm_collectionAccount_update'"
						type="text"
						@click="handleSetAccount('edit', row)"
					>
						编辑
					</el-button>
					<el-button
						v-has="'cpm_collectionAccount_user-list'"
						type="text"
						@click="handleClickRow(row, 'user')"
					>
						所有企业
					</el-button>
					<el-button v-has="'cpm_collectionAccount_modify-record'" type="text" @click="handleClickRow(row)">
						变更记录
					</el-button>
				</template>
			</GcTable>
		</div>
		<SetCollectionAccount
			ref="collectionAccountRef"
			:show.sync="showDialog"
			:editType="editType"
			:data="currentRow"
			@success="handleSearch"
		/>
	</div>
</template>

<script>
import { ruleRequired } from '@/utils/rules'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getColumn } from './tableColumn'
import { getFormItem } from './formItem.js'
import { apiGetCollectionAccountList, apiQueryCollectionBankList } from '@/api/userManage.api'
import SetCollectionAccount from './components/SetCollectionAccount.vue'

export default {
	name: 'CollectionAccountManage',
	components: { SetCollectionAccount },
	data() {
		return {
			// 左侧查询
			formData: {
				orgCode: '',
				bankCode: '',
				userName: '',
				collectionAgreementNumber: '',
				bankAccount: '',
				enterpriseNumber: '',
			},
			columns: getColumn(this),
			formItems: getFormItem(this),
			formAttrs: {
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
				},
			},
			// 右侧列表
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			showDialog: false,
			editType: 'add',
			currentRow: {},
		}
	},
	computed: {
		orgOptions() {
			return this.formItems[0]?.options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.$nextTick(() => {
						this.handleSearch()
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		async getBankList() {
			const data = await apiQueryCollectionBankList()
			this.formItems[1].options = data.map(item => {
				return {
					label: item.bankName,
					value: item.bankCode,
				}
			})
		},
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, { current, size })
				const { total = 0, records = [] } = await apiGetCollectionAccountList(formParams)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleReset() {
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.currentRow = {}
			this.$refs.formRef.resetFields()
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.handlePageChange({ page: 1 })
		},
		handleSetAccount(type, row) {
			this.editType = type
			this.showDialog = true
			if (type === 'edit') {
				this.currentRow = row
				this.$nextTick(() => {
					this.$refs.collectionAccountRef.assignForm(row)
				})
			}
		},
		handleClickRow(row, type) {
			const path =
				type === 'user' ? '/userManage/collectionAccountAllUsers' : '/userManage/collectionAccountRecords'
			this.$router.push({
				path,
				query: {
					recordId: row.collectionAccountId,
					num: row.collectionAgreementNumber,
				},
			})
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
	},
	mounted() {
		this.getBankList()
	},
}
</script>

<style lang="scss" scoped>
.right-top {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-bottom: 12px;
}
</style>
