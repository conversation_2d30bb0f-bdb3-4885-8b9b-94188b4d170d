<template>
	<gc-dialog
		:large="Boolean((priceForm.billingTypeId === '2' || realm === 'water') && operateType < 2)"
		class="price_operate_model"
		:title="`${operateType === 0 ? '新增' : operateType === 1 ? '调整' : '编辑'}价格`"
		width="700px"
		:visible.sync="dialogVisible"
	>
		<!-- 价格表单提交 -->
		<el-form
			:model="priceForm"
			:rules="priceRule"
			ref="priceForm"
			label-position="top"
			label-width="100px"
			class="dialog-form-s"
		>
			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item :label="`${fieldName.baseName}性质`" prop="usageNature">
						<el-cascader
							ref="waterSortRef"
							v-model="priceForm.usageNature"
							:options="waterNatureTreeData"
							filterable
							clearable
							:disabled="operateType === 1"
							:show-all-levels="false"
							:placeholder="`请选择${fieldName.baseName}性质`"
							:props="{
								label: 'natureName',
								value: 'priceNatureId',
								emitPath: false,
								checkStrictly: true,
							}"
						/>
					</el-form-item>
				</el-col>
				<el-col :span="12">
					<el-form-item label="价格编号" prop="priceCode">
						<el-input
							size="small"
							v-model="priceForm.priceCode"
							placeholder="请输入价格编号"
							:disabled="true"
						></el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :span="12">
					<el-form-item label="价格名称" prop="priceName">
						<el-input
							:disabled="operateType === 1"
							size="small"
							v-model="priceForm.priceName"
							placeholder="请输入价格名称"
						></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="12" v-if="operateType !== 2">
					<el-form-item label="计费类型" prop="billingTypeId" key="billingTypeId">
						<el-select
							v-model="priceForm.billingTypeId"
							size="small"
							filterable
							clearable
							placeholder="请选择计费类型"
						>
							<el-option
								v-for="item in initBillTypeId !== '3' && operateType === 1
									? billingTypeOptions.filter(o => o.sortValue !== '3')
									: billingTypeOptions"
								:key="item.sortValue"
								:label="item.sortName"
								:value="item.sortValue"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row :gutter="20">
				<!-- 价格，阶梯计费模式不存在 -->
				<el-col
					:span="12"
					v-if="(priceForm.billingTypeId === '1' || priceForm.billingTypeId === '3') && operateType !== 2"
				>
					<el-form-item label="价格" prop="singlePrice" key="singlePrice">
						<el-input size="small" v-model="priceForm.singlePrice" placeholder="请输入价格">
							<i slot="suffix" class="suffix_text">元/{{ fieldName.baseUnit }}</i>
						</el-input>
					</el-form-item>
				</el-col>

				<!-- 生效日期 -->
				<el-col :span="12" v-if="operateType === 1 && priceForm.billingTypeId === '1'">
					<el-form-item label="生效时间" prop="effectiveDate" key="effectiveDate">
						<el-date-picker
							style="width: 100%"
							v-model="priceForm.effectiveDate"
							type="date"
							value-format="yyyy-MM-dd"
							placeholder="请选择生效时间"
							:picker-options="pickerOptions"
						></el-date-picker>
					</el-form-item>
				</el-col>

				<!-- 时间单位 - 分时计价类型存在 -->
				<el-col :span="12" v-if="priceForm.billingTypeId === '3' && operateType !== 2">
					<el-form-item label="时间单位" prop="timeUnitCode" key="timeUnitCode">
						<el-select size="small" v-model="priceForm.timeUnitCode" clearable placeholder="请选择时间单位">
							<el-option
								v-for="item in timeUnitOptions"
								:key="item.sortValue"
								:label="item.sortName"
								:value="item.sortValue"
							></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>

			<!-- 阶梯计费模式存在 -->
			<div v-if="priceForm.billingTypeId === '2' && operateType !== 2">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="周期开始时间" prop="cycleStartTime">
							<el-date-picker
								format="yyyy-MM-dd"
								value-format="yyyy-MM-dd HH:mm:ss"
								v-model="priceForm.cycleStartTime"
								type="date"
								placeholder="选择日期"
								:picker-options="pickerOptions1"
							></el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="计费周期" prop="billingCycle">
							<el-input
								:disabled="priceForm.adjustLadder === 1"
								size="small"
								v-model.number="priceForm.billingCycle"
								placeholder="请输入计费周期"
							>
								<i slot="suffix" class="suffix_text">月</i>
							</el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12" v-if="isAllowPopulationLadder">
						<el-form-item label="是否人口阶梯" prop="ladderPopulation" key="ladderPopulation">
							<el-radio-group v-model="priceForm.ladderPopulation">
								<el-radio :label="1">是</el-radio>
								<el-radio :label="0">否</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :span="12" v-if="isAllowMonthAvg">
						<el-form-item label="是否按月均摊" prop="adjustLadder" key="adjustLadder">
							<el-radio-group v-model="priceForm.adjustLadder">
								<el-radio :label="1">是</el-radio>
								<el-radio :label="0">否</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="12" v-if="priceForm.ladderPopulation === 1">
						<el-form-item label="人口递增值" prop="populationIncrease" key="populationIncrease">
							<el-input
								size="small"
								v-model="priceForm.populationIncrease"
								placeholder="请输入人口递增值"
							>
								<i slot="suffix" class="suffix_text">{{ fieldName.baseUnit }}/人</i>
							</el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12" v-if="priceForm.ladderPopulation === 1">
						<el-form-item label="人口基数" prop="populationBase" key="populationBase">
							<el-input
								size="small"
								v-model.number="priceForm.populationBase"
								placeholder="请输入人口基数"
							>
								<i slot="suffix" class="suffix_text">人</i>
							</el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</div>
			<el-row :gutter="20" v-if="operateType === 1">
				<el-col :span="12" v-if="priceForm.billingTypeId !== '1'">
					<el-form-item label="生效时间" prop="effectiveDate" key="effectiveDate">
						<el-date-picker
							style="width: 100%"
							v-model="priceForm.effectiveDate"
							type="date"
							value-format="yyyy-MM-dd"
							placeholder="选择日期"
							:picker-options="pickerOptions"
						></el-date-picker>
					</el-form-item>
				</el-col>
				<el-col
					:span="12"
					v-if="allowDefineCycSurplus && initBillTypeId === '2' && priceForm.billingTypeId === '2'"
				>
					<el-form-item label="是否重置余量" prop="resetSurplus">
						<el-radio-group v-model="priceForm.resetSurplus" :value="priceForm.resetSurplus">
							<el-radio :label="0">是</el-radio>
							<el-radio :label="1">否</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>

		<!-- 阶梯计费策略 -->
		<tiered-billing
			ref="TieredBilling"
			:levelBorder="priceForm.levelBorder"
			:warnLevelBorder="priceForm.warnLevelBorder"
			:levelPrice="priceForm.levelPrice"
			:operateType="operateType"
			v-show="priceForm.billingTypeId === '2' && operateType !== 2"
		></tiered-billing>

		<!-- 分时计价策略 -->
		<time-billing
			ref="TimeBilling"
			:timeUnitCode="priceForm.timeUnitCode || '0'"
			:adjustTime="priceForm.adjustTime"
			:timePeriod="priceForm.timePeriod"
			:timePrice="priceForm.timePrice"
			v-show="priceForm.billingTypeId === '3' && operateType !== 2"
		></time-billing>

		<!-- 附加费（水务） -->
		<extra-billing
			ref="ExtraBilling"
			v-if="realm === 'water' && operateType !== 2"
			:priceBillItemList="priceForm.priceBillItemList"
		/>

		<span slot="footer" class="dialog-footer">
			<el-button @click="dialogVisible = false" size="small" round>取 消</el-button>
			<el-button size="small" type="primary" round @click="saveOperatePrice">
				确定{{ operateType === 0 ? '新增' : operateType === 1 ? '调整' : '编辑' }}
			</el-button>
		</span>
	</gc-dialog>
</template>

<script>
import TieredBilling from './TieredBilling'
import TimeBilling from './TimeBilling'
import ExtraBilling from './ExtraBilling'
import {
	ruleRequired,
	ruleRequiredInt,
	ruleMaxLength,
	RULE_PRICE,
	POPULATION_BASE_INCREASE_RULE,
	POPULATION_BASE_RULE,
	BILLINGCYCLE_RULE,
	RULE_ZERO_PRICE,
} from '@/utils/rules'
import mixin from '../mixins'
import { apiAdditionPrice, apiAdjustPrice, apiEditPrice } from '@/api/priceManage.api'
export default {
	mixins: [mixin],
	components: { TieredBilling, TimeBilling, ExtraBilling },
	/**
	 * @param { Number } operateType 弹窗操作类型 0 - 新增， 1 - 调价， 2 - 编辑
	 */
	props: {
		operateType: {
			type: Number,
			default: 0,
		},
		editPriceForm: {
			type: Object,
			default: () => {},
		},
		waterNatureTreeData: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		const metrologicalVerification = this.$store.state.user.tenant?.business_config?.is_metrological_verification
		return {
			dialogVisible: true,
			priceForm: {
				priceCode: '', // 价格编号
				priceName: '', // 价格名称
				usageNature: '', // 用气性质
				billingTypeId: '1', // 计费类型
				singlePrice: '', // 单一价格
				timeUnitCode: '0', // 时间单位
				cycleStartTime: '', // 周期开始时间
				billingCycle: '', // 计费周期
				populationBase: '', // 人口基数
				populationIncrease: '', // 人口递增值
				ladderPopulation: 0, // 是否人口阶梯
				adjustLadder: 0, // 是否按月均摊
				resetSurplus: '', // 是否重置余量
				effectiveDate: '', // 生效日期
				priceBillItemList: [],
			},
			priceRule: {
				priceName: [ruleRequired('请输入价格名称'), ruleMaxLength(20)],
				usageNature: [ruleRequired(`请选择${this.$store.state.user.fieldName.baseName}性质`)],
				billingTypeId: [ruleRequired('请选择计费类型')],
				singlePrice: [ruleRequired('请输入单一价格'), metrologicalVerification ? RULE_ZERO_PRICE : RULE_PRICE],
				timeUnitCode: [ruleRequired('请选择时间单位')],
				cycleStartTime: [ruleRequired('请选择周期开始时间')],
				billingCycle: [ruleRequired('请输入计费周期'), BILLINGCYCLE_RULE],
				populationBase: [ruleRequired('请输入人口基数'), POPULATION_BASE_RULE],
				populationIncrease: [ruleRequired('请输入人口递增值'), POPULATION_BASE_INCREASE_RULE],
				ladderPopulation: [ruleRequiredInt('请选择是否人口阶梯')],
				adjustLadder: [ruleRequiredInt('请选择是否按月均摊')],
				resetSurplus: [ruleRequiredInt('请选择是否重置余量')],
				effectiveDate: [ruleRequired('请选择生效日期')],
			},
			pickerOptions: {
				disabledDate(time) {
					return time.getTime() < Date.now()
				},
			},
			pickerOptions1: {
				disabledDate(time) {
					return new Date(time).getDate() > 28
				},
			},
			initBillTypeId: '1', // 初始调价计费类型
		}
	},

	created() {
		if (this.operateType > 0) {
			this.initBillTypeId = String(this.editPriceForm.billingTypeId)
			this.adjustPriceBackPackage()
		}
	},

	methods: {
		/* 调整价格和编辑时回显已存在价格数据信息 */
		adjustPriceBackPackage() {
			this.priceForm = {
				...this.priceForm,
				...JSON.parse(JSON.stringify(this.editPriceForm)),
				billingTypeId: String(this.editPriceForm.billingTypeId),
				ladderPopulation: this.editPriceForm.ladderPopulation || 0,
				adjustLadder: this.editPriceForm.adjustLadder || 0,
				resetSurplus: this.editPriceForm.resetSurplus || '',
			}
		},

		/**
		 * 新增、调整、编辑价格数据提交
		 * 校验阶梯计费和分时计费表格表单获取表格提交数据
		 */
		saveOperatePrice() {
			this.$refs.priceForm.validate(async valid => {
				if (valid) {
					const x = this.operateType
					const params = await this.validateLevelAndTimeTable()
					if (params || x === 2 || this.priceForm.billingTypeId === '1') {
						if (this.realm === 'water' && this.$refs.ExtraBilling) {
							// 水务附加费校验
							let flag = false
							this.$refs.ExtraBilling.$refs.extraBillingForm.validate(valid => (flag = valid))
							const billItems = this.$refs.ExtraBilling.extraBillingForm.tableData
							if (!flag) return
							this.priceForm.priceBillItemList = billItems
						}
						const API = x === 1 ? apiAdjustPrice : x === 0 ? apiAdditionPrice : apiEditPrice
						const searchParams =
							x === 2
								? {
										priceCode: this.priceForm.priceCode,
										priceId: this.priceForm.priceId,
										priceName: this.priceForm.priceName,
										usageNature: this.priceForm.usageNature,
								  }
								: {
										...this.priceForm,
										...(this.priceForm.billingTypeId === '1' ? {} : params),
										cycleStartTime:
											this.operateType === 0
												? this.priceForm.cycleStartTime || undefined
												: undefined,
										cycleStartDate:
											this.operateType === 1
												? this.priceForm.cycleStartTime &&
												  this.priceForm.cycleStartTime.substr(0, 10)
												: undefined,
								  }
						if (x === 1) {
							if (
								!(
									this.allowDefineCycSurplus &&
									this.initBillTypeId === '2' &&
									this.priceForm.billingTypeId === '2'
								)
							) {
								searchParams.resetSurplus = 0
							}
						}
						API(searchParams).then(() => {
							this.$message.success(`${x === 1 ? '调整' : x === 0 ? '新增' : '编辑'}价格成功`)
							this.$emit('operate-success')
							this.$emit('close-model')
						})
					}
				}
			})
		},

		/**
		 * 阶梯计费和分时计费表格表单校验
		 */
		validateLevelAndTimeTable() {
			return new Promise(resolve => {
				if (this.priceForm.billingTypeId === '2') {
					this.$refs.TieredBilling.$refs.levelPriceForm.validate(valid => {
						if (valid) {
							resolve(this.$refs.TieredBilling.packageParams())
						}
					})
				} else if (this.priceForm.billingTypeId === '3') {
					this.$refs.TimeBilling.$refs.timePriceForm.validate(valid => {
						if (valid) {
							resolve(this.$refs.TimeBilling.packageParams())
						}
					})
				}
				resolve(false)
			})
		},

		updatePriceCode() {
			const priceNatureId = this.priceForm.usageNature
			if (priceNatureId || priceNatureId === 0) {
				const natureObject = this.waterNatureMap[priceNatureId]
				this.priceForm.priceCode = (natureObject || {}).natureNo || ''
			} else {
				this.priceForm.priceCode = ''
			}
		},
	},
	computed: {
		waterNatureMap() {
			const buildMap = (data, map) => {
				data.forEach(item => {
					const copy = { ...item }
					if (copy.children) {
						buildMap(copy.children, map)
						delete copy.children
					}
					map[copy.priceNatureId] = copy
				})
				return map
			}

			return buildMap(this.waterNatureTreeData, {})
		},
	},
	watch: {
		dialogVisible(newVal) {
			if (!newVal) {
				this.$emit('close-model')
			}
		},
		// 阶梯计费按月均摊模式下计费周期自动变成12个月
		'priceForm.adjustLadder'(adjustLadder) {
			if (adjustLadder === 1) {
				this.priceForm.billingCycle = 12
			}
		},
		// 价格编号取用水性质编号
		'priceForm.usageNature'() {
			this.updatePriceCode()
		},
		// 价格编号取用水性质编号
		waterNatureTreeData() {
			this.updatePriceCode()
		},
	},
}
</script>

<style scoped lang="scss">
::v-deep .el-dialog__body {
	padding: 15px 0 0 !important;
	.el-form {
		padding: 0 20px;
	}
}
::v-deep {
	.el-cascader,
	.el-date-editor {
		width: 100%;
	}
}
.suffix_text {
	font-size: 12px;
}
</style>
