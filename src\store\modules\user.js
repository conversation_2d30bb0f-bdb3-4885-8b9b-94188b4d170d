import { apiLogin, apiOmLogin, apiGetTenantDetail, apiGetOrgDetail } from '@/api/user.api'
import {
	getToken,
	setToken,
	removeToken,
	getPermission,
	setPermission,
	removePermission,
	getUserInfo,
	setUserInfo,
	removeUserInfo,
	removeRoutes,
	getFieldName,
	setFieldName,
	removeDataList,
	removeOrgList,
	removeFieldName,
	getTenant,
	setTenant,
	removeTenant,
	setSS,
	removeSS,
	removeLS,
	removeInvoiceSet,
} from '@/utils/storage'
import lsKey from '@/consts/lsKey'
import { resetRouter } from '@/router'
import { waterFieldName, gasFieldName } from '@/consts/waterGasFieldName'
import { encrypt } from '@/utils'

export default {
	namespaced: true,
	state: {
		token: getToken(),
		userInfo: getUserInfo(),
		permissions: getPermission(),
		fieldName: getFieldName() || waterFieldName,
		tenant: getTenant() || {}, // address: 租户所在地址，basic租户基本信息，business_config租户业务配置
		showPwdDialog: false,
		needUpdatePwd: false,
	},
	mutations: {
		SET_TOKEN: (state, token) => {
			state.token = token
		},
		SET_USER_INFO: (state, userInfo) => {
			state.userInfo = userInfo
		},
		SET_PERMISSIONS: (state, permissions) => {
			state.permissions = permissions
		},
		SET_FIELD_NAME: (state, fieldName) => {
			state.fieldName = fieldName
		},
		SET_TENANT: (state, tenantInfo) => {
			state.tenant = tenantInfo
		},
		SET_PWD_DIALOG: (state, show) => {
			state.showPwdDialog = show
		},
		SET_NEED_UPDATE_PWD: (state, need) => {
			state.needUpdatePwd = need
		},
	},
	actions: {
		emitUpdatePwd({ commit, state }) {
			commit('SET_PWD_DIALOG', true)
			commit('SET_USER_INFO', {
				...state.userInfo,
				needUpdatePwd: true,
			})
		},
		handlePwdUpdated({ state, commit }) {
			commit('SET_PWD_DIALOG', false)
			const userInfo = {
				...state.userInfo,
				needUpdatePwd: false,
			}
			commit('SET_USER_INFO', userInfo)
			setUserInfo(userInfo)
		},
		// user login
		login({ commit, dispatch }, { isOmLogin, loginForm, loginCertify }) {
			const { username, password, dynamicCode, codeKey } = loginForm
			const requestInfo = {
				account: username,
				dynamicCode,
				codeKey,
				// password: password,
				code: 1,
				password: encrypt(password),
			}
			const api = isOmLogin ? apiOmLogin : apiLogin
			return new Promise((resolve, reject) => {
				api(requestInfo, loginCertify)
					.then(response => {
						const { permissions = [], accessToken, userLevel, isAdmin, pwStrength, ...restData } = {
							...response,
							permissions: response.permissions || [],
						}
						// set token
						commit('SET_TOKEN', accessToken)
						setToken(accessToken)
						setSS('sToken', accessToken)
						// set permissions
						if (permissions.length === 0) {
							reject('当前用户没有任何权限')
						}

						const permissionArr = permissions.map(item => item.permissionCode)
						commit('SET_PERMISSIONS', permissionArr)
						setPermission(permissionArr)

						const userInfo = {
							permissions: [],
							userLevel,
							isAdmin,
							needUpdatePwd: pwStrength && pwStrength == 2,
							...restData,
						}

						setUserInfo(userInfo)
						commit('SET_USER_INFO', userInfo)
						if (userInfo.realm) {
							dispatch('setFieldName', userInfo.realm)
						}
						resolve(response)
					})
					.catch(error => {
						reject(error)
					})
			})
		},
		setUserInfo({ commit }, info) {
			setUserInfo(info)
			commit('SET_USER_INFO', info)
		},
		setFieldName({ commit }, realm) {
			let fieldName
			switch (realm) {
				case 'gas':
					fieldName = gasFieldName
					break
				case 'water':
					fieldName = waterFieldName
					break
			}
			commit('SET_FIELD_NAME', fieldName)
			setFieldName(fieldName)
		},

		// user logout
		// logout({ state }) {
		//   return new Promise((resolve, reject) => {
		//     apiLogout(state.token)
		//       .then(() => {
		//         resolve();
		//       })
		//       .catch((error) => {
		//         reject(error);
		//       });
		//   });
		// },
		// 切换租户缓存租户信息
		resetTenant({ commit }, info) {
			return new Promise(resolve => {
				commit('SET_TENANT', info)
				setTenant(info)
				resolve(info)
			})
		},
		getOrgDetail({ commit, state }) {
			// eslint-disable-next-line no-unused-vars
			return new Promise((resolve, reject) => {
				apiGetOrgDetail({ id: state.userInfo.orgId }).then(res => {
					const tenant = {
						...state.tenant,
						address: res.basic,
					}
					commit('SET_TENANT', tenant)
					setTenant(tenant)
					resolve(res)
				})
			})
		},
		// 获取租户基本信息：租户地址，租户基本业务，租户详情
		getTenantDetail({ commit, state }) {
			// eslint-disable-next-line no-unused-vars
			return new Promise((resolve, reject) => {
				apiGetTenantDetail().then(res => {
					const tenant = {
						...state.tenant,
						...res,
					}
					commit('SET_TENANT', tenant)
					setTenant(tenant)
					resolve(res)
				})
			})
		},
		removeToken({ commit }) {
			commit('SET_TOKEN', '')
			removeToken()
			removeSS('sToken')
		},
		// remove token 账号长时间未登录或在别处登录，请重新登录！
		resetUserLS({ commit, dispatch }) {
			return new Promise(resolve => {
				dispatch('removeToken')
				dispatch('tagsView/delAllViews', null, { root: true })
				dispatch('dataList/removeDataList', {}, { root: true })
				dispatch('dataList/removeOrgList', [], { root: true })
				//清空档案模块缓存数据
				dispatch('archives/resetArchives', null, { root: true })
				// 清除state和storage

				commit('SET_PERMISSIONS', [])
				commit('SET_USER_INFO', {})
				commit('SET_FIELD_NAME', {})
				commit('SET_TENANT', {})

				removePermission()
				removeUserInfo()
				removeDataList()
				removeOrgList()
				removeFieldName()
				removeTenant()

				const { OrgsTemplateTree } = lsKey
				removeLS(OrgsTemplateTree)
				removeInvoiceSet()

				// reset routes
				removeRoutes()
				resetRouter()

				resolve()
			})
		},
	},
}
