export function getFormItems(_this) {
	const newWaterMeterFormItems = [
		{
			type: 'slot',
			label: '新水表信息',
			slotName: 'newMeterInfo',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-input',
			label: '水表编号',
			prop: 'meterNo',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-select',
			label: '水表类型',
			prop: 'meterTypeId',
			options: _this.meterTypeOptions,
			attrs: {
				col: 12,
			},
			events: {
				change: value => {
					if (value) {
						const { manufacturerName, meterRange } = _this.meterTypeOptions.find(
							item => item.value === value,
						)
						_this.formData.manufacturerName = manufacturerName
						_this.formData.ranges = meterRange
					} else {
						_this.formData.manufacturerName = ''
						_this.formData.ranges = ''
					}
				},
			},
		},
		{
			type: 'el-date-picker',
			label: '装表时间',
			prop: 'installationDate',
			attrs: {
				col: 12,
				valueFormat: 'yyyy-MM-dd',
			},
		},
		{
			type: 'el-input',
			label: '水表仓库编号',
			prop: 'meterWarehouseCode',
			attrs: {
				col: 12,
			},
		},

		{
			type: 'el-input',
			label: '水表厂商',
			prop: 'manufacturerName',
			attrs: {
				col: 12,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '防盗编号',
			prop: 'antiTheftCode',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '量程',
			prop: 'ranges',
			attrs: {
				col: 12,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '水表型号',
			prop: 'meterModel',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '口径',
			prop: 'caliber',
			attrs: {
				col: 12,
			},
		},

		{
			type: 'el-input',
			label: '初始读数',
			prop: 'startMeterReading',
			attrs: {
				col: 12,
				maxlength: 8,
			},
			events: {
				input: val => {
					// 只允许输入数字，实时过滤非数字字符
					const numericValue = val.replace(/[^0-9]/g, '')
					// 限制最大值
					const maxValue = 99999999
					const finalValue = numericValue ? Math.min(parseInt(numericValue), maxValue).toString() : ''

					if (finalValue !== val) {
						_this.formData.startMeterReading = finalValue
					}
				},
			},
		},
		{
			type: 'el-input',
			label: '水表标号',
			prop: 'baseMeterNo',
			attrs: {
				col: 12,
			},
		},
	]
	const baseFormItems = [
		{
			type: 'el-input',
			label: '表卡编号',
			prop: 'archivesIdentity',
			attrs: {
				col: 12,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '水表编号',
			prop: 'currentMeterNo',
			attrs: {
				col: 12,
				disabled: true,
			},
		},
		{
			type: 'el-date-picker',
			label: '恢复表卡时间',
			prop: 'resumeDate',
			labelWidth: '120px',
			attrs: {
				type: 'date',
				col: 12,
				valueFormat: 'yyyy-MM-dd',
			},
		},
		{
			type: 'el-input',
			label: '恢复表卡操作人员',
			prop: 'resumePerson',
			labelWidth: '145px',
			attrs: {
				col: 12,
				disabled: true,
			},
		},
		{
			type: 'el-select',
			label: '水表操作类型',
			prop: 'meterOperate',
			labelWidth: '120px',
			options:
				_this.$store.getters?.dataList?.operatorMeterType
					?.filter(item => ['2', '5'].includes(item.sortValue))
					.map(item => {
						return {
							label: item.sortName,
							value: item.sortValue,
						}
					}) || [],
			attrs: {
				col: 12,
			},
			events: {},
		},
		{
			type: 'el-input',
			label: '恢复表卡原因',
			prop: 'resumeReason',
			labelWidth: '120px',
			attrs: {
				type: 'textarea',
				col: 12,
				maxlength: '64',
				showWordLimit: true,
				autosize: {
					minRows: 4,
					maxRows: 4,
				},
			},
		},
	]
	baseFormItems.forEach(item => {
		_this.$set(_this.formData, item.prop, '')
	})
	return {
		baseFormItems,
		newWaterMeterFormItems,
	}
}
