<template>
	<GcElDialog
		width="1000px"
		:show="isShow"
		title="装表"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:meterInfo>
				<Search v-if="isShow" style="float: right" :active-tab="{ id: 3 }" @use="handleSearchMeter" />
			</template>
		</GcFormRow>
	</GcElDialog>
</template>

<script>
import { isBlank } from '@/utils/validate.js'
import { apiGetMeterType, apiArchivesIntallMeter, apiArchivesIntallMeter1 } from '@/api/meterManage.api.js'
import { getFormItems } from './formItem.js'
import { ruleRequired, ruleMaxLength, RULE_STARTMETER_READING } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import Search from '../search/index.vue'
export default {
	components: { Search },
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		detailData: {
			type: Object,
			default: () => ({}),
		},
		permissionCode: {
			type: String,
			default: 'cpm_archives_intallMeter',
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		formItems() {
			return getFormItems(this)
		},
		newDetailData() {
			return Object.assign({}, ...Object.values(this.detailData))
		},
	},
	watch: {
		isShow: {
			handler(val) {
				if (val) {
					this._apiGetMeterType()
				}
			},
		},
	},
	data() {
		return {
			formData: {
				meterNo: '',
				meterWarehouseCode: '',
				antiTheftCode: '',
				baseMeterNo: '',
				meterTypeId: '',
				manufacturerName: '',
				meterModel: '',
				caliber: '',
				ranges: '',
				useYears: '',
				installationDate: '',
				startMeterReading: '',
			},
			formAttrs: {
				labelWidth: '120px',
				rules: {
					meterNo: [ruleRequired('必填'), ruleMaxLength(32)],
					meterWarehouseCode: [ruleMaxLength(32)],
					antiTheftCode: [ruleMaxLength(32)],
					baseMeterNo: [ruleMaxLength(32)],
					meterTypeId: [ruleRequired('必填')],
					meterModel: [ruleMaxLength(30)],
					caliber: [ruleMaxLength(16)],
					ranges: [ruleRequired('必填')],
					installationDate: [ruleRequired('必填')],
					startMeterReading: [ruleRequired('必填'), RULE_STARTMETER_READING],
				},
			},
			meterTypeOptions: [],
			meterId: '',
		}
	},
	methods: {
		async _apiGetMeterType() {
			const res = await apiGetMeterType({
				tenantId: this.$store.getters.userInfo.tenantId,
			})

			this.meterTypeOptions = res.map(item => {
				return {
					value: item.meterTypeId,
					label: item.meterTypeName,
					...item,
				}
			})
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
			this.meterId = ''
		},
		handleSearchMeter(obj) {
			Object.keys(this.formData).forEach(key => {
				if (Object.prototype.hasOwnProperty.call(obj, key)) {
					if (key === 'startMeterReading' && !isBlank(obj.meterReading)) {
						this.formData[key] = obj.meterReading
					} else {
						this.formData[key] = obj[key]
					}
				}
			})
			this.meterId = obj.meterId
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			let formObj = trimParams(removeNullParams(this.formData))
			const { archivesId } = this.newDetailData
			try {
				const apiMethods = {
					cpm_archives_intallMeter: apiArchivesIntallMeter,
					cpm_archives_intallMeter1: apiArchivesIntallMeter1,
				}
				if (this.meterId) {
					Object.assign(formObj, {
						meterId: this.meterId,
					})
				}

				await apiMethods[this.permissionCode]({
					meter: formObj,
					archivesId,
				})
				this.$message.success('装表成功')
				this.handleClose()
				this.$emit('refresh')
			} catch (error) {
				console.log(error)
			}
		},
	},
}
</script>
