import { getLodop } from '../lodop/LodopFuncs'
import { geLsRecall } from '@/api/arrearageManage.api'

// 欠费通知单
export const generateArrearageTemplate = (data, type = 'preview') => {
	const LODOP = getLodop()

	LODOP.PRINT_INIT('过期水费催缴停水通知单')
	LODOP.PRINT_INITA(0, 0, 800, 1122, '过期水费催缴停水通知单')
	LODOP.SET_PRINT_MODE('PROGRAM_CONTENT_BYVAR', true)
	LODOP.SET_PRINT_STYLE('FontSize', 12)
	LODOP.SET_PRINT_STYLE('FontColor', '#808080')

	LODOP.ADD_PRINT_TEXT(143, 380, 215, 20, '缴费账号')
	LODOP.ADD_PRINT_TEXT(185, 188, 512, 20, '用户名称')
	LODOP.ADD_PRINT_TEXT(226, 192, 141, 20, 'xxxxxx区')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(226, 366, 154, 20, 'xxxx路')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(226, 608, 80, 20, 'xxxx号')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(268, 185, 51, 20, '2024')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(268, 255, 28, 20, '12')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(268, 301, 31, 20, '31')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(309, 190, 511, 20, '停水原因')
	LODOP.ADD_PRINT_TEXT(351, 190, 267, 20, '经办人')
	LODOP.ADD_PRINT_TEXT(516, 148, 189, 20, '用户名称')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(559, 226, 132, 20, 'xxxx区')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(559, 386, 135, 20, 'xxxx路')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(559, 607, 65, 20, 'xxxx号')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(601, 196, 95, 20, '缴费账号')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(602, 304, 54, 20, '2024')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(602, 380, 40, 20, '12')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(642, 496, 153, 20, '甘井子（东）')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(684, 222, 132, 20, '1555.2')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(726, 117, 45, 20, '2024')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(726, 184, 30, 20, '12')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(726, 232, 30, 20, '31')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(809, 189, 145, 20, '0571-8856855')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(934, 498, 113, 20, '甘井子（东）')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(976, 563, 121, 20, '经办人')

	LODOP.ADD_PRINT_TEXT(1018, 480, 46, 20, '2024')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(1018, 544, 30, 20, '12')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(1018, 592, 33, 20, '31')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_SETUP_BKIMG(`<img border='0' src='print-template/arrearage.jpg'>`)
	LODOP.SET_SHOW_MODE('BKIMG_WIDTH', 800)
	LODOP.SET_SHOW_MODE('BKIMG_HEIGHT', 1122)

	// 预览、打印时包含背景图片
	LODOP.SET_SHOW_MODE('BKIMG_IN_PREVIEW', 1)
	LODOP.SET_SHOW_MODE('BKIMG_PRINT', true)

	if (type === 'preview') {
		LODOP.PREVIEW()
		// LODOP.PRINT_DESIGN();
	} else {
		LODOP.PRINT()
	}
}

export const arrearagePrint = async () => {
	try {
		//   await xxx
		generateArrearageTemplate({})
	} catch (error) {
		console.error(error)
	}
}

export const billingLsBatchPrint = async (permission, recordsList) => {
	try {
		let resList = []
		if (permission === 'billing_recall_ls') {
			// 册本视图 抄表卡打印
			resList = await geLsRecall({
				recordsList,
			})
		}
		console.log(resList)
		generateBillingLsTemplate(resList)
	} catch (error) {
		console.error(error)
	}
}

export const generateBillingLsTemplate = (data, type = 'preview') => {
	const LODOP = getLodop()

	LODOP.PRINT_INIT('大连市自来水集团有限公司旅顺分公司水费通知单')
	LODOP.SET_PRINT_MODE('PROGRAM_CONTENT_BYVAR', true)
	LODOP.SET_PRINT_STYLE('FontSize', 12)
	LODOP.SET_PRINT_STYLE('FontColor', '#808080')

	data.forEach((item, index) => {
		console.log(index, item)
		// 0 页面第一张通知单 1 页面第二张通知单
		let remainder = index % 2
		let cursorHeight = remainder === 0 ? 0 : 540
		LODOP.ADD_PRINT_IMAGE(
			cursorHeight + 20,
			0,
			'100%',
			'100%',
			"<img border='0' src='print-template/lsbilling.png' />",
		)
		LODOP.SET_PRINT_STYLEA(0, 'Stretch', 2) // 按原图比例缩放
		LODOP.SET_PRINT_STYLE('FontSize', 12)
		LODOP.ADD_PRINT_TEXT(cursorHeight + 96, 135, 148, 20, item.archivesIdentity)
		LODOP.SET_PRINT_STYLE('FontSize', 10)
		LODOP.ADD_PRINT_TEXT(cursorHeight + 96, 375, 140, 40, item.userName)
		LODOP.SET_PRINT_STYLE('FontSize', 12)
		LODOP.ADD_PRINT_TEXT(cursorHeight + 96, 573, 140, 40, item.meterNo)
		LODOP.SET_PRINT_STYLE('FontSize', 10)
		LODOP.ADD_PRINT_TEXT(cursorHeight + 125, 135, 240, 40, item.addressFullName)
		LODOP.ADD_PRINT_TEXT(cursorHeight + 127, 505, 220, 20, item.bankNo)
		LODOP.SET_PRINT_STYLE('FontSize', 10)
		// 月指针
		LODOP.ADD_PRINT_TEXT(cursorHeight + 157, 135, 110, 20, item.lastMeterReading)
		// 月银行余额
		LODOP.ADD_PRINT_TEXT(cursorHeight + 157, 375, 102, 20, item.bankBalanceAmount)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3)
		// 月微信余额
		LODOP.ADD_PRINT_TEXT(cursorHeight + 157, 624, 70, 20, item.meterBalanceAmount)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3)
		// 本月指针
		LODOP.ADD_PRINT_TEXT(cursorHeight + 189, 135, 110, 20, item.curMeterReading)
		// 本月水量
		LODOP.ADD_PRINT_TEXT(cursorHeight + 189, 375, 102, 20, item.useAmount)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3)
		// 本月水费
		LODOP.ADD_PRINT_TEXT(cursorHeight + 189, 624, 70, 20, item.useAmt)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3)
		//年累计水量
		LODOP.ADD_PRINT_TEXT(cursorHeight + 221, 135, 90, 20, item.yearUseAmount)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3)
		//合计欠水量
		LODOP.ADD_PRINT_TEXT(cursorHeight + 221, 375, 102, 20, item.yearArrearsUseAmount)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3)
		//合计欠水费
		LODOP.ADD_PRINT_TEXT(cursorHeight + 221, 624, 70, 20, item.yearArrearsUseAmt)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3)

		item.priceInfoList.forEach((price, index) => {
			LODOP.ADD_PRINT_TEXT(cursorHeight + 255 + index * 32, 50, 193, 20, price.priceDesc)
			LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

			LODOP.ADD_PRINT_TEXT(cursorHeight + 255 + index * 32, 250, 115, 20, price.borderDesc)
			LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
		})
		//抄表员
		LODOP.ADD_PRINT_TEXT(cursorHeight + 400, 135, 146, 20, item.meterReadingStaffName)
		//抄表员电话
		LODOP.ADD_PRINT_TEXT(cursorHeight + 400, 375, 155, 20, item.meterReadingStaffPhone)

		if (remainder === 1 && index !== data.length - 1) {
			LODOP.NEWPAGE()
		}
	})

	if (type === 'preview') {
		LODOP.PREVIEW()
	} else {
		LODOP.PRINT()
	}
}
