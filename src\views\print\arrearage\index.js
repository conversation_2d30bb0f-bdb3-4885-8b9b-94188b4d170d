import { getLodop } from '../lodop/LodopFuncs'
import { geLsRecall } from '@/api/arrearageManage.api'

// 欠费通知单
export const generateArrearageTemplate = (data, type = 'preview') => {
	const LODOP = getLodop()

	LODOP.PRINT_INIT('过期水费催缴停水通知单')
	LODOP.PRINT_INITA(0, 0, 800, 1122, '过期水费催缴停水通知单')
	LODOP.SET_PRINT_MODE('PROGRAM_CONTENT_BYVAR', true)
	LODOP.SET_PRINT_STYLE('FontSize', 12)
	LODOP.SET_PRINT_STYLE('FontColor', '#808080')

	LODOP.ADD_PRINT_TEXT(143, 380, 215, 20, '缴费账号')
	LODOP.ADD_PRINT_TEXT(185, 188, 512, 20, '用户名称')
	LODOP.ADD_PRINT_TEXT(226, 192, 141, 20, 'xxxxxx区')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(226, 366, 154, 20, 'xxxx路')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(226, 608, 80, 20, 'xxxx号')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(268, 185, 51, 20, '2024')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(268, 255, 28, 20, '12')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(268, 301, 31, 20, '31')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(309, 190, 511, 20, '停水原因')
	LODOP.ADD_PRINT_TEXT(351, 190, 267, 20, '经办人')
	LODOP.ADD_PRINT_TEXT(516, 148, 189, 20, '用户名称')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(559, 226, 132, 20, 'xxxx区')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(559, 386, 135, 20, 'xxxx路')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(559, 607, 65, 20, 'xxxx号')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(601, 196, 95, 20, '缴费账号')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(602, 304, 54, 20, '2024')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(602, 380, 40, 20, '12')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(642, 496, 153, 20, '甘井子（东）')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(684, 222, 132, 20, '1555.2')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(726, 117, 45, 20, '2024')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(726, 184, 30, 20, '12')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(726, 232, 30, 20, '31')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(809, 189, 145, 20, '0571-8856855')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(934, 498, 113, 20, '甘井子（东）')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(976, 563, 121, 20, '经办人')

	LODOP.ADD_PRINT_TEXT(1018, 480, 46, 20, '2024')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(1018, 544, 30, 20, '12')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(1018, 592, 33, 20, '31')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_SETUP_BKIMG(`<img border='0' src='print-template/arrearage.jpg'>`)
	LODOP.SET_SHOW_MODE('BKIMG_WIDTH', 800)
	LODOP.SET_SHOW_MODE('BKIMG_HEIGHT', 1122)

	// 预览、打印时包含背景图片
	LODOP.SET_SHOW_MODE('BKIMG_IN_PREVIEW', 1)
	LODOP.SET_SHOW_MODE('BKIMG_PRINT', true)

	if (type === 'preview') {
		LODOP.PREVIEW()
		// LODOP.PRINT_DESIGN();
	} else {
		LODOP.PRINT()
	}
}

export const arrearagePrint = async () => {
	try {
		//   await xxx
		generateArrearageTemplate({})
	} catch (error) {
		console.error(error)
	}
}

export const billingLsBatchPrint = async (permission, recordsList) => {
	try {
		let resList = []
		if (permission === 'billing_recall_ls') {
			// 册本视图 抄表卡打印
			resList = await geLsRecall({
				recordsList,
			})
		}
		console.log(resList)
		generateBillingLsTemplate(resList)
	} catch (error) {
		console.error(error)
	}
}

export const generateBillingLsTemplate = (data, type = 'preview') => {
	const LODOP = getLodop()

	LODOP.PRINT_INIT('大连市自来水集团有限公司旅顺分公司水费通知单')
	LODOP.SET_PRINT_MODE('PROGRAM_CONTENT_BYVAR', true)
	LODOP.SET_PRINT_STYLE('FontSize', 12)
	LODOP.SET_PRINT_STYLE('FontColor', '#808080')

	data.forEach((item, index) => {
		console.log(index, item)
		// 0 页面第一张通知单 1 页面第二张通知单
		let remainder = index % 2
		let cursorHeight = remainder === 0 ? 0 : 540
		//绘制发票外边框
		LODOP.SET_PRINT_STYLE('FontColor', '#6D72B9')
		LODOP.ADD_PRINT_RECT(cursorHeight + 88, 45, 675, 338, 0, 3)
		//绘制横向边框
		LODOP.ADD_PRINT_LINE(cursorHeight + 120, 45, cursorHeight + 120, 719, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 152, 45, cursorHeight + 152, 719, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 184, 45, cursorHeight + 184, 719, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 216, 45, cursorHeight + 216, 719, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 248, 45, cursorHeight + 248, 719, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 280, 45, cursorHeight + 280, 368, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 312, 45, cursorHeight + 312, 368, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 344, 45, cursorHeight + 344, 368, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 394, 45, cursorHeight + 394, 532, 0, 3)
		//绘制竖向边框
		LODOP.ADD_PRINT_LINE(cursorHeight + 88, 132, cursorHeight + 248, 132, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 394, 130, cursorHeight + 426, 130, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 152, 248, cursorHeight + 344, 248, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 88, 286, cursorHeight + 120, 286, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 394, 284, cursorHeight + 426, 284, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 88, 370, cursorHeight + 426, 370, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 88, 501, cursorHeight + 248, 501, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 248, 532, cursorHeight + 426, 532, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 88, 568, cursorHeight + 120, 568, 0, 3)
		LODOP.ADD_PRINT_LINE(cursorHeight + 152, 622, cursorHeight + 248, 622, 0, 3)

		//绘制文本
		LODOP.SET_PRINT_STYLE('Bold', 1)
		LODOP.SET_PRINT_STYLE('FontSize', 12)

		//用户编号
		LODOP.ADD_PRINT_TEXT(cursorHeight + 96, 56, 148, 20, '用户编号')
		//姓名
		LODOP.ADD_PRINT_TEXT(cursorHeight + 96, 303, 148, 20, '姓  名')
		//水号
		LODOP.ADD_PRINT_TEXT(cursorHeight + 96, 511, 148, 20, '水  号')
		//地址
		LODOP.ADD_PRINT_TEXT(cursorHeight + 128, 60, 148, 20, '地   址')
		//银行账号
		LODOP.ADD_PRINT_TEXT(cursorHeight + 128, 397, 77, 20, '银行账号')
		LODOP.SET_PRINT_STYLEA(0, 'AlignJustify', 1) // 两端对齐
		//月指针
		LODOP.ADD_PRINT_TEXT(cursorHeight + 160, 77, 148, 20, '月指针')
		//月银行余额
		LODOP.ADD_PRINT_TEXT(cursorHeight + 160, 285, 148, 20, '月银行余额')
		LODOP.ADD_PRINT_TEXT(cursorHeight + 160, 480, 20, 20, '元')
		//月微信余额
		LODOP.ADD_PRINT_TEXT(cursorHeight + 160, 535, 148, 20, '月微信余额')
		LODOP.ADD_PRINT_TEXT(cursorHeight + 160, 699, 20, 20, '元')
		//本月指针
		LODOP.ADD_PRINT_TEXT(cursorHeight + 192, 49, 79, 20, '本月指针')
		LODOP.SET_PRINT_STYLEA(0, 'AlignJustify', 1) // 两端对齐
		//本月水量
		LODOP.ADD_PRINT_TEXT(cursorHeight + 192, 269, 79, 20, '本月水量')
		LODOP.SET_PRINT_STYLEA(0, 'AlignJustify', 1) // 两端对齐
		LODOP.ADD_PRINT_TEXT(cursorHeight + 192, 480, 20, 20, '吨')
		//本月水费
		LODOP.ADD_PRINT_TEXT(cursorHeight + 192, 520, 80, 20, '本月水费')
		LODOP.SET_PRINT_STYLEA(0, 'AlignJustify', 1) // 两端对齐
		LODOP.ADD_PRINT_TEXT(cursorHeight + 192, 699, 20, 20, '元')
		//年累计水量
		LODOP.ADD_PRINT_TEXT(cursorHeight + 224, 48, 148, 20, '年累计水量')
		LODOP.ADD_PRINT_TEXT(cursorHeight + 224, 226, 20, 20, '吨')
		//合计欠水量
		LODOP.ADD_PRINT_TEXT(cursorHeight + 224, 268, 148, 20, '合计欠水量')
		LODOP.ADD_PRINT_TEXT(cursorHeight + 224, 480, 20, 20, '吨')
		//合计欠水费
		LODOP.ADD_PRINT_TEXT(cursorHeight + 224, 521, 148, 20, '合计欠水费')
		LODOP.ADD_PRINT_TEXT(cursorHeight + 224, 699, 20, 20, '元')
		//价格提示
		LODOP.ADD_PRINT_TEXT(cursorHeight + 352, 128, 180, 20, '居民用户执行阶梯水价')
		LODOP.ADD_PRINT_TEXT(cursorHeight + 371, 112, 220, 20, '非居民用户按用水性质而定')
		//抄表员
		LODOP.ADD_PRINT_TEXT(cursorHeight + 402, 63, 100, 20, '抄表员')
		//电话
		LODOP.ADD_PRINT_TEXT(cursorHeight + 402, 303, 43, 20, '电话')
		LODOP.SET_PRINT_STYLEA(0, 'AlignJustify', 1) // 两端对齐

		//微信扫码
		LODOP.SET_PRINT_STYLE('FontSize', 14)
		LODOP.ADD_PRINT_TEXT(cursorHeight + 312, 385, 180, 20, '请微信扫码缴费')
		//标题
		LODOP.SET_PRINT_STYLE('FontSize', 16)
		LODOP.SET_PRINT_STYLE('FontColor', '#48ACF3')
		LODOP.ADD_PRINT_TEXT(cursorHeight + 60, 143, 548, 20, '大连市自来水集团有限公司旅顺分公司水费通知单')
		//二维码 0 c4 f7
		LODOP.SET_PRINT_STYLE('FontColor', '#00C4F7')
		LODOP.ADD_PRINT_BARCODE(
			cursorHeight + 252,
			540,
			200,
			200,
			'QRCode',
			'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx2fb2b0c6e08d5d9d&redirect_uri=https%3A%2F%2Fcrsp.bankcomm.com%2Fservices%2Fbranch%2Fdl%2Fdlcitywater%2Findex.html%3Fitem%3D%2Fhome&response_type=code&scope=snsapi_base&state=1#wechat_redirect',
		)
		LODOP.SET_PRINT_STYLEA(0, 'QRCodeVersion', 10)
		LODOP.SET_PRINT_STYLEA(0, 'QRCodeErrorLevel', 'L')

		//内容
		LODOP.SET_PRINT_STYLE('Bold', 0)
		LODOP.SET_PRINT_STYLE('FontSize', 12)
		LODOP.SET_PRINT_STYLE('FontColor', '#000')
		LODOP.ADD_PRINT_TEXT(cursorHeight + 96, 135, 148, 20, item.archivesIdentity)
		LODOP.SET_PRINT_STYLE('FontSize', 10)
		LODOP.ADD_PRINT_TEXT(cursorHeight + 96, 375, 140, 40, item.userName)
		LODOP.SET_PRINT_STYLE('FontSize', 12)
		LODOP.ADD_PRINT_TEXT(cursorHeight + 96, 573, 140, 40, item.meterNo)
		LODOP.SET_PRINT_STYLE('FontSize', 10)
		LODOP.ADD_PRINT_TEXT(cursorHeight + 125, 135, 240, 40, item.addressFullName)
		LODOP.ADD_PRINT_TEXT(cursorHeight + 127, 505, 220, 20, item.bankNo)
		LODOP.SET_PRINT_STYLE('FontSize', 10)
		// 月指针
		LODOP.ADD_PRINT_TEXT(cursorHeight + 157, 135, 110, 20, item.lastMeterReading)
		// 月银行余额
		LODOP.ADD_PRINT_TEXT(cursorHeight + 157, 375, 102, 20, item.bankBalanceAmount)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3)
		// 月微信余额
		LODOP.ADD_PRINT_TEXT(cursorHeight + 157, 624, 70, 20, item.meterBalanceAmount)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3)
		// 本月指针
		LODOP.ADD_PRINT_TEXT(cursorHeight + 189, 135, 110, 20, item.curMeterReading)
		// 本月水量
		LODOP.ADD_PRINT_TEXT(cursorHeight + 189, 375, 102, 20, item.useAmount)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3)
		// 本月水费
		LODOP.ADD_PRINT_TEXT(cursorHeight + 189, 624, 70, 20, item.receivableAmount)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3)
		//年累计水量
		LODOP.ADD_PRINT_TEXT(cursorHeight + 221, 135, 90, 20, item.yearUseAmount)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3)
		//合计欠水量
		LODOP.ADD_PRINT_TEXT(cursorHeight + 221, 375, 102, 20, item.yearArrearsUseAmount)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3)
		//合计欠水费
		LODOP.ADD_PRINT_TEXT(cursorHeight + 221, 624, 70, 20, item.yearArrearsAmt)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 3)

		item.priceInfoList.forEach((price, index) => {
			LODOP.ADD_PRINT_TEXT(cursorHeight + 255 + index * 32, 50, 193, 20, price.priceDesc)
			LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

			LODOP.ADD_PRINT_TEXT(cursorHeight + 255 + index * 32, 250, 115, 20, price.borderDesc)
			LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
		})
		//抄表员
		LODOP.ADD_PRINT_TEXT(cursorHeight + 400, 135, 146, 20, item.meterReadingStaffName)
		//抄表员电话
		LODOP.ADD_PRINT_TEXT(cursorHeight + 400, 375, 155, 20, item.meterReadingStaffPhone)

		if (remainder === 1 && index !== data.length - 1) {
			LODOP.NEWPAGE()
		}
	})

	if (type === 'preview') {
		LODOP.PREVIEW()
	} else {
		LODOP.PRINT()
	}
}
