<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-02 17:00:07
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-18 20:15:11
-->
<template>
	<div class="wrapper">
		<div class="content">
			<div class="content-top">
				<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
					<el-form-item>
						<el-button type="primary" @click="getCardInfo">查询</el-button>
						<el-button @click="handleReset">重置</el-button>
					</el-form-item>
				</GcFormSimple>
				<!-- 表具信息 -->
				<div class="data-container">
					<gc-model-header
						title="表具信息"
						:icon="require('@/assets/images/icon/title-meter.png')"
					></gc-model-header>
					<gc-group-detail :data="meterData"></gc-group-detail>
				</div>
			</div>
			<div class="content-bottom">
				<!-- 表具信息 -->
				<div class="data-container">
					<gc-model-header
						title="追收抄表"
						:icon="require('@/assets/images/icon/title-meter.png')"
					></gc-model-header>
					<GcFormSimple ref="bFormRef" v-model="bFormData" :formItems="bFormItems" :formAttrs="bFormAttrs">
						<template v-slot:footer>
							<el-form-item label=" ">
								<el-button type="primary" :disabled="!infoData.archivesId" @click="handleComputed">
									计算
								</el-button>
							</el-form-item>
						</template>
					</GcFormSimple>
					<h5>待生成账单明细</h5>
					<div class="total-level">
						<div class="total">
							<span>合并</span>
							<div>总水费：{{ waterAmtTotal || '--' }}元</div>
							<div>总污水费：{{ wastewaterAmtTotal || '--' }}元</div>
							<div>总应缴金额：{{ receivableAmountTotal || '--' }}元</div>
						</div>
						<div class="level-switch">
							<el-switch v-model="isLevelComputed" @change="handleSwitch"></el-switch>
							<span>是否进阶梯</span>
						</div>
					</div>
				</div>
				<div v-show="!getIsBlank(distributionMode) && virtualMeterFlag" class="mode-box">
					<div class="label">分配方式：</div>
					<el-radio-group v-model="distributionMode" disabled>
						<el-radio v-for="item in distributionModeList" :key="item.value" :label="item.value">
							{{ item.label }}
						</el-radio>
					</el-radio-group>
				</div>
				<div class="table-container">
					<GcTable :loading="loading" :columns="columns" :table-data="tableData">
						<template #priceDesc="{ row }">
							<div v-html="row.priceDesc" />
						</template>
						<template #priceBillItemList="{ row }">
							<div v-for="(item, index) in row.priceBillItemList" :key="index">
								<span>{{ item.itemName ? `${item.itemName}：` : '' }}</span>
								<span>{{ !getIsBlank(item.billItemPrice) ? `${item.billItemPrice}元` : '--' }}</span>
							</div>
						</template>
						<template #cycSurplusDesc="{ row }">
							<div v-html="row.cycSurplusDesc" />
						</template>
						<template #volume="{ row, $index }">
							<div class="volume-box" v-show="!row.isEditing">
								<span class="flex1">{{ row.volume }}</span>
								<i
									v-if="virtualMeterFlag"
									class="icon-btn el-icon-edit"
									@click="handleOpenEdit(row, $index)"
								/>
							</div>
							<div v-show="row.isEditing" class="volume-box">
								<el-form
									class="table-form flex1"
									:ref="`volumeRef${$index}`"
									:model="tableData[$index]"
								>
									<el-form-item
										prop="volume"
										:rules="{
											required: true,
											message: '请输入水量',
											trigger: 'blur',
										}"
									>
										<el-input-number
											v-model="tableData[$index].volume"
											class="input-number"
											placeholder="请输入水量"
											:min="0"
											:max="999999999"
											step-strictly
											:controls="false"
										/>
									</el-form-item>
								</el-form>
								<i class="icon-btn el-icon-close" @click="handleCloseEdit($index)" />
								<i class="icon-btn el-icon-check" @click="handleSaveEdit(row, $index)" />
							</div>
						</template>
					</GcTable>
				</div>
			</div>
		</div>
		<div class="button-group">
			<el-button @click="handleReset">取消</el-button>
			<el-button class="btn-create" type="primary" v-loading="btnLoading" @click="handleSubmit">
				确定追收并生成账单
			</el-button>
		</div>
	</div>
</template>

<script>
import { isBlank } from '@/utils/validate.js'
import { accAdd, accSub } from '@/utils/calc.js'
import { checkStatusOptions, distributionModeOptions } from '@/consts/optionList.js'
import {
	queryLastMeterReadingRecord,
	meterReadingCollection,
	modifyWaterVolume,
	createMeterReadingCollection,
	queryStaffByType,
} from '@/api/meterReading.api'

export default {
	name: '',
	components: {},
	data() {
		return {
			// 表具信息
			infoData: {
				archivesId: '',
				isVirtual: 0,

				archivesIdentity: '',
				userName: '',
				addressFullName: '',
				bookNo: '',
				meterReadingStaffName: '',
				meterReading: '',
				yearMeterCreditQuantity: '',

				lastRecord: {
					thisRecordDate: '',
					curMeterReading: '',
					useAmount: '',
					checkStatusDesc: '',
				},
			},
			formData: {
				archivesIdentity: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '90px',
				rules: {
					archivesIdentity: [{ required: true, message: '请输入表卡编号', trigger: 'blur' }],
				},
			},
			meterData: {
				list: [
					{
						key: '表卡编号',
						value: '--',
						field: 'archivesIdentity',
					},
					{
						key: '用户名称',
						value: '--',
						field: 'userName',
					},
					{
						key: '表具地址',
						value: '--',
						field: 'addressFullName',
						isEllipsis: true,
					},
					{
						key: '册本编号',
						value: '--',
						field: 'bookNo',
					},
					{
						key: '抄表员',
						value: '--',
						field: 'meterReadingStaffName',
					},
					{
						key: '表指针',
						value: '--',
						field: 'meterReading',
					},
					{
						key: '年累计量',
						value: '--',
						field: 'yearMeterCreditQuantity',
					},

					{
						key: '上次抄表日期',
						value: '--',
						field: 'thisRecordDate',
					},
					{
						key: '上次指针',
						value: '--',
						field: 'curMeterReading',
					},
					{
						key: '上次水量',
						value: '--',
						field: 'useAmount',
					},
					{
						key: '上次抄表情况',
						value: '--',
						field: 'checkStatusDesc',
					},
				],
				row: 7,
			},

			// 追收抄表
			bFormData: {
				thisRecordDate: this.dayjs().format('YYYY-MM-DD HH:mm:ss'),
				lastMeterReading: '',
				curMeterReading: undefined,
				volume: undefined,
				checkStatus: 0,
				meterReadingStaffId: '',
				splitFlag: true,
			},
			bFormItems: [
				{
					type: 'el-date-picker',
					label: '抄表日期',
					prop: 'thisRecordDate',
					attrs: {
						col: 6,
						type: 'datetime',
						clearable: false,
						valueFormat: 'yyyy-MM-dd HH:mm:ss',
						placeholder: '请选择抄表日期',
						pickerOptions: {
							disabledDate: time => {
								const timestamp = time.getTime()
								const currentYearMonth = this.dayjs()
								const currentDate = this.dayjs().valueOf() // 当前日期的时间戳
								// 日期必须大于上次抄表日期 小于的都禁用
								const { thisRecordDate = '' } = this.infoData.lastRecord || {}
								const isGreater = thisRecordDate
									? timestamp < this.dayjs(thisRecordDate).valueOf()
									: false

								return (
									timestamp > currentYearMonth.endOf('month').valueOf() ||
									timestamp < currentYearMonth.startOf('month').valueOf() ||
									timestamp > currentDate ||
									isGreater
								)
							},
						},
					},
					events: {
						blur: () => {
							const { thisRecordDate } = this.bFormData
							const selectedDate = new Date(thisRecordDate).getTime()
							const currentDate = new Date().getTime()
							if (selectedDate > currentDate) {
								this.$message.error('抄表日期必须小于等于当前日期时间')
								this.bFormData.thisRecordDate = this.dayjs().format('YYYY-MM-DD HH:mm:ss')
							}
						},
					},
				},
				{
					type: 'el-input',
					label: '上次指针',
					prop: 'lastMeterReading',
					attrs: {
						col: 6,
						disabled: true,
						placeholder: '',
					},
				},
				{
					type: 'el-input-number',
					label: '本次指针',
					prop: 'curMeterReading',
					attrs: {
						col: 6,
						clearable: true,
						placeholder: '请输入本次指针',
						min: 0,
						max: 999999999,
						stepStrictly: true,
						controls: false,
					},
					events: {
						change: value => {
							if (
								!isBlank(this.bFormData.lastMeterReading) &&
								!isBlank(value) &&
								this.bFormData.checkStatus !== 19
							) {
								const subValue = accSub(value, this.bFormData.lastMeterReading)
								if (subValue > 0) {
									this.bFormData.volume = subValue
									// 移除 本次水量验证错误信息
									this.$refs.bFormRef.clearValidate('volume')
								}
							} else {
								this.bFormData.volume = undefined
							}
						},
					},
				},
				{
					type: 'el-input-number',
					label: '本次水量',
					prop: 'volume',
					attrs: {
						col: 6,
						clearable: true,
						placeholder: '请输入本次水量',
						min: 0,
						max: 999999999,
						stepStrictly: true,
						controls: false,
					},
				},
				{
					type: 'el-select',
					label: '抄表情况',
					prop: 'checkStatus',
					options: checkStatusOptions,
					attrs: {
						col: 6,
						placeholder: '请选择抄表情况',
					},
					events: {
						change: value => {
							// 指针无关
							if (value === 19) {
								// 移除本次指针字段 验证错误信息
								this.$refs.bFormRef.clearValidate('curMeterReading')
							} else {
								this.$refs.bFormRef.validateField('curMeterReading')
							}
						},
					},
				},
				{
					type: 'el-select',
					label: '抄表员',
					prop: 'meterReadingStaffId',
					options: [],
					attrs: {
						col: 6,
						placeholder: '请选择抄表员',
					},
				},
				{
					hide: true,
					type: 'el-select',
					label: '是否分量计费',
					prop: 'splitFlag',
					options: [
						{ label: '是', value: true },
						{ label: '否', value: false },
					],
					attrs: {
						col: 6,
						placeholder: '请选择是否分量计费',
					},
				},
				{
					type: 'slot',
					slotName: 'footer',
				},
			],
			bFormAttrs: {
				inline: true,
				labelPosition: 'top',
				rules: {
					thisRecordDate: [{ required: true, message: '请选择抄表日期', trigger: 'change' }],
					curMeterReading: [
						{ required: true, message: '请输入本次指针', trigger: 'blur' },
						{
							validator: this.curMeterReadingValidtor,
							trigger: 'blur',
						},
					],
					volume: [
						{
							required: true,
							message: '请输入本次水量',
							trigger: ['blur'],
						},
						{
							pattern: /^[1-9]\d*$/,
							message: '水量不能为零',
							trigger: ['blur'],
						},
					],
					checkStatus: [{ required: true, message: '请选择抄表情况', trigger: 'change' }],
					meterReadingStaffId: [{ required: true, message: '请选择抄表员', trigger: 'change' }],
					splitFlag: [
						{
							required: true,
							message: '请选择是否分量计费',
							trigger: 'change',
						},
					],
				},
			},
			// 分配方式
			distributionMode: '',
			distributionModeList: distributionModeOptions,
			// 列表
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'virtualMeterTypeDesc',
					name: '表卡类型',
					tooltip: true,
				},
				{
					key: 'priceCode',
					name: '价格编号',
					tooltip: true,
				},
				{
					key: 'priceDesc',
					name: '价格',
					tooltip: true,
					width: 200,
				},
				{
					key: 'priceBillItemList',
					name: '污水费',
					tooltip: true,
				},
				{
					key: 'cycSurplusDesc',
					name: '阶梯剩余量',
					tooltip: true,
					width: 180,
				},
				{
					hide: true,
					key: 'subDistributionModeDesc',
					name: '水量分配方式',
					tooltip: true,
				},
				{
					hide: true,
					key: 'usageMeasure',
					name: '分配额',
					tooltip: true,
				},
				{
					key: 'volume',
					name: '水量',
					tooltip: true,
					width: 200,
				},
				{
					key: 'useAmt',
					name: '水费',
					tooltip: true,
				},
				{
					key: 'billItemAmt',
					name: '污水费',
					tooltip: true,
				},
				{
					key: 'receivableAmount',
					name: '应缴金额',
					tooltip: true,
				},
			],
			// 当前水量编辑行原始数据
			currentEditData: null,
			btnLoading: false,
			throttleTimeout: null,
			// 是否进阶梯计算
			isLevelComputed: true,
			disabledLevelSwitch: true,
		}
	},
	computed: {
		// 是虚表 且 是分量计费 条件
		virtualMeterFlag() {
			return this.infoData.isVirtual === 1 && this.bFormData.splitFlag
		},
		// 总水费
		waterAmtTotal() {
			return this.tableData.reduce((accumulator, currentObject) => {
				return accAdd(accumulator, Number(currentObject.useAmt))
			}, 0)
		},
		// 总污水费
		wastewaterAmtTotal() {
			return this.tableData.reduce((accumulator, currentObject) => {
				return accAdd(accumulator, Number(currentObject.billItemAmt))
			}, 0)
		},
		// 总应缴金额
		receivableAmountTotal() {
			return this.tableData.reduce((accumulator, currentObject) => {
				return accAdd(accumulator, Number(currentObject.receivableAmount))
			}, 0)
		},
	},
	watch: {
		infoData: {
			handler() {
				// 展示数据值的设置
				this.meterData.list.forEach((item, index) => {
					let data = ''
					if (index <= 6) {
						data = this.infoData[item.field]
					} else {
						// 从第七个数据项开始，数据从lastRecord对象中获取
						data = (this.infoData?.lastRecord && this.infoData?.lastRecord[item.field]) || ''
					}
					item.value = !isBlank(data) ? data : '--'
				})
				// 将 表指针 值设置到 追收抄表 - 上次抄表字段
				this.bFormData.lastMeterReading = this.infoData.meterReading ?? ''
				// 将 抄表员 值设置到 追收抄表 - 超编员字段
				this.bFormData.meterReadingStaffId = this.infoData.meterReadingStaffId ?? ''

				// 根据isVirtual字段 判断是否为虚表 虚表才展示 追收抄表-是否分量计费
				this.bFormItems[6].hide = this.infoData.isVirtual !== 1

				// 抄表日期默认此时此刻
				this.bFormData.thisRecordDate = this.dayjs().format('YYYY-MM-DD HH:mm:ss')
			},
			deep: true,
		},
		virtualMeterFlag(newVal) {
			// 水量分配方式、分配额列 动态展示
			this.columns[6].hide = !newVal
			this.columns[7].hide = !newVal
		},
	},
	activated() {
		const { archivesIdentity = '' } = this.$route.query
		if (archivesIdentity) {
			this.formData.archivesIdentity = archivesIdentity
			this.$nextTick(() => {
				this.getCardInfo()
			})
		}
	},
	methods: {
		getIsBlank(data) {
			return isBlank(data)
		},

		// 获取抄表员工数据
		async getStaffMapData() {
			try {
				const orgCode = this.infoData ? this.infoData.orgCode : ''
				const res = await queryStaffByType({
					staffType: 0,
					status: 0,
					orgCode,
				})
				if (res) {
					this.bFormItems[5].options = res.map(item => {
						const { staffId, staffName } = item
						return {
							value: staffId,
							label: staffName,
						}
					})
					const isStaffExist = this.bFormItems[5].options.find(
						item => item.value == this.infoData.meterReadingStaffId,
					)
					if (!isStaffExist) {
						this.infoData.meterReadingStaffId = ''
					}
				}
			} catch (error) {
				console.error(error)
				this.formItems[3].options = []
			}
		},
		// 获取表卡信息
		async getCardInfo() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				try {
					// 重置下方表单和列表
					this.$refs.bFormRef.resetFields()
					this.distributionMode = ''
					this.tableData = []
					this.isLevelComputed = true
					this.disabledLevelSwitch = true

					const data = await queryLastMeterReadingRecord(this.formData)
					this.infoData = data

					this.getStaffMapData()
				} catch (error) {
					this.infoData = {
						archivesId: '',
						isVirtual: 0,

						archivesIdentity: '',
						userName: '',
						addressFullName: '',
						bookNo: '',
						meterReadingStaffName: '',
						meterReadingStaffId: '',
						meterReading: '',
						yearMeterCreditQuantity: '',

						lastRecord: {
							thisRecordDate: '',
							curMeterReading: '',
							useAmount: '',
							checkStatusDesc: '',
						},
					}
					console.error(error)
				}
			}
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.infoData = {
				archivesId: '',
				isVirtual: 0,

				archivesIdentity: '',
				userName: '',
				addressFullName: '',
				bookNo: '',
				meterReadingStaffName: '',
				meterReading: '',
				yearMeterCreditQuantity: '',

				lastRecord: {
					thisRecordDate: '',
					curMeterReading: '',
					useAmount: '',
					checkStatusDesc: '',
				},
			}
			this.$refs.bFormRef.resetFields()
			this.distributionMode = ''
			this.tableData = []
			this.isLevelComputed = true
			this.disabledLevelSwitch = true
		},

		// 分配方式
		handleDistributionMode(value) {
			this.distributionMode = value
		},

		// 本次指针字段验证函数
		curMeterReadingValidtor(rule, value, callback) {
			if (!isBlank(this.bFormData.lastMeterReading) && !isBlank(value) && this.bFormData.checkStatus !== 19) {
				if (this.bFormData.lastMeterReading > value) {
					callback(new Error('本次指针不能小于上次指针'))
				}
			}
			callback()
		},

		// 计算
		async handleComputed() {
			const valid = await this.$refs.bFormRef.validate()
			if (valid) {
				this.loading = true
				try {
					const { distributionMode, list = [] } = await meterReadingCollection({
						archivesId: this.infoData.archivesId,
						...this.bFormData,
						useLadder: this.isLevelComputed ? 1 : 0,
					})
					this.distributionMode = distributionMode
					this.tableData = list.map(item => {
						return {
							...item,
							isEditing: false,
						}
					})
					this.disabledLevelSwitch = false
				} catch (error) {
					console.error(error)
					this.distributionMode = ''
					this.tableData = []
				} finally {
					this.loading = false
				}
			}
		},
		async handleSwitch(value) {
			if (this.disabledLevelSwitch) {
				this.isLevelComputed = !value
				this.$message.warning('请先点击计算')
				return
			}
			const valid = await this.$refs.bFormRef.validate()
			if (!valid) {
				this.isLevelComputed = !value
			} else {
				this.handleComputed(value)
			}
		},
		// 水量编辑 开启编辑
		handleOpenEdit(row, index) {
			this.currentEditData = row
			this.$set(this.tableData[index], 'isEditing', true)
		},
		// 水量编辑 取消编辑
		handleCloseEdit(index) {
			this.$set(this.tableData[index], 'volume', this.currentEditData.volume)
			this.$set(this.tableData[index], 'isEditing', false)
			this.currentEditData = null
		},
		//水量编辑 保存编辑
		async handleSaveEdit(row, index) {
			const valid = await this.$refs[`volumeRef${index}`].validate()
			if (valid) {
				try {
					const { archivesId, meterReadingRecordId, volume } = row
					const data = await modifyWaterVolume({
						archivesId,
						meterReadingRecordId,
						thisRecordDate: this.bFormData.thisRecordDate,
						volume,
					})
					this.tableData.splice(index, 1, { ...data, isEditing: false })
					this.currentEditData = null
				} catch (error) {
					console.error(error)
				}
			}
		},
		// 底部 确认追收并生成账单
		handleSubmit() {
			if (this.throttleTimeout) return
			this.throttleTimeout = setTimeout(async () => {
				this.btnLoading = true
				try {
					const formValid = await this.$refs.formRef.validate()
					const bFormValid = await this.$refs.bFormRef.validate()
					if (formValid && bFormValid) {
						if (this.tableData.length === 0) {
							this.$message.warning('请先点击计算')
							return
						}
						const waterTotal = this.tableData.reduce((accumulator, currentObject) => {
							return accAdd(accumulator, Number(currentObject.volume))
						}, 0)
						if (waterTotal !== this.bFormData.volume) {
							this.$message.warning('拆分水量与抄表水量不一致')
							return
						}
						const data = await createMeterReadingCollection({
							archivesId: this.infoData.archivesId,
							lastRecordDate: this.infoData.lastRecordDate,

							// thisRecordDate: "",
							// lastMeterReading: "",
							// curMeterReading: "",
							// volume: "",
							// checkStatus: "",
							...this.bFormData,
							waterList: this.virtualMeterFlag ? this.tableData : [],
							useLadder: this.isLevelComputed ? 1 : 0,
						})
						this.$message.success('生成账单成功')
						this.$router.push({
							path: '/costManage/paymentPage',
							query: {
								ids: data?.length ? data.join(',') : '',
								year: this.dayjs(this.bFormData.thisRecordDate).format('YYYY'),
							},
						})
						this.handleReset()
					}
				} catch (error) {
					console.error(error)
				} finally {
					this.btnLoading = false
					clearTimeout(this.throttleTimeout)
					this.throttleTimeout = null
				}
			}, 1000)
		},
	},
}
</script>

<style lang="scss" scoped>
::v-deep {
	.gc-group-detail {
		width: 100%;
		max-width: auto;
	}
	.el-input-number {
		width: 100%;
		.el-input__inner {
			text-align: left;
		}
	}
	.model-header {
		padding: 0;
	}
	.group-detail-info {
		p {
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}
	}
	.el-form {
		.el-form-item {
			margin-bottom: 8px;
		}
	}
	.el-radio__input.is-disabled {
		& + span.el-radio__label {
			color: #606266;
		}
		&.is-checked .el-radio__inner {
			background-color: #409eff;
			border-color: #409eff;
			&::after {
				background-color: #fff;
			}
		}
	}
}
.wrapper {
	display: flex;
	flex-direction: column;
	flex-wrap: wrap;
	height: 100%;
}
.content {
	width: 100%;
	height: 0;
	flex: 1;
	overflow: auto;
	display: flex;
	flex-direction: column;
	.content-top {
		padding: 20px 20px 0 20px;
		background-color: #fff;
		margin-bottom: 14px;
		::v-deep {
			.el-form {
				border-bottom: 1px dashed #eef0f3;
			}
		}
	}
	.content-bottom {
		flex: 1;
		display: flex;
		flex-direction: column;
		padding: 0 20px 10px 20px;
		background-color: #fff;
		overflow: auto;
		min-height: 200px;
		h5 {
			margin-bottom: 12px;
			font-weight: bold;
		}
		.total-level {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 12px;
		}
		.total {
			display: flex;
			align-items: center;
			span {
				margin-right: 8px;
			}
			div {
				margin-right: 8px;
				padding: 4px;
				background-color: #f2f6ff;
				color: #ec6b60;
			}
		}
		.level-switch span {
			margin-left: 8px;
		}
		.table-container {
			flex: 1;
			height: 0;
			flex-shrink: 0;
			min-height: 200px;
		}
	}
}
.button-group {
	width: 100%;
	height: 40px;
	.btn-create,
	.btn-preview {
		margin-top: 20px;
		border-radius: 4px;
		height: 32px;
	}
	.btn-create {
		width: 216px;
	}
	.btn-preview {
		width: 110px;
		border: 1px solid #2f87fe;
		color: #2f87fe;
	}
}

.mode-box {
	display: flex;
	align-items: baseline;
	margin-bottom: 12px;
}
.volume-box {
	display: flex;
	align-items: center;
	.flex1 {
		flex: 1;
	}
	.icon-btn {
		margin-left: 4px;
		padding: 4px;
		cursor: pointer;
	}
}
.table-form {
	height: 100%;
	::v-deep {
		.el-form-item {
			margin: 12px 0;
		}
		.el-form-item__error {
			padding-top: 0;
		}
	}
}
</style>
