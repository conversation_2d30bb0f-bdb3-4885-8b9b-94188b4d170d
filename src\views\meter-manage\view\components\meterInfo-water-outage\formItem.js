export const getFormItems = function () {
	return [
		{
			type: 'el-input',
			label: '表卡编号',
			prop: 'archivesIdentity',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '水表编号',
			prop: 'meterNo',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-date-picker',
			label: '停水时间',
			prop: 'disableDate',
		},
		{
			type: 'el-input',
			label: '停水操作人',
			prop: 'disable<PERSON>erson',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '停水原因',
			prop: 'disableReason',
			attrs: {
				type: 'textarea',
				maxlength: '64',
				showWordLimit: true,
				autosize: {
					minRows: 2,
					maxRows: 3,
				},
			},
		},
	]
}
