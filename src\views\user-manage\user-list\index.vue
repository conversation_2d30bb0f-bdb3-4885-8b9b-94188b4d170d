<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems"></GcFormSimple>
			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleChangePage({ page: 1 })">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handleChangePage"
				@dblclick="handleDbClick"
			/>
		</div>
	</div>
</template>

<script>
import { getfilterName } from '@/utils'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiGetUserList } from '@/api/userManage.api'
export default {
	data() {
		return {
			formData: {
				userType: '3',
				userSubType: '',
				userName: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '用户分类',
					prop: 'userType',
					options:
						this.$store.getters?.dataList?.userType?.map(item => {
							return {
								label: item.sortName,
								value: item.sortValue,
							}
						}) || [],
					events: {
						change: this.handleUserTypeChange,
					},
				},
				{
					type: 'el-select',
					label: '用户类型',
					prop: 'userSubType',
					options: [],
				},
				{
					type: 'el-input',
					label: '用户名称',
					prop: 'userName',
				},
			],
			columns: [
				{
					key: 'userName',
					name: '用户名称',
					tooltip: true,
				},
				{
					key: 'userSubType',
					name: '用户类型',
					tooltip: true,
					render: (h, row) => {
						const userSubTypeList =
							row.userType == 3
								? this.$store.getters.dataList.resident
								: this.$store.getters.dataList.business

						const value = userSubTypeList
							? getfilterName(userSubTypeList, row.userSubType, 'sortValue', 'sortName')
							: '--'
						return h('span', {}, value)
					},
				},
				{
					key: 'contactPeople',
					name: '联系人',
					tooltip: true,
				},
				{
					key: 'userMobile',
					name: '手机',
					tooltip: true,
				},
				{
					key: 'totalArchivesCount',
					name: '表卡数量',
					tooltip: true,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
		}
	},
	created() {
		this.handleChangePage({ page: 1 })
		this.handleUserTypeChange(this.formData.userType)
	},
	methods: {
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
				})
				const { records, total } = await apiGetUserList(formParams)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleUserTypeChange(v) {
			if (v) {
				const userSubTypeList =
					this.formData.userType == 3
						? this.$store.getters.dataList.resident.map(item => {
								return {
									label: item.sortName,
									value: item.sortValue,
								}
						  })
						: this.$store.getters.dataList.business.map(item => {
								return {
									label: item.sortName,
									value: item.sortValue,
								}
						  })

				this.formItems[1].options = userSubTypeList
			} else {
				this.formItems[1].options = []
			}
			this.formData.userSubType = ''
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.handleChangePage({ page: 1 })
			this.handleUserTypeChange(this.formData.userType)
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleDbClick(obj) {
			if (!this.$has('cpm_user_getUserDatailsById')) {
				this.$notify({
					message: '暂无权限访问，请联系管理员！',
					title: '提示',
					offset: 60,
					type: 'warning',
				})
				return
			}
			const userId = obj.row?.userId
			this.$router.push({
				path: '/userManage/userView',
				query: {
					userId,
				},
			})
		},
	},
}
</script>
