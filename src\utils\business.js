import { isBlank } from './validate'

/**
 * 过滤价格列表
 * @param {Array} data 价格列表原始数据
 * @param {Number} userType (用户类型)：1-工商 2-民用 3-居民 4-非居民 5-特种
 * @param {Number} meterCategory (表类别):0-普表 1-IC卡 2-远传表 3-IC卡远传表
 * @param {Number} icBillingMode (IC卡表结算模式):0-气量 1-金额
 * @param {Number} meterTypeId 表类型id
 * @param {Number} billingType (计费方式):0-表端计费 1-系统结算 2-不结算
 * @param {String} protocolType 协议类型
 * @returns Array
 */
export function filterPrice(data, ...args) {
	/**
	 * data相关字段
	 * billingTypeId 计费类型，1-单一计费、2-阶梯计费、3-分时计费(fcc-x双通道专用)
	 * adjustLadder 阶梯是否按月均摊处理，1-是，0-否
	 * ladderPopulation 人口阶梯
	 * ladderLevel 阶梯级数（最多5级）
	 */
	const { userType, protocolType, meterCategory, icBillingMode, meterTypeId, billingType } = args[0]
	let priceList = []
	if ([1, 4, 5].includes(+userType)) {
		// 工商户不能选择人口阶梯价格
		priceList = data.filter(item => {
			return item.ladderPopulation !== 1
		})
	} else {
		priceList = data
	}
	//表端计费的表具不支持按月均摊/人口阶梯的阶梯价
	const result = priceList.filter(item => {
		// FCC非阶梯只能选择单一价
		if (protocolType == '6012002' || meterTypeId == 27 || meterTypeId == 28 || meterTypeId == 68) {
			return item.billingTypeId == 1 //单一价格
		}
		// flag为true表明不支持按月均摊/人口阶梯
		let flag = item.adjustLadder != 1 && item.ladderPopulation != 1
		// 天信FCC-X的表具，可选择分时计价+单一计价+阶梯计价（大于3阶(非二阶)+不是按月均摊/人口阶梯）
		if (protocolType == '6012014' || protocolType == '6012016') {
			return flag && item.ladderLevel <= 3 && item.ladderLevel != 2
		}
		// IC卡气量表时不能选择按月均摊/人口阶梯的阶梯价和分时计价
		if (icBillingMode == 0 && (meterCategory == 1 || meterCategory == 3)) {
			return flag && item.billingTypeId != 3
		}
		if (!isBlank(billingType) && (billingType == 0 || billingType == 2)) {
			return flag && item.billingTypeId != 3
		}
		if (billingType == 1) {
			return item.billingTypeId != 3
		}
		return item
	})
	return result
}

/**
 * 根据协议类型判断是否为双通道表
 * @param {String} protocolType 协议类型
 * @returns {Boolean}
 */
export const isDoubleWayCard = protocolType => protocolType == '6012014' || protocolType == '6012016'

/**
 * 根据协议类型判断是否需要提示“若当前档案“设置过流切断功能参数”指令已生成，需手动取消重新下发！”
 * 6011001，6011004，6011006，6011007，(暂不实现-存量10表) 金卡电子温补、金卡NBIOT10、金卡热式R2、金卡热式R1、金卡NB超声10、金卡物联网10、建安NBIOT10、
 * 6011013-金卡NB膜式本安Ⅰ-D10 6011005-金卡NB超声II-S
 * @param {String} protocolType 协议类型
 * @returns {Boolean}
 */
export const isIncludeOvercurrent = protocolType => protocolType == '6011013' || protocolType == '6011005'

/**
 * 根据协议类型判断表具流量是否必填
 * 6011013-金卡NB膜式本安Ⅰ-D10 6011005-金卡NB超声II-S
 * @param {String} protocolType 协议类型
 * @returns {Boolean}
 */
export const isMeterFlowRequired = protocolType => protocolType == '6011013' || protocolType == '6011005'

/**
 * 根据指令ID判断是否需要出现关阀提示语
 * 4-设置几天不用气关阀
 * 5-设置几天无数据上传关阀下发
 * 110-设置几天无数据上传关阀下发
 * 111-设置几天不用气关阀
 * @param {String} id 指令id
 * @returns {Boolean}
 */
export const cmdValveInfo = id => [4, 5, 110, 111].includes(id)
