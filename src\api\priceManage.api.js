import service from './request'
import { CPM } from '@/consts/moduleNames'

// 价格列表获取接口联调
export function apiPriceList(data = { size: 1000 }) {
	return service({
		url: `${CPM}/prices/list`,
		method: 'post',
		data,
	})
}

// 查询价格详细信息
export function apiPriceInfo(params) {
	return service({
		url: `${CPM}/prices/info`,
		method: 'get',
		params,
	})
}

// 获取产品版本明细列表
export function apiVersionInfo(data) {
	return service({
		url: `${CPM}/prices/list-by-id`,
		method: 'post',
		data,
	})
}

// 新增价格
export function apiAdditionPrice(data) {
	return service({
		url: `${CPM}/prices`,
		method: 'post',
		data,
	})
}

// 调整价格
export function apiAdjustPrice(data) {
	return service({
		url: `${CPM}/prices/adjust`,
		method: 'post',
		data,
	})
}

// 删除价格
export function apiDeletePrice(params) {
	return service({
		url: `${CPM}/prices`,
		method: 'delete',
		params,
	})
}

// 编辑价格
export function apiEditPrice(data) {
	return service({
		url: `${CPM}/prices`,
		method: 'put',
		data,
	})
}

// 禁用价格
export function apiDisabledPrice(priceId) {
	return service({
		url: `${CPM}/prices/disable`,
		method: 'put',
		data: {
			priceId,
		},
	})
}

// 启用价格
export function apiEnablePrice(priceId) {
	return service({
		url: `${CPM}/prices/enable`,
		method: 'put',
		data: {
			priceId,
		},
	})
}
