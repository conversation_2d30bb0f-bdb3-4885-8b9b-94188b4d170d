/* 纯白主题 */
body.gc-theme-white {
  $base-menu-background: #fff;
  $base-color-blue: #1890ff;
  $base-color-blue-light: mix($base-color-white, $base-color-blue, 90%);

  @mixin container {
    color: #515a6e !important;
    background: $base-menu-background !important;
  }

  @mixin container-column {
    color: #515a6e !important;
    background: #f7faff !important;
  }

  @mixin active {
    &:hover {
      color: $base-color-blue !important;
      background-color: $base-color-blue-light !important;
    }

    &.is-active {
      color: $base-color-blue !important;
      background-color: $base-color-blue-light !important;
    }
  }

  .logo-container-vertical,
  .logo-container-horizontal {
    @include container;

    .title,
    .gc-icon {
      @include container;
    }
  }

  .logo-container-column {
    @include container;

    .title {
      @include container;
    }

    .logo,
    .gc-icon {
      @include container-column;
    }
  }

  .column-bar-container {
    .el-tabs {
      @include container-column;

      .el-tabs__nav-wrap.is-left {
        background: #f7faff !important;
      }

      .el-tabs__item,
      .el-tabs__nav {
        @include container-column;
      }

      .el-tabs__item.is-active {
        color: $base-color-white !important;
        background: $base-color-blue !important;
      }
    }

    .el-menu {
      .el-menu-item.is-active,
      .el-submenu__title.is-active,
      .el-menu-item:hover,
      .el-submenu__title:hover {
        i {
          color: $base-color-blue !important;
        }

        color: $base-color-blue !important;
        background-color: $base-color-blue-light !important;
      }
    }

    &-card {
      .el-tabs {
        .el-tabs__item {
          &.is-active {
            background: transparent !important;
          }
        }
      }
    }
  }

  .gc-layout-column,
  .gc-layout-vertical,
  .gc-layout-horizontal {
    .el-menu {
      @include container;

      .el-submenu__title {
        @include container;
      }

      .el-menu-item {
        @include container;
      }
    }

    .gc-side-bar {
      @include container;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08) !important;

      .el-menu-item {
        @include active;
      }
    }
  }

  .gc-header {
    @include container;

    .right-panel {
      .user-name,
      .user-name *,
      > i,
      > div > i,
      > span > i,
      > div > span > i,
      > svg,
      > div > svg,
      > span > svg,
      > div > span > svg {
        @include container;
      }
    }

    .gc-main {
      @include container;

      .ri-notification-line {
        color: #515a6e !important;
      }

      .el-menu {
        &--horizontal {
          .el-menu-item {
            &.is-active {
              @include active;
            }
          }

          .el-submenu,
          .el-menu-item {
            @include active;
          }
        }
      }
    }
  }
}
