<template>
	<div class="price-range">
		<el-select v-model="price_name" filterable placeholder="全部" size="small" class="priceList in1">
			<el-option
				v-for="(item, index) in price_nameList"
				:key="item.priceId"
				:label="item.priceName"
				:value="item.priceId"
			></el-option>
		</el-select>
	</div>
</template>

<script>
import { apiAllPrices } from '@/api/archives.api.js'
import identity from '@/mixin/identity.js'
export default {
	name: '',
	mixins: [identity],
	components: {},
	props: {
		value: {
			type: [Number, String],
		},
		orgCode: {
			type: String,
		},
	},
	data() {
		return {
			price_nameList: [], // 价格名称列表
		}
	},
	computed: {
		price_name: {
			get: function () {
				return this.value
			},
			set: function (val) {
				this.$emit('update:value', val)
			},
		},
	},
	watch: {
		orgCode(n) {
			this.price_name = ''
			this.getList({ orgCode: n })
		},
	},
	created() {
		this.getList()
	},
	mounted() {},
	methods: {
		getList(obj) {
			let params = {}
			if (this.userLevel == 0) {
				params['tenantId'] = this.tenantId
			}
			obj && obj.orgCode && (params['orgCode'] = obj.orgCode)

			apiAllPrices(params).then(res => {
				this.price_nameList = [
					{
						priceName: '全部',
						priceId: null,
					},
				].concat(res.records)
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.price-range {
	.el-select {
		width: 100%;
	}
}
</style>
