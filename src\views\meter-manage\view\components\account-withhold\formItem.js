export const getFormItems = function (_this) {
	return [
		{
			type: 'el-input',
			label: '表卡编号',
			prop: 'meterNo',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-select',
			label: '银行名称',
			prop: 'bankCode',
			options: _this.bankOptions,
		},
		{
			type: 'el-input',
			label: '银行账号',
			prop: 'accountNo',
		},
		{
			type: 'el-input',
			label: '银行地址',
			prop: 'accountAddr',
		},
		{
			type: 'el-input',
			label: '余额',
			prop: 'accountBalanceAmount',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '说明',
			prop: 'remark',
		},
		{
			type: 'el-radio',
			label: '是否代扣',
			prop: 'isWithhold',
			options: [
				{ label: '是', value: true },
				{ label: '否', value: false },
			],
		},
	]
}
