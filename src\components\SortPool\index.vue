<template>
	<div class="sort-pool">
		<template v-if="checkable">
			<div class="sort-check-action">
				<el-checkbox
					v-model="checkedAll"
					:disabled="!innerSource.length"
					:indeterminate="uncheckedItems.length > 0 && checkedItems.length > 0"
					border
					size="normal"
					@change="handleCheckedAllChange"
				>
					全选
				</el-checkbox>
				<el-button :disabled="!checkedItems.length || !uncheckedItems.length" @click="handleCheckedReverse">
					反选
				</el-button>
			</div>
			<transition-group name="list" tag="div" class="item-list">
				<div
					v-for="(item, index) in innerSource"
					:key="item.key"
					:data-index="index"
					draggable="true"
					@dragstart="onDragStart(index)"
					@dragover.prevent
					@drop="onDrop(index)"
					class="item"
				>
					<el-checkbox v-model="item.checked" border size="normal" @change="handleChange(item, index)">
						{{ item.name }}
						<div v-if="item.checked" class="drag-handle" @mousedown.stop @click.stop>
							<i class="el-icon-rank"></i>
						</div>
					</el-checkbox>
				</div>
			</transition-group>
		</template>
		<template v-else>
			<transition-group name="list" tag="div" class="item-list">
				<div
					v-for="(item, index) in innerSource"
					:key="item.key"
					:data-index="index"
					draggable="true"
					@dragstart="onDragStart(index)"
					@dragover.prevent
					@drop="onDrop(index)"
					class="item"
				>
					<el-button type="primary">
						<div class="drag-handle"><i class="el-icon-rank"></i></div>
						{{ item.name }}
					</el-button>
				</div>
			</transition-group>
		</template>
	</div>
</template>
<script>
export default {
	name: 'sort-pool',
	props: {
		source: {
			type: Array,
			default: () => [],
		},
		checkable: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			inputData: null,
			innerSource: [],
			draggedIndex: null,
			checkedAll: true,
			isIndeterminate: false,
		}
	},
	computed: {
		checkedItems() {
			return this.innerSource.filter(item => item.checked)
		},
		uncheckedItems() {
			return this.innerSource.filter(item => !item.checked)
		},
	},
	watch: {
		source: {
			deep: true,
			handler(val) {
				this.inputData = null
				this.innerSource = JSON.parse(JSON.stringify(val))
				this.calcCheckAllState()
			},
			immediate: true,
		},
		innerSource: {
			deep: true,
			immediate: true,
			handler(val) {
				if (!this.inputData) {
					return
				}
				this.$emit('change', this.getData())
			},
		},
	},
	methods: {
		onDragStart(index) {
			this.draggedIndex = index // 记录拖拽的索引
		},
		onDrop(dropIndex) {
			if (this.draggedIndex !== null && this.draggedIndex !== dropIndex) {
				const draggedItem = this.innerSource.splice(this.draggedIndex, 1)[0] // 移除拖拽的元素
				this.innerSource.splice(dropIndex, 0, draggedItem) // 插入到目标位置
				this.draggedIndex = null // 重置拖拽索引
			}
		},
		calcCheckAllState() {
			if (this.checkable) {
				this.checkedAll = this.uncheckedItems.length === 0
			}
		},
		handleChange(item) {
			this.calcCheckAllState()
			this.innerSource = [...this.checkedItems, ...this.uncheckedItems]
		},
		handleCheckedAllChange(val) {
			this.checkedAll = val
			for (const item of val ? this.uncheckedItems : this.checkedItems) {
				item.checked = val
			}
		},
		handleCheckedReverse() {
			for (const item of this.innerSource) {
				item.checked = !item.checked
			}
			this.innerSource = [...this.checkedItems, ...this.uncheckedItems]
		},
		setData(data) {
			this.inputData = data
			if (!data.length) return
			data = JSON.parse(JSON.stringify(data))
			const currentSource = JSON.parse(JSON.stringify(this.innerSource))
			const setResultSource = []
			if (this.checkable) {
				for (const item of currentSource) {
					item.checked = false
				}
			}
			for (const item of data) {
				const index = currentSource.findIndex(i => i.key === item.key)
				if (index !== -1) {
					const item = currentSource.splice(index, 1)[0]
					if (this.checkable) item.checked = true
					setResultSource.push(item)
				}
			}
			this.innerSource = [...setResultSource, ...currentSource]
			this.calcCheckAllState()
		},
		getData() {
			const current = this.checkable ? [...this.checkedItems] : [...this.innerSource]
			return current.map((item, index) => {
				const { key, name, tooltip } = item
				return {
					key,
					name,
					sort: index,
					original: item,
				}
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.sort-pool {
	.item-list {
		display: flex;
		flex-wrap: wrap;
		gap: 10px;
		.el-checkbox {
			margin-left: 0;
		}
	}
	/* 定义过渡动画 */
	.list-move {
		transition: transform 0.3s ease;
	}

	.list-enter-active,
	.list-leave-active {
		transition: all 0.3s ease;
	}

	.list-enter,
	.list-leave-to {
		opacity: 0;
		transform: translateY(30px);
	}

	display: flex;
	flex-wrap: wrap;
	gap: 10px;
	::v-deep {
		.el-checkbox {
			display: flex;
			align-items: center;
			margin: 0;
			padding: 4px 15px 4px 15px;
			height: 30px;
			&.is-checked {
				background-color: #2f87fe;
				.el-checkbox__inner {
					border-color: white;
					background-color: white;
					&::after {
						border: 2px solid #2f87fe;
						border-left: 0;
						border-top: 0;
					}
				}
				.el-checkbox__label {
					color: #fff;
					display: flex;
					gap: 10px;
					.drag-handle {
						font-size: 17px;
						cursor: move;
					}
				}
			}
		}
		.el-button {
			display: block;
			padding: 0 15px;
			margin: 0;
			& > span {
				display: flex;
				gap: 10px;
				align-items: center;
			}
			.drag-handle {
				font-size: 17px;
				cursor: move;
				display: inline;
			}
		}
	}
	.sort-check-action {
		display: flex;
		gap: 10px;
		.el-checkbox {
			height: 32px;
		}
	}
}
</style>
