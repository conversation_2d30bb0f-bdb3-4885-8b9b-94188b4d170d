<template>
	<el-autocomplete
		class="detail-address"
		ref="addressSearchInput"
		v-model="searchValue"
		:fetch-suggestions="queryDetailAddressList"
		:select-when-unmatched="true"
		placement="bottom-start"
		:popper-append-to-body="false"
		:trigger-on-focus="checkFocus"
		:suggestions-auto-close="false"
		placeholder="请输入详细地址"
		:disabled="isDisabled"
		@select="handleSelectDetailAddress"
		style="width: 100%"
	>
		<template slot-scope="{ item }">
			<div class="detail-item">
				<div class="left">
					<div class="address">{{ item.addressName }}</div>
					<div class="user-info">
						<el-row :gutter="10">
							<el-col :span="12">用户名：{{ item.userName || '--' }}</el-col>
							<el-col :span="12">手机号：{{ item.userMobile || '--' }}</el-col>
							<el-col :span="24">表具编号：{{ item.meterNo || '--' }}</el-col>
						</el-row>
					</div>
				</div>
				<div class="right">
					<gc-button btn-type="two">{{ btnText }}</gc-button>
				</div>
			</div>
		</template>
	</el-autocomplete>
</template>

<script>
import { apiGetAddressBydb } from '@/api/meterManage.api.js'
export default {
	name: 'AddressSearch',
	props: {
		isDisabled: {
			type: Boolean,
			default: false,
		},
		regionCode: {
			type: String,
			default: '',
		},
		addressAreaCode: {
			type: String,
			default: '',
		},
		searchName: {
			type: String,
			default: '',
		},
	},
	computed: {
		searchValue: {
			get: function () {
				return this.searchName
			},
			set: function (val) {
				this.$emit('update:searchName', val)
			},
		},
		btnText() {
			return '使用此地址'
		},
	},
	watch: {
		isDisabled(val) {
			if (val) {
				this.searchValue = ''
			}
		},
	},
	data() {
		return {
			checkFocus: false,
		}
	},
	methods: {
		/**
		 * 详细地址模糊查询
		 * queryString 查询条件
		 * cb 回调数据
		 */
		queryDetailAddressList(queryString, cb) {
			if (!queryString) {
				this.checkFocus = false
				cb([])
				return
			}
			this.checkFocus = true
			const searchValue = this.searchValue.trim()
			apiGetAddressBydb({
				regionCode: this.regionCode, // 区/县
				addressName: searchValue, // 详细地址
				addressAreaCode: this.addressAreaCode, // 街道/小区/乡镇/村庄
			})
				.then(res => {
					cb(res || [])
				})
				.catch(() => {
					cb([])
				})
		},
		// 选择详细地址
		handleSelectDetailAddress(item) {
			// eslint-disable-next-line no-prototype-builtins
			if (item.hasOwnProperty('value')) return // 回车操作
			this.searchValue = item.addressName
			this.$emit('use', item)
		},
	},
}
</script>
<style lang="scss" scoped>
.detail-item {
	display: flex;
	align-items: center;

	.left {
		flex: 1;
		max-width: calc(100% - 120px);
		.address {
			@include text-overflow;
			color: rgba(78, 78, 78, 1);
			font-weight: 500;
		}
		.user-info {
			color: rgba(153, 153, 153, 1);
			.el-col-12,
			.el-col-24 {
				@include text-overflow;
			}
			.el-col-12 {
				min-width: 160px;
			}
		}
	}

	.right {
		width: 100px;
		margin-left: 15px;
		.gc-button-two {
			padding: 0;
			width: 100%;
			height: 28px;
			line-height: 28px;
		}
	}
}
</style>
