export const getFormItems = function (_this) {
	return [
		{
			type: 'el-input',
			label: '表卡编号',
			prop: 'archivesIdentity',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-date-picker',
			label: '注销表卡时间',
			prop: 'closeDate',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '注销表卡操作人员',
			prop: 'closePerson',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '注销表卡原因',
			prop: 'closeReason',
			attrs: {
				type: 'textarea',
				maxlength: '64',
				showWordLimit: true,
				autosize: {
					minRows: 2,
					maxRows: 3,
				},
			},
		},
		{
			type: 'el-select',
			label: '水表操作类型',
			prop: 'meterOperate',
			options: (_this.$store.getters.dataList.operatorMeterType || [])
				.filter(item => ['3', '4'].includes(item.sortValue))
				.map(item => {
					return {
						label: item.sortName,
						value: item.sortValue,
					}
				})
				.concat([{ label: '无操作', value: 'wucaozuo' }]),
		},
	]
}
