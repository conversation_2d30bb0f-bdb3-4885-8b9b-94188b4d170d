<template>
	<div class="device-type-wrapper">
		<div class="device-sort" v-if="source === 'alarm'">
			<el-select v-model="deviceSort" filterable placeholder="请选择" @change="handleDeviceTypeChange">
				<el-option
					v-for="item in deviceSortOptions"
					:key="item.sortValue"
					:label="item.sortName"
					:value="item.sortValue"
				></el-option>
				<span slot="prefix" class="fixed-text">设备类型：</span>
			</el-select>
		</div>
		<div class="split"></div>
		<div class="device-type-list">
			<template v-if="deviceSort === '0'">
				<div
					class="device-type-item"
					:class="{ active: deviceTypeId === item.meterTypeId }"
					v-for="item in deviceTypeList"
					:key="item.meterTypeId"
					@click="check(item, 'meterTypeId')"
				>
					{{ item.meterTypeName }}
				</div>
			</template>
			<template v-else>
				<div
					class="device-type-item"
					:class="{ active: deviceTypeId === item.dtuTypeId }"
					v-for="item in deviceTypeList"
					:key="item.dtuTypeId"
					@click="check(item, 'dtuTypeId')"
				>
					{{ item.dtuTypeName }}
				</div>
			</template>
		</div>
	</div>
</template>

<script>
import { apiGetDeviceTypeList } from '@/api/meterMonitor.api'
export default {
	name: 'DeviceType',
	components: {},
	props: {
		source: {
			// 页面来源：alarm-报警参数配置（可选择设备类型），directive-开户指令集配置（设备类型仅meter）
			type: String,
			default: 'alarm',
		},
	},
	data() {
		return {
			deviceSort: '0', // 设备分类：1-dtu，0-meter
			deviceTypeList: [],
			deviceTypeId: '',
		}
	},
	computed: {
		dataList() {
			return this.$store.getters.dataList
		},
		deviceSortOptions() {
			return this.dataList.deviceCategory
		},
	},
	watch: {},
	created() {},
	mounted() {
		this.getDeviceTypeList(this.deviceSort)
	},
	methods: {
		check(item, key) {
			this.deviceTypeId = item[key]
			this.$emit('on-device-choose', item)
		},
		handleDeviceTypeChange(val) {
			this.deviceSort = val
			this.getDeviceTypeList(val)
		},
		// 获取设备类型列表
		getDeviceTypeList(deviceSort) {
			apiGetDeviceTypeList(deviceSort, {
				tenantId: this.$store.getters?.tenant?.id,
			}).then(res => {
				this.deviceTypeList = res
				this.check(res[0], `${deviceSort == 0 ? 'meter' : 'dtu'}TypeId`)
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.device-type-wrapper {
	display: flex;
	flex-direction: column;
	background: white;
	padding-top: 20px;
	.device-sort {
		margin: 0 20px 0 16px;
		padding-bottom: 20px;
		position: relative;
		box-sizing: border-box;
		border-bottom: 0.5px dashed #cccccc;
		::v-deep .el-select {
			.el-input__inner {
				width: 344px;
				height: 40px;
				line-height: 40px;
				color: #2f87fe;
				padding: 0 15px 0 80px;
			}
			.el-input__icon {
				line-height: 40px;
			}
		}
		.fixed-text {
			line-height: 40px;
			padding-left: 10px;
			color: #2f87fe;
		}
	}
	.device-type-list {
		margin: 10px 20px 0;
		padding-bottom: 10px;
		flex: 1;
		overflow-y: scroll;
		.device-type-item {
			margin-top: 10px;
			padding-left: 20px;
			height: 40px;
			line-height: 40px;
			background: #f2f2f2;
			border-radius: 4px;
			box-sizing: border-box;
			font-size: 14px;
			color: #666666;
			&.active {
				background: #e6f0ff;
				color: #2f87fe;
			}
		}
	}
}
</style>
