<template>
	<GcElDialog
		:show="isShow"
		:title="titleName"
		:okText="titleName"
		@close="handleClose"
		@ok="handleSave"
		@cancel="handleClose"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs[tab]">
			<template v-slot:file>
				<GcButton btn-type="two" @click.native="$refs.fileInput.click()">选择 txt 文件</GcButton>
				<input ref="fileInput" type="file" accept=".txt" @change="handleFileChange" style="display: none" />
				<span v-if="returnFileName" class="file-name">已选择文件: {{ returnFileName }}</span>
			</template>
		</GcFormSimple>
	</GcElDialog>
</template>

<script>
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { importBankReturnFile, createBankSendFile } from '@/api/costManage.api'
export default {
	name: 'FileDialog',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		tab: {
			require: true,
			type: String,
		},
		rowData: {
			type: Object,
			default: () => ({}),
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		titleName() {
			if (this.tab === 'create') return '生成送盘文件'
			if (this.tab === 'import') return '导入回盘文件'
			return ''
		},
	},
	watch: {
		tab: {
			handler(val) {
				{
					if (val === 'import') {
						this.formItems = [
							{
								type: 'slot',
								slotName: 'file',
								prop: 'file',
								label: '回盘文件选择',
							},
						]
					} else if (val === 'create') {
						this.formItems = [
							{
								type: 'el-select',
								label: '营业分公司',
								prop: 'orgCode',
								options: this.$store.getters.orgList,
							},
							{
								type: 'el-select',
								label: '托收渠道',
								prop: 'channel',
								options: [
									{
										label: '人民银行',
										value: 1,
									},
									{
										label: '工商银行',
										value: 2,
									},
								],
							},
							{
								type: 'el-date-picker',
								label: '账期',
								prop: 'billMonth',
								attrs: {
									valueFormat: 'yyyy-MM',
									type: 'monthrange',
									startPlaceholder: '开始日期',
									endPlaceholder: '结束日期',
								},
							},
							{
								type: 'el-input',
								label: '册本编号',
								prop: 'bookNo',
							},
							{
								type: 'el-input',
								label: '托收协议号',
								prop: 'collectionAgreementNumber',
							},
							{
								type: 'el-input',
								label: '企业编号',
								prop: 'enterpriseNumber',
							},
						]
					}
					this.formItems.map(item => {
						let value = ''
						if (item.prop === 'file') {
							value = []
						}
						this.$set(this.formData, item.prop, value)
					})
				}
			},
			immediate: true,
		},
	},
	data() {
		return {
			formData: {},
			formItems: [],
			formAttrs: {
				create: {
					labelPosition: 'top',
					rules: {
						orgCode: [ruleRequired('必填')],
						channel: [ruleRequired('必填')],
						billMonth: [ruleRequired('必填')],
					},
				},
				import: {
					labelPosition: 'top',
					rules: {
						file: [ruleRequired('必填')],
					},
				},
			},
			returnFileName: null,
		}
	},
	methods: {
		handleFileChange(e) {
			const file = e.target.files[0]
			if (!file) {
				this.returnFileName = null
				return (this.formData.file = '')
			}
			this.formData.file = file
			this.returnFileName = file.name
		},
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const formParams = trimParams(removeNullParams(this.formData))
			if (this.tab === 'create') {
				const [billMonthBegin, billMonthEnd] = formParams.billMonth
				delete formParams.billMonth
				formParams.billMonthBegin = billMonthBegin
				formParams.billMonthEnd = billMonthEnd
				this.$confirm(
					'生成送盘文件后, 相关账单将被锁定，无法进行减免，调整以及部分缴费，请确认账单是否调整完毕',
					'确认对该查询条件生成送盘文件吗?',
				).then(() => {
					createBankSendFile(formParams)
						.then(() => {
							this.$message.success('生成送盘文件成功')
							this.$parent.getList()
							this.handleClose()
						})
						.catch(err => {
							this.$message.error(err.message || '生成送盘文件失败')
						})
				})
			} else if (this.tab === 'import') {
				formParams.sendFileId = this.rowData.sendFileId
				const formData = new FormData()
				formData.append('file', this.formData.file)
				formData.append('sendFileId', formParams.sendFileId)
				importBankReturnFile(formData)
					.then(() => {
						this.$message.success('导入回盘文件成功')
						this.$parent.getList()
						this.handleClose()
					})
					.catch(err => {
						this.$message.error(err.message || '导入回盘文件失败')
					})
			}
		},
		handleClose() {
			if (this.tab === 'import') {
				this.$refs.fileInput.value = ''
				this.returnFileName = null
			}
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
	},
}
</script>

<style lang="scss" scoped>
.file-name {
	padding-left: 15px;
	font-size: 14px;
	color: #666;
	line-height: 32px;
}
</style>
