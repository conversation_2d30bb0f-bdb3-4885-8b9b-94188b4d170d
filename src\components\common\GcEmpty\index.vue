<template>
	<div class="gc-blank-page">
		<img class="no-data-img" src="@/assets/images/pic/no-data.png" />
		<p class="text">{{ text }}</p>
		<div class="blank-page-btn" :class="{ disabled }" v-if="showBtn" @click="clickBtn">
			{{ btnText }}
		</div>
	</div>
</template>

<script>
export default {
	name: 'GcEmpty',
	components: {},
	props: {
		showBtn: {
			type: Boolean,
			default: false,
		},
		disabled: {
			type: Boolean,
			default: false,
		},
		btnText: String,
		text: {
			type: String,
			default: '暂无数据',
		},
	},
	data() {
		return {}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		clickBtn() {
			this.$emit('clickBtn')
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-blank-page {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	z-index: 1;
	color: #999;
	.no-data-img {
		width: 155px;
		height: 139px;
	}
	.text {
		margin-top: 20px;
	}
	.blank-page-btn {
		padding: 9px 16px;
		margin-top: 30px;
		border: 1px solid #2f87fe;
		border-radius: 4px;
		font-size: 14px;
		color: #2f87fe;
		&:hover {
			background-color: #d6e5f8;
			cursor: pointer;
		}
	}
	.disabled {
		border: 1px solid #accfff;
		color: #accfff;
		&:hover {
			cursor: not-allowed;
			background-color: #fff;
		}
	}
}
</style>
