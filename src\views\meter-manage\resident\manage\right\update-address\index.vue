<template>
	<GcElDialog
		:show="isShow"
		title="修改地址信息"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
	</GcElDialog>
</template>

<script>
import { ruleMaxLength } from '@/utils/rules.js'
import { getFormItems } from './form.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiBatchUpdateAddress } from '@/api/meterManage.api.js'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		data: {
			type: Array,
			default: () => [],
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				tapWaterNo: '',
				houseYear: '',
				floorNum: '',
				pressureZone: '',
				gisCode: '',
				pipeNetworkCode: '',
			},
			formItems: getFormItems(this),
			formAttrs: {
				rules: {
					tapWaterNo: [ruleMaxLength(16)],
					pipeNetworkCode: [ruleMaxLength(32)],
					gisCode: [ruleMaxLength(32)],
					pressureZone: [ruleMaxLength(32)],
					houseYear: [ruleMaxLength(16)],
					floorNum: [
						{
							pattern: /^(?:[0-9]{1,4}|9999)$/,
							message: '请输入0-9999的整数',
							trigger: '',
						},
					],
				},
			},
		}
	},
	methods: {
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			let formObj = trimParams(removeNullParams(this.formData))
			const archivesIds = this.data.map(item => item.archives.archivesId)
			Object.assign(formObj, { archivesIds })

			apiBatchUpdateAddress(formObj)
				.then(() => {
					this.$message.success('修改成功')
					this.handleClose()
					this.$emit('refresh')
				})
				.catch(error => {
					console.log(error)
				})
		},
	},
}
</script>
