<template>
	<GcElDialog :show="isShow" title="托收账户查询" width="800px" @close="handleClose">
		<GcFormSimple
			ref="searchFormRef"
			v-model="searchForm"
			:formItems="searchFormItems"
			:formAttrs="searchFormAttrs"
		>
			<el-form-item>
				<el-button type="primary" @click="handleSearch">查询</el-button>
			</el-form-item>
		</GcFormSimple>
		<el-radio-group v-model="tabValue" style="margin-bottom: 30px" @input="handleSearch">
			<el-radio-button label="information">托收资料</el-radio-button>
			<el-radio-button label="company">所有企业</el-radio-button>
		</el-radio-group>
		<!-- 托收资料 -->
		<GcFormRow
			v-show="tabValue == 'information'"
			ref="formRef"
			v-model="formData"
			:formItems="formItems"
			:formAttrs="formAttrs"
		>
			<template v-slot:collectionAgreementNumber>
				<el-input
					v-if="btnType === 'add'"
					v-model="formData.collectionAgreementNumber"
					class="account-more"
					readonly
				>
					<img slot="append" src="@/assets/images/icon/get-num.svg" @click="_apiGetAgreementNumber" />
				</el-input>
				<el-input v-else v-model="formData.collectionAgreementNumber" disabled placeholder="请输入"></el-input>
			</template>
		</GcFormRow>
		<!-- 所有企业 -->
		<GcTable
			v-show="tabValue == 'company'"
			style="height: 432px"
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			showPage
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			@current-page-change="handlePageChange"
		/>
		<template #footer>
			<GcButton v-show="btnType === 'normal'" btn-type="three" @click.native="clickBtn('add')">新建</GcButton>
			<GcButton @click.native="handleSave" v-show="btnType === 'normal'">确认选中</GcButton>
			<GcButton v-show="btnType === 'add'" btn-type="three" @click.native="clickBtn('normal')">取消</GcButton>
			<GcButton v-show="btnType === 'add'" @click.native="handleCreate">新建并选中</GcButton>
		</template>
	</GcElDialog>
</template>

<script>
import {
	ruleRequired,
	ruleMaxLength,
	RULE_INTEGERONLY,
	RULE_INCORRECTEMAIL,
	RULE_PHONE,
	RULE_POSTALCODE,
} from '@/utils/rules'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { isBlank } from '@/utils/validate.js'
import {
	apiQueryCollectionBankList,
	apiQueryConsignBankCodeList,
	apiCreateCollectionAccount,
	apiGetCollectionAccountSingleDetail,
	apiGetCollectionAccountUserList,
	apiGetAgreementNum,
} from '@/api/userManage.api'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		collectionAgreementNumber: {
			type: String,
			default: '',
		},
		orgCode: {
			type: String,
			default: '',
		},
	},
	watch: {
		show(val) {
			if (val) {
				this.getBankList()
				if (this.collectionAgreementNumber) {
					this.searchForm.collectionAgreementNumber = this.collectionAgreementNumber
					this.getCollectionAccount()
				}
			}
		},
		btnType: {
			handler(val) {
				if (val === 'normal') {
					this.formAttrs.rules = {}
				} else {
					this.formAttrs.rules = {
						orgCode: [ruleRequired('请选择营业分公司')],
						collectionAgreementNumber: [ruleRequired('必填'), ruleMaxLength(30), RULE_INTEGERONLY],
						bankCode: [ruleRequired('必填')],
						bankNo: [ruleRequired('必填'), ruleMaxLength(64)],
						bankBranchCode: [ruleRequired('必填')],
						depositBank: [ruleRequired('必填'), ruleMaxLength(64)],
						bankAcctName: [ruleRequired('必填'), ruleMaxLength(64)],
						bankAcctCode: [ruleRequired('必填'), ruleMaxLength(64)],
						bankAddres: [ruleRequired('必填'), ruleMaxLength(64)],
						contactPeople: [ruleRequired('必填'), ruleMaxLength(64)],
						contactPhone: [RULE_PHONE],
						email: [RULE_INCORRECTEMAIL],
						mailingAddress: [ruleMaxLength(64)],
						remarks: [ruleMaxLength(64)],
						zipCode: [RULE_POSTALCODE],
					}
				}
			},
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			searchForm: {
				collectionAgreementNumber: '',
			},
			searchFormItems: [
				{
					type: 'el-input',
					label: '托收协议号',
					prop: 'collectionAgreementNumber',
				},
			],
			searchFormAttrs: {
				inline: true,
			},
			formData: {
				orgCode: '',
				collectionAgreementNumber: '',
				bankCode: '',
				bankNo: '',
				bankBranchCode: '',
				depositBank: '',
				bankAcctName: '',
				bankAcctCode: '',
				bankAddres: '',
				contactPeople: '',
				contactPhone: '',
				email: '',
				mailingAddress: '',
				remarks: '',
				zipCode: '',
				collectionAccountId: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						disabled: true,
					},
				},
				{
					type: 'slot',
					label: '托收协议号',
					prop: 'collectionAgreementNumber',
					slotName: 'collectionAgreementNumber',
				},
				{
					type: 'el-select',
					label: '银行名称',
					prop: 'bankCode',
					options: [],
					events: {
						change: value => {
							this.formData.bankNo = ''
							this.formData.bankBranchCode = ''
							this.formItems[3].options = []
							if (value) {
								this.getConsignBankList(value)
							}
						},
					},
				},
				{
					type: 'el-select',
					label: '银行支行行名',
					prop: 'bankNo',
					options: [],
					events: {
						change: value => {
							this.formData.bankBranchCode = value
						},
					},
				},
				{
					type: 'el-input',
					label: '银行支行行号',
					prop: 'bankBranchCode',
					attrs: {
						disabled: true,
					},
				},
				{
					type: 'el-input',
					label: '开户银行',
					prop: 'depositBank',
				},
				{
					type: 'el-input',
					label: '开户户名',
					prop: 'bankAcctName',
				},
				{
					type: 'el-input',
					label: '开户账号',
					prop: 'bankAcctCode',
				},
				{
					type: 'el-input',
					label: '开户地址',
					prop: 'bankAddres',
				},
				{
					type: 'el-input',
					label: '联系人',
					prop: 'contactPeople',
				},
				{
					type: 'el-input',
					label: '联系电话',
					prop: 'contactPhone',
				},
				{
					type: 'el-input',
					label: '电子邮箱',
					prop: 'email',
				},
				{
					type: 'el-input',
					label: '邮寄地址',
					prop: 'mailingAddress',
				},
				{
					type: 'el-input',
					label: '备注',
					prop: 'remarks',
				},
				{
					type: 'el-input',
					label: '邮编',
					prop: 'zipCode',
				},
			],
			formAttrs: {
				labelWidth: '115px',
				rules: {},
				disabled: true,
			},
			tabValue: 'information',
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'enterpriseNumber',
					name: '企业编号',
					tooltip: true,
				},
				{
					key: 'userName',
					name: '企业名称',
					tooltip: true,
				},
				{
					key: 'enterpriseAddress',
					name: '企业地址',
					tooltip: true,
				},
				{
					key: 'contactPeople',
					name: '联系人',
					tooltip: true,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			btnType: 'normal',
		}
	},
	methods: {
		// 托收资料：获取托收协议号
		async _apiGetAgreementNumber() {
			const { orgCode } = this.formData
			if (isBlank(orgCode)) {
				this.$message.error('请选择营业分公司')
				return
			}

			try {
				const { collectionAgreementNumber = '' } = await apiGetAgreementNum({
					orgCode,
				})
				const message = collectionAgreementNumber ? '获取托收协议号成功' : '未查询到数据'
				this.formData.collectionAgreementNumber = collectionAgreementNumber
				this.$message.success(message)
			} catch (error) {
				console.error(error)
			}
		},
		// 托收资料：查询银行
		async getBankList() {
			const data = await apiQueryCollectionBankList()
			this.formItems[2].options = data.map(item => {
				return {
					label: item.bankName,
					value: item.bankCode,
				}
			})
		},
		// 托收资料：查询银行支行
		async getConsignBankList(value) {
			const data = await apiQueryConsignBankCodeList({
				bankType: value,
			})
			this.formItems[3].options = data.map(item => {
				return {
					label: item.bankName,
					value: item.bankCode,
				}
			})
		},
		async handleSearch() {
			await this.getCollectionAccount()
			if (this.tabValue === 'company') {
				this.handlePageChange({ page: 1 })
			}
		},
		// 查询所有企业
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getCompany()
		},
		// 查询所有企业
		async getCompany() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const { collectionAccountId } = this.formData
				const { total = 0, records = [] } = await apiGetCollectionAccountUserList({
					size,
					current,
					recordId: collectionAccountId,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 查询托收账户
		async getCollectionAccount() {
			try {
				const formParams = trimParams(removeNullParams(this.searchForm))
				const res = await apiGetCollectionAccountSingleDetail(formParams)
				if (!res) {
					this.$message.error('未查询到数据')
					this.clearCollectionAccount()
					return
				}

				this.assignForm(res)
			} catch (error) {
				console.error(error)
			}
		},
		// 清空托收账户
		clearCollectionAccount() {
			this.$refs.formRef.resetForm()
			this.$nextTick(() => {
				this.$refs.formRef.clearValidate()
			})
		},
		// 回显托收资料
		assignForm(obj) {
			const keys = Object.keys(this.formData)
			if (obj.bankCode) {
				this.getConsignBankList(obj.bankCode)
			}

			keys.forEach(key => {
				if (obj && Object.prototype.hasOwnProperty.call(obj, key)) {
					this.formData[key] = obj[key]
					if (key === 'bankNo') {
						this.formData.bankBranchCode = obj[key]
					}
				}
			})
		},
		clickTab(val) {
			if (val === 'company') {
				this.handlePageChange({ page: 1 })
			}
		},
		clickBtn(type) {
			this.btnType = type
			this.$refs.searchFormRef.resetFields()
			this.tabValue = 'information'
			this.clearCollectionAccount()
			if (type === 'add') {
				this.formAttrs.disabled = false
				this.formData.orgCode = this.orgCode
			} else {
				this.formAttrs.disabled = true
			}
		},
		async handleSave() {
			this.$emit('success', this.formData)
			this.handleClose()
		},
		async handleCreate() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				const formParams = trimParams(removeNullParams(this.formData))
				delete formParams.collectionAccountId
				const data = await apiCreateCollectionAccount(formParams)
				if (data) {
					this.formData.collectionAccountId = data
					this.$message.success('新建成功')
					this.handleSave()
				}
			}
		},
		handleClose() {
			this.$refs.searchFormRef.resetFields()
			this.$refs.formRef.resetForm()
			this.$refs.formRef.clearValidate()
			this.tabValue = 'information'
			this.btnType = 'normal'
			this.formAttrs.disabled = true
			this.isShow = false
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
.el-form {
	padding: 0 !important;
}
</style>
