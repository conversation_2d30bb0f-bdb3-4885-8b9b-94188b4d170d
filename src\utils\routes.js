import { resolve } from 'path'
import store from '@/store'
import { TENANT_ROUTES, ORG_ROUTES } from '@/consts/routerWatcher.js'

// 判断当前用户的permissions是否包含路由指定permission
export function hasPermission(permissions, route) {
	const { userLevel, isAdmin, roleId } = store.getters && store.getters.userInfo
	const { showForSys = false, hideForSys = false } = route.meta
	let has = false
	if (route.meta.title === '租户配置') {
		has = ['1', '2'].includes(roleId)
	} else {
		if (!route.meta.permissions) {
			has = true
		} else {
			has = permissions.some(permission => route.meta.permissions.includes(permission))
		}
	}

	if (isAdmin) {
		return !hideForSys
	} else if (userLevel == 0) {
		// 金卡管理员、金卡运维管理员：仅系统级账号展示且有权限，且没有hideForSys
		if (hideForSys) {
			return false
		} else if (showForSys) {
			return has
		}
		return has
	} else {
		return !showForSys && has
	}

	// if (route.meta.showForSys) {
	//   // const { isAdmin } = store.getters && store.getters.userInfo;
	//   if (userLevel == 0 && isAdmin) {
	//     return true;
	//   } else if (userLevel == 0 && !isAdmin) {
	//   }
	//   return false;
	// } else if (route.meta && route.meta.permissions) {
	//   // 有权限但系统级账号不显示 / 有权限, 如：小区管理
	//   const has = permissions.some((permission) =>
	//     route.meta.permissions.includes(permission)
	//   );
	//   return (
	//     has &&
	//     (!route.meta.hideForSys || (route.meta.hideForSys && userLevel != 0))
	//   );
	// } else {
	//   return true;
	// }
}

// 递归过滤异步路由
export function filterAsyncRoutes(routes, permissions) {
	const res = []
	routes.forEach(route => {
		const tmp = { ...route }
		if (hasPermission(permissions, tmp)) {
			if (tmp.children) {
				tmp.children = filterAsyncRoutes(tmp.children, permissions)
			}
			res.push(tmp)
		}
	})
	return res
}
export function filterRoutesForBusiness(routes, business_config) {
	const res = []
	routes.forEach(route => {
		const tmp = { ...route }
		if (showForBusiness(tmp, business_config)) {
			if (tmp.children) {
				tmp.children = filterRoutesForBusiness(tmp.children, business_config)
			}
			res.push(tmp)
		}
	})
	return res
}
function showForBusiness(tmp, { is_need_sms, is_need_wechat, is_need_charge_limit, is_metrological_verification }) {
	if (tmp.meta) {
		const { msgBusiness, chargeLimit, verification } = tmp.meta
		return (
			(!msgBusiness && !chargeLimit && !verification) ||
			(msgBusiness && (is_need_sms || is_need_wechat)) ||
			(chargeLimit && is_need_charge_limit) ||
			(verification && is_metrological_verification)
		)
	}
	return true
}

// fullPath 路由地址补全
export function filterRoutes(routes, baseUrl = '/') {
	return routes.map(route => {
		route.fullPath = resolve(baseUrl, route.path)
		if (route.children) {
			route.children = filterRoutes(route.children, route.fullPath)
		}
		return route
	})
}

// 根据当前route获取激活菜单
export function handleActivePath(route, isTabsBar = false) {
	const { meta, fullPath } = route
	const rawPath = route.matched ? route.matched[route.matched.length - 1].path : fullPath
	if (isTabsBar) return meta.dynamicNewTab ? fullPath : rawPath
	if (meta.activeMenu) return meta.activeMenu
	return fullPath ? fullPath : rawPath
}

// 获取当前跳转登录页的Route
// eslint-disable-next-line no-unused-vars
export function toLoginRoute(currentPath) {
	// if (currentPath !== "/")
	//   return {
	//     path: "/login",
	//     query: { redirect: currentPath },
	//     replace: true,
	//   };
	// else return { path: "/login", replace: true };
	// 登录不需要redirect到上次退出页
	return { path: '/login', replace: true }
}

// 切换租户/切换组织机构弹框的条件
export function passRouterValidate(router) {
	const { userLevel, isCrossDomain, orgId } = store.getters && store.getters.userInfo
	const tenant = store.getters && store.getters.tenant

	if (TENANT_ROUTES.includes(router.name)) {
		if (userLevel == 0 && !tenant['name']) {
			store.commit('routes/SET_IS_SWITCH_TENANT', true)
			return false
		}
	}
	// FEAT 3.7.0取消跨域运维角色
	if (ORG_ROUTES.includes(router.name)) {
		if (isCrossDomain && !orgId) {
			store.commit('routes/SET_IS_SWITCH_ORG', true)
			return false
		}
	}
	// 3.7.0迭代以后取消了跨域运维角色，代码暂时保留，后续删除-结束
	return true
}
