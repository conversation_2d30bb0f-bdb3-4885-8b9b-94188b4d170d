<template>
	<div class="form-wrap">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button
					v-has="'meterReadingBook_transfer_register'"
					:loading="loading"
					type="primary"
					@click="handleCheckIn"
				>
					移交登记
				</el-button>
			</el-form-item>
		</GcFormSimple>
	</div>
</template>

<script>
import { ruleMaxLength, RULE_CHINESE } from '@/utils/rules'
import { meterCardRegister } from '@/api/meterReading.api'

export default {
	components: {},
	data() {
		return {
			formData: {
				registerTime: '',
				operator: '',
				handoverChannelCode: '',
				bookType: '',
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '移交时间',
					prop: 'registerTime',
					attrs: {
						col: 24,
						type: 'datetime',
						format: 'yyyy-MM-dd HH:mm',
						valueFormat: 'yyyy-MM-dd HH:mm:ss',
						placeholder: '请选择移交时间',
					},
				},
				{
					type: 'el-input',
					label: '移交人',
					prop: 'operator',
					attrs: {
						col: 24,
						placeholder: '请输入移交人',
					},
				},
				{
					type: 'el-select',
					label: '移交渠道',
					prop: 'handoverChannelCode',
					options:
						this.$store.getters?.dataList?.handoverChannel?.map(item => {
							return {
								label: item.sortName,
								value: item.sortValue,
							}
						}) || [],
					attrs: {
						col: 24,
						placeholder: '请选择移交渠道',
					},
				},
			],
			formAttrs: {
				labelWidth: '90px',
				rules: {
					registerTime: {
						required: true,
						message: '请选择移交时间',
						trigger: 'change',
					},
					operator: [
						{
							required: true,
							message: '请输入移交人',
							trigger: 'change',
						},
						RULE_CHINESE,
						ruleMaxLength(10, '移交人'),
					],
					handoverChannelCode: {
						required: true,
						message: '请选择移交渠道',
						trigger: 'change',
					},
				},
			},

			loading: false,
		}
	},
	methods: {
		async handleCheckIn() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.loading = true
				try {
					await meterCardRegister(this.formData)
					this.$message.success('移交登记成功')
					this.$emit('check')
				} catch (error) {
					console.error(error)
				} finally {
					this.loading = false
				}
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.form-wrap {
	width: 50%;
	padding: 20px 0;
}
</style>
