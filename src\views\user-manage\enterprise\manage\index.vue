<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleChangePage({ page: 1 })">
					筛选
				</el-button>
			</div>
		</div>
		<div class="page-right">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handleChangePage"
				@dblclick="handleDbClick"
			>
				<template v-slot:operate="{ row }">
					<el-button v-has="'cpm_enterprise_update'" type="text" @click="handleEdit(row)">修改</el-button>
				</template>
			</GcTable>
		</div>
	</div>
</template>

<script>
import { ruleRequired } from '@/utils/rules'
import { getfilterName } from '@/utils'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { queryEnterprisePage } from '@/api/userManage.api'
export default {
	data() {
		return {
			formData: {
				orgCode: '',
				enterpriseNumber: '',
				enterpriseName: '',
				collectionAgreementNumber: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
				},
				{
					type: 'el-input',
					label: '企业编号',
					prop: 'enterpriseNumber',
				},
				{
					type: 'el-input',
					label: '企业名称',
					prop: 'enterpriseName',
				},
				{
					type: 'el-input',
					label: '托收协议号',
					prop: 'collectionAgreementNumber',
				},
			],
			formAttrs: {
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
				},
			},
			columns: [
				{
					key: 'enterpriseNumber',
					name: '企业编号',
					tooltip: true,
				},
				{
					key: 'enterpriseName',
					name: '企业名称',
					tooltip: true,
				},
				{
					key: 'enterpriseAddress',
					name: '企业地址',
					tooltip: true,
				},
				{
					key: 'contactPeople',
					name: '联系人',
					tooltip: true,
				},
				{
					key: 'userMobile',
					name: '手机号',
					tooltip: true,
				},
				{
					key: 'chargingMethod',
					name: '收费方式',
					tooltip: true,
					render: (h, row) => {
						const value = getfilterName(
							this.$store.getters.dataList.chargingMethod,
							row.chargingMethod,
							'sortValue',
							'sortName',
						)
						return h('span', {}, value)
					},
				},
				{
					key: 'collectionAgreementNumber',
					name: '托收协议号',
					tooltip: true,
				},
				{
					key: 'archivesCount',
					name: '表卡数量',
					tooltip: true,
				},
				{
					key: 'operate',
					name: '操作',
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
		}
	},
	computed: {
		orgOptions() {
			return this.formItems[0]?.options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.$nextTick(() => {
						this.handleChangePage({ page: 1 })
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
				})
				const { records, total } = await queryEnterprisePage(formParams)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.handleChangePage({ page: 1 })
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleDbClick(obj) {
			// if (!this.$has('cpm_enterprise_update')) {
			// 	this.$notify({
			// 		message: '暂无权限访问，请联系管理员！',
			// 		title: '提示',
			// 		offset: 60,
			// 		type: 'warning',
			// 	})
			// 	return
			// }
			const { id = '' } = obj.row
			this.$router.push({
				path: '/userManage/companyView',
				query: {
					userId: id,
				},
			})
		},
		handleEdit(row) {
			if (!this.$has('cpm_enterprise_update')) {
				this.$notify({
					message: '暂无权限访问，请联系管理员！',
					title: '提示',
					offset: 60,
					type: 'warning',
				})
				return
			}
			const { enterpriseNumber = '' } = row
			this.$router.push({
				path: '/userManage/enterpriseModify',
				query: {
					enterpriseNumber,
				},
			})
		},
	},
}
</script>
