import Layout from '@/layout'

export default [
	{
		path: '/userManage',
		name: 'UserManage',
		component: Layout,
		redirect: '/userManage/userList',
		meta: {
			title: '用户',
			icon: 'icon-cis_yj_yonghu',
			permissions: [
				'cpm_user_list',
				'cpm_collectionAccount_list',
				'cpm_collectionAccount_add',
				'cpm_collectionAccount_modify-record',
				'cpm_collectionAccount_user-list',
				'cpm_archives_modify-user-name-record2',
				'cpm_archives_change-user-record2',
				'cpm_user_getUserDatailsById',
				'cpm_enterprise_add',
				'cpm_enterprise_update',
				'cpm_enterprise_queryEnterprisePage',
			],
		},
		children: [
			{
				path: 'userList',
				name: 'UserList',
				component: () => import('@/views/user-manage/user-list/index.vue'),
				meta: {
					title: '用户管理',
					icon: 'icon-cis_ej_yonghuguan<PERSON>',
					keepAlive: true,
					permissions: ['cpm_user_list'],
				},
			},
			{
				path: 'collectionAccountManage',
				name: 'CollectionAccountManage',
				component: () => import('@/views/user-manage/collection-account-manage/index.vue'),
				meta: {
					title: '托收账户管理',
					icon: 'icon-cis_ej_tuoshouzhanghu',
					keepAlive: true,
					permissions: ['cpm_collectionAccount_list', 'cpm_collectionAccount_add'],
				},
			},
			{
				path: 'collectionAccountRecords',
				name: 'CollectionAccountRecords',
				component: () => import('@/views/user-manage/collection-account-records/index.vue'),
				meta: {
					title: '托收账户变更记录',
					keepAlive: true,
					permissions: ['cpm_collectionAccount_modify-record'],
				},
				hidden: true,
			},
			{
				path: 'collectionAccountAllUsers',
				name: 'CollectionAccountAllUsers',
				component: () => import('@/views/user-manage/collection-account-all-users/index.vue'),
				meta: {
					title: '托收账户所有用户',
					keepAlive: true,
					permissions: ['cpm_collectionAccount_user-list'],
				},
				hidden: true,
			},
			{
				path: 'renameTransferManage',
				name: 'RenameTransferManage',
				component: () => import('@/views/user-manage/rename-transfer-manage/index.vue'),
				meta: {
					title: '更名过户管理',
					icon: 'icon-cis_ej_gengmingguohu',
					keepAlive: true,
					permissions: ['cpm_archives_modify-user-name-record2', 'cpm_archives_change-user-record2'],
				},
			},
			{
				path: 'userView',
				name: 'UserView',
				component: () => import('@/views/user-manage/user-view/index.vue'),
				meta: {
					title: '用户视图',
					keepAlive: true,
					permissions: ['cpm_user_getUserDatailsById'],
				},
				hidden: true,
			},
			{
				path: 'companyView',
				name: 'CompanyView',
				component: () => import('@/views/user-manage/company-view/index.vue'),
				meta: {
					title: '企业视图',
					keepAlive: true,
					// permissions: ['cpm_user_getUserDatailsById'],
				},
				hidden: true,
			},
			{
				path: 'enterpriseCreate',
				name: 'EnterpriseCreate',
				component: () => import('@/views/user-manage/enterprise/create/index.vue'),
				meta: {
					title: '创建企业',
					icon: '',
					keepAlive: true,
					permissions: ['cpm_enterprise_add'],
				},
				hidden: true,
			},
			{
				path: 'enterpriseModify',
				name: 'EnterpriseModify',
				component: () => import('@/views/user-manage/enterprise/create/index.vue'),
				meta: {
					title: '修改企业',
					icon: '',
					keepAlive: true,
					permissions: ['cpm_enterprise_update'],
				},
				hidden: true,
			},
			{
				path: 'enterpriseList',
				name: 'EnterpriseList',
				component: () => import('@/views/user-manage/enterprise/manage/index.vue'),
				meta: {
					title: '企业管理',
					icon: 'icon-cis_ej_qiyeguan',
					keepAlive: true,
					permissions: ['cpm_enterprise_queryEnterprisePage'],
				},
			},
		],
	},
]
