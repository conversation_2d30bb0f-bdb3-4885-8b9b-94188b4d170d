/* 滚动条全局样式 */
textarea::-webkit-scrollbar {
  cursor: pointer;
  position: absolute;
  margin: auto;
  transition: opacity 0.5s;
  user-select: none;
  border-radius: inherit;
  height: 18.2046%;
  background: rgba(144, 147, 153, 0.3);
  width: 7px;
  opacity: 1;
  transform: translateY(303.03%);
  left: 0px;
  right: 0px;
}
/* 滚动条的设置 */
textarea::-webkit-scrollbar-thumb {
  cursor: pointer;
  position: absolute;
  margin: auto;
  transition: opacity 0.5s;
  user-select: none;
  border-radius: inherit;
  height: 18.2046%;
  background: rgba(144, 147, 153, 0.3);
  width: 7px;
  opacity: 1;
  transform: translateY(303.03%);
  left: 0px;
  right: 0px;
}
/** 去除input number输入框样式 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}
input[type="number"] {
  -moz-appearance: textfield;
}
// 卡片详情数据项样式-开始
.gc-row {
  margin-top: 20px;
  &:first-child {
    margin-top: 30px;
  }
  &:last-child {
    margin-bottom: 30px;
  }
}

.gc-item-key {
  font-size: 12px;
  color: #999999;
  max-width: 250px;
}
.gc-item-value {
  margin-top: 11px;
  font-size: 14px;
  font-weight: 400;
  color: #4e4e4e;
  line-height: 21px;
  max-width: 250px;
}
// 卡片详情数据项样式-结束

.left-w-model {
  width: 380px;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}
.right-w-model {
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  flex: 1;
  margin-left: 16px;
}

.footer-submit-b {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 66px;
  line-height: 66px;
  background: #fff;
  z-index: 9;
  padding: 0 20px;
  button {
    width: 80px;
  }
}

.operate-text-group {
  span {
    cursor: pointer;
    margin-right: 12px;
    font-size: 14px;
    &.btn-del {
      color: #ec6b60;
    }
    &.btn-edit {
      color: #2f87fe;
    }
  }
}

/* gc-dropdown下拉动画 */
.gc-dropdown {
  transition: $base-transition;
  &-active {
    transform: rotateZ(180deg);
  }
}
/* nprogress进度条 */
#nprogress {
  position: fixed;
  z-index: $base-z-index + 3;
  .bar {
    background: $base-color-blue;
  }
  .peg {
    box-shadow: 0 0 10px $base-color-blue, 0 0 5px $base-color-blue;
  }
}

.gc-layout-header,
[class*="-bar-container"] {
  transition: $base-transition;
  * {
    transition: $base-transition;
  }
}
/* 过渡动画 */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: $base-transition;
}
.fade-transform-enter {
  opacity: 0;
}
.fade-transform-leave-to {
  opacity: 0;
}

/* 旋转动画 */
@keyframes rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 状态-小原点样式 */
.dot {
  &::before {
    content: "";
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #ccc;
    margin-right: 6px;
    vertical-align: middle;
  }
  &.green::before,
  &.success::before {
    background-color: #19be6b;
  }
  &.red::before,
  &.error::before {
    background-color: $base-color-red;
  }
  &.blue::before,
  &.primary::before {
    background-color: $base-color-blue;
  }
}

// 左右布局列表通用样式
.page-layout {
  display: flex;
  height: 100%;
}
.page-left {
  display: flex;
  flex-direction: column;
  flex: 0 0 270px;
  margin-right: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  .el-form {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    // padding: 0 10px;
  }
  .btn-group {
    flex: 0 0 52px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .el-button {
      height: 30px;
    }
  }
}
.page-right {
  display: flex;
  flex-direction: column;
  width: 0;
  flex: 1;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}
