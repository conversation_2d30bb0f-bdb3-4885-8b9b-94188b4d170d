<template>
	<gc-el-dialog :show="isShow" title="推送缴费日调整" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { isBlank } from '@/utils/validate.js'
import { queryAccountOpeningDate, updateAccountOpeningDate } from '@/api/basicConfig.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			orgCode: '',
			formData: {
				openDate: '',
				cycle: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '每月推送缴费日',
					prop: 'openDate',
					options: Array.from({ length: 28 }, (_, index) => {
						return {
							label: index + 1,
							value: index + 1,
						}
					}),
					attrs: {
						col: 24,
						placeholder: '请选择每月推送缴费日',
					},
				},
				{
					type: 'el-select',
					label: '选择推送缴费的抄表期',
					prop: 'cycle',
					options: [
						{
							label: '本月抄表期',
							value: 1,
						},
						{
							label: '上月抄表期',
							value: 0,
						},
					],
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择推送缴费的抄表期',
					},
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					openDate: [
						{
							required: true,
							message: '请选择每月开账日',
							trigger: 'change',
						},
					],
					cycle: [
						{
							required: true,
							message: '请选择开账的抄表期',
							trigger: 'change',
						},
					],
				},
			},
		}
	},
	methods: {
		// 获取详情数据
		async getData() {
			try {
				const { openDate = '', cycle = '' } = await queryAccountOpeningDate({
					orgCode: this.orgCode,
				})
				!isBlank(openDate) && (this.formData.openDate = openDate)
				!isBlank(cycle) && (this.formData.cycle = cycle)
			} catch (error) {
				console.error(error)
				this.formData = {
					openDate: '',
					cycle: '',
				}
			}
		},
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.$confirm('将按照新开账日对账单开账，是否确认修改？', '开账日调整').then(async () => {
					await updateAccountOpeningDate({
						...this.formData,
						orgCode: this.orgCode,
					})
					this.$message.success('开账日调整成功')
					this.$emit('success')
					this.isShow = false
				})
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},

		// 设置orgCode
		setOrgCode(orgCode) {
			this.orgCode = orgCode
			this.getData()
		},
		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
</style>
