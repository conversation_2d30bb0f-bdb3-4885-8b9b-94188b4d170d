<template>
	<div class="template-download">
		<div class="title">模板下载：</div>
		<div class="template-list">
			<div class="template-item-label">
				<div class="label" v-for="item in templateList" :key="item.title">
					<img src="@/assets/images/pic/xls-icon.png" alt="" />
					<span>{{ '"' + item.title + '"' }} 模板</span>
				</div>
			</div>
			<div class="template-item-url">
				<a v-for="item in templateList" :key="item.url" :href="`${publicPath}` + item.url">下载</a>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'TemplateDownload',
	props: {
		templateList: {
			type: Array,
			required: true,
			default: () => [],
		},
	},
	data() {
		return {
			publicPath: process.env.BASE_URL,
		}
	},
}
</script>
<style lang="scss" scoped>
.template-download {
	width: 100%;
	border: 1px dashed #d5d5d5;
	padding: 12px 17px;
	margin-top: 12px;
	display: flex;
	.title {
		font-size: $base-font-size-small;
		color: #ababab;
	}
	.template-list {
		display: flex;
		.template-item-label {
			.label {
				font-size: $base-font-size-small;
				color: $base-color-4;
				flex: 1;
				img,
				span {
					vertical-align: top;
				}
				img {
					width: 12px;
					margin-right: 4px;
				}
			}
			.label:not(:first-child) {
				padding-top: 12px;
			}
		}
		.template-item-label,
		.template-item-url {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
		}
		.template-item-url {
			margin-left: 12px;
			a {
				color: $base-color-blue;
				font-size: $base-font-size-small;
				text-decoration: none;
			}
			a:not(:first-child) {
				padding-top: 12px;
			}
		}
	}
}
</style>
