.tab-content{
    width:100%;
    height: 100%;
    // padding-right: 3px;
    // background: #fff;
  }
  .bg-overflow{
    width: 100%;
    height: 100%;
    background: #eceff8;
    overflow: auto;
  }
  .content-bg{
    background: #fff;
  }
  .data-container{
    background:#fff;
    border-radius: 4px;
    .footer {
      padding: 0 $base-padding $base-padding $base-padding;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      span + span {
        margin-left: 16px;
      }
      span {
        font-size: $base-font-size-default;
        cursor: pointer;
        display: flex;
        align-items: center;
        i {
          padding-right: 3px;
        }
      }
      .footer-left,.footer-right{
        display: flex;
      }
      .footer-left {
        .color-blue {
          color: $base-color-blue;
        }
        .color-red {
          color: $base-color-red;
        }
      }
      .footer-right {
        color: $base-color-yellow;
      }
    }
  }
  
  // 左右布局样式
  .layout-overview {
    display: flex;
    justify-content: flex-end;
    align-items: stretch;
    position: relative;
    min-height: 100%;
    .left{
      flex:1;
      width: 0;
      margin-right: $base-margin;
      display: flex;
      flex-direction: column;
      // position: absolute;
      // left: 0;
      // height: 100%;
      .data-container + .data-container {
        margin-top: $base-margin;
      }
    }
    .right{
      width: 40%;
      // background: #fff;
      // border-radius: 4px;
      // padding: 0 20px;
     
      // background: linear-gradient(137deg, #7dd1c7 0%, #3dad9f 100%);
      // box-shadow: 0px 2px 25px 0px rgba(0, 0, 0, 0.1);
      // border-radius: 0 4px 4px;
      // padding: 20px;
      color: #fff;
    }
  }

  