import Layout from '@/layout'

export default [
	{
		path: '/costManage',
		name: 'CostManage',
		component: Layout,
		redirect: '/costManage/billManage',
		meta: {
			title: '费用',
			icon: 'icon-cis_yj_feiyong',
			permissions: [
				'billing_bill_list',
				'billing_bill-adjust_reduction',
				'billing_bill-adjust_partial-payment',
				'billing_bill-adjust_adjustment',
				'payment_invoice_open-invoice',
				'payment_invoice_batch-open-invoice',
				'billing_bill_pending_list',
				'billing_bill_open',
				'billing_bill_search_open',
				'billing_bill_clear',
				'billing_bill-adjust_reduction2',
				'billing_bill-adjust_partial-payment2',
				'billing_bill-adjust_adjustment3',
				'payment_invoice_batch-open-invoice2',
				'billing_bill_account-clear-list',
				'billing_bill_account-clear',
				'billing_bill_account-clear-batch',
				'payment_bill-pay-record_counter-list',
				'payment_bill-pay-record_revers',
				'payment_invoice_open-invoice4',
				'billing_charge-batch',
				'billing_bill-adjust_record',
				'billing_bankCollection_queryBankBillList',
				'billing_unionCollection_queryUnionBillStatistics',
				'billing_bankCollection_queryBankSendFile',
				'billing_unionCollection_queryUnionSendFile',
				'billing_pay-record_balance-list',
				'billing_pay-record_list',
			],
		},
		children: [
			{
				path: 'billManage',
				name: 'BillManage',
				component: () => import('@/views/cost-manage/bill-manage/index.vue'),
				meta: {
					title: '账单管理',
					icon: 'icon-cis_ej_zhangdanguanli',
					keepAlive: true,
					permissions: [
						'billing_bill_list',
						'billing_bill-adjust_reduction',
						'billing_bill-adjust_partial-payment',
						'billing_bill-adjust_adjustment',
						'payment_invoice_open-invoice',
						'payment_invoice_merge-open-invoice',
						'payment_invoice_batch-open-invoice',
					],
				},
			},
			{
				path: 'billPending',
				name: 'BillPending',
				component: () => import('@/views/cost-manage/bill-pending/index.vue'),
				meta: {
					title: '账单推送缴费',
					icon: 'icon-cis_ej_zhangdankaizhang',
					keepAlive: true,
					permissions: ['billing_bill_pending_list', 'billing_bill_open', 'billing_bill_search_open'],
				},
			},
			{
				path: 'paymentPage',
				name: 'PaymentPage',
				component: () => import('@/views/cost-manage/payment-page/index.vue'),
				meta: {
					title: '缴费',
					icon: 'icon-cis_ej_jiaofei',
					keepAlive: true,
					permissions: [
						'billing_bill_clear',
						'billing_bill-adjust_reduction2',
						'billing_bill-adjust_partial-payment2',
						'billing_bill-adjust_adjustment3',
						'payment_invoice_batch-open-invoice2',
					],
				},
			},
			{
				path: 'billClearList',
				name: 'BillClearList',
				component: () => import('@/views/cost-manage/bill-clear-list/index.vue'),
				meta: {
					title: '预存销账',
					icon: 'icon-cis_ej_yucunxiaozhang',
					keepAlive: true,
					permissions: [
						'billing_bill_account-clear-list',
						'billing_bill_account-clear',
						'billing_bill_account-clear-batch',
					],
				},
			},
			{
				path: 'counterPaymentList',
				name: 'CounterPaymentList',
				component: () => import('@/views/cost-manage/counter-payment-list/index.vue'),
				meta: {
					title: '缴费记录',
					icon: 'icon-cis_ej_guitaijiaofeiliebiao',
					keepAlive: true,
					permissions: [
						'payment_bill-pay-record_counter-list',
						'payment_bill-pay-record_revers',
						'payment_invoice_open-invoice4',
					],
				},
			},
			{
				path: 'costRelief',
				name: 'CostRelief',
				component: () => import('@/views/cost-manage/relief/index.vue'),
				meta: {
					title: '减免',
					keepAlive: true,
					permissions: ['billing_bill-adjust_reduction'],
				},
				hidden: true,
			},
			{
				path: 'partialPayment',
				name: 'PartialPayment',
				component: () => import('@/views/cost-manage/partial-payment/index.vue'),
				meta: {
					title: '部分缴费',
					keepAlive: true,
					permissions: ['billing_bill-adjust_partial-payment'],
				},
				hidden: true,
			},
			{
				path: 'feeAdjustment',
				name: 'FeeAdjustment',
				component: () => import('@/views/cost-manage/fee-adjustment/index.vue'),
				meta: {
					title: '费用调整',
					keepAlive: true,
					permissions: ['billing_bill-adjust_adjustment'],
				},
				hidden: true,
			},
			{
				path: 'preDepositManage',
				name: 'PreDepositManage',
				component: () => import('@/views/cost-manage/pre-deposit-manage/index.vue'),
				meta: {
					title: '预存款管理',
					icon: 'icon-cis_erji_yucunkuan',
					keepAlive: true,
					permissions: ['billing_pay-record_balance-list'],
				},
			},
			{
				path: 'preDepositRecord',
				name: 'PreDepositRecord',
				component: () => import('@/views/cost-manage/pre-deposit-record/index.vue'),
				meta: {
					title: '预存记录',
					icon: 'icon-cis_erji_yucunjilu',
					keepAlive: true,
					permissions: ['billing_pay-record_list'],
				},
			},
			{
				path: 'joinChargeManage',
				name: 'JoinChargeManage',
				component: () => import('@/views/cost-manage/join-charge-manage/index.vue'),
				meta: {
					title: '联合收费管理',
					icon: 'icon-cis_erji_lianheshoufei',
					keepAlive: true,
					permissions: [
						'billing_unionCollection_queryUnionSendFile',
						'billing_unionCollection_queryUnionReturnFile',
					],
				},
			},
			{
				path: 'bankCollectionManage',
				name: 'BankCollectionManage',
				component: () => import('@/views/cost-manage/bank-collection-manage/index.vue'),
				meta: {
					title: '银行托收管理',
					icon: 'icon-cis_erji_yinhangtuo',
					keepAlive: true,
					permissions: ['billing_bankCollection_queryBankSendFile'],
				},
			},
			{
				path: 'unionChargebackRecords',
				name: 'UnionChargebackRecords',
				component: () => import('@/views/cost-manage/union-chargeback-records/index.vue'),
				meta: {
					title: '联合收费扣款记录',
					icon: 'icon-cis_erji_lianhekoukuan',
					keepAlive: true,
					permissions: ['billing_unionCollection_queryUnionBillStatistics'],
				},
			},
			{
				path: 'bankCollectionChargebackRecords',
				name: 'BankCollectionChargebackRecords',
				component: () => import('@/views/cost-manage/bank-collection-chargeback-records/index.vue'),
				meta: {
					title: '银行托收扣款记录',
					icon: 'icon-cis_erji_yinhangkoukuan',
					keepAlive: true,
					permissions: ['billing_bankCollection_queryBankBillList'],
				},
			},
			{
				path: 'billTrace',
				name: 'BillTrace',
				component: () => import('@/views/cost-manage/bill-trace/index.vue'),
				meta: {
					title: '账单追溯',
					icon: 'icon-cis_erji_zhangdanzhuisu',
					keepAlive: true,
					permissions: ['billing_bill-adjust_record'],
				},
			},
			{
				path: 'batPayment',
				name: 'batPayment',
				component: () => import('@/views/cost-manage/bat-payment/index.vue'),
				meta: {
					title: '批量充值',
					icon: 'icon-cis_ej_jiaofei',
					keepAlive: true,
					permissions: [
						'billing_charge-batch',
						'billing_charge-batch_record_list',
						'billing_charge-batch_fail',
					],
				},
			},
		],
	},
]
