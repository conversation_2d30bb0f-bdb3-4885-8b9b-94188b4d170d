import { getfilterName } from '@/utils'
export function getColumn(_this) {
	return [
		{
			key: 'billDate',
			name: '账单账期',
			tooltip: true,
			minWidth: 100,
		},
		{
			key: 'billStatus',
			name: '账单状态',
			render: (h, row, total, scope) => {
				const { billStatus = [] } = _this.$store.getters.dataList || {}
				const valueStr = getfilterName(billStatus, row[scope.column.property], 'sortValue', 'sortName')
				return h('span', {}, valueStr)
			},
		},
		{
			key: 'bookNo',
			name: '表册编号',
			tooltip: true,
		},
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'readingTime',
			name: '抄表时间',
			tooltip: true,
			minWidth: 200,
		},
		{
			key: 'useAmount',
			name: '水量',
			tooltip: true,
		},
		{
			key: 'priceCode',
			name: '价格编号',
			tooltip: true,
		},
		{
			key: 'useAmt',
			name: '水费',
			tooltip: true,
		},
		{
			key: 'billItemAmt',
			name: '污水费',
			tooltip: true,
		},
		{
			key: 'billItemAmt2',
			name: '附加费',
			tooltip: true,
		},
		{
			key: 'receivableAmount',
			name: '应缴金额',
			tooltip: true,
		},
	]
}
