<template>
	<!-- 此处涉及到表单和非表单，单独封装item进行值的绑定较为困难
  但是下拉列表请求逻辑保持高度一致，使用方法基本一致，可使用相同的js(mixin) -->
	<div class="gc-adress">
		<el-select
			v-model="district"
			filterable
			placeholder="区/县"
			size="small"
			@change="getStreetList('street', district)"
		>
			<el-option
				v-for="item in districtList"
				:key="item.regionCode"
				:label="item.regionName"
				:value="item.regionCode"
			></el-option>
		</el-select>
		<el-select v-model="street" filterable placeholder="请输入街道/小区/乡镇/村庄" size="small">
			<el-option
				v-for="item in streetList"
				:key="item.addressAreaCode"
				:label="item.addressAreaName"
				:value="item.addressAreaCode"
			></el-option>
		</el-select>
	</div>
</template>

<script>
import gcAddress from './index.js'
export default {
	name: 'GcAddress',
	mixins: [gcAddress],
}
</script>

<style lang="scss" scoped>
.gc-adress {
	.el-select {
		width: 100%;
	}
	.el-select:nth-child(2) {
		padding-top: 12px;
	}
}
</style>
