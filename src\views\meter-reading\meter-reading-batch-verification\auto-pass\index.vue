<template>
	<div class="wrapper">
		<div class="container-search">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<template v-slot:rate>
					<div class="custom-box">
						&nbsp;
						<span>大水量区间</span>
						&nbsp;
						<el-form-item prop="useAmountLower" style="margin-bottom: 0">
							<el-input-number
								v-model="formData.useAmountLower"
								:controls="false"
								:step="1"
								step-strictly
								style="width: 100px"
							/>
						</el-form-item>
						<span>至</span>
						&nbsp;
						<el-form-item prop="useAmountUpper" style="margin-bottom: 0">
							<el-input-number
								v-model="formData.useAmountUpper"
								:controls="false"
								:step="1"
								step-strictly
								style="width: 100px"
							/>
						</el-form-item>
					</div>
				</template>
				<el-form-item>
					<el-button type="primary" @click="handleSearch">筛选</el-button>
					<el-button @click="handleReset">重置</el-button>
				</el-form-item>
			</GcFormSimple>
			<div class="btn-group">
				<!-- TODO -->
				<el-checkbox
					v-model="checkedAllPage"
					label="选中所有页"
					border
					:disabled="tableData.length === 0"
					class="select-all"
					@change="handleCheckedAllPage"
				></el-checkbox>
				<el-button
					v-has="'plan-collection_meterReadingReview_asyncBatchReviewPassAndCreateBill'"
					type="primary"
					:disabled="selectedData.length === 0"
					@click="handlePassAndCreateBill"
				>
					批量通过且账单开账
				</el-button>
				<el-button
					v-has="'plan-collection_meterReadingReview_asyncBatchReviewPassAndCreateBill2'"
					type="primary"
					:disabled="selectedData.length === 0"
					@click="handlePass(true)"
				>
					批量复核通过
				</el-button>
				<el-button
					v-has="'plan-collection_meterReadingReview_v2_batchReviewReject'"
					type="primary"
					:disabled="selectedData.length === 0"
					@click="handleReject(true)"
				>
					批量复核驳回
				</el-button>
			</div>
		</div>
		<div class="table-container">
			<GcTable
				ref="gcTableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				row-key="meterReadingRecordId"
				showPage
				needType="selection"
				@selectChange="handleSelectChange"
				@current-page-change="handlePageChange"
			>
				<!-- 抄表经纬度 -->
				<template v-slot:latitudeLongitude="{ row }">
					<span>
						{{ row.longitude && row.latitude ? `${row.longitude},${row.latitude}` : '--' }}
					</span>
				</template>
				<!-- 本次指针 -->
				<template v-slot:curMeterReading="{ row, $index }">
					<span v-show="!row.isEditing">
						{{ !judgeBlank(row.curMeterReading) ? row.curMeterReading : '--' }}
					</span>
					<el-form
						v-show="row.isEditing"
						:ref="`curMeterReadingFormRef${$index}`"
						class="table-form"
						:model="tableData[$index]"
					>
						<el-form-item
							prop="curMeterReading"
							:rules="{
								validator: validFn('curMeterReading', $index),
								trigger: 'blur',
							}"
						>
							<el-input-number
								v-model="tableData[$index].curMeterReading"
								class="input-number"
								placeholder="请输入本次指针"
								:min="0"
								:max="999999999"
								step-strictly
								:controls="false"
								@change="handleCalcUseAmt($index)"
							/>
						</el-form-item>
					</el-form>
				</template>
				<!-- 本次水量 -->
				<template v-slot:useAmount="{ row, $index }">
					<span v-show="!row.isEditing">{{ !judgeBlank(row.useAmount) ? row.useAmount : '--' }}</span>
					<el-form
						v-show="row.isEditing"
						:ref="`useAmountFormRef${$index}`"
						class="table-form"
						:model="tableData[$index]"
					>
						<el-form-item
							prop="useAmount"
							:rules="{
								validator: validFn('useAmount', $index),
								trigger: 'blur',
							}"
						>
							<el-input-number
								v-model="tableData[$index].useAmount"
								class="input-number"
								placeholder="请输入本次水量"
								:min="-999999999"
								:max="999999999"
								step-strictly
								:controls="false"
							/>
						</el-form-item>
					</el-form>
				</template>
				<!-- 抄表情况 -->
				<template v-slot:checkStatusDesc="{ row, $index }">
					<span v-show="!row.isEditing">{{ row.checkStatusDesc || '--' }}</span>
					<el-form
						v-if="row.isEditing"
						:ref="`checkStatusFormRef${$index}`"
						class="table-form"
						:model="tableData[$index]"
					>
						<el-form-item
							prop="checkStatus"
							:rules="[
								{
									required: true,
									message: '请选择抄表情况',
									trigger: 'change',
								},
								{ validator: checkStatusValidtor($index), trigger: 'change' },
							]"
						>
							<el-select
								v-model="tableData[$index].checkStatus"
								clearable
								filterable
								placeholder="请选择抄表情况"
							>
								<el-option
									v-for="item in checkStatusList"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
					</el-form>
				</template>
				<template v-slot:imageUrl="{ row }">
					<UploadImgSimple v-model="row.imageUrl" />
				</template>
				<template v-slot:deal="{ row, $index }">
					<div v-show="!row.isEditing">
						<el-button
							v-has="'plan-collection_meterReadingReview_reviewPass3'"
							type="text"
							size="medium"
							@click="handlePass(false, row)"
						>
							复核通过
						</el-button>
						<el-button
							v-has="'plan-collection_meterReadingReview_reviewReject3'"
							type="text"
							size="medium"
							@click="handleReject(false, row)"
						>
							驳回
						</el-button>
						<el-button
							v-has="'plan-collection_meterReadingTask_updateMeterReadingRecord4'"
							type="text"
							size="medium"
							@click="handleAdjust(row, $index)"
						>
							修改
						</el-button>
					</div>
					<div v-has="'plan-collection_meterReadingTask_updateMeterReadingRecord4'" v-show="row.isEditing">
						<el-button type="text" size="medium" @click="handleAdjustSave(row, $index)">保存</el-button>
						<el-button type="text" size="medium" @click="handleAdjustCancel($index)">取消</el-button>
					</div>
				</template>
			</GcTable>
		</div>
		<!-- 批量通过进度显示 -->
		<BatchProcessDialog
			:show.sync="processDialogShow"
			:can-closed="canClosedDialog"
			:data="processResult"
			@refresh="getList(1)"
		/>
	</div>
</template>

<script>
import UploadImgSimple from '@/components/UploadImgSimple'
import BatchProcessDialog from '../components/BatchProcessDialog'
import { getColumn } from './tableColumn.js'
import { isBlank } from '@/utils/validate.js'
import { checkStatusOptions, specialCheckStatus, CHECK_STATUS_CALC_METHODS } from '@/consts/optionList.js'
import {
	reviewPass,
	reviewReject,
	getReviewDetailListNewV2,
	batchReviewRejectV2,
	updateMeterReadingRecord2,
	asyncBatchReviewPassAndCreateBill,
	getBatchReviewResult,
} from '@/api/meterReading.api.js'

export default {
	name: '',
	components: { UploadImgSimple, BatchProcessDialog },
	props: {
		type: Number,
		topParams: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
				useAmountLower: undefined,
				useAmountUpper: undefined,
			},
			formItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
				{
					type: 'slot',
					prop: 'rate',
					slotName: 'rate',
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '80px',
			},
			loading: false,
			columns: getColumn(this),
			tableData: [
				// {
				//   isEditing: false,
				//   archivesNo: "测试数据",
				//   curMeterReading: undefined,
				//   useAmount: 123,
				//   checkStatus: 13,
				// },
				// {
				//   isEditing: false,
				//   archivesNo: "1",
				//   curMeterReading: undefined,
				//   useAmount: undefined,
				//   checkStatus: 1,
				// },
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			// 抄表情况下拉选择
			checkStatusList: checkStatusOptions,
			// 列表选中数据
			selectedData: [],

			// 编辑的当前行数据（用于取消时恢复）
			currentEditData: {},
			// 选中所有页
			checkedAllPage: false,
			progressInterval: null,
			processDialogShow: false,
			// 批量通过且账单开账/批量通过结果
			processResult: {},
			canClosedDialog: false,
		}
	},
	computed: {
		selectedDataRecordIds() {
			return this.selectedData.map(item => item.meterReadingRecordId)
		},
	},
	created() {
		this.getList()
	},
	methods: {
		handleSelectChange(data) {
			this.selectedData = data
			this.$nextTick(() => {
				if (this.checkedAllPage && this.selectedData.length !== this.tableData.length) {
					this.checkedAllPage = false
				}
			})
		},
		handleSearch() {
			const { useAmountLower, useAmountUpper } = this.formData
			if (!isBlank(useAmountLower) && isBlank(useAmountUpper)) {
				return this.$message.error('请输入水量截至值')
			}
			if (isBlank(useAmountLower) && !isBlank(useAmountUpper)) {
				return this.$message.error('请输入水量起始值')
			}
			if (!isBlank(useAmountLower) && !isBlank(useAmountUpper) && useAmountUpper < useAmountLower) {
				return this.$message.error('水量截至值必须大于水量起始值')
			}
			this.getList(1)
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.formData.useAmountLower = undefined
			this.formData.useAmountUpper = undefined
			this.getList(1)
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		async getList(curPage) {
			this.loading = true

			this.selectedData = []
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getReviewDetailListNewV2({
					type: this.type,
					current,
					size,
					...this.formData,
					...this.topParams,
				})
				this.tableData = records.map(item => {
					return {
						...item,
						curMeterReading: !isBlank(item.curMeterReading) ? item.curMeterReading : undefined,
						useAmount: !isBlank(item.useAmount) ? item.useAmount : undefined,
					}
				})
				this.pageData.total = total

				// 勾选 选中所有页
				if (this.checkedAllPage && this.tableData.length) {
					this.$nextTick(() => {
						this.tableData.forEach(item => {
							this.$refs.gcTableRef.toggleRowSelectionUseInnerData(copyTableData => {
								return copyTableData.find(row => row.meterReadingRecordId === item.meterReadingRecordId)
							}, true)
						})
					})
				} else {
					this.checkedAllPage = false
				}
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 复核通过
		handlePass(isBatch = false, row) {
			this.$confirm('确定复核通过吗?').then(async () => {
				if (isBatch) {
					const taskIdsAndRecordIdsMap = this.getTaskIdsAndRecordIdsMap(isBatch, row)
					const data = await asyncBatchReviewPassAndCreateBill({
						type: this.type,
						createBill: false,
						reviewPass: true,
						...this.formData,
						...this.topParams,
						taskIdsAndRecordIdsMap,
					})
					this.handleBatchReviewResult(data)
					this.processResult = {}
					this.processDialogShow = true
					this.canClosedDialog = false
				} else {
					await reviewPass({
						meterReadingTaskId: row.meterReadingTaskId,
						taskYear: this.topParams.taskYear,
						meterReadingRecordId: [row.meterReadingRecordId],
					})
					this.checkedAllPage = false
					this.$message.success('复核通过成功')
					this.getList(1)
				}
			})
		},
		// 复核驳回
		handleReject(isBatch = false, row) {
			this.$confirm('确定要驳回吗?').then(async () => {
				if (isBatch) {
					const taskIdsAndRecordIdsMap = this.getTaskIdsAndRecordIdsMap(isBatch, row)
					await batchReviewRejectV2({
						type: this.type,
						...this.formData,
						...this.topParams,
						taskIdsAndRecordIdsMap,
					})
				} else {
					await reviewReject({
						taskYear: this.topParams.taskYear,
						meterReadingRecordId: [row.meterReadingRecordId],
					})
				}
				this.checkedAllPage = false
				this.$message.success('驳回成功')
				this.getList(1)
			})
		},
		// 修改
		handleAdjust(row, index) {
			this.currentEditData = row
			this.$set(this.tableData[index], 'isEditing', true)
		},
		// 本次指针、本次水量验证函数
		validFn(key, index) {
			return (rule, value, callback) => {
				if (!specialCheckStatus.includes(this.tableData[index].checkStatus) && isBlank(value)) {
					callback(new Error(`请输入本次${key === 'curMeterReading' ? '指针' : '水量'}`))
				} else {
					callback()
				}
			}
		},
		// 抄表情况验证函数
		checkStatusValidtor(index) {
			return async (rule, value, callback) => {
				if (value && specialCheckStatus.includes(value)) {
					this.$refs[`curMeterReadingFormRef${index}`].clearValidate()
					this.$refs[`useAmountFormRef${index}`].clearValidate()
					callback()
				} else {
					const promise1 = new Promise(resolve => {
						this.$refs[`curMeterReadingFormRef${index}`].validate(valid => {
							resolve(valid)
						})
					})
					const promise2 = new Promise(resolve => {
						this.$refs[`useAmountFormRef${index}`].validate(valid => {
							resolve(valid)
						})
					})
					const valids = await Promise.all([promise1, promise2])
					if (!valids.includes(false)) {
						callback()
					}
				}
			}
		},
		// 修改保存
		async handleAdjustSave(row, index) {
			const form1Promise = new Promise(resolve => {
				this.$refs[`curMeterReadingFormRef${index}`].validate(valid => {
					resolve(valid)
				})
			})
			const form2Promise = new Promise(resolve => {
				this.$refs[`useAmountFormRef${index}`].validate(valid => {
					resolve(valid)
				})
			})
			const form3Promise = new Promise(resolve => {
				this.$refs[`checkStatusFormRef${index}`].validate(valid => {
					resolve(valid)
				})
			})

			const valids = await Promise.all([form1Promise, form2Promise, form3Promise])
			if (!valids.includes(false)) {
				const {
					meterReadingRecordId,
					checkStatus,
					curMeterReading,
					useAmount,
					meterReadingStaffId,
					imageUrl,
					thisRecordDate,
					lastMeterReading,
					lastRecordDate,
				} = row
				await updateMeterReadingRecord2({
					meterReadingRecordId,
					checkStatus,
					curMeterReading,
					useAmount,
					meterReadingStaffId,
					imageUrl,
					thisRecordDate,
					taskYear: this.topParams.taskYear,
					lastMeterReading,
					lastRecordDate,
					addModifyLog: true,
				})
				this.$message.success('修改成功')
				this.getList(1)
			}
		},
		handleAdjustCancel(index) {
			this.tableData.splice(index, 1, {
				...this.currentEditData,
				isEditing: false,
			})
			this.currentEditData = {}
		},
		judgeBlank(val) {
			return isBlank(val)
		},
		// 选中所有页
		handleCheckedAllPage(value) {
			if (value) {
				this.tableData.forEach(item => {
					this.$refs.gcTableRef.toggleRowSelectionUseInnerData(copyTableData => {
						return copyTableData.find(row => row.meterReadingRecordId === item.meterReadingRecordId)
					}, true)
				})
			} else {
				this.$refs.gcTableRef.clearCheckTableSelection()
			}
		},
		// 获取选中记录数据
		getTaskIdsAndRecordIdsMap(isBatch, row) {
			let taskIdsAndRecordIdsMap = {}
			if (isBatch) {
				if (!this.checkedAllPage) {
					this.selectedData.forEach(item => {
						if (taskIdsAndRecordIdsMap[item.meterReadingTaskId]) {
							taskIdsAndRecordIdsMap[item.meterReadingTaskId].push(item.meterReadingRecordId)
						} else {
							taskIdsAndRecordIdsMap[item.meterReadingTaskId] = [item.meterReadingRecordId]
						}
					})
				}
			} else {
				taskIdsAndRecordIdsMap[row.meterReadingTaskId] = [row.meterReadingRecordId]
			}
			return taskIdsAndRecordIdsMap
		},
		// 批量通过且账单开账
		handlePassAndCreateBill() {
			this.$confirm('确定复核通过且账单开账吗?').then(async () => {
				const taskIdsAndRecordIdsMap = this.getTaskIdsAndRecordIdsMap(true)
				try {
					const data = await asyncBatchReviewPassAndCreateBill({
						type: this.type,
						createBill: true,
						reviewPass: true,
						...this.formData,
						...this.topParams,
						taskIdsAndRecordIdsMap,
					})
					this.handleBatchReviewResult(data, true)
					this.processResult = {}
					this.processDialogShow = true
					this.canClosedDialog = false
				} catch (e) {
					this.$message.error('批量复核通过且账单开账失败')
				}
			})
		},
		// 查询审批进度
		async handleBatchReviewResult(syncId, isOpen) {
			try {
				// 调用查询进度接口
				const data = await getBatchReviewResult({ syncId })
				this.processResult = data || {}
				if (data.processRate == 100) {
					clearInterval(this.progressInterval)
					this.canClosedDialog = true
					if (!data.failMsg || !data.failMsg.length) {
						this.checkedAllPage = false
						this.$message.success(`批量复核通过${isOpen ? '且账单开账' : ''}成功`)
						this.getList(1)
					} else {
						this.$message.error(`批量复核通过${isOpen ? '且账单开账' : ''}失败`)
					}
				} else {
					this.progressInterval = setTimeout(() => this.handleBatchReviewResult(syncId, isOpen), 3000)
				}
			} catch (error) {
				this.canClosedDialog = true
				console.error('查询审批进度失败', error)
				clearInterval(this.progressInterval)
			}
		},
		// 计算用水量
		// 抄表复核页面在调整下
		// 1) 本次指针输入后， 用量 自动计算 ，用量统一=本次指针 - 上次
		// 2）用量输入框： 允许调整，调整后，不用再次计算
		handleCalcUseAmt(index) {
			const data = this.tableData[index]
			if (data) {
				const { curMeterReading, lastMeterReading } = data
				const methods = CHECK_STATUS_CALC_METHODS.default
				const { useAmount } = methods({
					curMeterReading,
					lastMeterReading,
				})
				data.useAmount = useAmount
			}
		},
	},
	beforeDestroy() {
		this.progressInterval && clearInterval(this.progressInterval)
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	flex: 1;
	height: 0;
	display: flex;
	flex-direction: column;
	padding: 20px;
}
.container-search {
	display: flex;
	justify-content: space-between;
}
.table-container {
	flex: 1;
	height: 0;
}

.input-number {
	width: 100%;
	::v-deep {
		.el-input__inner {
			text-align: left;
		}
	}
}
.table-form {
	height: 100%;
	::v-deep {
		.el-form-item {
			margin: 12px 0;
		}
		.el-form-item__error {
			padding-top: 0;
		}
	}
}
.select-all {
	margin-right: 10px;
}
.custom-box {
	span {
		color: #4e4e4e;
	}
}
</style>
