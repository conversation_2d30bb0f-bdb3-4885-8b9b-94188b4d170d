<template>
	<GcElDialog
		:show="isShow"
		title="分表列表"
		custom-top="120px"
		width="1000px"
		:showFooter="false"
		@close="handleClose"
	>
		<el-input v-model="archivesIdentity" class="search-container" style="width: 250px" placeholder="请输入表卡编号">
			<el-button slot="append" icon="el-icon-search" @click="handlePageChange({ page: 1 })"></el-button>
		</el-input>
		<div class="table-container">
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handlePageChange"
			></GcTable>
		</div>
	</GcElDialog>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiGetListSubArchivesRecord2 } from '@/api/meterManage.api.js'
import { getColumn } from './tableColumn'
export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		dmaArchivesId: {
			type: [Number, String],
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	watch: {
		show: {
			handler(val) {
				if (val) {
					this.getList()
				}
			},
		},
	},
	data() {
		return {
			archivesIdentity: '',
			columns: getColumn(this),
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	methods: {
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(
					removeNullParams({
						archivesIdentity: this.archivesIdentity,
						dmaArchivesId: this.dmaArchivesId,
					}),
				)
				Object.assign(formParams, {
					current,
					size,
				})
				const { total = 0, records = [] } = await apiGetListSubArchivesRecord2(formParams)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleClose() {
			this.isShow = false
			this.archivesIdentity = ''
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.search-container {
	margin-bottom: 10px;
}
.table-container {
	height: 400px;
}
</style>
