<template>
	<gc-el-dialog :show="isShow" title="选择用户" custom-top="120px" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { queryRoleStaff, addRoleUsers } from '@/api/statisticsManage.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				roleId: '',
				roleName: '',
				userIds: [],
			},
			formItems: [
				{
					type: 'el-input',
					label: '角色名称',
					prop: 'roleName',
					attrs: {
						col: 24,
						disabled: true,
						placeholder: ' ',
					},
				},
				{
					type: 'el-select',
					label: '用户',
					prop: 'userIds',
					options: [],
					attrs: {
						col: 24,
						multiple: true,
						clearable: true,
						filterable: true,
						placeholder: '请选择用户',
					},
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					roleName: [{ required: true, message: '角色不能为空', trigger: 'change' }],
					userIds: [{ required: true, message: '请选择用户', trigger: 'change' }],
				},
			},
		}
	},
	methods: {
		// 获取用户数据
		async getUsers() {
			try {
				const data = await queryRoleStaff({ roleId: this.formData.roleId })
				this.formItems[1].options = data.map(item => {
					return {
						label: item.staffName,
						value: item.staffId,
					}
				})
			} catch (error) {
				console.error(error)
				this.formItems[1].options = []
			}
		},

		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				await addRoleUsers({
					roleId: this.formData.roleId,
					userIds: this.formData.userIds,
				})
				this.$message.success(`用户绑定成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
.el-cascader {
	width: 100%;
}
::v-deep {
	.el-dialog__body {
		height: 240px;
	}
}
</style>
