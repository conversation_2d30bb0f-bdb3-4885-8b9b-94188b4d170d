export default function (props) {
	return {
		tooltip: {
			trigger: 'axis',
			formatter: params => {
				const unit = props.yAxis.name || ''
				let str = params[0]?.name || ''
				str += '<br/>'
				str += params.map(item => `${item.marker}${item.seriesName}：${item.value}${unit}`).join('<br/>')
				return str
			},
			...props.tooltip,
		},
		grid: {
			left: 60,
			right: 20,
			top: 40,
			bottom: 80,
			...props.grid,
		},
		dataZoom: [
			{
				show: false,
				height: 12,
				xAxisIndex: [0],
				bottom: '8%',
				handleIcon:
					'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
				handleSize: '110%',
				handleStyle: {
					color: '#d3dee5',
				},
				textStyle: {
					color: '#fff',
				},
				borderColor: '#90979c',
				...props.dataZoom,
			},
			{
				type: 'inside',
				show: true,
				height: 15,
				start: 1,
				end: 35,
			},
		],
		legend: {
			selectedMode: false,
			...props.legend,
		},
		xAxis: {
			type: 'category',
			data: [],
			...props.xAxis,
		},
		yAxis: {
			type: 'value',
			nameTextStyle: {
				padding: [0, 0, 0, -30], //间距分别是 上 右 下 左
			},
			...props.yAxis,
		},
		series: props.seriesData,
	}
}
