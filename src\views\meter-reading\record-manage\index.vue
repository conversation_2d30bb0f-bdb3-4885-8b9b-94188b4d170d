<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-02 19:07:24
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 13:14:54
-->
<template>
	<div class="container">
		<Left @reset="handleReset" @search="handleSearch" />
		<Right ref="rightRef" :params="params" />
	</div>
</template>

<script>
import Left from './left/left.vue'
import Right from './right/right.vue'
export default {
	name: '',
	components: { Left, Right },
	data() {
		return {
			params: {},
		}
	},
	computed: {},
	created() {},
	methods: {
		handleReset() {
			this.$refs.rightRef.resetTableData()
		},
		handleSearch(params) {
			this.params = params
			this.$nextTick(() => {
				this.$refs.rightRef.getList(1, true)
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	height: 100%;
}
</style>
