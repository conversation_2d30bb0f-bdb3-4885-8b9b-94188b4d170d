<template>
	<GcElDialog
		:show="isShow"
		:title="`${typeText}部门`"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
	</GcElDialog>
</template>

<script>
import { getDepartmentFormItems } from './formItems.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { ruleRequired, ruleMaxLength, RULE_PHONE } from '@/utils/rules.js'
import { apiGetRegion } from '@/api/addressManage.api.js'
import { apiAddDepartment, apiUpdateDepartment, apiQueryOrgMap } from '@/api/organizationManage.api.js'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		editType: {
			type: String,
			default: 'add',
		},
		editData: {
			type: Object,
			default: () => {},
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		typeText() {
			return this.editType === 'add' ? '新建' : '编辑'
		},
	},
	watch: {
		isShow: {
			handler(val) {
				if (val) {
					this.formItems = getDepartmentFormItems(this)
					this._getAddressData(0, 'province')
					this._apiQueryOrgMap()
					if (this.editType === 'edit' && Object.keys(this.editData).length) {
						const { province } = this.editData
						this.assignForm(this.editData)
						if (province) {
							this._getAddressData(province, 'city')
						}
					} else {
						this.$nextTick(() => {
							this.$refs.formRef.resetForm()
							this.$refs.formRef.clearValidate()
						})
					}
				}
			},
			deep: true,
		},
	},
	data() {
		return {
			formData: {
				department_name: '',
				department_code: '',
				principal: '',
				phone: '',
				province: '',
				city: '',
				location: '',
			},
			formAttrs: {
				displayItem: 'block',
				labelPosition: 'top',
				rules: {
					department_name: [ruleRequired('必填'), ruleMaxLength(20)],
					department_code: [ruleRequired('必填')],
					principal: [ruleRequired('必填'), ruleMaxLength(24)],
					phone: [ruleRequired('必填'), RULE_PHONE],
					province: [ruleRequired('必填')],
					city: [ruleRequired('必填')],
					location: [ruleMaxLength(32)],
				},
			},
			formItems: [],
		}
	},
	methods: {
		// 获取省市
		async _getAddressData(value, key) {
			const { records } = await apiGetRegion({ regionCode: value })
			const obj = this.formItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = records.map(item => {
				return {
					value: item.regionCode,
					label: item.regionName,
				}
			})
		},
		// 获取所属组织
		async _apiQueryOrgMap() {
			const code = this.editType === 'add' ? this.editData.org_code : this.editData.parent_org_code
			try {
				const res = await apiQueryOrgMap({
					orgCode: code,
				})
				const obj = this.formItems.find(item => item.prop === 'department_code')
				if (!obj) return
				obj.options = res.map(item => {
					return {
						value: item.org_code,
						label: item.name,
					}
				})
			} catch (error) {
				console.log(error)
			}
		},
		// 设置表单数据
		assignForm(obj) {
			const keys = Object.keys(this.formData)
			keys.forEach(key => {
				if (obj && Object.prototype.hasOwnProperty.call(obj, key)) {
					if (key === 'department_code') {
						this.formData[key] = this.editData.parent_org_code
					} else {
						this.formData[key] = obj[key] + ''
					}
				}
			})
		},
		handleClose() {
			this.$refs.formRef.resetForm()
			this.isShow = false
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				const formParams = trimParams(removeNullParams(this.formData))

				try {
					if (this.editType === 'add') {
						Object.assign(formParams, {
							parent_org_code: this.editData.tree_code,
						})
						await apiAddDepartment(formParams)
					} else {
						delete formParams.department_code
						Object.assign(formParams, {
							id: this.editData.id,
						})
						await apiUpdateDepartment(formParams)
					}
					this.$message.success(`${this.typeText}成功`)
					this.$emit('success')
					this.isShow = false
				} catch (error) {
					console.log(error)
				}
			}
		},
	},
}
</script>
