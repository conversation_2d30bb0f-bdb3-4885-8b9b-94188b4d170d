<template>
	<el-drawer
		append-to-body
		custom-class="gc-drawer"
		direction="rtl"
		size="280px"
		title="框架配置"
		:visible.sync="drawerVisible"
	>
		<div class="el-drawer__body">
			<el-form ref="form" label-position="left" :model="theme">
				<el-form-item label="布局">
					<el-select v-model="theme.layout" :disabled="device === 'mobile'" filterable>
						<el-option key="column" label="分栏" value="column" />
						<el-option key="horizontal" label="横向" value="horizontal" />
					</el-select>
				</el-form-item>
				<el-form-item label="主题">
					<el-select v-model="theme.themeName" filterable @change="setTheme">
						<el-option key="default" label="默认" value="default" />
						<el-option key="white" label="纯白" value="white" />
					</el-select>
				</el-form-item>
			</el-form>
		</div>
		<div class="el-drawer__footer">
			<el-button type="primary" @click="handleSaveTheme">保存</el-button>
			<el-button @click="setDefaultTheme">恢复默认</el-button>
		</div>
	</el-drawer>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'

export default {
	name: 'GcThemeDrawer',
	data() {
		return {
			drawerVisible: false,
		}
	},
	computed: {
		...mapGetters({
			theme: 'settings/theme',
			device: 'settings/device',
		}),
	},
	created() {
		this.$baseEventBus.$on('theme', () => {
			this.handleOpenTheme()
		})
		this.setTheme()
	},
	methods: {
		...mapActions({
			saveTheme: 'settings/saveTheme',
			resetTheme: 'settings/resetTheme',
		}),
		handleOpenTheme() {
			this.drawerVisible = true
		},
		async setDefaultTheme() {
			await this.resetTheme()
			this.drawerVisible = false
		},
		async handleSaveTheme() {
			await this.saveTheme()
			this.drawerVisible = false
		},
		setTheme() {
			document.getElementsByTagName('body')[0].className = `gc-theme-${this.theme.themeName}`
		},
	},
}
</script>

<style lang="scss" scoped>
.theme-scrollbar {
	height: calc(100vh - 118px) !important;
	overflow: hidden;
}
</style>
<style lang="scss">
.gc-drawer {
	.el-drawer__header {
		margin-bottom: $base-margin;
	}

	.el-drawer__body {
		padding: 0 $base-padding/2 $base-padding/2 $base-padding/2;

		.el-divider--horizontal {
			margin: 20px 0 20px 0;
		}

		.el-form-item {
			display: flex;
			align-items: center;

			.el-form-item__label {
				flex: 1 1;
			}

			.el-form-item__content {
				flex: 0 0 auto;
			}
		}

		.el-form-item--small.el-form-item {
			.el-input__inner {
				width: 115px;
			}
		}
	}

	.el-drawer__footer {
		padding: $base-padding/2;
		border-top: 1px solid $base-border-color;
	}
}
</style>
