<template>
	<GcElDialog
		:show="isShow"
		title="修改地址"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
	</GcElDialog>
</template>

<script>
import { ruleRequired, ruleMaxLength } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getFormItems } from './formItem'
import { apiGetRegion, apiGetAddressAreaMap, apiUpdateAddress } from '@/api/addressManage.api.js'

export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		data: {
			type: Object,
			default: () => {},
		},
	},
	watch: {
		show(val) {
			if (val) {
				this.formItems = getFormItems(this)
				this.$nextTick(() => {
					this.$refs.formRef.$refs.formRef.clearValidate()
				})
				this.assignForm(this.data)
				this._getCityOriRegionData(21, 'cityCode')
				this.data.cityCode && this._getCityOriRegionData(this.data.cityCode, 'regionCode')
				this.data.regionCode && this._getAddressAreaMap(this.data.regionCode, 'streetCode')
				this.data.streetCode && this._getAddressAreaMap(this.data.streetCode, 'neighbourhoodCode')
				this.data.neighbourhoodCode && this._getAddressAreaMap(this.data.neighbourhoodCode, 'buildingCode')
			}
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {},
			formItems: [],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					cityCode: [ruleRequired('必填')],
					regionCode: [ruleRequired('必填')],
					streetCode: [ruleRequired('必填')],
					addressName: [ruleRequired('必填'), ruleMaxLength(64)],
				},
			},
		}
	},
	methods: {
		assignForm(obj) {
			const keys = Object.keys(this.formData)

			keys.forEach(key => {
				if (obj && Object.prototype.hasOwnProperty.call(obj, key)) {
					this.formData[key] = obj[key]
				}
			})
		},
		// 获取市、区县数据  key:cityCode、regionCode
		async _getCityOriRegionData(value, key) {
			const { records } = await apiGetRegion({ regionCode: value })
			const obj = this.formItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = records.map(item => {
				return {
					value: item.regionCode,
					label: item.regionName,
				}
			})
		},
		// 获取街道、小区、楼栋数据  key:streetCode, neighbourhoodCode, buildingCode
		async _getAddressAreaMap(value, key) {
			try {
				const data = await apiGetAddressAreaMap({
					parentCode: value,
				})
				const obj = this.formItems.find(item => item.prop === key)
				if (!obj) return
				obj.options = data.map(item => {
					let label = item.addressAreaName
					if (key === 'buildingCode') {
						label = label + item.unit
					}
					return {
						value: item.addressAreaCode,
						label,
					}
				})
			} catch (error) {
				console.log(error)
			}
		},
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				try {
					const formParams = trimParams(removeNullParams(this.formData))
					Object.assign(formParams, {
						addressId: this.data.addressId,
					})

					await apiUpdateAddress(formParams)
					this.$message.success('修改成功')
					this.$emit('success')
					this.isShow = false
				} catch (error) {
					console.log(error)
				}
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
	},
}
</script>
<style lang="scss" scoped>
.el-tag {
	margin-right: 10px;
}
</style>
