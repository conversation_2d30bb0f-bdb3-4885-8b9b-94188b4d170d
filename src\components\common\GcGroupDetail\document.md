<gc-group-detail :data="">
  <template v-slot:foot>
    <el-button icon="el-icon-search" circle></el-button>
  </template>
</gc-group-detail>

data: {
  title: "标题名称", // 不传则不显示标题
  row: 5, // 每行展示列数， 默认展示3行
  list: [
    {
      key: "姓名",
      value: "金卡服务",
    },
    {
      key: "测试",
      value: "金卡服务123金卡服务123金卡服务123金123金卡服务123金卡服务123金卡服务123",
      isEllipsis: true, // 超出是否显示省略号，默认false
      col: 4, // 此数据需占列数，默认1列
    },
    {
      key: "测试",
      value: "金卡服务123",
      slot: "foot", // 自定义slot名称
    },
    {
      key: "测试",
      value: "测试服务",
    },
    {
      key: "测试",
      value: "测试服务",
    },
    {
      key: "测试",
      value: "测试服务",
    },
    {
      key: "测试",
      value: "测试服务",
    },
  ],
};