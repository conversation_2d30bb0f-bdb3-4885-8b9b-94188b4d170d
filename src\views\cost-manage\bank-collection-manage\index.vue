<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />

			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div class="right-top">
				<el-button
					v-has="'billing_bankCollection_createBankSendFile'"
					type="primary"
					@click="createBankSendFile"
				>
					生成送盘文件
				</el-button>
			</div>
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:operate="{ row }">
					<el-button
						v-if="row.status !== sendFileStatusEnum.已取消 && row.status !== sendFileStatusEnum.生成中"
						type="text"
						@click="gotoBillDetail(row)"
					>
						查看账单
					</el-button>
					<el-button
						v-has="'billing_bankCollection_exportBankSendFile'"
						v-if="row.status > 2"
						type="text"
						@click="bankExportSendFile(row)"
					>
						导出送盘文件
					</el-button>
					<el-button
						v-has="'billing_bankCollection_importBankReturnFile'"
						v-if="row.status === sendFileStatusEnum.已生成"
						type="text"
						@click="importBankReturnFile(row)"
					>
						导入回盘文件
					</el-button>
					<el-button
						v-if="row.status === sendFileStatusEnum.已回盘"
						v-has="'billing_bankCollection_complete'"
						type="text"
						@click="completeBankCollection(row)"
					>
						完成对账
					</el-button>
					<el-button
						v-has="'billing_bankCollection_cancelBankSendFile'"
						v-if="row.status === sendFileStatusEnum.生成失败 || row.status === sendFileStatusEnum.已生成"
						type="text"
						@click="cancelBankSendFile(row)"
					>
						取消
					</el-button>
				</template>
			</GcTable>
		</div>
		<!-- 文件 -->
		<FileDialog :show.sync="showDialog" :rowData="currentRow" :tab="tab" />
	</div>
</template>

<script>
import { getColumn } from './tableColumn'
import { getFormItems } from './formItem.js'
import { ruleRequired } from '@/utils/rules'
import { removeNullParams, trimParams } from '@/utils/index.js'
import FileDialog from './components/FileDialog.vue'
import { cancelBankSendFile, completeBankCollection, queryBankSendFile, bankExportSendFile } from '@/api/costManage.api'
import { sendFileStatusEnum } from '@/consts/enums'

export default {
	name: 'BankCollectionManage',
	components: {
		FileDialog,
	},
	data() {
		this.sendFileStatusEnum = sendFileStatusEnum
		return {
			// 左侧查询
			formData: {
				orgCode: '',
				channel: '',
				fileMonth: '',
				sendFileNo: '',
			},
			columns: getColumn(this),
			formItems: getFormItems(this),
			formAttrs: {
				rules: {
					orgCode: [ruleRequired('请选择营业分公司', 'change')],
				},
			},
			// 右侧列表
			loading: false,
			tableData: [{}],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			showDialog: false,
			tab: null,
			currentRow: null,
		}
	},
	mounted() {
		const defaultOrgCode = this.$store.getters.orgList[0].value
		if (defaultOrgCode) {
			this.formData.orgCode = defaultOrgCode
			this.getList()
		}
	},
	methods: {
		async getList() {
			this.loading = true
			this.tableData = []
			this.pageData.total = 0
			try {
				const { current, size } = this.pageData
				const params = trimParams(removeNullParams(this.formData))
				Object.assign(params, {
					current,
					size,
				})
				const { total = 0, records = [] } = await queryBankSendFile(params)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		gotoBillDetail(row) {
			this.$router.push({
				name: 'BankCollectionChargebackRecords',
				query: {
					sendFileNo: row.sendFileNo,
				},
			})
		},
		handleReset() {
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.$refs.formRef.resetFields()
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.handlePageChange({ page: 1 })
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		createBankSendFile() {
			this.tab = 'create'
			this.showDialog = true
		},
		completeBankCollection(row) {
			this.$confirm('完成对账后，将作为结账处理，结账后不能进行手动销帐。', '完成对账确认').then(() => {
				completeBankCollection({
					sendFileId: row.sendFileId,
				})
					.then(() => {
						this.$message.success('完成对账成功！')
						this.getList()
					})
					.catch(e => {
						this.$message.error(e.message || '完成对账失败！')
					})
			})
		},
		bankExportSendFile(row) {
			bankExportSendFile(
				{
					sendFileId: row.sendFileId,
				},
				row.sendFileNo + '.txt',
			)
				.then(() => {
					this.$message.success('导出送盘文件成功！')
				})
				.catch(e => {
					this.$message.error(e.message || '导出送盘文件失败！')
				})
		},
		cancelBankSendFile(row) {
			this.$confirm('请谨慎取消, 取消送盘文件后, 将影响回盘文件无法对账', '确认要取消这份送盘文件吗？').then(
				() => {
					cancelBankSendFile({
						sendFileId: row.sendFileId,
					})
						.then(() => {
							this.$message.success('取消送盘文件成功！')
							this.getList()
						})
						.catch(e => {
							this.$message.error(e.message || '取消送盘文件失败！')
						})
				},
			)
		},
		importBankReturnFile(row) {
			this.tab = 'import'
			this.showDialog = true
			this.currentRow = row
		},
	},
}
</script>

<style lang="scss" scoped>
.right-top {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-bottom: 12px;
}
</style>
