import { apiGetCodeImg, apiGetNeedVerify } from '@/api/user.api'
import {
	ruleRequired,
	ruleMaxLength,
	// eslint-disable-next-line
	ruleComplexPassValidate,
} from '@/utils/rules'
import UpdatePwd from '@/layout/components/GcAvatar/updatePass.vue'
import { clearIcLS } from '@/utils/storage.js'

const DEBUG = false
export default {
	data() {
		return {
			codeTimer: null,
			loginForm: {
				username: '', //tenantZBadmin
				password: '', // 123456
				dynamicCode: '',
				codeKey: '',
			},
			loginRules: {
				username: [ruleRequired('账号不能为空'), ruleMaxLength(32)],
				password: [ruleRequired('密码不能为空'), ruleMaxLength(18)],
				dynamicCode: [ruleRequired('登录令牌不能为空'), ruleMaxLength(20)],
			},
			showPwdEye: false,
			passwordType: 'password',
			loading: false,
			showDialog: false,
			codeImg: '',
			needCertify: false, // 租户登录是否需要证书验证。个别租户首次登录需要验证

			btnText: '立即登录',
			certifyOrgName: '', // 认证机构名称，needCertify时才显示
			timer: null,
			isOmLogin: false,
			loginErrTimer: null,
			loginDisabled: false,
		}
	},
	computed: {
		userInfo() {
			return this.$store.getters.userInfo
		},
		showPwdDialog() {
			return this.$store.state.user.showPwdDialog
		},
	},
	components: { UpdatePwd },
	created() {
		this.isOmLogin = this.$route.path === '/om-login'
		this.btnText = this.isOmLogin ? '跨域运维登录' : '立即登录'
		this.loadCodeImg()
	},
	mounted() {
		const subDomain =
			process.env.NODE_ENV == 'development' ? process.env.VUE_APP_DOMAIN : location.host.split('.')[0]
		window.onload = () => {
			if (subDomain !== 'manage') {
				this.getIsNeedCertify(subDomain)
			}
		}
	},
	methods: {
		// ----------------- 事件 -----------------
		handlePwdChange() {
			this.loginForm.password = this.loginForm.password.replace(/[\u4e00-\u9fa5]/g, '')
		},
		handleDialogClose() {
			this.$store.commit('user/SET_PWD_DIALOG', false)
		},
		handlePwdUpdated() {
			this.$store.dispatch('user/handlePwdUpdated').then(() => {
				this.getSysBasicInfo()
			})
		},
		getIsNeedCertify(subdomain) {
			apiGetNeedVerify(subdomain)
				.then(res => {
					this.needCertify = res.is_enabled
					if (this.needCertify) {
						// this.loading = true;
						// this.btnText = "正在认证...";
						window.postMessage(
							{
								type: 'FROM_PAGE',
								method: 'connect',
								text: { connect: 'true' },
							},
							'*',
						)
						window.postMessage({ type: 'FROM_PAGE', method: 'send', text: { cmd: 'preLoad' } }, '*')

						window.addEventListener('message', this.setCertifyCode, false)
					}
				})
				.catch(() => {})
		},
		setCertifyCode(event) {
			if (event.data.type && event.data.type == 'FROM_CS') {
				let response = event.data.text
				if (response.orgName) {
					this.certifyOrgName = response.orgName
				}
				if (response.key && response.request.rnd) {
					window.signCode = response
					this.loading = false
					this.btnText = '正在登录'
					this.handleLogin()
				}
			}
		},
		generateCertifyCode() {
			let time = parseInt(+new Date() / 1000)
			// 随机生成6位数，用于签名
			let rnd = parseInt((Math.random() * 9 + 1) * 1000000)
			return { cmd: 'signLogin', time, rnd }
		},
		loadCodeImg() {
			apiGetCodeImg({
				// width: "100",
				// height: "40",
				color: 'white',
				// size: "29",
				from: '79,112,178',
				to: '79,112,178',
			})
				.then(res => {
					this.codeImg = res.codeImage
					this.loginForm.codeKey = res.codeKey
				})
				.catch(res => {
					this.$message.error('获取验证码失败！')
					console.warn(res)
				})
		},
		showPwd() {
			if (this.passwordType === 'password') {
				this.passwordType = ''
			} else {
				this.passwordType = 'password'
			}
			this.$nextTick(() => {
				this.$refs.password.focus()
			})
		},
		handleLoginClick() {
			this.$refs.loginForm.validate(valid => {
				if (valid) {
					if (this.needCertify) {
						this.btnText = '正在认证...'
						window.postMessage(
							{
								type: 'FROM_PAGE',
								method: 'send',
								text: this.generateCertifyCode(),
							},
							'*',
						)
						if (this.timer) this.timer = null
						this.timer = setTimeout(() => {
							const signCode = window.signCode || {}
							if (!signCode.key) {
								this.loading = false
								this.btnText = '正在登录'
								this.$message.error('未获取到认证机构信息，请刷新再试')
							}
						}, 6000)
					} else {
						this.handleLogin()
					}
				}
			})
		},
		handleLogin() {
			const signCode = window.signCode || {}
			let loginHeaders = {}
			if (this.needCertify && !signCode.key) {
				this.$message.error('未获取到认证机构信息，请刷新再试')
				return
			} else if (this.needCertify) {
				loginHeaders = {
					'gcv-time': signCode.request.time,
					'gcv-rnd': signCode.request.rnd,
					// gcv_cert:signCode.cert,
					'gcv-sign': signCode.sign,
					'gcv-key': signCode.key,
				}
			}

			if (this.loginDisabled) return
			this.loading = true
			this.loginDisabled = true
			this.$store
				.dispatch('user/login', {
					isOmLogin: this.isOmLogin,
					loginForm: this.loginForm,
					loginCertify: loginHeaders,
				})
				.then(res => this.handleLoginSuccess(res))
				.catch(err => this.handleLoginErr(err))
				.finally(() => {
					this.loading = false
				})
		},
		handleLoginSuccess(res) {
			this.loginDisabled = false
			this.btnText = this.isOmLogin ? '跨域运维登录' : '立即登录'
			const { pwStrength } = res // pwStrength 1:high 2:weak
			if (DEBUG || pwStrength == 2) {
				this.$message({
					message: '密码强度较低，为了您的账户安全，请先修改密码',
					type: 'error',
					duration: 1000,
					onClose: () => {
						this.$store.dispatch('user/emitUpdatePwd')
					},
				})
			} else {
				this.getSysBasicInfo()
			}
		},
		getSysBasicInfo() {
			// 根据权限映射路由表
			this.$store.dispatch('routes/generateRoutes', this.$store.getters.permissions).then(() => {
				this.$router.push({
					path: '/',
				})
			})

			// 静默获取数据字典
			const { realm } = this.userInfo
			this.$store.dispatch('dataList/getDataList', {
				// 获取数据字典增加租户类型区分
				realm,
				tenantType: realm,
			})
			// 营业分公司数据map获取
			this.$store.dispatch('dataList/getOrgList')

			const { userLevel, isAdmin, isTenantAdmin, isCrossDomain, tenantId, orgId, roleId } = this.userInfo

			const sysUI = userLevel == 0 || (userLevel != 0 && tenantId == 0)
			// FEAT 3.7.0取消跨域运维角色
			if (!sysUI && !isAdmin && !isTenantAdmin && !isCrossDomain && orgId) {
				this.$store.dispatch('user/getTenantDetail').then(res => this.reGenerateRoutes(res))
				this.$store.dispatch('user/getOrgDetail')
			} else if (userLevel == 1 && (isTenantAdmin || roleId == 3)) {
				this.$store.dispatch('user/getTenantDetail').then(res => this.reGenerateRoutes(res))
			}
		},
		reGenerateRoutes(res) {
			const { business_config } = res
			if (business_config) {
				// 消息业务列表页面 需 租户开通微信和短信才能显示
				this.$store.dispatch('routes/reGenerateRoutes', business_config)
			}
			// 请求当前用户的发票配置信息
			// this.$store.dispatch("archives/getInvoiceSet");
		},
		handleLoginErr(err) {
			console.error('login error: ', err)
			const { code } = err

			if (code == '10117') {
				let count = 60
				if (this.loginErrTimer) clearInterval(this.loginErrTimer)
				this.loginErrTimer = setInterval(() => {
					if (count < 1) {
						this.btnText = this.isOmLogin ? '跨域运维登录' : '立即登录'
						this.loginDisabled = false
						clearInterval(this.loginErrTimer)
					} else {
						this.btnText = count-- + 's后重试'
					}
				}, 1000)
			} else {
				this.loginDisabled = false
				this.$message.error(err.message || '登录失败-调用接口失败')
			}
			// this.loadCodeImg()
		},
	},
	activated() {
		// 清空本地IC卡存储（icReadData_xx）
		clearIcLS()
	},
	beforeDestroy() {
		this.timer = null
		if (this.codeTimer) {
			clearInterval(this.codeTimer)
		}
	},
}
