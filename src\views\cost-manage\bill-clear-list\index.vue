<template>
	<div class="page-layout">
		<div class="page-left">
			<el-tabs v-model="activeTab" type="border-card" @tab-click="handleChangeTab">
				<el-tab-pane v-for="(tab, index) in tabs" :key="index" :label="tab.label" :name="tab.name">
					<div class="left-search-container">
						<GcSearchFake
							@handleclick="handleClickFakeSearch"
							@clear="clearFormComponent"
							:valueObj="valueObj"
						></GcSearchFake>
						<!-- 弹窗 -->
						<GcSearchDialogNew
							title="搜索"
							width="500px"
							:dialogVisible.sync="showSearchPopou"
							:search-condition="searchCondition"
							:active-condition.sync="activeCondition"
							:selected-condition="selectedCondition"
							:valueObj="valueObj"
							:append-to-body="true"
							@delete-one="deleteOne"
							@empty="deleteAll"
							@dialog-close="closeDialog"
							@public-search="searchTable"
						/>
					</div>
					<GcFormSimple
						:ref="'formRef' + index"
						v-model="formData"
						:formItems="formItems"
						:formAttrs="formAttrs"
					/>
				</el-tab-pane>
			</el-tabs>
			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div class="btn-container">
				<div class="btn-container-left">
					<el-button type="primary" @click="$router.push('/costManage/counterPaymentList')">
						查看销账记录
					</el-button>
					<el-button
						v-has="'billing_bill_account-clear-batch'"
						type="primary"
						:disabled="isPayButtonDisabled"
						@click="handleBatch"
					>
						批量销账
					</el-button>
				</div>
				<div class="btn-container-right">
					<el-button
						v-has="'billing_bill_account-clear-list_export_excel'"
						type="primary"
						:disabled="isExportButtonDisabled"
						@click="handleExportButtonClick"
					>
						导出
					</el-button>
				</div>
			</div>
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				:selectable="selectable"
				needType="selection"
				@selectChange="selectChange"
				@current-page-change="handleChangePage"
			/>
		</div>
	</div>
</template>

<script>
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams, exportBlob } from '@/utils/index.js'
import { accAdd, accSub } from '@/utils/calc.js'
import { getColumn } from './tableColumn'
import {
	apiGetAccountClearList,
	apiClearAccount,
	apiBillClearBatch,
	apiExportAccountClearList,
} from '@/api/costManage.api'
export default {
	data() {
		return {
			tabs: [
				{
					label: '居民',
					name: 'resident',
				},
				{
					label: '企业',
					name: 'company',
				},
			],
			activeTab: 'resident',
			formData: {
				orgCode: '',
				billDate: '',
				clear: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
				},
				{
					type: 'el-date-picker',
					label: '账期',
					prop: 'billDate',
					attrs: {
						type: 'month',
						clearable: true,
						valueFormat: 'yyyy-MM',
						pickerOptions: {
							disabledDate(time) {
								const currentDate = new Date() // 当前日期
								const maxYear = currentDate.getFullYear() + 1 // 当前年份加一年
								const maxDate = new Date(maxYear, 11, 31) // 当前年份加一年的最后一天（12月31日）

								return (
									time.getFullYear() > maxYear ||
									(time.getFullYear() === maxYear && time.getMonth() > maxDate.getMonth())
								)
							},
						},
					},
				},
				{
					type: 'el-select',
					label: '是否销账',
					prop: 'clear',
					options: [
						{
							label: '全部',
							value: '',
						},
						{
							label: '不可销账',
							value: 0,
						},
						{
							label: '可销账',
							value: 1,
						},
					],
				},
			],
			formAttrs: {
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
					// 林彬要求，账期修改为非必填
					// billDate: [ruleRequired('请选择账期')],
				},
			},
			valueObj: {
				value: null,
			},
			showSearchPopou: false,
			residentSearchCondition: [
				{
					key: 'archivesIdentity',
					label: '表卡编号',
				},
				{
					key: 'userName',
					label: '用户名称',
				},
				{
					key: 'bookNo',
					label: '表册编号',
				},
			],
			companySearchCondition: [
				{
					key: 'enterpriseNumber',
					label: '企业编号',
				},
				{
					key: 'bookNo',
					label: '表册编号',
				},
				{
					key: 'collectionAgreementNumber',
					label: '托收协议号',
				},
			],
			activeCondition: 'archivesIdentity',
			selectedCondition: [],
			//  右边列表
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			searchParams: {},
			billList: [],
			totalMoney: '',
			loading: false,
		}
	},
	computed: {
		columns() {
			return getColumn(this)
		},
		// 直接销账按钮是否禁用
		isPayButtonDisabled() {
			return !this.billList.length
		},
		isExportButtonDisabled() {
			return !this.tableData.length
		},
		orgOptions() {
			return this.formItems[0].options
		},
		refName() {
			return `formRef${this.tabs.findIndex(item => item.name === this.activeTab)}`
		},
		searchCondition() {
			return this.activeTab === 'resident' ? this.residentSearchCondition : this.companySearchCondition
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.$nextTick(() => {
						this.handleSearch()
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		getSearchData() {
			const data = trimParams(removeNullParams(this.formData))
			const newArr = []
			Object.keys(data).forEach(key => {
				const obj = this.formItems.find(item => item.prop === key)
				if (obj && obj.options) {
					const selectItem = obj.options.find(subItem => subItem.value === data[key])
					newArr.push({
						key,
						value: selectItem.label,
					})
				} else {
					newArr.push({
						key,
						value: data[key],
					})
				}
			})
			this.selectedCondition = newArr
		},
		clearFormComponent() {
			this.valueObj = { value: null }
			if (this.activeTab === 'resident') {
				this.activeCondition = 'archivesIdentity'
			} else {
				this.activeCondition = 'enterpriseNumber'
			}
		},
		deleteOne(key) {
			this.selectedCondition = this.selectedCondition.filter(item => item.key !== key)
			this.formData[key] = ''
		},
		deleteAll() {
			this.$refs[this.refName][0].resetForm()
			this.selectedCondition = []
		},
		closeDialog(params) {
			this.valueObj = params
		},
		searchTable(params) {
			this.valueObj = params
			this.handleSearch()
		},
		handleClickFakeSearch() {
			this.showSearchPopou = true
			this.getSearchData()
		},
		handleReset() {
			this.$refs[this.refName][0].resetFields()
			this.selectedCondition = []
			this.clearFormComponent()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
		async handleSearch() {
			const valid = await this.$refs[this.refName][0].validate()
			if (!valid) return
			this.handleChangePage({ page: 1 })
		},
		// -------------------右侧表格交互
		// 不能销账的禁止勾选
		selectable(row) {
			const value = accSub(row.meterBalanceAmount, row.arrearsAmountSum)
			return value >= 0
		},
		handleBatch() {
			const isResidentTab = this.activeTab === 'resident'
			const label = `确定要对选中的${isResidentTab ? '表卡' : '企业'}余额进行销账? 总欠费金额${this.totalMoney}元`
			this.$confirm(label, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
				center: true,
			})
				.then(async () => {
					const archives = this.billList.map(item => item.archivesId)
					const user = this.billList.map(item => item.userId)
					const billDate = this.billList[0].billDate
					const params = {
						billDate,
					}
					if (isResidentTab) {
						params.archivesIdList = archives
					} else {
						params.userIdList = user
					}
					try {
						await apiBillClearBatch(params)
						this.$message.success('账户销账成功')
						this.handleChangePage({ page: 1 })
					} catch (error) {
						console.log(error)
					}
				})
				.catch(() => {})
		},
		getParams() {
			const { current, size } = this.pageData
			const formParams = trimParams(removeNullParams(this.formData))
			const isResident = this.activeTab === 'resident'
			Object.assign(formParams, {
				current,
				size,
				userType: isResident ? 3 : 4,
			})
			if (this.valueObj.value) {
				formParams[this.valueObj.key] = this.valueObj.value
			}

			return formParams
		},
		async getList() {
			this.loading = true
			try {
				const formParams = this.getParams()
				const { records, total } = await apiGetAccountClearList(formParams)
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		// 多选
		selectChange(arr) {
			const amountList = arr.map(item => item.arrearsAmountSum)
			this.billList = arr
			this.totalMoney = this.accAddMultiple(amountList)
		},
		accAddMultiple(args) {
			return args.reduce((acc, curr) => accAdd(acc, curr), 0)
		},
		async _apiClearAccount(params) {
			try {
				await apiClearAccount(params)
				this.$message.success('销账成功')
				this.handleChangePage({ page: 1 })
			} catch (error) {
				console.log(error)
			}
		},
		handleChangeTab() {
			this.valueObj = { value: null }
			this.activeCondition = this.activeTab === 'resident' ? 'archivesIdentity' : 'enterpriseNumber'
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.handleSearch()
		},
		handleExportButtonClick() {
			const maxLength = 300000
			const { total } = this.pageData
			const params = this.getParams()

			if (total > maxLength) {
				this.$message.error('导出数量不能超过30万条')
				return
			}

			params.current = 1
			params.size = total
			apiExportAccountClearList(params).then(res => {
				exportBlob(res, '预存销账列表')
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.left-search-container {
	margin-bottom: 20px;
	margin-top: 12px;
	display: flex;
	justify-content: center;
}
.btn-container {
	margin-bottom: 10px;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
}
.page-left {
	padding-top: 0;
	padding-left: 0;
	padding-right: 0;
	.btn-group {
		padding: 0 20px;
	}
	// 隐掉tab的边框
	::v-deep {
		.el-tabs {
			width: 100%;
			height: 100%;
			box-shadow: none;
			border: none;
			border-radius: 4px 4px 0 0;
			overflow: hidden;
			.el-tabs__content {
				padding: 0;
				height: calc(100% - 38px);
				.el-tab-pane {
					height: 100%;
				}
			}
		}
		.el-tabs--border-card > .el-tabs__header {
			border: none;
			height: 38px;
			margin-bottom: 10px;
			.el-tabs__nav {
				display: flex;
				align-items: center;
				width: 100%;
				border: none;
				height: 38px;
				.el-tabs__item {
					margin: 0;
					background: #e1ebfa;
					border: none;
					font-size: 14px;
					color: #6d7480;
					flex: 1;
					padding: 0;
					text-align: center;
					height: 38px;
					&.is-active {
						background: #ffffff;
						font-weight: 500;
						color: #2f87fe;
					}
				}
			}
		}
		.el-tabs__item:focus.is-active.is-focus:not(:active) {
			-webkit-box-shadow: none;
			box-shadow: none;
		}
		.el-form {
			padding: 0 20px;
		}
	}
}
</style>
