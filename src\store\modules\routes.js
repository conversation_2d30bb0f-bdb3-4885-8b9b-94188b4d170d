// 路由拦截状态管理
import { asyncRoutes, constantRoutes } from '@/router/router.config'
import { filterRoutes, filterAsyncRoutes, filterRoutesForBusiness } from '@/utils/routes'
import { getRoutes, setRoutes, getLS, setLS, removeLS } from '@/utils/storage'

const state = () => ({
	routes: getRoutes() || [],
	addRoutes: [],
	isSwitchTenantShow: false,
	isSwitchOrgShow: false,
	targetRouterPath: getLS('targetRouterPath') || '',
})
const getters = {
	routes: state => state.routes,
	isSwitchTenantShow: state => state.isSwitchTenantShow,
	isSwitchOrgShow: state => state.isSwitchOrgShow,
	targetRouterPath: state => state.targetRouterPath,
}
const mutations = {
	SET_ROUTES(state, routes) {
		state.routes = routes
	},
	SET_IS_SWITCH_TENANT(state, value) {
		state.isSwitchTenantShow = value
	},
	SET_IS_SWITCH_ORG(state, value) {
		state.isSwitchOrgShow = value
	},
	SET_TARGET_ROUTER_PATH(state, value) {
		state.targetRouterPath = value
		if (!value) {
			removeLS('targetRouterPath')
			return
		}
		setLS('targetRouterPath', value)
	},
}
const actions = {
	generateRoutes({ commit }, permissions) {
		return new Promise(resolve => {
			// 根据meta.permissions 和 meta.showForSys、hideForSys 筛选出动态路由
			let accessedAsyncRoutes = filterAsyncRoutes(asyncRoutes, permissions)
			let accessedRoutes = constantRoutes.concat(accessedAsyncRoutes)
			accessedRoutes = filterRoutes(accessedRoutes) //补全路由fullPath
			commit('SET_ROUTES', accessedRoutes)
			setRoutes(accessedRoutes)
			resolve(accessedRoutes)
		})
	},
	reGenerateRoutes({ commit, state }, business_config) {
		return new Promise(resolve => {
			const finalRoutes = filterRoutesForBusiness(state.routes, business_config)
			commit('SET_ROUTES', finalRoutes)
			setRoutes(finalRoutes)
			resolve(finalRoutes)
		})
	},
}
export default { state, getters, mutations, actions }
