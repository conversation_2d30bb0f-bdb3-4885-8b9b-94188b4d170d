<template>
	<div
		class="logo-container"
		:class="{
			['logo-container-' + theme.layout]: true,
		}"
	>
		<router-link to="/">
			<span class="logo">
				<img :src="require('@/assets/images/logo-white.svg')" alt="" />
			</span>
			<span class="title" :class="{ 'hidden-xs-only': theme.layout === 'horizontal' }">
				{{
					userLevel == 0 ? '大连市自来水集团有限公司' : realm !== 'water' ? title : '大连市自来水集团有限公司'
				}}
			</span>
		</router-link>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import identity from '@/mixin/identity.js'

export default {
	name: 'Gc<PERSON><PERSON>',
	mixins: [identity],
	computed: {
		...mapGetters({
			logo: 'settings/logo',
			title: 'settings/title',
			theme: 'settings/theme',
		}),
	},
}
</script>

<style lang="scss" scoped>
@mixin container {
	position: relative;
	height: $base-top-bar-height;
	overflow: hidden;
	line-height: $base-top-bar-height;
	background: transparent;
}

@mixin logo {
	display: inline-block;
	width: 30px;
	vertical-align: middle;
}

@mixin title {
	display: inline-block;
	margin-left: 5px;
	overflow: hidden;
	font-size: 18px;
	font-family: SourceHanSansCN-Medium, SourceHanSansCN;
	font-weight: 500;
	color: $base-title-color;
	text-overflow: ellipsis;
	white-space: nowrap;
	vertical-align: middle;
}

.logo-container {
	&-horizontal {
		@include container;

		.logo img {
			@include logo;
		}

		.title {
			@include title;
		}
	}

	&-vertical,
	&-column {
		@include container;

		height: $base-logo-height;
		line-height: $base-logo-height;
		text-align: center;

		.logo img {
			@include logo;
		}
		.logo {
			position: relative;
			&::after {
				content: '';
				width: 25px;
				height: 1px;
				background: #7fb5fe;
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
			}
		}

		.title {
			@include title;
			max-width: $base-left-menu-width - 60;
		}
	}

	&-column {
		background: $base-column-submenu-background !important;

		.logo {
			position: fixed;
			top: 0;
			display: block;
			width: $base-left-menu-width-min;
			height: $base-logo-height;
			margin: 0;
			background: $base-color-blue;
		}

		.title {
			padding-right: 15px;
			padding-left: 15px;
			margin-left: $base-left-menu-width-min !important;
			color: $base-color-blue !important;
			background: $base-column-submenu-background !important;
			@include title;
		}
	}
}
</style>
