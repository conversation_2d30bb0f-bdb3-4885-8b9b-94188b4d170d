<template>
	<button
		type="button"
		class="gc-button"
		:class="`gc-button-${btnType} ${isDisabled ? 'disabled' : ''}`"
		:disabled="isDisabled"
		v-click-blur
	>
		<i class="el-icon el-icon-loading" v-if="loading"></i>
		<slot></slot>
	</button>
</template>

<script>
export default {
	name: 'GcButton',
	components: {},
	props: {
		btnType: {
			type: String,
			default: 'one', // one, two , three
		},
		isDisabled: {
			type: Boolean,
			default: false,
		},
		loading: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	methods: {},
}
</script>
<style lang="scss" scoped>
.el-icon-loading {
	margin-right: 5px;
}
</style>
