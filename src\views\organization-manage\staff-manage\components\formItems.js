export const getStaffFormItems = _this => {
	const arr = [
		{
			type: 'slot',
			slotName: 'baseInfo',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-input',
			label: '员工姓名',
			prop: 'name',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-cascader',
			label: '所属部门',
			prop: 'department_code',
			attrs: {
				filterable: true,
				'show-all-levels': false,
				options: [],
				col: 12,
				props: {
					emitPath: false,
				},
			},
		},
		{
			type: 'el-select',
			label: '性别',
			prop: 'gender',
			options: _this.$option.genderOptions,
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-select',
			label: '所属角色',
			options: [],
			prop: 'role_id',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '员工号',
			prop: 'staff_no',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-select',
			label: '状态',
			options: _this.$option.resignedTypeOptions,
			prop: 'is_resigned',
			attrs: {
				col: 12,
				disabled: _this.editType === 'add',
			},
		},
		{
			type: 'el-input',
			label: '手机号码',
			prop: 'mobile',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '办公电话',
			prop: 'phone',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '身份证号',
			prop: 'id_card_no',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '备注',
			prop: 'remark',
			attrs: {
				col: 24,
				type: 'textarea',
				placeholder: '请输入备注',
				maxlength: '128',
				showWordLimit: true,
				autosize: {
					minRows: 3,
					maxRows: 4,
				},
			},
		},
	]
	return _this.newEditType === 'add'
		? arr.concat([
				{
					type: 'slot',
					slotName: 'dash',
					attrs: {
						col: 24,
					},
				},
				{
					type: 'el-input',
					label: '登录账号',
					prop: 'account_name',
					attrs: {
						col: 12,
					},
				},
				{
					type: 'el-input',
					label: '登录密码',
					prop: 'account_password',
					attrs: {
						col: 12,
						disabled: true,
					},
				},
		  ])
		: arr
}
export const getDepartmentFormItems = _this => {
	return [
		{
			type: 'el-input',
			label: '部门名称',
			prop: 'department_name',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-select',
			label: '所属组织',
			prop: 'department_code',
			options: [],
			attrs: {
				col: 12,
				disabled: _this.editType === 'edit',
			},
		},
		{
			type: 'el-input',
			label: '负责人',
			prop: 'principal',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '手机号码',
			prop: 'phone',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-select',
			label: '所在省份',
			prop: 'province',
			options: [],
			attrs: {
				col: 12,
			},
			events: {
				change: value => {
					_this.formData.city = ''
					const obj = _this.formItems.find(item => item.prop === 'city')
					if (!obj) return
					obj.options = []
					if (value) {
						_this._getAddressData(value, 'city')
					}
				},
			},
		},
		{
			type: 'el-select',
			label: '所在市',
			prop: 'city',
			options: [],
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '地址',
			prop: 'location',
			attrs: {
				col: 24,
			},
		},
	]
}
