import axios from 'axios'
import store from '@/store'
import { getToken } from '@/utils/storage'
import Vue from 'vue'
import { CPM, READING } from '@/consts/moduleNames'

// create an axios instance
const service = axios.create({
	baseURL: process.env.VUE_APP_API_BASE_URL, // url = base url + request url
	timeout: 60000, // request timeout
})
let reqCache = []
const CancelToken = axios.CancelToken
const urlWhiteList = [
	`${CPM}/archives-batch`,
	`${CPM}/charge-batch/recharge`,
	`${CPM}/charge-batch/reissue`,
	`${CPM}/charge-batch/register`,
	`${CPM}/region/children`,
	`${CPM}/address-areas/list`,
	`${CPM}/area/queryAddressAreaMap`,
	`${CPM}/area/queryStaffList`,
	`${CPM}/meterReadingStaff/queryStaffByType`,
	`${READING}/meterReadingBook/getBookArchivesList2`,
]
const cancelReq = config => {
	if (config) {
		if (urlWhiteList.includes(config.url)) {
			return false
		}

		let reqIndex = 0
		let reqItem = reqCache.find((item, index) => {
			reqIndex = index
			return item.url == config.url && item.method == config.method
		})
		if (reqItem) {
			reqItem.cancel('Cancel：Repeat Request')
			reqCache.splice(reqIndex, 1)
		}
	} else {
		reqCache.forEach((item, index) => {
			if (!urlWhiteList.includes(item.url)) {
				item.cancel()
				reqCache.splice(index, 1)
			}
		})
	}
}
// request interceptor
service.interceptors.request.use(
	config => {
		if (store.getters.token) {
			// 待调试：原5.0系统登录不能带token？
			// && !config.url.includes("login")
			config.headers['accessToken'] = getToken()
		}

		const { url, method } = config
		cancelReq(config)
		config.cancelToken = new CancelToken(cancel => {
			reqCache.push({
				url,
				method,
				cancel,
			})
		})
		return config
	},
	error => {
		console.error('REQUEST ERROR:', error) // for debug
		return Promise.reject(error)
	},
)

const errorHandler = error => {
	if (!error.response) {
		return Promise.reject(error)
	}

	const status = error.response.status
	if (status === 403) {
		// 屏蔽首页提示
		!error.response.config.cancelUnitErrToast &&
			Vue.prototype.$notify({
				title: '提示',
				message: '暂无权限访问，请联系系统管理员！',
				offset: 60,
				type: 'warning',
			})
		return Promise.reject(403)
	}

	if (status === 401) {
		// 账号长时间未登录或在别处登录，请重新登录
		Vue.prototype.$notify({
			title: '提示',
			message: '账号长时间未登录或在别处登录，请重新登录！',
			offset: 60,
			type: 'warning',
		})
		setTimeout(() => {
			store.dispatch('user/resetUserLS').then(() => location.reload())
		}, 1000)
		return Promise.reject(401)
	}

	error.message &&
		Vue.prototype.$message({
			message: error.message,
			type: 'error',
			duration: 3 * 1000,
		})
	return Promise.reject(error)
}

// response interceptor
// 需要返回请求体的接口
const responseApiList = [
	`${CPM}/planUsage/import`,
	`${CPM}/planWaste/import`,
	`${READING}/meterReadingTask/importMeterReadingRecordExcel`,
]
service.interceptors.response.use(response => {
	const { responseType, url } = response.config
	if (responseApiList.includes(url)) {
		return response
	}
	// 天气接口兼容
	if (!response.data.code && response.data.showapi_res_code == 0) {
		return response.data.showapi_res_body
	}
	if (responseType == 'blob') {
		return response.data
	} else if (response.data.code == '00' || response.data.code == '0') {
		// 标准：{code,data,message} 这里只把data暴露到业务
		// 非标：{code,message}/ {code,id,message}/ {code,data,message,total_count,total_pages,pageable}
		// 非标情况把其余key放到data中,{data：原data,k1,k2}形式一并透传

		// eslint-disable-next-line no-unused-vars
		let { data, code, message, ...others } = response.data
		return Object.keys(others).length ? { data, ...others } : data
	} else {
		if (!response.config.headers.nomessageTip) {
			Vue.prototype.$message({
				message: response.data.message || 'Response Error',
				type: 'error',
				duration: 3 * 1000,
			})
		}
		return Promise.reject(response.data)
	}
}, errorHandler)

export default service
