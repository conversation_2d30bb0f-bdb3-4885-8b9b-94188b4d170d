<template>
	<transition name="dialog-fade" @after-enter="afterEnter" @after-leave="afterLeave">
		<div v-show="visible" class="el-dialog__wrapper" @click.self="handleWrapperClick">
			<div
				role="dialog"
				:key="key"
				aria-modal="true"
				:aria-label="title || 'dialog'"
				:class="['el-dialog', { 'is-fullscreen': fullscreen, 'el-dialog--center': center }, customClass]"
				ref="dialog"
				:style="style"
			>
				<div class="el-dialog__header" ref="gc-dialog-header">
					<slot name="title">
						<span class="el-dialog__title">
							<img src="@/assets/images/icon/popup-general-icon.png" alt="" />
							{{ title }}
						</span>
					</slot>
					<button
						type="button"
						class="el-dialog__headerbtn"
						aria-label="Close"
						v-if="showClose"
						@click="handleClose"
					>
						<i class="el-dialog__close el-icon el-icon-close"></i>
					</button>
				</div>
				<div class="el-dialog__body" ref="gc-dialog-body" :class="{ is_large: large }" v-if="rendered">
					<slot v-if="!large || !contentTable"></slot>
					<vue-scroll v-else :ops="{ bar: { background: '#e3e3e3' } }">
						<slot></slot>
					</vue-scroll>
				</div>
				<div class="el-dialog__footer" v-if="$slots.footer" ref="gc-dialog-footer">
					<slot name="footer"></slot>
				</div>
			</div>
		</div>
	</transition>
</template>

<script>
import Popup from 'element-ui/src/utils/popup'
import Migrating from 'element-ui/src/mixins/migrating'
import emitter from 'element-ui/src/mixins/emitter'

export default {
	name: 'GcDialog',

	mixins: [Popup, emitter, Migrating],

	props: {
		title: {
			type: String,
			default: '',
		},

		large: {
			type: Boolean,
			default: false,
		},

		contentTable: {
			type: Boolean,
			default: true,
		},

		iconClass: String,

		modal: {
			type: Boolean,
			default: true,
		},

		modalAppendToBody: {
			type: Boolean,
			default: true,
		},

		appendToBody: {
			type: Boolean,
			default: false,
		},

		lockScroll: {
			type: Boolean,
			default: true,
		},

		closeOnClickModal: {
			type: Boolean,
			default: false,
		},

		closeOnPressEscape: {
			type: Boolean,
			default: false,
		},

		showClose: {
			type: Boolean,
			default: true,
		},

		width: String,

		fullscreen: Boolean,

		customClass: {
			type: String,
			default: '',
		},

		top: {
			type: String,
		},

		beforeClose: Function,

		center: {
			type: Boolean,
			default: false,
		},

		destroyOnClose: Boolean,
	},

	data() {
		return {
			closed: false,
			key: 0,
		}
	},

	methods: {
		getMigratingConfig() {
			return {
				props: {
					size: 'size is removed.',
				},
			}
		},
		handleWrapperClick() {
			if (!this.closeOnClickModal) return
			this.handleClose()
		},
		handleClose() {
			if (typeof this.beforeClose === 'function') {
				this.beforeClose(this.hide)
			} else {
				this.hide()
			}
		},
		hide(cancel) {
			if (cancel !== false) {
				this.$emit('update:visible', false)
				this.$emit('close')
				this.closed = true
			}
		},
		updatePopper() {
			this.broadcast('ElSelectDropdown', 'updatePopper')
			this.broadcast('ElDropdownMenu', 'updatePopper')
		},
		afterEnter() {
			this.$emit('opened')
		},
		afterLeave() {
			this.$emit('closed')
		},
		updateDialogStyle() {
			this.$nextTick(() => {
				let gcDialogHeader = this.$refs['gc-dialog-header']
				let gcDialogFooter = this.$refs['gc-dialog-footer']
				let headHeight = gcDialogHeader ? gcDialogHeader.clientHeight : 0
				let footHeight = gcDialogFooter ? gcDialogFooter.clientHeight : 0
				let dialogBodyHeight = `calc(100vh - ${headHeight}px - ${footHeight}px - 116px)`
				this.$refs['gc-dialog-body'].style.height = dialogBodyHeight
			})
		},
	},

	mounted() {
		if (this.visible) {
			this.rendered = true
			this.open()
			if (this.appendToBody) {
				document.body.appendChild(this.$el)
			}
		}
		if (this.large) {
			this.updateDialogStyle()
		}
	},

	watch: {
		visible(val) {
			if (val) {
				this.closed = false
				this.$emit('open')
				this.$el.addEventListener('scroll', this.updatePopper)
				this.$nextTick(() => {
					this.$refs.dialog.scrollTop = 0
				})
				if (this.appendToBody) {
					document.body.appendChild(this.$el)
				}
			} else {
				this.$el.removeEventListener('scroll', this.updatePopper)
				if (!this.closed) this.$emit('close')
				if (this.destroyOnClose) {
					this.$nextTick(() => {
						this.key++
					})
				}
			}
		},

		large(newVal) {
			if (newVal) {
				this.updateDialogStyle()
			} else {
				this.$refs['gc-dialog-body'].style.height = 'auto'
			}
		},
	},

	computed: {
		style() {
			let style = {}
			if (!this.fullscreen) {
				style.marginTop = this.top || this.large ? '60px' : '120px'
				if (this.width) {
					style.width = this.width
				}
			}
			return style
		},
	},

	destroyed() {
		// if appendToBody is true, remove DOM node after destroy
		if (this.appendToBody && this.$el && this.$el.parentNode) {
			this.$el.parentNode.removeChild(this.$el)
		}
	},
}
</script>
<style scoped lang="scss">
.el-dialog__title {
	display: flex;
	align-items: center;
	img {
		width: 18px;
		margin-right: 6px;
	}
}
.el-dialog__body {
	padding: 15px 20px;
	&.is_large {
		box-sizing: border-box;
	}
}
</style>
