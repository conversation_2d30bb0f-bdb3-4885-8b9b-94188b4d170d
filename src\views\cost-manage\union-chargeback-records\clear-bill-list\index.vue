<template>
	<div class="wrapper">
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handleChangePage"
			/>
		</div>
	</div>
</template>

<script>
import { getColumn } from './tableColumn.js'
export default {
	props: {
		loading: {
			type: Boolean,
			default: false,
		},
		tableData: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			columns: getColumn(),
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	methods: {
		pageDataReset() {
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.$emit('page-change', this.pageData)
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	flex: 1;
	height: calc(100% - 109px);
	display: flex;
	flex-direction: column;
	padding: 0 20px 10px;
}
.table-container {
	flex: 1;
	height: 0;
}
</style>
