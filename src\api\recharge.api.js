import service from './request'
export const moduleNames = 'cpm/'

// ----批量缴费 start----

// 批量充值
export function apiBatchRecharge(data) {
	let obj = {
		url: moduleNames + 'charge-batch/recharge',
		method: 'POST',
		data: data,
		timeout: 600000,
	}
	return service(obj)
}
// 批量补气
export function apiBatchReissue(data) {
	let obj = {
		url: moduleNames + 'charge-batch/reissue',
		method: 'POST',
		data: data,
		timeout: 600000,
	}
	return service(obj)
}
// 批量登记其他费用
export function apiBatchRegister(data) {
	let obj = {
		url: moduleNames + 'charge-batch/register',
		method: 'POST',
		data: data,
		timeout: 600000,
	}
	return service(obj)
}
// 新增批量操作记录
export function apiRechargeRecordAdd(data) {
	let obj = {
		url: moduleNames + 'charge-batch/record',
		method: 'POST',
		data: data,
		timeout: 600000,
	}
	return service(obj)
}

// ----批量缴费 End----

// ic卡充值
export function apiIcChargeConfirm(data) {
	return service({
		url: moduleNames + 'icCharge/icChargeConfirm',
		method: 'post',
		data: data,
	})
}

// 远传表收费-保存
export function apiRecharge(data) {
	return service({
		url: moduleNames + 'charge/recharge',
		method: 'post',
		data: data,
	})
}

// 远传表补气-保存
export function apiReissue(data) {
	return service({
		url: moduleNames + 'charge/reissue',
		method: 'post',
		data: data,
	})
}

// ic卡补气
export function apiIcReissueGasConfirm(data) {
	return service({
		url: moduleNames + 'icCharge/icReissueGasConfirm',
		method: 'post',
		data: data,
	})
}
