/*
 * @Description
 * @Version:
 * @Autor: ho<PERSON>an
 * @Date: 2024-09-03 16:12:44
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 20:19:55
 */
import { bookTypeOptions, meterReadingCycleOptions, meterReadingNumberOptions } from '@/consts/optionList.js'

export default function (_) {
	return {
		formItems: [
			{
				type: 'el-select',
				label: '营业分公司',
				prop: 'orgCode',
				options: _.$store.getters.orgList,
				attrs: {
					col: 12,
					disabled: false,
					placeholder: '请选择营业分公司',
				},
				events: {
					change: () => {
						_.formData.alleyId = ''
						_.formData.bookNo = ''
						_.formData.meterReadingStaffId = ''
						_.formData.staffPhone = ''
					},
				},
			},
			{
				type: 'el-select',
				label: '坊别',
				prop: 'alleyId',
				options: [],
				attrs: {
					col: 12,
					disabled: false,
					clearable: true,
					noDataText: '请先选择营业分公司',
					placeholder: '请先选择坊别',
				},
				events: {
					change: () => {
						// 生成抄表编号
						if (_.formData.bookType !== 1) {
							_.generateBookNoData()
						}
					},
				},
			},
			{
				type: 'el-select',
				label: '册本类型',
				prop: 'bookType',
				options: bookTypeOptions,
				attrs: {
					col: 12,
					disabled: false,
					clearable: false,
					placeholder: '请选择册本类型',
				},
				events: {
					change: value => {
						const isBusiness = value === 1
						// 生成抄表编号
						_.generateBookNoData()

						// 类型为企业时 坊别、册本范围非必填
						_.formAttrs.rules.alleyId.required = !isBusiness

						if (isBusiness) {
							// 企业时 清空坊别字段验证错误信息
							_.$refs.formRef.$refs.formRef.clearValidate(['alleyId', 'rangeStart', 'rangeEnd'])
						}
						// 修改表册编号的位数验证规则
						_.handleBookNoRule(value)
						// 修改表册范围起始号、结束号验证规则
						_.handleRangeRule(value)
					},
				},
			},
			{
				type: 'el-input',
				label: '表册编号',
				prop: 'bookNo',
				attrs: {
					col: 12,
					disabled: false,
					placeholder: '请输入表册编号',
				},
			},
			{
				type: 'el-select',
				label: '抄表员',
				prop: 'meterReadingStaffId',
				options: [],
				attrs: {
					col: 12,
					placeholder: '请选择抄表员',
					noDataText: '请先选择营业分公司',
				},
				events: {
					change: value => {
						const { staffPhone = '' } = _.formItems[4].options.find(item => item.value === value)
						_.formData.staffPhone = staffPhone
					},
				},
			},
			{
				type: 'el-input',
				label: '抄表员电话',
				prop: 'staffPhone',
				attrs: {
					disabled: true,
					col: 12,
				},
			},

			{
				type: 'el-select',
				label: '抄表周期',
				prop: 'meterReadingCycle',
				options: meterReadingCycleOptions,
				attrs: {
					col: 12,
					placeholder: '请选择抄表周期',
				},
			},
			{
				type: 'el-select',
				label: '抄表次数',
				prop: 'meterReadingNumber',
				options: meterReadingNumberOptions,
				attrs: {
					col: 12,
					placeholder: '请选择抄表次数',
				},
			},
			{
				type: 'slot',
				slotName: 'range',
				attrs: {
					col: 12,
				},
			},
			{
				type: 'el-input',
				label: '备注',
				prop: 'remark',
				attrs: {
					col: 12,
					disabled: false,
					placeholder: '请输入备注',
				},
			},
		],
	}
}
