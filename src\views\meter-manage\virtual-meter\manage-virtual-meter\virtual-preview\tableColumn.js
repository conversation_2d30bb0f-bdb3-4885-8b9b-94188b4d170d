import { getfilterName } from '@/utils'
export function getColumn(_this) {
	const baseColumn = [
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'virtualMeterType',
			name: '表卡类型',
			tooltip: true,
			render: (h, row, total, scope) => {
				const key = scope.column.property
				return h(
					'span',
					{},
					getfilterName(_this.$store.getters.dataList.virtualMeterType, row[key], 'sortValue', 'sortName'),
				)
			},
		},
		{
			key: 'priceCode',
			name: '价格编号',
		},
		{
			key: 'priceName',
			name: '价格名称',
			tooltip: true,
		},
		{
			key: 'natureName',
			name: '用水性质',
			tooltip: true,
		},
		{
			key: 'archivesStatus',
			name: '表卡状态',
			tooltip: true,
			render: (h, row, total, scope) => {
				const key = scope.column.property
				return h(
					'span',
					{},
					getfilterName(_this.$store.getters.dataList.archiveState, row[key], 'sortValue', 'sortName'),
				)
			},
		},
	]

	if (_this.currentRow?.distributionMode === 0) {
		const newBaseColumn = baseColumn.slice(0)
		newBaseColumn.splice(2, 0, {
			key: 'usageMeasure',
			name: '用水比例（%）',
		})
		newBaseColumn.push({
			key: 'sort',
			name: '排序',
			width: 100,
		})
		return newBaseColumn
	} else if (_this.currentRow?.distributionMode === 1) {
		const newBaseColumn = baseColumn.slice(0)
		newBaseColumn.splice(2, 0, {
			key: 'usageMeasure',
			name: '固定水量（吨）',
		})
		newBaseColumn.push({
			key: 'sort',
			name: '排序',
			width: 100,
		})
		return newBaseColumn
	} else if (_this.currentRow?.distributionMode === 2 || _this.currentRow?.distributionMode === 4) {
		const subDistributionModeEnums = {
			0: '总量占比',
			1: '固定量',
			2: '剩余量占比',
		}
		const unitEnums = {
			0: '%',
			1: '吨',
			2: '%',
		}
		const newBaseColumn = baseColumn.slice(0)
		newBaseColumn.splice(
			2,
			0,
			{
				key: 'subDistributionMode',
				name: '分配方式',
				render: (h, row, total, scope) => {
					const key = scope.column.property
					return h('span', {}, subDistributionModeEnums[row[key]])
				},
			},
			{
				key: 'usageMeasure',
				name: '分配额',
				render: (h, row, total, scope) => {
					const key = scope.column.property
					const unit = unitEnums[row.subDistributionMode] || ''
					return h('span', {}, row[key] + unit)
				},
			},
		)
		newBaseColumn.push({
			key: 'sort',
			name: '排序',
			width: 100,
		})
		return newBaseColumn
	} else {
		return baseColumn
	}
}
