<template>
	<div class="filter-card">
		<div v-if="$slots.header" class="header" :class="{ 'border-bottom': showLine }">
			<slot name="header" />
		</div>
		<div class="main">
			<slot />
		</div>
		<div v-if="showFooter" class="footer">
			<slot v-if="$slots.footer" name="footer" />
			<template v-else>
				<button class="gc-button gc-button-three" @click="$emit('reset')" v-click-blur>重 置</button>
				<button class="gc-button gc-button-one" @click="$emit('filter')" v-click-blur>筛 选</button>
			</template>
		</div>
	</div>
</template>

<script>
export default {
	name: 'FilterCard',
	props: {
		showLine: {
			type: Boolean,
			default: () => true,
		},
		showFooter: {
			type: Boolean,
			default: () => true,
		},
	},
}
</script>
<style lang="scss" scoped>
.filter-card {
	display: flex;
	flex-direction: column;
	height: 100%;

	.header {
		margin: 0 16px;
		padding: 20px 0;
		&.border-bottom {
			border-bottom: 1px dotted #ccc;
		}
	}

	.main {
		flex: 1;
		overflow-y: auto;
		padding: 20px 16px;
		@include base-scrollbar(3px);
	}

	.footer {
		height: 60px;
		padding: 0 16px;
		@include flex-center;
		justify-content: space-between;

		.gc-button {
			width: 110px;
		}
	}
}
</style>
