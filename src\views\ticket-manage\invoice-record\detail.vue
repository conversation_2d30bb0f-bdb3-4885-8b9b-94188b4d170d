<template>
	<div class="flex">
		<div class="invoice-detail">
			<GcModelHeader title="发票信息" :icon="require('@/assets/images/icon/title-cash.png')"></GcModelHeader>
			<GcGroupDetail :data="recordsData"></GcGroupDetail>
		</div>
		<div class="table-panel">
			<GcModelHeader title="账单明细" :icon="require('@/assets/images/icon/title-cash.png')"></GcModelHeader>
			<div class="top-gradient-edge"></div>
			<div class="bill-total">
				<span>账单总应缴金额：{{ (recordDetail.totalReceivableAmount || 0).toFixed(2) }}</span>
				<span>账单已缴总金额：{{ (recordDetail.totalClearedAmount || 0).toFixed(2) }}</span>
				<span>账单未缴总金额：{{ (recordDetail.totalPendingPaymentAmount || 0).toFixed(2) }}</span>
			</div>
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="recordDetail.invoiceBillList || []"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
			></GcTable>
		</div>
	</div>
</template>

<script>
import { getInvoiceRecordDetail } from '@/api/ticketManage.api'
import { getfilterName } from '@/utils'
export default {
	name: 'InvoiceRecord',
	data() {
		return {
			invoiceTypeMap: (this.$store.getters?.dataList?.invoiceType || []).reduce((map, item) => {
				map[item.sortValue] = item.sortName
				return map
			}, {}),
			recordDetail: {
				invoiceBillList: [],
				totalClearedAmount: 0,
				totalPendingPaymentAmount: 0,
				totalReceivableAmount: 0,
				orderAmount: 0,
			},
			columns: [
				{
					key: 'billDate',
					name: '账期',
				},
				{
					key: 'billNo',
					name: '账单编号',
					width: 220,
				},
				{
					key: 'receivableAmount',
					name: '应缴金额(元)',
					align: 'right',
					render: function (h, row) {
						const amount = row.receivableAmount - 0
						return h('span', {}, amount.toFixed(2))
					},
				},
				{
					key: 'billStatus',
					name: '账单状态',
					render: (h, row) => {
						const { billStatus = [] } = this.$store.getters.dataList || {}
						const valueStr = getfilterName(billStatus, row.billStatus, 'sortValue', 'sortName')
						return h('span', {}, valueStr)
					},
				},
				{
					key: 'archivesIdentity',
					name: '表卡编号',
				},
				{
					key: 'useAmount',
					name: '水量(方)',
					align: 'right',
				},
				{
					key: 'priceCode',
					name: '价格编号',
				},
				{
					key: 'useAmt',
					name: '水费(元)',
					align: 'right',
					render: function (h, row) {
						const amount = row.useAmt - 0
						return h('span', {}, amount.toFixed(2))
					},
				},
				{
					key: 'billItemAmt',
					name: '污水费(元)',
					align: 'right',
					render: function (h, row) {
						const amount = row.billItemAmt - 0
						return h('span', {}, amount.toFixed(2))
					},
				},
				{
					key: 'collectionAgreementNumber',
					name: '托收协议号',
				},
				{
					key: 'enterpriseNumber',
					name: '企业编号',
				},
				{
					key: 'userName',
					name: '用户名称',
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
		}
	},
	activated() {
		this.init()
	},
	computed: {
		recordsData() {
			const list = [
				{
					key: '开票类型',
					value: '--',
					field: 'invoiceType',
				},
				{
					key: '用户名称',
					value: '--',
					field: 'payerName',
				},
				{
					key: '纳税人识别号',
					value: '--',
					field: 'payerTaxNo',
				},
				{
					key: '开户银行',
					value: '--',
					field: 'openBank',
				},
				{
					key: '银行账号',
					value: '--',
					field: 'bankAccount',
				},
				{
					key: '开票时间',
					value: '--',
					field: 'invoiceOpenTime',
				},
				{
					key: '开票金额',
					value: '--',
					field: 'orderAmount',
				},
				{
					key: '发票号码',
					value: '--',
					field: 'invoiceNo',
				},
			]
			list.forEach(item => {
				const v = this.recordDetail[item.field]
				if ('orderAmount' === item.field) {
					item.value = (v || 0).toFixed(2)
					return
				}
				if ('invoiceType' === item.field) {
					item.value = this.invoiceTypeMap[v] || '--'
					return
				}
				item.value = v
			})
			return {
				list,
				row: 5,
			}
		},
	},
	methods: {
		async init() {
			this.loading = true
			const res = await getInvoiceRecordDetail({
				invoiceRecordId: this.$route.query.invoiceRecordId,
			})
			this.recordDetail = res
			this.pageData.total = res.invoiceBillList ? res.invoiceBillList.length : 0
			this.loading = false
		},
	},
}
</script>

<style lang="scss" scoped>
.flex {
	display: flex;
	flex-direction: column;
	height: 100%;
}
.invoice-detail {
	background-color: #fff;
	margin-bottom: 12px;
	.el-form {
		margin: 20px 20px 0 20px;
		border-bottom: 1px dashed #eef0f3;
	}
}
.table-panel {
	position: relative;
	background-color: #fff;
	height: calc(100% - 196px);
	::v-deep {
		.gc-table {
			height: calc(100% - 99px);
			padding: 10px 20px;
		}
	}
}
.bill-total {
	margin: 10px 20px 15px;
	display: flex;
	gap: 20px;
}
</style>
