<template>
	<gc-el-dialog :show="isShow" title="新增结账年份" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<GcTable ref="tableRef" :columns="columns" :table-data="formData.tableData" style="height: 400px">
				<template v-slot:date="{ row, $index }">
					<el-form-item
						class="date-form-item"
						:prop="`tableData[${$index}].date`"
						label-width="0px"
						:rules="[
							{ required: true, message: '请选择结账日期', trigger: 'change' },
							dateValidator($index),
						]"
					>
						<el-date-picker
							v-model="formData.tableData[$index].date"
							:picker-options="generatePickerOptions(row)"
							value-format="yyyy-MM-dd"
							:default-value="new Date(`${formData.dateYear}-${row.month}-01`)"
							placeholder="请选择结账日期"
						/>
					</el-form-item>
				</template>
			</GcTable>
		</GcFormSimple>
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { addBalanceSheetDate } from '@/api/basicConfig.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				dateYear: '',
				tableData: [
					{ month: 1, date: '' },
					{ month: 2, date: '' },
					{ month: 3, date: '' },
					{ month: 4, date: '' },
					{ month: 5, date: '' },
					{ month: 6, date: '' },
					{ month: 7, date: '' },
					{ month: 8, date: '' },
					{ month: 9, date: '' },
					{ month: 10, date: '' },
					{ month: 11, date: '' },
					{ month: 12, date: '' },
				],
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '结账年份',
					prop: 'dateYear',
					attrs: {
						col: 24,
						type: 'year',
						valueFormat: 'yyyy',
						placeholder: '请选择结账年份',
					},
					events: {
						change: () => {
							this.formData.tableData = [
								{ month: 1, date: '' },
								{ month: 2, date: '' },
								{ month: 3, date: '' },
								{ month: 4, date: '' },
								{ month: 5, date: '' },
								{ month: 6, date: '' },
								{ month: 7, date: '' },
								{ month: 8, date: '' },
								{ month: 9, date: '' },
								{ month: 10, date: '' },
								{ month: 11, date: '' },
								{ month: 12, date: '' },
							]
						},
					},
				},
			],
			formAttrs: {
				labelPosition: 'right',
				labelWidth: '110px',
				rules: {
					dateYear: [
						{
							required: true,
							message: '请选择结账年份',
							trigger: 'change',
						},
					],
				},
			},
			columns: [
				{
					key: 'month',
					name: '月份',
					width: 200,
				},
				{
					key: 'date',
					name: '结账日期',
					tooltip: true,
				},
			],
		}
	},
	methods: {
		dateValidator(index) {
			return {
				validator: (rule, value, callback) => {
					// 辅助函数：验证下一个月的日期
					const validateNextDate = currentIndex => {
						const nextIndex = currentIndex + 1
						if (nextIndex < this.formData.tableData.length && this.formData.tableData[nextIndex].date) {
							this.$refs.formRef.validateField(`tableData[${nextIndex}].date`)
						}
					}
					// 辅助函数：比较日期大小
					const compareDates = (prevDate, currentDate) => {
						const prevTimestamp = this.dayjs(prevDate).valueOf()
						const currentTimestamp = this.dayjs(currentDate).valueOf()
						return prevTimestamp >= currentTimestamp
					}

					// 当前是第一个日期字段
					if (index === 0) {
						validateNextDate(index)
						return callback()
					}
					// 获取上一个月的日期
					const prevDate = this.formData.tableData[index - 1].date
					// 比较当前日期和上一个月的日期
					if (compareDates(prevDate, value)) {
						return callback(new Error('不能小于等于上月结账日'))
					}
					// 验证通过后，触发下一个月的日期验证
					validateNextDate(index)
					// 验证通过
					callback()
				},
				trigger: 'change',
			}
		},

		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				const { dateYear, tableData = [] } = this.formData
				let dateList = {}
				tableData.forEach(({ month, date }) => {
					dateList[`date${month}`] = date
				})
				await addBalanceSheetDate({
					dateYear,
					...dateList,
				})
				this.$message.success('新增结账年份')
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},

		// 列表中日期选择禁用日期配置函数
		generatePickerOptions(data) {
			return {
				disabledDate: time => {
					const { dateYear } = this.formData
					if (!dateYear) return false

					const timestamp = time.getTime()
					const currentYearMonth = this.dayjs(`${dateYear}-${data.month}`)
					// 可先当月和下月的日期
					return (
						timestamp > currentYearMonth.add(1, 'month').endOf('month').valueOf() ||
						timestamp < currentYearMonth.startOf('month').valueOf()
					)
				},
			}
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.date-form-item {
		&.el-form-item {
			margin: 12px 0;
		}
	}
	.el-form-item__error {
		padding-top: 0;
	}
}
</style>
