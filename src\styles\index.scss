@import "./normalize.scss";
@import "./reset.scss";
@import "./element-ui.scss";
@import "../components/common/GcButton/_button.scss";

html {
  body,
  body[class*="gc-theme-"] {
    position: relative;
    box-sizing: border-box;
    height: 100vh;
    overflow: hidden;
    font-family: $base-font-family;
    font-size: $base-font-size-default;
    color: $base-color-black;
    background: $base-color-background;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    #app {
      height: 100vh;
      overflow: auto;
      @include base-scrollbar;

      .gc-main {
        transition: $base-transition;
        background: $base-color-background;

        .gc-app-main {
          width: 100%;
          transition: $base-transition;

          > section {
            padding: $base-padding;
            overflow: auto;
            height: calc(100vh - #{$base-nav-height} - #{$base-tabs-height});
            transition: $base-transition;

            .page-container {
              display: flex;
              height: 100%;
              .page-left {
                width: 270px;
                overflow-y: auto;
                @include base-card;
              }
              .page-right {
                flex: 1;
                margin-left: 20px;
                width: 0; // 解决表格宽度一直在增大变化的问题
              }
            }
          }
        }
      }
    }
    * {
      box-sizing: border-box;
      outline: none !important;
      @include base-scrollbar;
    }
    /*a标签 */
    a {
      color: $base-color-blue;
      text-decoration: none;
    }
    /*图片 */
    img {
      object-fit: cover;
    }

    /* gc-dropdown下拉动画 */
    .gc-dropdown {
      transition: $base-transition;

      &-active {
        transform: rotateZ(180deg);
      }
    }

    /* el-scrollbar滚动条 */
    .el-scrollbar {
      height: 100%;

      &__bar {
        z-index: 999;
      }

      &__thumb {
        background-color: rgba($base-menu-background, 0.1);

        &:hover {
          background-color: rgba($base-menu-background, 0.2);
        }
      }
    }

    /* nprogress进度条 */
    #nprogress {
      position: fixed;
      z-index: $base-z-index + 3;

      .bar {
        background: $base-color-blue;
      }

      .peg {
        box-shadow: 0 0 10px $base-color-blue, 0 0 5px $base-color-blue;
      }
    }
    @include base-scrollbar;
  }
}

/* 标签样式初始化 */
h1, h2, h3, h4, h5, h6, hr, p, blockquote, dl, dt, dd, ul, ol, li, pre, form, fieldset, legend, button, input, textarea, th, td  {
  margin: 0;
  padding: 0;
}
ul, ol {
  list-style-type: none;
}

