import {
	getModifyParamsList,
	setModifyParamsList,
	removeModifyParamsList,
	getArchivesUpdateList,
	setArchivesUpdateList,
	removeArchivesUpdateList,
	getInvoiceSet,
	setInvoiceSet,
} from '@/utils/storage'
import { apiGetInvoiceOpenSetList, apiInvoiceSaler } from '@/api/systemSetting.api.js'

export default {
	namespaced: true,
	state: {
		modifyParamsList: getModifyParamsList() || [],
		archivesUpdateList: getArchivesUpdateList() || [],
		invoiceSet: getInvoiceSet() || null, //发票设置信息(默认档案 发票类型)
	},
	mutations: {
		SET_MODIFY_PARAMS_LIST: (state, modifyParamsList) => {
			state.modifyParamsList = modifyParamsList
		},
		SET_ARCHIVES_UPDATE_LIST: (state, archivesUpdateList) => {
			state.archivesUpdateList = archivesUpdateList
		},
		SET_INVOICE_SET: (state, invoice) => {
			state.invoiceSet = invoice
		},
	},
	actions: {
		setParams({ commit, state }, current) {
			const { modifyParamsList } = state
			let index = modifyParamsList.findIndex(item => item.archivesId == current.archivesId)
			if (index == -1) {
				modifyParamsList.push(current)
			} else {
				if (modifyParamsList[index].activeTabId != current.activeTabId) {
					modifyParamsList[index].activeTabId = current.activeTabId
				}
			}
			commit('SET_MODIFY_PARAMS_LIST', modifyParamsList)
			setModifyParamsList(modifyParamsList)
		},
		// 删除不存在的参数
		deleteParams({ commit, state }, current) {
			const { modifyParamsList } = state
			let index = modifyParamsList.findIndex(item => item.archivesId == current.archivesId)
			modifyParamsList.splice(index, 1)
			commit('SET_MODIFY_PARAMS_LIST', modifyParamsList)
			if (modifyParamsList.length == 0) {
				removeModifyParamsList()
			} else {
				setModifyParamsList(modifyParamsList)
			}
		},
		// 添加需要更新的页面
		setUpdateList({ commit, state }, el) {
			const { archivesUpdateList } = state
			let index = archivesUpdateList.findIndex(item => item == el)
			if (index == -1) {
				archivesUpdateList.push(el)
			}
			commit('SET_ARCHIVES_UPDATE_LIST', archivesUpdateList)
			setArchivesUpdateList(archivesUpdateList)
		},
		// 删除更新过的页面
		delUpdated({ commit, state }, el) {
			const { archivesUpdateList } = state
			let index = archivesUpdateList.findIndex(item => item == el)
			archivesUpdateList.splice(index, 1)
			commit('SET_ARCHIVES_UPDATE_LIST', archivesUpdateList)
			if (archivesUpdateList.length == 0) {
				removeArchivesUpdateList()
			} else {
				setArchivesUpdateList(archivesUpdateList)
			}
		},
		// 获取发票设置信息
		getInvoiceSet({ commit, state, rootState }) {
			const { invoiceSet } = state
			const { userLevel, branchCompanyOrgCode, isTenantAdmin } = rootState.user?.userInfo || {}
			const { id } = rootState.user?.tenant || {}
			if (!invoiceSet && userLevel != 0 && !isTenantAdmin) {
				let params = {
					tenantId: id,
					orgCode: branchCompanyOrgCode,
				}
				Promise.all([apiGetInvoiceOpenSetList(params), apiInvoiceSaler(params)]).then(resArr => {
					let result = {
						resident: {
							...((resArr[0] || []).find(item => item.userType == 2) || {}),
						}, //民用配置
						business: {
							...((resArr[0] || []).find(item => item.userType == 1) || {}),
						}, //工商配置
						info: {
							...(resArr[1] || {}),
						}, //发票信息
					}
					commit('SET_INVOICE_SET', result)
					setInvoiceSet(result)
				})
			}
		},
		// 退出清空
		resetArchives({ commit }) {
			commit('SET_MODIFY_PARAMS_LIST', [])
			commit('SET_ARCHIVES_UPDATE_LIST', [])

			removeModifyParamsList()
			removeArchivesUpdateList()
		},
	},
}
