<template>
	<GcElDialog
		:show="isShow"
		title="换表"
		width="1280px"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:dividLine>
				<div class="divid-line"></div>
			</template>
			<template v-slot:newMeterInfo>
				<Search v-if="isShow" style="float: right" :active-tab="{ id: 3 }" @use="handleSearchMeter" />
			</template>
		</GcFormRow>
	</GcElDialog>
</template>

<script>
import { isBlank } from '@/utils/validate.js'
import { RULE_STARTMETER_READING, ruleMaxLength, ruleRequired } from '@/utils/rules'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiGetMeterType, apiArchivesSwitchMeter, apiArchivesSwitchMeter1 } from '@/api/meterManage.api.js'
import { getFormItems } from './form.js'
import Search from '../search/index.vue'
export default {
	components: { Search },
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		detailData: {
			type: Object,
			default: () => {
				return {}
			},
		},
		permissionCode: {
			type: String,
			default: 'cpm_archives_switching-meter',
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	watch: {
		isShow: {
			async handler(val) {
				if (val) {
					await this._apiGetMeterType()
					this.formData.currentArchivesIdentity = this.detailData.archivesIdentity
					this.formData.currentMeterNo = this.detailData.meterNo
					if (!isBlank(this.detailData.meterReading)) {
						this.formData.meterReading = this.detailData.meterReading
					}
				}
			},
		},
	},
	data() {
		return {
			formData: {
				currentArchivesIdentity: '',
				currentMeterNo: '',
				operatorName: this.$store.getters.userInfo.staffName,
				meterReading: '',
				// 新水表信息
				meterNo: '',
				meterTypeId: '',
				meterWarehouseCode: '',
				manufacturerName: '',
				antiTheftCode: '',
				ranges: '',
				meterModel: '',
				caliber: '',
				installationDate: '',
				startMeterReading: '',
				baseMeterNo: '',
				useYears: '',
			},
			formItems: getFormItems(this),
			formAttrs: {
				labelWidth: '120px',
				rules: {
					meterReading: [ruleRequired('必填'), RULE_STARTMETER_READING],
					meterNo: [ruleRequired('必填'), ruleMaxLength(32)],
					meterTypeId: [{ required: true, message: '必填', trigger: 'change' }],
					installationDate: [ruleRequired('必填')],
					meterWarehouseCode: [ruleMaxLength(32)],
					antiTheftCode: [ruleMaxLength(32)],
					meterModel: [ruleMaxLength(30)],
					caliber: [ruleMaxLength(16)],
					startMeterReading: [ruleRequired('必填'), RULE_STARTMETER_READING],
					baseMeterNo: [ruleMaxLength(32)],
				},
			},
			meterId: '',
		}
	},
	methods: {
		_apiGetMeterType() {
			apiGetMeterType({
				tenantId: this.$store.getters.userInfo.tenantId,
			})
				.then(res => {
					const obj = this.formItems.find(item => item.prop === 'meterTypeId')
					obj.options = res.map(item => {
						return {
							value: item.meterTypeId,
							label: item.meterTypeName,
							...item,
						}
					})
				})
				.catch(() => {})
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.meterId = ''
			this.isShow = false
		},
		handleSearchMeter(obj) {
			Object.keys(this.formData).forEach(key => {
				if (Object.prototype.hasOwnProperty.call(obj, key) && key !== 'startMeterReading') {
					if (key === 'meterReading' && !isBlank(obj[key])) {
						this.formData.startMeterReading = obj[key]
					} else {
						this.formData[key] = obj[key]
					}
				}
			})
			this.meterId = obj.meterId
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const params = trimParams(removeNullParams(this.formData))
			delete params.currentArchivesIdentity

			const newParams = {
				meter: {
					...params,
					meterId: this.meterId,
				},
				archivesId: this.detailData.archivesId,
				originMeter: {
					meterReading: this.formData.meterReading,
				},
			}

			try {
				const apiMethods = {
					'cpm_archives_switching-meter': apiArchivesSwitchMeter,
					'cpm_archives_switching-meter1': apiArchivesSwitchMeter1,
				}
				await apiMethods[this.permissionCode](newParams)
				this.$message.success('换表成功')
				this.handleClose()
				this.$emit('refresh')
			} catch (error) {
				console.log(error)
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.divid-line {
	height: 1px;
	background-color: #eceff8;
}
::v-deep {
	.title {
		.el-form-item__label {
			font-weight: 500 !important;
			color: #000000 !important;
		}
	}
}
</style>
