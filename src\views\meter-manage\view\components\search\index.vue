<template>
	<div>
		<el-autocomplete
			ref="searchInput"
			v-model="inputValue"
			:fetch-suggestions="querySearchAsync"
			:select-when-unmatched="true"
			popper-class="box-search"
			placement="bottom-end"
			:popper-append-to-body="false"
			:suggestions-auto-close="false"
			:placeholder="placeholder"
			suffix-icon="el-icon-search"
			@select="handleUse"
		>
			<el-select
				v-if="isUserSearch"
				v-model="userSelect"
				slot="prepend"
				filterable
				:popper-append-to-body="false"
				style="width: 100px"
			>
				<el-option label="用户名" value="userName"></el-option>
				<el-option label="手机号" value="userMobile"></el-option>
			</el-select>
			<template v-if="isMeterSearch" slot="prepend">水表编号</template>
			<template v-if="isCompanySearch" slot="prepend">企业名称</template>
			<template slot-scope="{ item }">
				<div class="item">
					<div class="left" v-if="isUserSearch && userSelect == 'userMobile'">
						<div class="row-top">
							{{ item.userMobile }}
						</div>
						<div class="row-bottom">
							<el-row :gutter="10">
								<el-col :span="8">
									<span>用户类型：{{ item.userTypeName }}</span>
								</el-col>
								<el-col :span="16">
									<span>用户名称：{{ item.userName }}</span>
								</el-col>
							</el-row>
						</div>
					</div>
					<div class="left" v-if="isUserSearch && userSelect == 'userName'">
						<div class="row-top text-overflow">
							{{ item.userName }}
						</div>
						<div class="row-bottom">
							<el-row :gutter="10">
								<el-col :span="8">
									<span>用户类型：{{ item.userTypeName }}</span>
								</el-col>
								<el-col :span="16">
									<span>手机号：{{ item.userMobile }}</span>
								</el-col>
							</el-row>
						</div>
					</div>
					<div class="left" v-if="isMeterSearch">
						<div class="row-top text-overflow">
							{{ item.meterNo }}
						</div>
						<div class="row-bottom">
							<el-row :gutter="10">
								<el-col :span="12">
									<span>表类型：{{ item.meterTypeName || '--' }}</span>
								</el-col>
								<el-col :span="12">
									<span>水表仓库编号：{{ item.meterWarehouseCode || '--' }}</span>
								</el-col>
							</el-row>
							<el-row>
								<el-col :span="12">
									<span>厂商名称：{{ item.manufacturerName || '--' }}</span>
								</el-col>
							</el-row>
						</div>
					</div>
					<div class="left" v-if="isCompanySearch && userSelect == 'userName'">
						<div class="row-top text-overflow">
							{{ item.userName }}
						</div>
						<div class="row-bottom">
							<el-row :gutter="10">
								<el-col :span="8">
									<span>企业地址：{{ item.companyAddress }}</span>
								</el-col>
							</el-row>
						</div>
					</div>
					<div class="right">
						<button class="gc-button gc-button-two" type="button">
							{{ btnText }}
						</button>
					</div>
				</div>
			</template>
		</el-autocomplete>
	</div>
</template>

<script>
import { apiGetUserInfoByPhoneOrUserName, apiGetMeterInfoByMeterNo } from '@/api/meterManage.api.js'
import { isBlank, isArray } from '@/utils/validate'

export default {
	name: 'Search',
	props: {
		activeTab: Object,
		userType: [String, Number],
	},
	data() {
		return {
			inputValue: '',
			userInput: '',
			meterInput: '',
			userSelect: 'userName',
		}
	},
	watch: {
		'activeTab.id': {
			handler(id) {
				switch (id) {
					case 2:
						this.inputValue = this.userInput
						break
					case 3:
						this.inputValue = this.meterInput
						break
				}
			},
		},
	},
	computed: {
		// 用户信息搜索
		isUserSearch() {
			return this.activeTab.id === 2
		},
		// 水表编号信息搜索
		isMeterSearch() {
			return this.activeTab.id === 3
		},
		// 企业名称搜索
		isCompanySearch() {
			return this.activeTab.id === 4
		},
		placeholder() {
			let str = '请输入'
			if (this.isMeterSearch) {
				str += '水表编号'
			}
			if (this.isUserSearch) {
				str += this.userSelect === 'userMobile' ? '完整手机号' : '用户名'
			}
			if (this.isCompanySearch) {
				str += '企业名称'
			}
			return str
		},
		btnText() {
			return this.isMeterSearch ? '使用此水表' : '使用此用户'
		},
		// 系统数据
		dataList() {
			return this._.get(this.$store, 'getters.dataList', {})
		},
		// 用户类型
		userTypeList() {
			return this._.get(this.dataList, 'userType', [])
		},
	},
	methods: {
		async querySearchAsync(queryString, cb) {
			if (isBlank(queryString)) return cb([])
			let result, methodsName
			if (this.isUserSearch) {
				if (this.userSelect === 'userMobile' && queryString.length !== 11) {
					// 手机号查询接口只支持11位
					return cb([])
				}
				methodsName = 'getUserSearch'
				this.userInput = queryString
			}
			if (this.isCompanySearch) {
				methodsName = 'getUserSearch'
				this.userInput = queryString
			}
			if (this.isMeterSearch) {
				methodsName = 'getMeterSearch'
				this.meterInput = queryString
			}
			result = await this[methodsName](queryString)
			return cb(result)
		},
		getUserSearch(queryString) {
			const obj = {}
			obj[this.userSelect] = queryString
			if (this.userType) {
				obj['userType'] = this.userType
			}
			const userTypes = {}
			for (let i = 0; i < this.userTypeList.length; i++) {
				userTypes[this.userTypeList[i].sortValue] = this.userTypeList[i].sortName
			}
			return new Promise((resolve, reject) => {
				apiGetUserInfoByPhoneOrUserName(obj)
					.then((res = []) => {
						const datas = res.records.map(item => {
							return {
								...item,
								userTypeName: userTypes[item.userType],
							}
						})
						resolve(datas)
					})
					.catch(() => {
						reject([])
					})
			})
		},
		getMeterSearch(queryString) {
			return new Promise((resolve, reject) => {
				apiGetMeterInfoByMeterNo({ meterNo: queryString })
					.then(res => {
						const records = isArray(res) ? res : []
						resolve(records)
					})
					.catch(() => {
						reject([])
					})
			})
		},
		handleUse(item) {
			// eslint-disable-next-line no-prototype-builtins
			if (item.hasOwnProperty('value')) return // 回车操作
			this.$emit('use', item)
		},
	},
}
</script>
<style lang="scss" scoped>
::v-deep .el-input-group__prepend {
	width: 100px;
}

::v-deep .box-search {
	width: 561px !important;
	color: rgba(78, 78, 78, 1);

	.popper__arrow {
		/* // todo 去掉下拉框角css不起作用 */
		border: 1px solid black;
		display: none !important;
	}

	.item {
		.left {
			float: left;
			width: calc(100% - 115px);

			.row-top {
				font-weight: 500;
				color: rgba(78, 78, 78, 1);
				&.text-overflow {
					@include text-overflow;
				}
			}

			.row-bottom {
				color: rgba(153, 153, 153, 1);
				font-size: 14px;
			}

			.el-col-5,
			.el-col-8,
			.el-col-11,
			.el-col-12,
			.el-col-24 {
				@include text-overflow;
			}
		}

		.right {
			float: right;
			width: 105px;
			height: 65px;
			line-height: 60px;
		}
	}
}
</style>
