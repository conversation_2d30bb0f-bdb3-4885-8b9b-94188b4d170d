export const getFormItem = _this => {
	return [
		{
			type: 'el-select',
			label: '开票类型',
			prop: 'invoiceType',
			options:
				_this.$store.getters?.dataList?.invoiceType?.map(item => {
					return {
						label: item.sortName,
						value: item.sortValue,
					}
				}) || [],
		},
		{
			type: 'el-input',
			label: '纳税人识别号',
			prop: 'taxpayerIdentity',
		},
		{
			type: 'el-input',
			label: '开户银行',
			prop: 'openBank',
		},
		{
			type: 'el-input',
			label: '银行账户',
			prop: 'bankAccount',
		},
	]
}
