<template>
	<div class="container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleChangePage({ page: 1 })">
					<i class="iconfontCis icon-small-search"></i>
					筛选
				</el-button>
				<el-button @click="handleReset">
					<i class="iconfontCis icon-reset"></i>
					重置
				</el-button>
			</el-form-item>
		</GcFormSimple>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handleChangePage"
			>
				<template v-slot:imageUrl="{ row }">
					<el-image
						v-if="row.imageUrl"
						:src="row.imageUrl"
						:previewSrcList="[row.imageUrl]"
						style="width: 60px; height: 60px"
					/>
					<span v-else>--</span>
				</template>
			</GcTable>
		</div>
	</div>
</template>

<script>
import { getfilterName } from '@/utils'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiGetMeterModifyRecords } from '@/api/meterManage.api'
export default {
	props: {
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			formData: {
				modifyTime: '',
				meterNo: '',
				operatorType: '',
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '操作日期',
					prop: 'modifyTime',
					attrs: {
						type: 'daterange',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						style: {
							width: '250px',
						},
					},
				},
				{
					type: 'el-input',
					label: '水表编号',
					prop: 'meterNo',
				},
				{
					type: 'el-select',
					label: '操作类型',
					prop: 'operatorType',
					options: this.$store.getters.dataList.operatorMeterType
						? this.$store.getters.dataList.operatorMeterType.map(item => {
								return {
									label: item.sortName,
									value: item.sortValue,
								}
						  })
						: [],
					attrs: {
						style: {
							width: '150px',
						},
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '80px',
			},
			columns: [
				{
					key: 'createTime',
					name: '操作时间',
					tooltip: true,
					minWidth: 200,
				},
				{
					key: 'operatorType',
					name: '操作类型',
					tooltip: true,
					render: (h, row, total, scope) => {
						const valueStr = getfilterName(
							this.$store.getters.dataList?.operatorMeterType,
							row[scope.column.property],
							'sortValue',
							'sortName',
						)
						return h('span', {}, valueStr)
					},
				},
				{
					key: 'createStaffName',
					name: '操作人',
					tooltip: true,
				},
				{
					key: 'meterNo',
					name: '水表编号',
					tooltip: true,
				},
				{
					key: 'manufacturerName',
					name: '水厂厂商',
					tooltip: true,
				},
				{
					key: 'meterModel',
					name: '水表型号',
					tooltip: true,
				},
				{
					key: 'meterTypeName',
					name: '水表类型',
					tooltip: true,
				},
				{
					key: 'meterReading',
					name: '指针数',
					tooltip: true,
				},
				{
					key: 'imageUrl',
					name: '相关照片',
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
		}
	},
	methods: {
		async getList() {
			this.loading = true
			try {
				const extractedData = Object.assign({}, ...Object.values(this.tabData))
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
					archivesId: extractedData?.archivesId,
				})
				if (formParams.modifyTime && formParams.modifyTime.length > 1) {
					Object.assign(formParams, {
						modifyTimeStart: this.dayjs(formParams.modifyTime[0]).format('YYYY-MM-DD'),
						modifyTimeEnd: this.dayjs(formParams.modifyTime[1]).format('YYYY-MM-DD'),
					})
					delete formParams.modifyTime
				}
				const { records, total } = await apiGetMeterModifyRecords(formParams)
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleSearch() {
			this.handleChangePage({ page: 1 })
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.handleSearch()
		},
	},
}
</script>
