<template>
	<div class="archives-detail" v-loading.fullscreen.lock="loading">
		<!-- 表卡信息 -->
		<MeterInfo :reqData="archivesDetail" @refresh="_apiGetArchivesDetail" />
		<!-- 详情tab切换区 -->
		<div class="archives-content">
			<gc-detail-tab
				ref="detailTabRef"
				:tab-list="tabList"
				:default-active-name.sync="defaultActiveName"
				@controlLoading="controlLoading"
				@refresh="_apiGetArchivesDetail"
				@tab-change="handleTabChange"
				@openInvoice="handleOpenInvoice"
			></gc-detail-tab>
		</div>
		<!-- 开票 -->
		<PaymentInvoiceDialog
			ref="openInvoiceDialog"
			:type="openInvoiceType"
			:billList="billList"
			:api="invoiceApi"
			:show.sync="openInvoiceDialogShow"
			@success="handleOpenInvoiceSuccess"
		/>
	</div>
</template>

<script>
import { apiGetArchivesDetail } from '@/api/meterManage.api'
import MeterInfo from './meter-info' // 表卡信息
import Overview from './overview' // 概览
import MeterRecords from './meter-records' // 水表记录
import UserRecords from './user-records' // 用户记录
import ReadingRecords from './reading-records' // 抄表记录
import PaymentRecords from './payment-records' // 缴费记录
import BillRecords from './bill-records' // 账单记录
// import PreDepositRecords from "./pre-deposit-records"; // 预存记录
import CallRecords from './call-records' // 催缴记录
import InvoiceRecords from './invoice-records' // 开票记录
import PriceRecords from './price-records' // 价格记录
import StatusChangeRecords from './status-change-records' // 状态变更记录
import PaymentInvoiceDialog from '@/components/PaymentInvoiceDialog'
import { mergeOpenInvoice2 } from '@/api/costManage.api'
export default {
	name: 'MeterView',
	components: {
		MeterInfo,
		PaymentInvoiceDialog,
	},
	computed: {
		tabList() {
			let arr = [
				{
					name: 'Overview',
					label: '概览',
					component: Overview,
					data: this.archivesDetail,
				},
			]
			this.$has('cpm_archives_meter-modify-records') &&
				this.archivesDetail?.archives?.virtualMeterType !== 1 &&
				arr.push({
					name: 'MeterRecords',
					label: '水表记录',
					component: MeterRecords,
					data: this.archivesDetail,
				})
			this.$has('cpm_archives_modifyrecords') &&
				this.archivesDetail?.archives?.virtualMeterType !== 1 &&
				arr.push({
					name: 'UserRecords',
					label: '用户记录',
					component: UserRecords,
					data: this.archivesDetail,
				})
			this.$has('cpm_meterReadingTask_archivesId-record-list') &&
				this.archivesDetail?.archives?.virtualMeterType !== 1 &&
				arr.push({
					name: 'ReadingRecords',
					label: '抄表记录',
					component: ReadingRecords,
					data: this.archivesDetail,
				})

			this.$has('cpm_archives_billPay-record-list') &&
				arr.push({
					name: 'PaymentRecords',
					label: '缴费记录',
					component: PaymentRecords,
					data: this.archivesDetail,
				})

			this.$has('cpm_archives_bill-record-list') &&
				arr.push({
					name: 'BillRecords',
					label: '账单记录',
					component: BillRecords,
					data: this.archivesDetail,
				})
			this.$has('cpm_urge_payment_queryUrgeRecordPage1') &&
				arr.push({
					name: 'CallRecords',
					label: '催缴记录',
					component: CallRecords,
					data: this.archivesDetail,
				})
			// this.$has("cpm_archives_costrecords") &&
			//   this.archivesDetail?.archives?.virtualMeterType !== 1 &&
			//   arr.push({
			//     name: "PreDepositRecords",
			//     label: "预存记录",
			//     component: PreDepositRecords,
			//     data: this.archivesDetail,
			//   });

			this.$has('payment_invoice_record-list1') &&
				arr.push({
					name: 'InvoiceRecords',
					label: '开票记录',
					component: InvoiceRecords,
					data: this.archivesDetail,
				})
			this.$has('cpm_archives_history-price') &&
				arr.push({
					name: 'PriceRecords',
					label: '价格记录',
					component: PriceRecords,
					data: this.archivesDetail,
				})
			this.$has('cpm_archives_mobile-modify-records') &&
				arr.push({
					name: 'StatusChangeRecords',
					label: '状态变更记录',
					component: StatusChangeRecords,
					data: this.archivesDetail,
				})
			return arr
		},
	},
	data() {
		this.invoiceApi = mergeOpenInvoice2
		return {
			loading: false,
			defaultActiveName: 'Overview',
			controlLoading: false,
			archivesDetail: {},
			openInvoiceType: '',
			billList: [],
			openInvoiceDialogShow: false,
			onInvoiceOpenDone: null,
		}
	},
	activated() {
		const { archivesId, tabName } = this.$route.query
		if (archivesId) {
			if (tabName) {
				this.defaultActiveName = tabName
			}
			this._apiGetArchivesDetail({ archivesId })
		}
	},
	methods: {
		_apiGetArchivesDetail() {
			apiGetArchivesDetail({ archivesId: this.$route.query.archivesId })
				.then(res => {
					this.archivesDetail = res
				})
				.catch(() => {})
		},
		handleTabChange(data) {
			this.$refs.detailTabRef.$refs.componentRef[data.index].handleSearch()
		},
		handleOpenInvoice({ type, data, onSuccess }) {
			if (!data || !data.length) {
				return this.$message.error('请选择需要开票的账单')
			}
			this.openInvoiceType = type
			this.openInvoiceDialogShow = true
			this.billList = data
			this.onInvoiceOpenDone = onSuccess
		},
		handleOpenInvoiceSuccess() {
			if (this.onInvoiceOpenDone) {
				this.onInvoiceOpenDone()
				this.onInvoiceOpenDone = null
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.archives-detail {
	width: 100%;
	height: 100%;
	display: flex;
	.archives-content {
		width: calc(100% - 290px);
	}
}
::v-deep {
	.container {
		background-color: #fff;
		padding: 20px;
		display: flex;
		flex-direction: column;
		height: 100%;
		.table-container {
			flex: 1;
			overflow: auto;
		}
	}
}
</style>
