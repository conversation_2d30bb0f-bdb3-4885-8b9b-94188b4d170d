<template>
	<gc-el-dialog
		:show="isShow"
		title="修改册内序号"
		custom-top="220px"
		width="500px"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleOk"
	>
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
	</gc-el-dialog>
</template>

<script>
import { updateArchivesSeq } from '@/api/meterReading.api.js'

export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		id: {
			type: [String, Number],
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				recordSeq: '',
			},
			formItems: [
				{
					type: 'el-input-number',
					label: '册内序号',
					prop: 'recordSeq',
					attrs: {
						col: 24,
						min: 0,
						max: 99999999,
						stepStrictly: true,
						placeholder: '请输入册内序号',
						style: {
							width: '180px',
						},
					},
				},
			],
			formAttrs: {
				labelWidth: '110px',
				labelPosition: 'right',
				rules: {
					recordSeq: [{ required: true, message: '请输入册内序号', trigger: 'blur' }],
				},
			},
		}
	},
	methods: {
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		async handleOk() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				await updateArchivesSeq({
					archivesId: this.id,
					recordSeq: this.formData.recordSeq,
				})
				this.$message.success('修改成功')
				this.$emit('success')
				this.isShow = false
			}
		},
		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>

<style lang="scss" scoped></style>
