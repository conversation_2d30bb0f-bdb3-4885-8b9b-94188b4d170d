<template>
	<gc-el-dialog :show="isShow" :title="`新增罚没款`" custom-top="120px" width="860px" @close="handleClose">
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:recoverAmount>
				<div class="recover-amount">
					<span class="label">追缴金额</span>
					<span class="value">￥ {{ formData.recoverAmount | toThousand }}</span>
				</div>
			</template>
		</GcFormRow>
		<GcFormRow
			ref="invoiceFormRef"
			v-model="invoiceFormData"
			:formItems="invoiceFormItems"
			:formAttrs="invoiceFormAttrs"
		>
			<template v-slot:invoiceInfo>
				<h5 class="gap-title">开票信息</h5>
			</template>
			<template v-slot:taxpayerIdentity>
				<el-autocomplete
					style="width: 100%"
					v-model="invoiceFormData.taxpayerIdentity"
					:fetch-suggestions="queryTaxpayerIdentity"
					placeholder="请输入"
					@select="handleTaxpayerIdentitySelect"
					@change="handleTaxpayerIdentityChange"
				>
					<template slot-scope="{ item }">
						<div class="billing-information-item">
							<p class="billing-information-item-row">纳税人识别号：{{ item.taxpayerIdentity }}</p>
							<p class="billing-information-item-row">开票抬头：{{ item.userName }}</p>
						</div>
					</template>
				</el-autocomplete>
			</template>
		</GcFormRow>

		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import _ from 'lodash'
import { RULE_POSITIVEINTEGERONLY, ruleMaxLength, RULE_INCORRECTEMAIL, RULE_PHONE } from '@/utils/rules'
import { addFine, getInfoByArchivesNo, calculatePrice } from '@/api/specialWater.api'
import { apiGetPriceList_all } from '@/api/meterManage.api.js'
import { queryInvoiceInfoList } from '@/api/print.api'
import { apiQueryInvoiceBuyer } from '@/api/userManage.api'

const simpleMerge = (primary, secondary) => {
	const isEmpty = value => {
		if (value === null || value === undefined) return true
		if (typeof value === 'string' && value.trim() === '') return true
		if (Array.isArray(value) && value.length === 0) return true
		if (typeof value === 'object' && Object.keys(value).length === 0) return true
		return false
	}
	Object.keys(primary).forEach(key => {
		if (isEmpty(primary[key])) {
			primary[key] = secondary[key] || ''
		}
	})

	return primary
}

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		invoiceFormItems() {
			return [
				{
					type: 'slot',
					slotName: 'invoiceInfo',
					attrs: {
						col: 24,
						className: 'invoice-info',
					},
				},
				{
					type: 'el-select',
					label: '开票类型',
					prop: 'invoiceType',
					options:
						this.$store.getters?.dataList?.invoiceType?.map(item => {
							return {
								label: item.sortName,
								value: Number(item.sortValue),
							}
						}) || [],
					attrs: {
						col: 12,
					},
				},
				{
					type: 'el-input',
					label: '手机号',
					prop: 'mobile',
					attrs: {
						col: 12,
					},
				},
				{
					type: 'el-input',
					label: '电子邮箱',
					prop: 'email',
					attrs: {
						col: 12,
					},
				},

				{
					type: 'slot',
					label: '纳税人识别号',
					slotName: 'taxpayerIdentity',
					attrs: {
						col: 12,
					},
				},
				{
					type: 'el-input',
					label: '开票抬头',
					prop: 'userName',
					attrs: {
						disabled: this.billingInfoDisabled,
						col: 12,
					},
				},
				{
					type: 'el-input',
					label: '开户银行',
					prop: 'openBank',
					attrs: {
						disabled: this.billingInfoDisabled,
						col: 12,
					},
				},
				{
					type: 'el-input',
					label: '银行账户',
					prop: 'bankAccount',
					attrs: {
						disabled: this.billingInfoDisabled,
						col: 12,
					},
				},
			]
		},
	},
	filters: {
		// 千分位
		toThousand(num) {
			return new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(num)
		},
	},
	created() {
		this._apiEffectivePrice()
		this.queryTaxpayerIdentity = _.debounce(this.queryTaxpayerIdentity, 250)
	},
	data() {
		return {
			formData: {
				orgCode: '',
				recoverReason: '',
				archivesNo: '',
				userName: '',
				phoneNum: '',
				recoverWaterVolume: '',
				recoverAmount: '',
				priceId: '',
				priceVersion: '',
			},
			invoiceFormData: {
				invoiceType: '',
				mobile: '',
				email: '',
				taxpayerIdentity: '',
				openBank: '',
				bankAccount: '',
				userName: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						col: 12,
						placeholder: '请选择营业分公司',
					},
					events: {
						change: value => {
							if (value) {
								this.formData.archivesNo = ''
								this.formData.userName = ''
								this.formData.phoneNum = ''
								this.formItems[1].options = []
							}
						},
					},
				},
				{
					type: 'el-select',
					label: '表卡编号',
					prop: 'archivesNo',
					attrs: {
						col: 12,
						filterable: true,
						remote: true,
						loading: false,
						clearable: true,
						placeholder: '请输入表卡编号进行查询',
						remoteMethod: this.getArchivesNoByInput,
					},
					options: [],
					events: {
						change: value => {
							if (value) {
								const target = this.archivesNoData.find(item => item.archivesNo === value)
								if (target) {
									this.setFormData(target)
								}
								this.queryInvoiceInfoList(target)
							} else {
								this.formItems[1].options = []
								this.archivesNoData = []
							}
						},
					},
				},
				{
					type: 'el-input',
					label: '用户名称',
					prop: 'userName',
					attrs: {
						col: 12,
						disabled: true,
						placeholder: '',
					},
				},
				{
					type: 'el-input',
					label: '手机号码',
					prop: 'phoneNum',
					attrs: {
						col: 12,
						disabled: true,
						placeholder: '',
					},
				},
				{
					type: 'el-input',
					label: '追缴水量',
					prop: 'recoverWaterVolume',
					attrs: {
						col: 12,
						placeholder: '请输入追缴水量',
					},
					events: {
						change: value => {
							if (value) {
								this.calculatePrice()
							} else {
								this.formData.recoverAmount = ''
							}
						},
					},
				},
				{
					type: 'el-select',
					label: '水量价格',
					prop: 'priceId',
					options: [],
					attrs: {
						col: 12,
						placeholder: '请选择水量价格',
					},
					events: {
						change: value => {
							if (value) {
								const priceField = this.formItems.find(item => item.prop === 'priceId')
								const price = priceField.options.find(item => item.priceId === value)
								this.formData.priceVersion = price.priceVersion
								this.calculatePrice()
							} else {
								this.formData.recoverAmount = ''
								this.formData.priceVersion = ''
							}
						},
					},
				},
				{
					type: 'el-input',
					label: '追缴原因',
					prop: 'recoverReason',
					attrs: {
						col: 24,
						type: 'textarea',
						placeholder: '请输入追缴原因',
						maxlength: '300',
						showWordLimit: true,
						autosize: {
							minRows: 2,
							maxRows: 6,
						},
					},
				},
				{
					type: 'slot',
					slotName: 'recoverAmount',
					attrs: {
						col: 24,
					},
				},
			],
			formAttrs: {
				labelPosition: 'right',
				labelWidth: '110px',
				rules: {
					orgCode: [{ required: true, message: '请选择营业分公司', trigger: 'change' }],
					archivesNo: [
						{
							required: true,
							message: '请选择表卡编号',
							trigger: 'change',
						},
					],
					recoverReason: [{ required: true, message: '请输入追缴原因', trigger: 'blur' }],
					recoverWaterVolume: [
						{ required: true, message: '请输入追缴水量', trigger: 'blur' },
						ruleMaxLength(10, '追缴水量'),
						{
							type: 'number',
							message: '只能输入数字',
							trigger: 'blur',
							transform: value => Number(value),
						},
						RULE_POSITIVEINTEGERONLY,
					],
					priceId: [{ required: true, message: '请选择水量价格', trigger: 'change' }],
				},
			},
			invoiceFormAttrs: {
				labelPosition: 'right',
				labelWidth: '110px',
				rules: {
					invoiceType: [{ required: true, message: '请选择开票类型', trigger: 'change' }],
					mobile: [RULE_PHONE],
					email: [RULE_INCORRECTEMAIL],
					userName: [ruleMaxLength(32)],
				},
			},
			// 表卡编号原始下拉数据
			archivesNoData: [],
			billingInfoDisabled: false,
		}
	},
	methods: {
		// 根据输入点表卡编号获取数据
		async getArchivesNoByInput(queryString) {
			if (queryString !== '') {
				this.formItems[1].attrs.loading = true
				try {
					const res = await getInfoByArchivesNo({
						orgCode: this.formData.orgCode,
						archivesNo: queryString,
					})
					this.archivesNoData = res || []
					this.formItems[1].options = this.archivesNoData.map(item => {
						return {
							label: item.archivesNo,
							value: item.archivesNo,
						}
					})
				} catch (error) {
					console.error(error)
					this.archivesNoData = []
					this.formItems[1].options = []
				} finally {
					this.formItems[1].attrs.loading = false
				}
			}
		},
		// 查询价格
		async _apiEffectivePrice() {
			const { records } = await apiGetPriceList_all()
			const obj = this.formItems.find(item => item.prop === 'priceId')
			obj.options = records.map(item => {
				return {
					label: `${item.priceName}`,
					value: item.priceId,
					disabled: [1, 9].includes(item.enableFlag) ? false : true,
					...item,
				}
			})
		},
		// 查询发票信息
		async queryInvoiceInfoList(target) {
			const data = await queryInvoiceInfoList({
				archivesIds: [target.archivesId],
				// 罚没款查询
				finesQuery: true,
				orgCode: this.formData.orgCode,
				archivesNo: target.archivesNo,
			})
			this.invoiceFormData.mobile = target.phoneNum
			this.invoiceFormData.email = target.email
			if (data && data.length) {
				const invoiceInfo = data.find(item => item.defualt) || {}
				delete invoiceInfo.phoneNumber
				delete invoiceInfo.email
				const { invoiceType } = invoiceInfo
				this.invoiceFormData = simpleMerge(this.invoiceFormData, invoiceInfo)
				if (invoiceType) {
					this.invoiceFormData.invoiceType = Number(invoiceType)
				}
			}
		},
		// 计算追缴金额
		async calculatePrice() {
			const { recoverWaterVolume, priceId, priceVersion } = this.formData
			if (!recoverWaterVolume || !priceId) {
				this.formData.recoverAmount = ''
				return
			}
			const params = {
				priceId,
				priceVersion,
				usAmount: recoverWaterVolume,
			}
			const data = await calculatePrice(params)
			this.formData.recoverAmount = data
		},

		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			const invoiceValid = await this.$refs.invoiceFormRef.validate()
			if (valid && invoiceValid) {
				this.formData.invoiceInfo = this.invoiceFormData
				await addFine(this.formData)
				this.$message.success(`新增成功`)
				this.$emit('success')
				this.isShow = false
			}
		},

		handleClose() {
			this.formData = {
				orgCode: '',
				recoverReason: '',
				archivesNo: '',
				userName: '',
				phoneNum: '',
				recoverWaterVolume: '',
				recoverAmount: '',
				priceId: '',
				priceVersion: '',
			}
			this.invoiceFormData = {
				invoiceType: '',
				mobile: '',
				email: '',
				taxpayerIdentity: '',
				openBank: '',
				bankAccount: '',
				userName: '',
			}
			this.$refs.formRef.resetFields()
			this.archivesNoData = []
			this.formItems[1].options = []
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			const { invoiceInfo, ...formData } = data
			this.formData = Object.assign(this.formData, formData)
			this.formData.recoverWaterVolume = (formData.recoverWaterVolume ?? '') + ''
			this.invoiceFormData = Object.assign(this.invoiceFormData, invoiceInfo)
		},
		async queryTaxpayerIdentity(taxpayerIdentity, callback) {
			taxpayerIdentity = taxpayerIdentity || ''
			try {
				let result = await apiQueryInvoiceBuyer({
					taxpayerIdentity,
				})
				result = result || []
				const list = result.map(item => {
					const { taxpayerIdentity } = item
					return { ...item, value: taxpayerIdentity }
				})

				callback(list)
			} catch (e) {
				console.log(e)
				callback([])
			}
		},
		handleTaxpayerIdentitySelect(item) {
			const { userName, openBank, bankAccount } = item
			this.invoiceFormData.openBank = openBank
			this.invoiceFormData.userName = userName
			this.invoiceFormData.bankAccount = bankAccount
			this.billingInfoDisabled = true
		},
		handleTaxpayerIdentityChange() {
			this.billingInfoDisabled = false
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.el-dialog__body {
		height: 500px;
	}
	.invoice-info {
		margin-bottom: 10px;
	}
}
.recover-amount {
	display: flex;
	align-items: center;
	.label {
		width: 110px;
		padding-right: 20px;
		text-align: right;
	}
	.value {
		font-size: 20px;
		font-weight: 600;
	}
}
.gap-title {
	margin-left: 10px;
	padding-top: 10px;
	color: #222222;
	font-size: 14px;
	font-weight: bold;
	border-top: 1px dashed #d9d9d9;
}

.billing-information-item {
	padding: 8px 0;
}
</style>
