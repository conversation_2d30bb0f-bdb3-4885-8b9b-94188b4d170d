import Layout from '@/layout'

export default [
	{
		path: '/ticketManage',
		name: 'TicketManage',
		component: Layout,
		redirect: '/ticketManage/invoiceInfo',
		meta: {
			title: '票据',
			icon: 'icon-cis_yj_piaoju',
			permissions: ['payment_invoice-buyer_list', 'payment_invoice_record-list', 'payment_invoice_pdf'],
		},
		children: [
			{
				path: 'invoiceInfo',
				name: 'InvoiceInfo',
				component: () => import('@/views/ticket-manage/invoice-info/index.vue'),
				meta: {
					title: '开票信息',
					keepAlive: true,
					icon: 'icon-cis_ej_kaipiaoxinxi',
					permissions: ['payment_invoice-buyer_list'],
				},
			},
			{
				path: 'invoiceRecord',
				name: 'InvoiceRecord',
				component: () => import('@/views/ticket-manage/invoice-record/index.vue'),
				meta: {
					title: '开票记录',
					keepAlive: true,
					icon: 'icon-cis_ej_kaipiaojilu',
					permissions: ['payment_invoice_record-list', 'payment_invoice_pdf'],
				},
			},
			{
				path: 'invoiceRecordDetail',
				name: 'invoiceRecordDetail',
				component: () => import('@/views/ticket-manage/invoice-record/detail.vue'),
				hidden: true,
				meta: {
					title: '发票详情',
					keepAlive: true,
					icon: 'icon-cis_ej_kaipiaojilu',
					permissions: ['payment_invoice_record-list'],
				},
			},
		],
	},
]
