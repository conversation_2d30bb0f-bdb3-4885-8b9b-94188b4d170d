<template>
	<GcElDialog
		:show="isShow"
		:title="titleName"
		:okText="titleName"
		@close="handleClose"
		@ok="handleSave"
		@cancel="handleClose"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:file>
				<GcUploadFile v-model="formData.file" />
			</template>
		</GcFormSimple>
	</GcElDialog>
</template>

<script>
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
export default {
	name: 'GiveBackDialog',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		tab: {
			require: true,
			type: String,
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		titleName() {
			return this.tab === 'give' ? '生成送盘文件' : '导入回盘文件'
		},
	},
	watch: {
		tab: {
			handler(val) {
				{
					if (val === 'give') {
						this.formItems = [
							{
								type: 'el-select',
								label: '营业分公司',
								prop: 'orgCode',
								options: this.$store.getters.orgList,
							},
						]
					} else {
						this.formItems = [
							{
								type: 'el-select',
								label: '营业分公司',
								prop: 'orgCode',
								options: this.$store.getters.orgList,
							},
							{
								type: 'el-date-picker',
								label: '对账文件月份',
								prop: 'month',
								attrs: {
									type: 'month',
									format: 'yyyy-MM',
									valueFormat: 'yyyy-MM',
								},
							},
							{
								type: 'el-date-picker',
								label: '回盘日期',
								prop: 'date1',
								attrs: {
									type: 'date',
									format: 'yyyy-MM-dd',
									valueFormat: 'yyyy-MM-dd',
								},
							},
							{
								type: 'slot',
								slotName: 'file',
								prop: 'file',
								label: '文件选择',
								attrs: {},
							},
						]
					}
					this.formItems.map(item => {
						let value = ''
						if (item.prop === 'file') {
							value = []
						}
						this.$set(this.formData, item.prop, value)
					})
				}
			},
			immediate: true,
		},
	},
	data() {
		return {
			formData: {},
			formItems: [],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					orgCode: [ruleRequired('必填')],
					month: [ruleRequired('必填')],
					date1: [ruleRequired('必填')],
					file: [ruleRequired('必填')],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const formParams = trimParams(removeNullParams(this.formData))
			console.log(formParams)
			const executeSave = async () => {
				// await apiRecordTakeOver(formParams);
				this.$message.success(this.titleName + '成功')
				this.$emit('success')
				this.handleClose()
			}
			if (this.tab === 'give') {
				this.$confirm(
					'确认对该查询条件生成送盘文件吗?',
					'生成送盘文件后, 相关账单将被锁定，无法进行减免，调整以及部分缴费，请确认账单是否调整完毕',
				).then(executeSave)
			} else {
				await executeSave()
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
	},
}
</script>
