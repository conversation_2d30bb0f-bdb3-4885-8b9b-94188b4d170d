import { getDataList, setDataList, getOrgList, setOrgList } from '@/utils/storage'
import { apiGetDataList } from '@/api/user.api'
import { queryBusinessHallMap } from '@/api/basicConfig.api'
import DataList from '@/consts/dataList.js'
export default {
	namespaced: true,
	state: {
		dataList: getDataList() || {},
		orgList: getOrgList() || [],
	},
	mutations: {
		SET_DATA_LIST(state, dataList) {
			state.dataList = dataList
		},
		SET_ORG_LIST(state, orgList) {
			state.orgList = orgList
		},
	},
	actions: {
		removeDataList({ commit }) {
			commit('SET_DATA_LIST', {})
		},
		getDataList({ commit }, data) {
			commit('SET_DATA_LIST', DataList)
			setDataList(DataList)
			return new Promise((resolve, reject) => {
				apiGetDataList(data)
					.then(res => {
						if (data && data.codes) {
							// 分模块获取直接resolve response
							resolve(res)
						}
						commit('SET_DATA_LIST', res)
						setDataList(res)
						resolve(res)
					})
					.catch(err => {
						reject(err)
					})
			})
		},
		removeOrgList({ commit }) {
			commit('SET_ORG_LIST', {})
		},
		getOrgList({ commit }) {
			return new Promise((resolve, reject) => {
				queryBusinessHallMap()
					.then(res => {
						const data = res
							? res.map(item => {
									return {
										label: item.orgName,
										value: item.orgCode,
									}
							  })
							: []
						commit('SET_ORG_LIST', data)
						setOrgList(data)
						resolve(data)
					})
					.catch(error => {
						reject(error)
					})
			})
		},
	},
}
