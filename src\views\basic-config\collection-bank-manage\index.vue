<template>
	<div class="wrapper">
		<div class="right-top">
			<el-button type="primary" @click="handleAdd">新增托收银行</el-button>
		</div>
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			@current-page-change="handlePageChange"
		>
			<template v-slot:deal="{ row }">
				<el-button type="text" size="medium" @click="handleAdjust(row)">修改</el-button>
				<el-button type="text" size="medium" @click="handleDelete(row)">删除</el-button>
			</template>
		</GcTable>

		<!-- 新增、编辑弹窗 -->
		<UpdateDialog ref="updateDialogRef" :show.sync="showUpdate" :editType="editType" @success="getList(1)" />
	</div>
</template>

<script>
import UpdateDialog from './components/UpdateDialog.vue'

export default {
	name: 'CollectionBankManage',
	components: { UpdateDialog },
	data() {
		return {
			loading: false,
			tableData: [{}],
			columns: [
				{
					key: 'bankName',
					name: '银行名称',
					tooltip: true,
				},
				{
					key: 'bankNo',
					name: '行号',
					tooltip: true,
				},
				{
					key: 'status',
					name: '状态',
					tooltip: true,
				},
				{
					key: 'deal',
					name: '操作',
					fixed: 'right',
					width: 120,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 新增、编辑弹窗
			showUpdate: false,
			editType: 'add',
		}
	},

	created() {
		this.getList()
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.getList(1)
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				// TODO: 接口对接
				// const { current, size } = this.pageData;
				// const { total = 0, records = [] } = await getBookList({
				//   size,
				//   current,
				//   ...this.params,
				// });
				// this.pageData.total = total;
				// this.tableData = records;
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 新增
		handleAdd() {
			this.editType = 'add'
			this.showUpdate = true
		},
		// 修改
		handleAdjust(data) {
			this.editType = 'edit'
			this.showUpdate = true
			this.$nextTick(() => {
				this.$refs.updateDialogRef.setFormData(data)
			})
		},
		// 删除
		handleDelete(data) {
			console.log('🚀 ~ handleDelete ~ data:', data)
			this.$confirm(`正在对【${data.bankName ?? '--'}】进行删除，请确认是否删除？`).then(() => {
				this.$message.success('删除成功')
				this.getList(1)
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	padding: 20px;
}

.right-top {
	display: flex;
	justify-content: flex-end;
	align-items: flex-start;
	margin-bottom: 12px;
}
</style>
