<template>
	<div class="fake-search-container" @click="onfakeSearchClick">
		<span v-if="curValue" class="content">{{ curValue }}</span>
		<span v-else>{{ title }}</span>
		<i class="el-icon-close" v-if="curValue" @click.stop="clearVal"></i>
		<i class="el-icon-search" v-else></i>
	</div>
</template>

<script>
export default {
	name: 'GcSearchFake',
	components: {},
	props: {
		title: {
			type: String,
			default: '搜索',
		},
		valueObj: {
			type: Object,
		},
	},
	data() {
		return {}
	},
	computed: {
		curValue() {
			return (this.valueObj && this.valueObj['value']) || ''
		},
	},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		onfakeSearchClick() {
			this.$emit('handleclick')
		},
		clearVal() {
			this.valueObj['value'] = null
			this.$emit('clear', this.valueObj)
		},
	},
}
</script>
<style lang="scss" scoped>
.fake-search-container {
	position: relative;
	width: 238px;
	height: 40px;
	background: #ffffff;
	border-radius: 4px;
	border: 1px solid #2f87fe;
	padding: 10px 20px 10px 10px;
	box-sizing: border-box;
	cursor: pointer;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	.content {
		color: #4a4a4a;
	}
	span {
		width: 174px;
		height: 14px;
		font-size: 14px;
		font-weight: 400;
		color: #b2d2fe;
		line-height: 21px;
		text-shadow: 0px 6px 23px rgba(0, 0, 0, 0.03);
	}
	i {
		position: absolute;
		top: 14px;
		right: 10px;
		font-size: 10px;
		color: #2f87fe;
	}
}
</style>
