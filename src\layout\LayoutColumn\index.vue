<template>
	<!--分栏布局 -->
	<div class="gc-layout-column fixed">
		<div class="gc-layout-column">
			<column-bar />
		</div>
		<div
			class="gc-main gc-main-card"
			:class="{
				'is-collapse-main': collapse,
			}"
		>
			<div class="gc-layout-header fixed-header">
				<gc-nav />
				<gc-tags-view />
			</div>
			<gc-app-main />
		</div>
	</div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
	name: 'LayoutColumn',
	props: {
		collapse: {
			type: Boolean,
			default() {
				return false
			},
		},
	},
	data() {
		return {
			arrKey: [],
		}
	},
	computed: {
		...mapGetters({
			theme: 'settings/theme',
		}),
	},
	created() {
		document.addEventListener('keydown', this.handleKeyDown)
		document.addEventListener('keyup', this.handleKeyUp)
	},
	methods: {
		...mapActions({
			toggleCollapse: 'settings/toggleCollapse',
		}),
		handleKeyDown(e) {
			if (this.arrKey.length > 0) {
				// a-z的按键 长按去重
				if (this.arrKey.indexOf(e.key.toLowerCase()) >= 0) {
					return
				}
			}
			this.arrKey.push(e.key.toLowerCase())
			this.keydown = this.arrKey.join('+')
			// 监听按键捕获
			if (this.keydown == 'shift+c') {
				this.keydown = ''
				this.toggleCollapse()
				e.preventDefault() //取消浏览器原有的操作
			}
		},
		handleKeyUp(e) {
			this.arrKey.splice(this.arrKey.indexOf(e.key.toLowerCase()), 1)
			this.keydown = this.arrKey.join('+')
			e.preventDefault()
		},
	},
	destroyed() {
		document.removeEventListener('keydown', this.handleKeyDown)
		document.removeEventListener('keyup', this.handleKeyUp)
	},
}
</script>

<style lang="scss" scoped>
.gc-layout-column {
	.gc-main {
		.fixed-header {
			left: $base-left-menu-width;
			width: $base-right-content-width;
		}

		&.is-collapse-main {
			&.gc-main-horizontal {
				margin-left: $base-left-menu-width-min * 1.3;

				::v-deep {
					.fixed-header {
						left: $base-left-menu-width-min * 1.3;
						width: calc(100% - #{$base-left-menu-width-min} * 1.3);
					}
				}
			}
		}
	}
}
</style>
