/* 默认主题 */
body.gc-theme-default {
  $base-color-blue-active: $base-color-blue;
  $base-color-blue-light: $base-column-submenu-background-active;

  @mixin container {
    background: $base-color-blue !important;
  }

  @mixin active {
    &:hover {
      color: $base-color-white;
      background-color: $base-color-blue-active !important;
    }

    &.is-active {
      color: $base-color-white;
      background-color: $base-color-blue-active !important;
    }
  }

  .logo-container-horizontal {
    background: $base-color-blue !important;
  }

  .logo-container-vertical {
    @include container;
  }

  .logo-container-column {
    .logo {
      @include container;
    }
  }

  .column-bar-container {
    .el-tabs {
      .el-tabs__nav-wrap.is-left {
        @include container;
        padding-top: 30px;
      }

      .el-tabs__nav {
        @include container;
      }
    }

    .el-menu {
      .el-menu-item.is-active,
      .el-submenu__title.is-active,
      .el-menu-item:hover,
      .el-submenu__title:hover {
        i {
          color: $base-color-blue !important;
        }

        color: $base-color-blue !important;
        background-color: $base-color-blue-light !important;
      }
    }

    &-card {
      .el-tabs {
        .el-tabs__item {
          &.is-active {
            background: transparent !important;
          }
        }
      }
    }
  }

  .gc-layout-column .gc-sidebar-column,
  .gc-layout-horizontal {
    .gc-header {
      background: $base-color-blue !important;
    }

    .el-menu {
      background: $base-color-blue !important;

      .el-submenu__title {
        color: rgba(255, 255, 255, 0.7);
        &:hover {
          background-color: $base-color-blue;
        }
      }

      .el-menu-item {
        background: $base-color-blue !important;
      }

      .is-active {
        .el-submenu__title {
          color: #fff;
        }
      }
    }

    .gc-side-bar {
      background: $base-color-blue !important;

      .el-menu-item {
        @include active;
      }
    }

    .el-menu--collapse {
      .el-submenu.is-active,
      .three-level-menu.is-active,
      .el-submenu__title:hover {
        background: $base-menu-background-active !important;
        color: #fff;
      }
      .el-submenu.menu-item.is-active {
        background: $base-main-menu-background-active !important;
        color: #fff;
      }

      .el-menu-item {
        font-size: 13px;
      }
    }
  }

  .gc-layout-vertical {
    .gc-side-bar {
      @include container;

      .el-menu {
        @include container;
        @include active;

        .el-submenu__title,
        .el-menu-item {
          background-color: transparent !important;
          @include active;

          &.is-active {
            background-color: $base-color-blue-active !important;
          }
        }
      }
    }
  }

  .gc-header {
    background-color: $base-color-blue !important;

    .gc-main {
      .el-menu.el-menu {
        background-color: $base-color-blue !important;

        &--horizontal {
          .el-submenu,
          .el-menu-item {
            background-color: $base-color-blue !important;

            &.is-active {
              background-color: $base-color-blue-active !important;
            }
          }

          > .el-menu-item,
          .el-submenu__title {
            background-color: $base-color-blue !important;

            &.is-active {
              background-color: $base-color-blue-active !important;
            }
          }
        }
      }
    }
  }
}
