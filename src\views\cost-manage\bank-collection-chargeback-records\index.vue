<template>
	<div class="container">
		<div class="container-title">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" :loading="mainLoading" @click="handleSearch">
						<i class="iconfontCis icon-small-search"></i>
						筛选
					</el-button>
					<el-button @click="handleReset">
						<i class="iconfontCis icon-reset"></i>
						重置
					</el-button>
				</el-form-item>
			</GcFormSimple>
		</div>
		<div class="bill-container">
			<el-tabs v-model="tableDataQuery.type" type="border-card">
				<el-tab-pane
					v-for="(tab, index) in tabs"
					:key="index"
					:label="tab.label"
					:name="tab.name"
				></el-tab-pane>
			</el-tabs>
			<div class="container-search">
				<GcFormSimple
					v-model="tableDataQuery"
					:formItems="listFilterFormItems"
					:formAttrs="listFilterFormAttrs"
				>
					<el-form-item>
						<el-button type="primary" :loading="loading" @click="handleSubQuery">
							<i class="iconfontCis icon-small-search"></i>
							筛选
						</el-button>
						<el-button @click="handleListFilterReset">
							<i class="iconfontCis icon-reset"></i>
							重置
						</el-button>
					</el-form-item>
				</GcFormSimple>
				<div class="btn-group">
					<el-button
						v-has="'billing_bankCollection_closeBankBill'"
						v-if="billClosable && +tableDataQuery.type !== 2"
						type="primary"
						:disabled="!selectedBillList.length"
						@click="handleClearList"
					>
						手动销账
					</el-button>
					<el-button
						v-has="'billing_bankCollection_exportBill'"
						type="primary"
						:disabled="!tableData.length"
						@click="handleExport"
					>
						导出对账记录
					</el-button>
				</div>
			</div>
			<component
				ref="listTable"
				:is="listTable"
				:loading="loading"
				:tableData="tableData"
				:billSum="currentBillSum"
				:billClosable="billClosable"
				@page-change="handlePageChange"
				@select-change="handleSelectChange"
			/>
		</div>
	</div>
</template>

<script>
import PushBillList from './push-bill-list'
import ClearBillList from './clear-bill-list'
import AbnormalBillList from './abnormal-bill-list'
import { queryBankBillList, closeBankBill, bankCollectionExportBill } from '@/api/costManage.api'
import { ruleRequired } from '@/utils/rules.js'
export default {
	name: '',
	components: { PushBillList, ClearBillList, AbnormalBillList },
	data() {
		return {
			tabs: [
				{ label: '已推送账单', name: '1' },
				{ label: '已销账账单', name: '2' },
				{ label: '异常账单', name: '3' },
			],
			loading: false,
			mainLoading: false,
			formData: {
				sendFileNo: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '文件编号',
					prop: 'sendFileNo',
				},
			],
			formAttrs: {
				inline: true,
				rules: {
					sendFileNo: [ruleRequired('请输入文件编号')],
				},
			},
			listFilterFormItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
						width: 150,
					},
				},
			],
			listFilterFormAttrs: {
				inline: true,
				labelWidth: '80px',
			},
			tableDataQuery: {
				archivesIdentity: '',
				type: '1',
			},
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			currentBillSum: {
				count: '--',
				amount: '--',
			},
			selectedBillList: [],
			billClosable: false,
			billSendFileNo: '',
		}
	},
	computed: {
		listTable() {
			const type = +this.tableDataQuery.type
			return type === 1 ? 'PushBillList' : type === 2 ? 'ClearBillList' : 'AbnormalBillList'
		},
	},
	methods: {
		pageReset() {
			this.pageData.current = 1
			this.pageData.total = 0
			this.$refs.listTable.pageDataReset()
		},
		filterFormData(data) {
			const filteredData = {}
			for (const key in data) {
				if (data[key]) {
					filteredData[key] = data[key]
				}
			}
			return filteredData
		},
		handleSearch() {
			this.pageReset()
			this.mainLoading = true
			this.queryBankBillList()
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.tableDataQuery.type = '1'
			this.tableDataQuery.archivesIdentity = ''
			this.pageReset()
		},
		handleListFilterReset() {
			this.tableDataQuery.archivesIdentity = ''
			this.pageReset()
			this.queryBankBillList()
		},
		handleSubQuery() {
			this.pageReset()
			this.loading = true
			this.queryBankBillList()
		},
		async queryBankBillList() {
			const params = Object.assign(this.filterFormData(this.formData), this.tableDataQuery)
			if (!params.sendFileNo || !params.type) {
				this.mainLoading = false
				this.loading = false
				return
			}
			params.type = +params.type
			params.size = this.pageData.size
			params.current = this.pageData.current
			queryBankBillList(params)
				.then(res => {
					this.currentBillSum.count = res.billNum
					this.currentBillSum.amount = res.billAmount.toFixed(2)
					this.tableData = res.billList.records
					this.billClosable = res.status === 4
					this.billSendFileNo = res.sendFileNo
					this.pageData.total = res.billList.total
					this.$refs.listTable.pageData.total = res.billList.total
				})
				.catch(err => {
					console.log(err)
					this.currentBillSum = {
						count: '--',
						amount: '--',
					}
					this.tableData = []
					this.billClosable = false
					this.billSendFileNo = ''
					this.pageReset()
				})
				.finally(() => {
					this.mainLoading = false
					this.loading = false
				})
		},
		handlePageChange(pageData) {
			this.pageData = pageData
			this.queryBankBillList()
		},
		handleSelectChange(arr) {
			this.selectedBillList = arr
		},
		handleClearList() {
			const totalAmount = this.selectedBillList.reduce((total, item) => total + +item.receivableAmount, 0)
			const label = `确定要对选中的表卡进行销账? 总金额 ${totalAmount.toFixed(2)} 元`
			this.$confirm(label, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
				center: true,
			})
				.then(() => {
					const data = {
						sendFileNo: this.billSendFileNo,
						sendFileRefIds: this.selectedBillList.map(item => item.sendFileRefId),
					}
					this._apiClearAccount(data)
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消销账',
					})
				})
		},
		async _apiClearAccount(params) {
			const result = await closeBankBill(params).catch(err => {
				console.log(err)
				this.$message.error(err.message || '销账失败！')
			})
			if (result !== undefined) {
				this.$message.success('销账成功')
				this.pageReset()
				this.queryBankBillList()
			}
		},
		handleExport() {
			const params = Object.assign(this.filterFormData(this.formData), this.tableDataQuery)
			if (!params.sendFileNo || !params.type) return
			params.type = +params.type
			this.loading = true
			bankCollectionExportBill(params)
				.then(() => {
					this.$message.success('导出成功！')
				})
				.catch(err => {
					console.log(err)
					this.$message.error('导出失败！')
				})
				.finally(() => {
					this.loading = false
				})
		},
	},
	watch: {
		'tableDataQuery.type'() {
			this.tableData = []
			this.queryBankBillList()
		},
	},
	activated() {
		if (this.$route.query.sendFileNo) {
			this.formData.sendFileNo = this.$route.query.sendFileNo
			this.handleSearch()
		}
	},
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	.container-title {
		padding: 20px 20px 0 20px;
		display: flex;
		align-items: center;
		border-bottom: 10px solid #eceff8;
	}
}
.container-title {
	padding: 20px 20px 0 20px;
	display: flex;
	align-items: center;
	border-bottom: 10px solid #eceff8;
}
.el-radio-group {
	padding: 0 20px;
}
.data-item {
	display: flex;
	align-items: center;
	.label {
		color: #4e4e4e;
		font-size: 14px;
	}
	.value {
		font-weight: bold;
	}
}
.ml12 {
	margin-left: 12px;
}
::v-deep {
	.el-tabs {
		width: 100%;
		box-shadow: none;
		border: none;
		border-radius: 4px 4px 0 0;
		overflow: hidden;
		.el-tabs__content {
			padding: 0;
		}
	}
	.el-tabs--border-card > .el-tabs__header {
		border: none;
		.el-tabs__nav {
			.el-tabs__item {
				border: none;
				font-size: 14px;
				color: #6d7480;
				&.is-active {
					background: #ffffff;
					font-weight: 500;
					color: #2f87fe;
				}
			}
		}
	}
	.el-tabs__item:focus.is-active.is-focus:not(:active) {
		-webkit-box-shadow: none;
		box-shadow: none;
	}
}
.container-search {
	display: flex;
	justify-content: space-between;
	padding: 20px 20px 0 20px;
	::v-deep {
		.el-form-item--small.el-form-item {
			margin-bottom: 15px;
		}
	}
}
.bill-container {
	flex: 1;
	padding: 0;
	.el-radio-group {
		padding: 0 20px;
	}
}
</style>
