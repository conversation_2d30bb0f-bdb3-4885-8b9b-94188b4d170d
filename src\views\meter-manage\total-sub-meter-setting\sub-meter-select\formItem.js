export function getFormItems(_this) {
	return [
		{
			type: 'el-select',
			label: '营业分公司',
			prop: 'orgCode',
			options: _this.$store.getters.orgList,
			events: {
				change: v => {
					_this.formData.alleyId = ''
					_this.formData.meterReadingStaffId = ''
					if (!v) {
						_this.formItems[1].options = []
						_this.formItems[3].options = []
					} else {
						_this._getAlleyMap()
						_this._getStaffMapData()
					}
				},
			},
		},
		{
			type: 'el-select',
			label: '坊别',
			prop: 'alleyId',
			options: [],
		},
		{
			type: 'el-input',
			label: '表册编号',
			prop: 'bookNo',
		},
		{
			type: 'el-select',
			label: '抄表员',
			prop: 'meterReadingStaffId',
			options: [],
		},
		{
			type: 'slot',
			label: '表卡编号区间',
			slotName: 'otherInfo',
		},
	]
}
