<template>
	<component :is="menuComponent" v-if="!item.hidden" :item-or-menu="itemOrMenu">
		<template v-if="item.children && item.children.length">
			<gc-menu class="three-level-menu" v-for="route in item.children" :key="route.fullPath" :item="route" />
		</template>
	</component>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
	name: 'GcMenu',
	props: {
		item: {
			type: Object,
			required: true,
		},
		layout: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			itemOrMenu: this.item,
			menuComponent: 'MenuItem',
		}
	},
	computed: {
		...mapGetters({
			collapse: 'settings/collapse',
		}),
	},
	created() {
		const showChildren = this.handleChildren(this.item.children)
		if (showChildren.length) {
			if (showChildren.length === 1 && this.item.alwaysShow !== true) this.itemOrMenu = this.item.children[0]
			this.menuComponent = 'Submenu'
		}
	},
	methods: {
		handleChildren(children = []) {
			if (!children) return []
			return children.filter(item => {
				return item.hidden !== true
			})
		},
	},
}
</script>
