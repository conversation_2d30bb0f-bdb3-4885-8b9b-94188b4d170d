<template>
	<gc-el-dialog :show="isShow" title="更换抄表员" custom-top="120px" width="600px" @close="handleClose">
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>
<script>
import { queryStaffByType } from '@/api/meterReading.api.js'
import { batchChangeMeterReadingStaff } from '@/api/meterReading.api.js'
export default {
	props: {
		// 弹窗显示/隐藏
		show: {
			type: Boolean,
			default: false,
		},
		orgCode: {
			type: [String, Number],
			default: '',
		},
		params: {
			type: [Object],
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			formData: {
				modifyMeterReadingStaffId: '',
				staffPhone: '',
			},
			staffList: [],
			formAttrs: {
				labelWidth: '110px',
				labelPosition: 'right',
				rules: {
					modifyMeterReadingStaffId: [
						{
							required: true,
							message: '请选择抄表员',
							trigger: ['blur', 'change'],
						},
					],
				},
			},
		}
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		formItems() {
			const { staffList } = this
			return [
				{
					type: 'el-select',
					label: '抄表员',
					prop: 'modifyMeterReadingStaffId',
					options: staffList || [],
					attrs: {
						col: 24,
						placeholder: '请选择抄表员',
						noDataText: '',
					},
					events: {
						change: value => {
							const { staffPhone = '' } = this.staffList.find(item => item.value === value)
							this.formData.staffPhone = staffPhone
						},
					},
				},
				{
					type: 'el-input',
					label: '抄表员电话',
					prop: 'staffPhone',
					attrs: {
						disabled: true,
						col: 24,
					},
				},
			]
		},
	},
	watch: {
		orgCode: {
			handler(orgCode) {
				this.getStaffMapData(orgCode)
			},
			immediate: true,
		},
	},
	methods: {
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.$confirm('即将进行更换抄表员，请确认是否继续？', '提示')
					.then(async () => {
						const { formData, params } = this
						const requestData = {
							...formData,
							...params,
						}
						const result = await batchChangeMeterReadingStaff(requestData).catch(e => {
							console.error(e)
							this.$message.error(e.message || '抄表员更改失败')
						})
						if (result === undefined) return
						this.$message.success(`抄表员更改成功`)
						this.$emit('success')
						this.isShow = false
					})
					.catch(() => {})
			}
		},
		// 获取抄表员工数据
		async getStaffMapData(orgCode) {
			if (!orgCode) return

			try {
				const res = await queryStaffByType({
					orgCode,
					staffType: 0,
					status: 0,
				})
				this.staffList = res.map(item => {
					const { staffId, staffName, staffPhone } = item
					return {
						value: staffId,
						label: staffName,
						staffPhone,
					}
				})
			} catch (error) {
				console.error(error)
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
</style>
