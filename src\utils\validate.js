import { removeNullParams } from './'

// 判断是否是字符串
export function isString(value) {
	return typeof value === 'string' || value instanceof String
}

// 判断是否是数组
export function isArray(arg) {
	if (typeof Array.isArray === 'undefined') {
		return Object.prototype.toString.call(arg) === '[object Array]'
	}
	return Array.isArray(arg)
}

//  判断是否为空
export function isBlank(value) {
	return (
		value === undefined ||
		value === null ||
		false ||
		value === '' ||
		value.toString().trim() === '' ||
		value.toString().toLocaleLowerCase().trim() === 'null'
	)
}

// 判断是否为json
export function isJson(value) {
	if (typeof value === 'string') {
		const obj = JSON.parse(value)
		return !!(typeof obj === 'object' && obj)
	}
	return false
}

// 判断是否为对象
export function isObject(value) {
	return Object.prototype.toString.call(value) === '[object Object]'
}

// 判断是否为非空对象
export function isValidObj(value) {
	if (!value) return false
	const obj = removeNullParams(value)
	return !!Object.keys(obj).length
}

// 判断空对象
export function isEmptyObj(obj) {
	if (!obj) return false
	const result = Object.keys(obj).every(key => {
		return obj[key] === null || obj[key] === ''
	})
	return result
}
