<template>
	<GcElDialog
		:show="show"
		title="批量修改地址"
		okText="确认"
		width="600px"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<el-tabs v-model="activeTab" @tab-click="handleTabClick" type="border-card">
			<el-tab-pane label="修改区/县" name="region">
				<GcFormSimple
					ref="regionFormRef"
					v-model="regionFormData"
					:formItems="regionFormItems"
					:formAttrs="formAttrs"
				></GcFormSimple>
			</el-tab-pane>
			<el-tab-pane label="仅修改地址描述" name="desc">
				<GcFormSimple
					ref="descFormRef"
					v-model="descFormData"
					:formItems="descFormItems"
					:formAttrs="formAttrs"
				></GcFormSimple>
			</el-tab-pane>
		</el-tabs>
	</GcElDialog>
</template>

<script>
import { ruleRequired, ruleMaxLength } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getFormItems } from './formItem.js'
import { apiGetRegion, apiGetAddressAreaMap } from '@/api/addressManage.api.js'

export default {
	name: 'AddressBatchModify',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		selectedData: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			activeTab: 'region',
			// 修改区/县表单数据
			regionFormData: {
				regionCode: '',
				streetCode: '',
				communityCode: '',
			},
			// 仅修改地址描述表单数据
			descFormData: {
				oldText: '',
				newText: '',
			},
			regionFormItems: getFormItems(this).region,
			descFormItems: getFormItems(this).desc,
			formAttrs: {
				rules: {
					regionCode: [ruleRequired('必填')],
					streetCode: [ruleRequired('必填')],
					communityCode: [ruleRequired('必填')],
					oldText: [ruleRequired('必填'), ruleMaxLength(100)],
					newText: [ruleRequired('必填'), ruleMaxLength(100)],
				},
			},
		}
	},
	watch: {
		show: {
			handler(val) {
				if (val) {
					this.resetAllForms()
					this.updateAddress()
				}
			},
			immediate: true,
		},
	},
	methods: {
		async updateAddress() {
			const { regionCode, streetCode, communityCode } = this.regionFormData || {}
			const promises = []

			promises.push(this._getRegionData())
			if (regionCode) {
				promises.push(this._getAddressAreaMap(regionCode, 'streetCode'))
			}
			if (streetCode) {
				promises.push(this._getAddressAreaMap(streetCode, 'communityCode'))
			}
			await Promise.all(promises)
			const regionIndex = this.regionFormItems.findIndex(item => item.prop === 'regionCode')
			const streetIndex = this.regionFormItems.findIndex(item => item.prop === 'streetCode')
			const communityIndex = this.regionFormItems.findIndex(item => item.prop === 'communityCode')
			if (regionCode) {
				const ifExist = this.regionFormItems[regionIndex].options.some(item => item.value === regionCode)
				if (!ifExist) {
					this.regionFormData.regionCode = ''
					this.regionFormItems[streetIndex].options = []
					this.regionFormItems[communityIndex].options = []
				}
			}
			if (streetCode) {
				const ifExist = this.regionFormItems[streetIndex].options.some(item => item.value === streetCode)
				if (!ifExist) {
					this.regionFormData.streetCode = ''
					this.regionFormItems[communityIndex].options = []
				}
			}
			if (communityCode) {
				const ifExist = this.regionFormItems[communityIndex].options.some(item => item.value === communityCode)
				if (!ifExist) {
					this.regionFormData.communityCode = ''
				}
			}
		},
		// 获取区县数据
		async _getRegionData() {
			const { records } = await apiGetRegion({ regionCode: 2102 })
			const obj = this.regionFormItems.find(item => item.prop === 'regionCode')
			if (!obj) return
			obj.options = records.map(item => {
				return {
					value: item.regionCode,
					label: item.regionName,
				}
			})
		},
		// 获取街道、小区下拉数据
		async _getAddressAreaMap(value, key) {
			const data = await apiGetAddressAreaMap({
				parentCode: value,
				status: 1, // 启用
			})
			const obj = this.regionFormItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = data.map(item => {
				return {
					value: item.addressAreaCode,
					label: item.addressAreaName,
					...item,
				}
			})
		},
		// 改变地址
		async handleChangeAddress(value, type) {
			const streetIndex = this.regionFormItems.findIndex(item => item.prop === 'streetCode')
			const communityIndex = this.regionFormItems.findIndex(item => item.prop === 'communityCode')

			if (type === 'regionCode') {
				this.regionFormData.streetCode = ''
				this.regionFormData.communityCode = ''
				this.regionFormItems[streetIndex].options = []
				this.regionFormItems[communityIndex].options = []
				if (value) {
					await this._getAddressAreaMap(value, 'streetCode')
				}
			} else if (type === 'streetCode') {
				this.regionFormData.communityCode = ''
				this.regionFormItems[communityIndex].options = []
				if (value) {
					await this._getAddressAreaMap(value, 'communityCode')
				}
			}
		},
		handleTabClick(tab) {
			console.log('切换到tab:', tab.name)
		},
		resetAllForms() {
			// 重置所有表单数据
			this.regionFormData = {
				regionCode: '',
				streetCode: '',
				communityCode: '',
			}
			this.descFormData = {
				oldText: '',
				newText: '',
			}
		},
		handleClose() {
			this.$emit('update:show', false)
		},
		async handleSave() {
			let valid = false
			let formData = {}

			// 根据当前激活的tab验证对应的表单
			switch (this.activeTab) {
				case 'region':
					valid = await this.$refs.regionFormRef.validate()
					formData = this.regionFormData
					break
				case 'desc':
					valid = await this.$refs.descFormRef.validate()
					formData = this.descFormData
					break
			}

			if (!valid) return

			try {
				const params = {
					type: this.activeTab,
					data: trimParams(removeNullParams(formData)),
					selectedIds: this.selectedData.map(item => item.id || item.archivesId),
				}

				console.log('批量修改地址参数:', params)

				// TODO: 调用批量修改地址的API
				// await apiBatchModifyAddress(params)

				this.$message.success('批量修改地址成功')
				this.handleClose()
				this.$emit('refresh')
			} catch (error) {
				console.error('批量修改地址失败:', error)
				this.$message.error('批量修改地址失败')
			}
		},
	},
}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__content {
	padding-top: 20px;
}

::v-deep .el-form-item {
	margin-bottom: 20px;
}
</style>
