<template>
	<GcElDialog
		:show="show"
		title="批量修改地址"
		okText="确认"
		width="600px"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<el-tabs v-model="activeTab" @tab-click="handleTabClick" type="border-card">
			<el-tab-pane label="修改区/县" name="region">
				<GcFormSimple
					ref="regionFormRef"
					v-model="regionFormData"
					:formItems="formItems"
					:formAttrs="formAttrs"
				></GcFormSimple>
			</el-tab-pane>
			<el-tab-pane label="仅修改地址描述" name="desc">
				<GcFormSimple
					ref="descFormRef"
					v-model="descFormData"
					:formItems="formItems"
					:formAttrs="formAttrs"
				></GcFormSimple>
			</el-tab-pane>
		</el-tabs>
	</GcElDialog>
</template>

<script>
import { ruleRequired, ruleMaxLength } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getFormItems } from './formItem.js'
import { apiGetRegion, apiGetAddressAreaMap } from '@/api/addressManage.api.js'

export default {
	name: 'AddressBatchModify',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		selectedData: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			activeTab: 'region',
			// 修改区/县表单数据
			regionFormData: {
				regionCode: '',
				streetCode: '',
				communityCode: '',
			},
			// 仅修改地址描述表单数据
			descFormData: {
				oldText: '',
				newText: '',
			},
			formItems: [],
			formAttrs: {
				rules: {
					regionCode: [ruleRequired('必填')],
					streetCode: [ruleRequired('必填')],
					communityCode: [ruleRequired('必填')],
					oldText: [ruleRequired('必填'), ruleMaxLength(100)],
					newText: [ruleRequired('必填'), ruleMaxLength(100)],
				},
			},
		}
	},
	watch: {
		show: {
			handler(val) {
				console.log('watch show:', val, ', selectedData=', this.selectedData)
				if (val) {
					this.resetAllForms()
					this.updateAddress()
				}
			},
			immediate: true,
		},
	},
	methods: {
		async updateAddress() {
			const { regionCode, streetCode, communityCode } = this.formData || {}
			const promises = []

			promises.push(this._getRegionData())
			if (regionCode) {
				promises.push(this._getAddressAreaMap(regionCode, 'streetCode'))
			}
			if (streetCode) {
				promises.push(this._getAddressAreaMap(streetCode, 'communityCode'))
			}
			await Promise.all(promises)
			const regionIndex = this.formItems.findIndex(item => item.prop === 'regionCode')
			const streetIndex = this.formItems.findIndex(item => item.prop === 'streetCode')
			const communityIndex = this.formItems.findIndex(item => item.prop === 'communityCode')
			if (regionCode) {
				const ifExist = this.formItems[regionIndex].options.some(item => item.value === regionCode)
				if (!ifExist) {
					this.formData.regionCode = ''
					this.formItems[streetIndex].options = []
					this.formItems[communityIndex].options = []
				}
			}
			if (streetCode) {
				const ifExist = this.formItems[streetIndex].options.some(item => item.value === streetCode)
				if (!ifExist) {
					this.formData.streetCode = ''
					this.formItems[communityIndex].options = []
				}
			}
			if (communityCode) {
				const ifExist = this.formItems[communityIndex].options.some(item => item.value === communityCode)
				if (!ifExist) {
					this.formData.communityCode = ''
				}
			}
		},
		// 获取区县数据
		async _getRegionData() {
			const { records } = await apiGetRegion({ regionCode: 2102 })
			const obj = this.formItems(this).region.find(item => item.prop === 'regionCode')
			if (!obj) return
			obj.options = records.map(item => {
				return {
					value: item.regionCode,
					label: item.regionName,
				}
			})
		},
		// 获取街道、小区、楼栋下拉数据
		async _getAddressAreaMap(value, key) {
			const data = await apiGetAddressAreaMap({
				parentCode: value,
				status: 1, // 启用
			})
			const obj = this.formItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = data.map(item => {
				let label = item.addressAreaName
				if (key === 'buildingCode') {
					label = label + item.unit
				}
				return {
					value: item.addressAreaCode,
					label,
					...item,
				}
			})
		},
		// 改变地址
		async handleChangeAddress(value, type) {
			const regionIndex = this.formItems.findIndex(item => item.prop === 'regionCode')
			const streetIndex = this.formItems.findIndex(item => item.prop === 'streetCode')
			const communityIndex = this.formItems.findIndex(item => item.prop === 'communityCode')
			const regionObj = this.formItems[regionIndex].options.find(item => item.value === this.formData.regionCode)
			const streetObj = this.formItems[streetIndex].options.find(item => item.value === this.formData.streetCode)
			const communityObj = this.formItems[communityIndex].options.find(
				item => item.value === this.formData.communityCode,
			)
			const regionFullName = regionObj?.label || ''
			const streetFullName = regionFullName + (streetObj?.label || '')
			const communityFullName = streetFullName + (communityObj?.label || '')

			if (type === 'regionCode') {
				this.formData.streetCode = ''
				this.formData.communityCode = ''
				this.formData.buildingCode = ''
				this.formItems[streetIndex].options = []
				this.formItems[communityIndex].options = []
				if (value) {
					await this._getAddressAreaMap(value, 'streetCode')
					this.addressAreaCode = regionObj?.value || ''
					this.addressFullName = regionFullName
				} else {
					this.addressFullName = ''
					this.addressAreaCode = ''
				}
			} else if (type === 'streetCode') {
				this.formData.communityCode = ''
				this.formData.buildingCode = ''
				this.formItems[communityIndex].options = []
				if (value) {
					await this._getAddressAreaMap(value, 'communityCode')
					this.addressFullName = streetFullName
					this.addressAreaCode = value
				} else {
					this.addressFullName = regionFullName
					this.addressAreaCode = regionObj?.value || ''
				}
			} else if (type === 'communityCode') {
				if (value) {
					this.addressFullName = communityFullName
					this.addressAreaCode = value
				} else {
					this.addressFullName = streetFullName
					this.addressAreaCode = streetObj?.value || ''
				}
			}
		},
		handleTabClick(tab) {
			console.log('切换到tab:', tab.name)
			if (tab.name === 'region') {
				this.formItems = getFormItems(this).region
			} else if (tab.name === 'desc') {
				this.formItems = getFormItems(this).desc
			}
		},
		resetAllForms() {
			// 重置所有表单数据
			this.regionFormData = {
				region: '',
				street: '',
				community: '',
			}
			this.descFormData = {
				oldText: '',
				newText: '',
			}
		},
		handleClose() {
			this.$emit('update:show', false)
		},
		async handleSave() {
			let valid = false
			let formData = {}

			// 根据当前激活的tab验证对应的表单
			switch (this.activeTab) {
				case 'region':
					valid = await this.$refs.regionFormRef.validate()
					formData = this.regionFormData
					break
				case 'desc':
					valid = await this.$refs.descFormRef.validate()
					formData = this.descFormData
					break
			}

			if (!valid) return

			try {
				const params = {
					type: this.activeTab,
					data: trimParams(removeNullParams(formData)),
					selectedIds: this.selectedData.map(item => item.id || item.archivesId),
				}

				console.log('批量修改地址参数:', params)

				// TODO: 调用批量修改地址的API
				// await apiBatchModifyAddress(params)

				this.$message.success('批量修改地址成功')
				this.handleClose()
				this.$emit('refresh')
			} catch (error) {
				console.error('批量修改地址失败:', error)
				this.$message.error('批量修改地址失败')
			}
		},
	},
}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__content {
	padding-top: 20px;
}

::v-deep .el-form-item {
	margin-bottom: 20px;
}
</style>
