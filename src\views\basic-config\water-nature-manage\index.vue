<template>
	<div class="wrapper">
		<div class="right-top">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" @click="handleSearch(false)">筛选</el-button>
					<el-button @click="handleReset">重置</el-button>
				</el-form-item>
			</GcFormSimple>
			<el-button v-has="'cpm_price_nature_add'" type="primary" @click="handleAdd">新增用水性质</el-button>
		</div>
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			@current-page-change="handlePageChange"
		>
			<template v-slot:deal="{ row }">
				<el-button
					v-has="'cpm_price_nature_modify'"
					v-if="row.level !== 1"
					type="text"
					size="medium"
					@click="handleAdjust(row)"
				>
					修改
				</el-button>
			</template>
		</GcTable>

		<!-- 新增、编辑弹窗 -->
		<UpdateDialog
			ref="updateDialogRef"
			:show.sync="showUpdate"
			:editType="editType"
			:tree-data="treeDataComp"
			@success="getList(1)"
		/>
	</div>
</template>

<script>
import UpdateDialog from './components/UpdateDialog.vue'
import { queryWaterNatureList, queryWaterNatureTree } from '@/api/basicConfig.api'

export default {
	name: 'WaterNatureManage',
	components: { UpdateDialog },
	data() {
		return {
			// 用水类型tree
			treeData: [],

			formData: {
				natureNo_1: '',
				natureName: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '用水分类',
					prop: 'natureNo_1',
					options: [],
					attrs: {
						clearable: true,
						placeholder: '请选择用水分类',
					},
				},
				{
					type: 'el-input',
					label: '用水性质',
					prop: 'natureName',
					attrs: {
						clearable: true,
						placeholder: '请输入用水性质',
					},
				},
			],
			formAttrs: { inline: true, labelWidth: '80px' },

			loading: false,
			tableData: [{ natureName: '居民用水' }],
			columns: [
				{
					key: 'natureNo',
					name: '编号',
					tooltip: true,
				},
				{
					key: 'natureName',
					name: '用水性质',
					tooltip: true,
				},
				{
					key: 'parentNatureName',
					name: '用水分类',
					tooltip: true,
				},
				{
					hide: !this.$has('cpm_price_nature_modify'),
					key: 'deal',
					name: '操作',
					fixed: 'right',
					width: 100,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 新增、编辑弹窗
			showUpdate: false,
			editType: 'add',
		}
	},
	computed: {
		treeDataComp() {
			return this.treeData.map(item => {
				// 移除第二级节点的 children 属性
				const itemWithoutChildren = {
					...item,
					children: item.children
						? item.children.map(child => ({ ...child, children: undefined }))
						: undefined,
				}
				// 确保不会递归地删除更深层次的 children 属性
				return itemWithoutChildren
			})
		},
	},
	created() {
		this.getTree()
		this.getList()
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.getList(1)
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await queryWaterNatureList({
					size,
					current,
					...this.formData,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},

		// 获取用水类型tree
		async getTree() {
			try {
				const data = await queryWaterNatureTree()
				this.treeData = data
				this.formItems[0].options = data.map(item => {
					return {
						label: item.natureName,
						value: item.natureNo,
					}
				})
			} catch (error) {
				console.error(error)
				this.treeData = []
				this.formItems[0].options = []
			}
		},
		// 新增
		handleAdd() {
			this.editType = 'add'
			this.showUpdate = true
		},
		// 修改
		handleAdjust(data) {
			this.editType = 'edit'
			this.showUpdate = true
			this.$nextTick(() => {
				this.$refs.updateDialogRef.setFormData(data)
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	padding: 20px;
}

.right-top {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 12px;
	::v-deep {
		.el-form-item {
			margin-bottom: 0;
		}
	}
}
</style>
