<template>
	<div class="card-total">
		<div class="title-wrapper">新增卡片提示</div>
		<div class="card-value">{{ archives }}</div>
		<div class="card-name">昨日新增卡表数</div>
	</div>
</template>

<script>
export default {
	name: '',
	props: {
		archives: Number,
	},
	components: {},
	data() {
		return {}
	},
	computed: {},
	created() {},
	methods: {},
}
</script>

<style lang="scss" scoped>
.card-total {
	border: 1px solid #d5d8e2;
	border-radius: 8px;
	padding: 10px;
	width: 354px;
	height: 468px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-direction: column;
	.title-wrapper {
		width: 100%;
		display: flex;
		justify-content: space-between;
		border-radius: 8px;
		padding: 12px;
		background: linear-gradient(92.14deg, rgba(151, 203, 255, 0.4) -1.61%, rgba(191, 219, 255, 0.35) 96.3%);
		font-family: <PERSON><PERSON><PERSON>;
		font-weight: 700;
		font-size: 18px;
		color: #000000;
	}
	.card-name {
		margin-bottom: 40px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		font-size: 14px;
		color: #000000;
	}
	.card-value {
		font-family: DIN Alternate;
		font-weight: 700;
		font-size: 80px;
		color: #2f87fe;
	}
}
</style>
