.search-dialog {
  ::v-deep {
    div {
      box-sizing: border-box;
    }
    .el-dialog {
      // margin-top: 120px !important;
      border-radius: 4px;
      overflow: hidden;
    }

    .el-dialog__header {
      height: 48px !important;
      padding: 14px 10px 14px 24px;
      background: linear-gradient(90deg, #1571e9 0%, #3b90ff 100%);
      .el-dialog__headerbtn .el-dialog__close {
        color: #fff;
      }
      .el-dialog__title {
        font-size: 14px;
        color: #fff;
      }
    }

    .el-dialog__body {
      padding: 20px 20px 40px 20px;
      .content {
        .tab-switch {
          display: flex;
          padding-bottom: 24px;
          .tab-item {
            width: 76px;
            height: 28px;
            margin-right: 10px;
            line-height: 26px;
            text-align: center;
            border: 1px solid #66a6fe;
            border-radius: 4px;
            color: #66a6fe;
            background: #fff;
            cursor: pointer;
          }
          .tab-item:last-child{
            margin-right: 0;
          }
          .tab-item.active {
            background: $base-color-blue;
            border: 1px solid $base-color-blue;
            color: #fff;
          }
        }
        .content-operation {
          padding-bottom: 10px;
          .el-input__prefix{
            left: -5px;
          }
          .el-input__inner {
            border-radius: 0;
            border: none;
            border-bottom: 1px solid $base-color-blue;
            outline: medium;
            font-size: 14px;
            height: 42px;
            line-height: 42px;
            
          }
          .el-icon-search {
            font-size: 16px;
            color: $base-color-blue;
          }
          .el-input__suffix {
            line-height: 40px;
            cursor: pointer;
            i {
              font-style: normal;
              font-size: 14px;
              color: $base-color-blue;
            }
          }
          .address-special {
            .el-input__inner {
              padding-left: 0;
            }
            .select {
              display: flex;
              justify-content: space-between;
              padding-bottom: 20px;
              .el-select {
                width: 48% !important;
                .el-input__inner {
                  width: 100%;
                }
              }
            }
          }
        }
        .filter-condition {
          font-size: 12px;
          .all {
            color: #999999;
            .el-button{
              margin-left: 10px;
              color: $base-color-blue;
              border: none;
            }
            .el-button[disabled]{
              color: #cccccc;
              border: none;
              background: #fff;
            }
          }
          .collection-one {
            span {
              display: inline-block;
              padding: 4px 12px;
              border: 1px solid #d9d9d9;
              border-radius: 10px;
              margin: 4px 4px 0 0;
              i {
                font-style: normal;
                padding: 0 4px;
                cursor: pointer;
              }
            }
            .collection-empty{
              font-size: 12px;
              color: #cccccc;
              padding-top: 3px;
            }
          }
        }
        // 菜单项
        .archives-menu {
          .menu-list {
            .menu-item:not(:first-child) {
              padding-top: 20px;
            }
            .menu-item {
              border-bottom: 1px dashed #cccccc;
              display: flex;
              flex-wrap: wrap;
              padding-bottom: 10px;
              &.pointer {
                cursor: pointer;
              }
              .archives-search {
                color: $base-color-blue;
                font-weight: 500;
                width: 100%;
                padding-bottom: 16px;
              }
              .detail-item {
                padding-bottom: 14px;
                font-size: 12px;
                color: #999999;
                font-weight: 400;
                min-width: 33%;
              }
              .detail-item.address {
                width: 100%;
              }
              .status0 {
                color: #12b3c7;
              }
              .status1 {
                color: #ec6b60;
              }
            }
          }
          .page-turn {
            display: flex;
            justify-content: flex-end;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.65);
            i {
              font-style: normal;
            }
            span {
              line-height: 24px;
            }
            .previous,
            .next {
              display: inline-block;
              width: 26px;
              height: 26px;
              border-radius: 4px;
              border: 1px solid #d9d9d9;
              font-size: 16px;
              margin-left: 10px;
              text-align: center;
              line-height: 24px;
              cursor: pointer;
            }
          }
          .menu-empty {
            display: flex;
            justify-content: flex-start;
            padding-bottom: 136px;
            color: #CCCCCC;
          }
        }
      }
    }
  }
  &.show-list {
    ::v-deep {
      .el-dialog__body {
        padding-bottom: 0;
      }
    }
  }
}

::v-deep .gc-global-search {
  position: absolute;
  right: 185px;
  border-radius: 0 0 4px 4px !important;

  .box-tab {
    height: 100%;
    display: flex;
    align-items: center;
    color: #fff;
  }
  .tab-item {
    margin-right: 20px;
    cursor: pointer;
    &.active {
      position: relative;
      &::after {
        content: "";
        position: absolute;
        bottom: -18px;
        left: 50%;
        transform: translateX(-50%);
        border-width: 8px;
        border-style: solid;
        border-color: transparent transparent #fff transparent;
      }
    }
  }
  .el-dialog__body{
      padding-bottom: 20px;
  }
}
