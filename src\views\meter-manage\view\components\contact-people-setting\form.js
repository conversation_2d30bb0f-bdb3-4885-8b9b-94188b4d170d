export function getFormItems(_this) {
	return [
		{
			type: 'el-input',
			label: '联系人',
			prop: 'contactName',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '手机号',
			prop: 'contactMobile',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-select',
			label: '开票类型',
			prop: 'invoiceType',
			options:
				_this.$store.getters?.dataList?.invoiceType?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '电子邮箱',
			prop: 'email',
			attrs: {
				col: 12,
			},
		},

		{
			type: 'slot',
			label: '纳税人识别号',
			prop: 'taxpayerIdentity',
			slotName: 'taxpayerIdentity',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '开票抬头',
			prop: 'buyerName',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '开户银行',
			prop: 'openBank',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '银行账户',
			prop: 'bankAccount',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '邮寄地址',
			prop: 'mailingAddress',
			attrs: {
				col: 12,
				type: 'textarea',
				maxlength: '64',
				showWordLimit: true,
				autosize: {
					minRows: 3,
					maxRows: 4,
				},
			},
		},
	]
}
