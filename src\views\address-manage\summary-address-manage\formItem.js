export function getFormItems(_this) {
	const communityFormItem = [
		{
			type: 'el-select',
			label: '市',
			prop: 'cityCode',
			options: [],
			events: {
				change: value => {
					const regionIndex = _this.formItems.findIndex(item => item.prop === 'regionCode')
					const streetIndex = _this.formItems.findIndex(item => item.prop === 'streetCode')
					_this.formItems[regionIndex].options = []
					_this.formItems[streetIndex].options = []
					_this.formData.regionCode = ''
					_this.formData.streetCode = ''
					if (value) {
						_this._getCityOriRegionData(value, 'regionCode')
					}
				},
			},
		},
		{
			type: 'el-select',
			label: '区/县',
			prop: 'regionCode',
			options: [],
			events: {
				change: value => {
					const streetIndex = _this.formItems.findIndex(item => item.prop === 'streetCode')
					_this.formData.streetCode = ''
					_this.formItems[streetIndex].options = []
					if (value) {
						_this._getStreetData(value)
					}
				},
			},
		},
		{
			type: 'el-select',
			label: '街道/乡镇',
			prop: 'streetCode',
			options: [],
		},
		{
			type: 'el-input',
			label: '小区/村庄',
			prop: 'neighbourhoodName',
		},
		{
			type: 'el-select',
			label: '是否接管',
			prop: 'takeOver',
			options: [
				{
					label: '已接管',
					value: 1,
				},
				{
					label: '未接管',
					value: 0,
				},
			],
		},
	]
	const streetFormItem = [
		{
			type: 'el-select',
			label: '市',
			prop: 'cityCode',
			options: [],
			events: {
				change: value => {
					const regionIndex = _this.formItems.findIndex(item => item.prop === 'regionCode')
					_this.formItems[regionIndex].options = []
					_this.formData.regionCode = ''
					if (value) {
						_this._getCityOriRegionData(value, 'regionCode')
					}
				},
			},
		},
		{
			type: 'el-select',
			label: '区/县',
			prop: 'regionCode',
			options: [],
		},
		{
			type: 'el-input',
			label: '街道/乡镇',
			prop: 'streetName',
		},
	]
	const regionFormItem = [
		{
			type: 'el-select',
			label: '市',
			prop: 'cityCode',
			options: [],
		},
		{
			type: 'el-input',
			label: '区/县',
			prop: 'regionName',
		},
	]
	let formItems = []
	if (_this.activeTab === 'community') {
		formItems = communityFormItem
	} else if (_this.activeTab === 'street') {
		formItems = streetFormItem
	} else if (_this.activeTab === 'region') {
		formItems = regionFormItem
	}

	formItems.forEach(item => {
		_this.$set(_this.formData, item.prop, '')
	})
	return formItems
}
