<template>
	<el-form :model="formData" v-bind="formAttrs" ref="formRef">
		<template v-for="(item, index) in formItems">
			<el-form-item
				v-if="!item.hide"
				:key="index"
				:label="item.label"
				:prop="item.prop"
				v-bind="item.formItemAttrs"
			>
				<!-- slot具名插槽 -->
				<template v-if="item.type === 'slot'">
					<slot :name="item.slotName"></slot>
				</template>
				<!-- el-input、el-input-number -->
				<template v-if="item.type.indexOf('el-input') !== -1 || item.type === 'el-autocomplete'">
					<component
						:is="item.type"
						v-model.trim="formData[item.prop]"
						v-bind="{ placeholder: '请输入', ...item.attrs }"
						v-on="item.events"
					/>
				</template>
				<!-- el-select -->
				<template v-if="item.type === 'el-select'">
					<el-select
						v-model="formData[item.prop]"
						v-bind="{
							placeholder: '请选择',
							clearable: true,
							filterable: true,
							...item.attrs,
						}"
						v-on="item.events"
					>
						<el-option
							v-for="option in item.options"
							:key="option.value"
							:label="option.label"
							:value="option.value"
							:disabled="option.disabled"
						></el-option>
					</el-select>
				</template>
				<!-- el-radio -->
				<template v-if="item.type === 'el-radio'">
					<el-radio-group v-model="formData[item.prop]" v-bind="item.attrs" v-on="item.events">
						<el-radio
							v-for="option in item.options"
							:key="option.value"
							:label="option.value"
							:disabled="option.disabled"
						>
							{{ option.label }}
						</el-radio>
					</el-radio-group>
				</template>
				<!-- el-date-picker -->
				<template v-if="item.type === 'el-date-picker'">
					<el-date-picker
						v-model="formData[item.prop]"
						v-bind="{ placeholder: '请选择', clearable: true, ...item.attrs }"
						:type="(item.attrs && item.attrs.type) || 'date'"
						v-on="item.events"
					></el-date-picker>
				</template>
				<!-- el-cascader -->
				<template v-if="item.type === 'el-cascader'">
					<el-cascader
						v-model="formData[item.prop]"
						style="width: 100%"
						v-bind="{ placeholder: '请选择', clearable: true, ...item.attrs }"
					></el-cascader>
				</template>
				<!-- tofix:待补充 -->
			</el-form-item>
		</template>
		<!-- 插槽 -->
		<slot></slot>
	</el-form>
</template>

<script>
export default {
	name: 'GcFormSimple',
	props: {
		value: {
			type: Object,
			required: true,
		},
		formItems: {
			type: Array,
			required: true,
		},
		formAttrs: {
			type: Object,
		},
	},
	computed: {
		formData: {
			get() {
				return this.value
			},
			set(newVal) {
				this.$emit('input', newVal)
			},
		},
	},
	methods: {
		validate() {
			return new Promise(resolve => {
				this.$refs.formRef.validate(valid => {
					resolve(valid)
				})
			})
		},
		submitForm() {
			this.$refs.formRef.validate(valid => {
				if (valid) {
					this.resetForm()
				} else {
					console.log('error submit!!')
					return false
				}
			})
		},
		resetFormSmooth() {
			this.$refs.formRef.resetFields()
		},
		resetFields() {
			this.$refs.formRef.resetFields()
		},
		resetForm() {
			Object.keys(this.formData).forEach(key => {
				const value = this.formData[key]
				if (Array.isArray(value)) {
					this.$set(this.formData, key, [])
				} else if (typeof value === 'boolean') {
					this.$set(this.formData, key, false)
				} else if (value === null || value === undefined) {
					this.$set(this.formData, key, null)
				} else if (typeof value === 'object' && value !== null) {
					this.$set(this.formData, key, {})
				} else {
					this.$set(this.formData, key, '')
				}
			})
		},
		clearValidate(data = '') {
			this.$refs.formRef.clearValidate(data)
		},
		validateField(data, callback) {
			this.$refs.formRef.validateField(data, callback)
		},
	},
}
</script>
<style lang="scss" scoped>
.el-form {
	::v-deep {
		.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label,
		.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label {
			position: relative;
			&:before {
				position: static !important;
				transform: none !important ;
			}
		}
		.el-select {
			width: 100%;
		}
		.el-date-editor {
			width: 100%;
		}
	}
}
</style>
