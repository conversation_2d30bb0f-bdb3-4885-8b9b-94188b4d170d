<template>
	<el-dialog
		:top="top"
		:title="title"
		:visible.sync="innerVisible"
		:width="width"
		class="search-dialog"
		:append-to-body="appendToBody"
		:close-on-click-modal="false"
		:show-close="true"
		@close="publicSearch('close')"
	>
		<div class="content">
			<div class="tab-switch" v-if="searchCondition.length > 1">
				<div
					class="tab-item"
					v-for="(item, index) in searchCondition"
					:key="index"
					:class="[item.key == activeTab ? 'active' : '']"
					@click="changeActiveCondition(item.key)"
				>
					{{ item.label }}
				</div>
			</div>
			<el-form ref="searchInputForm" :model="searchInputForm" @submit.native.prevent>
				<div class="content-operation">
					<!-- 日期 -->
					<template v-if="currentTabObj.type === 'el-date-picker'">
						<el-form-item prop="input">
							<el-date-picker
								v-model="searchInputForm.input"
								v-bind="currentTabObj.attrs"
								:type="(currentTabObj.attrs && currentTabObj.attrs.type) || 'date'"
								style="width: 95%"
							></el-date-picker>
							<i @click="publicSearch" class="search-button">搜索</i>
						</el-form-item>
					</template>
					<!-- input -->
					<template v-else>
						<el-form-item prop="input" :rules="formRules">
							<el-input
								v-model="searchInputForm.input"
								:placeholder="activePlaceholder"
								@keyup.enter.native="publicSearch"
							>
								<i slot="suffix" @click="publicSearch">搜索</i>
								<i slot="prefix" class="el-input__icon el-icon-search"></i>
							</el-input>
						</el-form-item>
					</template>
				</div>
			</el-form>
			<div class="filter-condition" v-if="filterConditionVisible">
				<div class="all">
					<span>筛选条件</span>
					<el-button type="text" @click="emptyCondition" :disabled="filterConditionDisabled">清空</el-button>
				</div>
				<div class="collection-one">
					<div v-if="selectedCondition.length > 0">
						<span v-for="item in selectedCondition" :key="item.key">
							{{ item.value }}
							<i v-show="!item.hideClear" @click="deleteOne(item.key)">×</i>
						</span>
					</div>
					<div v-else class="collection-empty">暂无筛选条件</div>
				</div>
			</div>
		</div>
	</el-dialog>
</template>

<script>
import { searchKeys } from '@/consts/search'
import { nameConversion } from '@/utils/index.js'

export default {
	name: 'GcSearchDialogNew',
	components: {},
	props: {
		// 弹窗的显示/关闭
		dialogVisible: {
			type: Boolean,
			required: true,
			default: false,
		},
		//弹窗名称
		title: {
			type: String,
			required: true,
			default: '搜索',
		},
		// 弹窗内的搜索条件
		searchCondition: {
			type: Array,
			required: true,
			default: () => [],
		},
		// 弹窗当前所处的tab
		activeCondition: {
			type: String,
			required: true,
			default: () => '',
		},
		// 弹窗外选中的条件
		selectedCondition: {
			type: Array,
			default: () => [],
		},
		// 筛选条件是否显示
		filterConditionVisible: {
			type: Boolean,
			default: true,
		},

		// 传值
		valueObj: {
			type: Object,
			default: () => {
				return {}
			},
		},
		appendToBody: {
			type: Boolean,
			default: false,
		},
		loading: {
			type: Boolean,
			default: false,
		},
		width: {
			type: String,
			default: '647px',
		},
	},
	data() {
		return {
			searchKeys,
			searchInputForm: {
				input: '', //输入框内容
			},
		}
	},
	computed: {
		formRules() {
			const current = this.searchCondition.find(item => item.key === this.activeCondition)
			return current?.rule || {}
		},
		// 控制弹窗的显示/隐藏
		innerVisible: {
			get: function () {
				return this.dialogVisible
			},
			set: function (val) {
				this.$emit('update:dialogVisible', val)
			},
		},
		// placeholder的值
		activePlaceholder() {
			let str = ''
			if (this.searchCondition) {
				this.searchCondition.map(item => {
					if (item.key == this.activeTab) {
						if (item.attrs && item.attrs.placeholder) {
							str += item.attrs.placeholder
						} else {
							str += '请输入' + item.label
						}
					}
				})
			}
			return str
		},
		activeTab: {
			get: function () {
				return this.activeCondition
			},
			set: function (val) {
				this.$emit('update:active-condition', val)
			},
		},
		top() {
			return '120px'
		},
		// 筛选条件清空按钮是否置灰
		filterConditionDisabled() {
			const arr = this.selectedCondition.filter(item => !item.hideClear)
			return !arr.length
		},
		// 当前选中的tab对象
		currentTabObj() {
			return this.searchCondition.find(item => item.key === this.activeTab)
		},
	},
	watch: {
		activeCondition(newVal) {
			this.activeTab = newVal
		},
		activeTab() {
			// 当切换到不同的tab时候，清空数据
			this.resetfn()
		},
		valueObj(newVal) {
			// 清空数据
			if (Object.keys(newVal).length == 0) {
				this.resetfn()
			} else {
				this.searchInputForm.input = newVal.value
			}
		},
	},
	methods: {
		nameConversion,
		resetfn() {
			this.searchInputForm.input = ''
		},
		// 切换所在tab
		changeActiveCondition(val) {
			this.activeTab = val
		},
		// 清空
		emptyCondition() {
			this.$emit('empty')
		},
		// 删除单个的筛选条件
		deleteOne(val) {
			this.$emit('delete-one', val)
		},
		//搜索
		publicSearch(flag) {
			if (flag !== 'page' && flag !== 'close') {
				let error = false
				this.$refs.searchInputForm.validate(valid => {
					error = !valid
				})
				if (error) {
					this.$message.error('请修正错误')
					return
				}
			}
			let val = null
			let currentLabel = null
			this.searchCondition.map(item => {
				if (item.key == this.activeTab) {
					currentLabel = item.label
				}
			})
			val = {
				key: this.activeTab,
				label: currentLabel,
				value: this.searchInputForm.input,
			}
			if (flag == 'close') {
				// this.resetfn();
				this.$emit('dialog-close', val)
				this.innerVisible = false
			} else {
				this.$emit('public-search', val)
				this.innerVisible = false
			}
		},
	},
}
</script>
<style lang="scss" scoped>
@import './index.scss';
.search-button {
	font-style: normal;
	font-size: 14px;
	color: #2f87fe;
	cursor: pointer;
}
</style>
