/**
 * 水气差异
 * 本文档字段key：字段名，value：根据租户类型显示字段值
 */
// 默认模板按照燃气类型配置
// const defaultFieldName = {
//   usageCharacter: ''
// }
export const fieldNameVersion = '20211130' // 更新FieldName字段时需对应更新版本号，否则可能造成显示undefind

export const gasFieldName = {
	version: fieldNameVersion,
	baseText: '气',
	baseName: '用气',
	baseUnit: 'm³',
	reportName: '用气类报表',
	quantity: '气量',
	useQuantity: '用气量',
	oldUsageNature: '旧用气性质',
	newUsageNature: '新用气性质',
}

export const waterFieldName = {
	version: fieldNameVersion,
	baseText: '水',
	baseName: '用水',
	baseUnit: '吨',
	reportName: '用水类报表',
	quantity: '水量',
	useQuantity: '用水量',
	oldUsageNature: '旧用水性质',
	newUsageNature: '新用水性质',
}

// 档案管理筛选条件显隐判定条件
export const archiveListFilterConfig = {
	// 档案状态
	archivesStatus: {
		gas: true,
		water: true,
	},
	// 用户类型
	userType: {
		gas: true,
		water: true,
	},
	// 用户子类
	userSubType: {
		gas: true,
		water: false,
	},
	// 所属机构
	orgCode: {
		gas: true,
		water: true,
	},
	// 所属机构
	adminOrgCode: {
		gas: true,
		water: true,
	},
	// 价格名称
	priceId: {
		gas: true,
		water: true,
	},
	// 表类型
	meterTypeId: {
		gas: true,
		water: true,
	},
	// 建档日期
	createTime: {
		gas: true,
		water: true,
	},
}

// 档案管理表格表头显隐判定条件
export const archiveListColumnConfig = {
	archivesNo: {
		gas: true,
		water: true,
	},
	archivesIdentity: {
		gas: true,
		water: true,
	},
	userName: {
		gas: true,
		water: true,
	},
	userMobile: {
		gas: true,
		water: true,
	},
	regionName: {
		gas: true,
		water: true,
	},
	streetName: {
		gas: true,
		water: true,
	},
	addressAreaName: {
		gas: true,
		water: true,
	},
	addressName: {
		gas: true,
		water: true,
	},
	userType: {
		gas: true,
		water: true,
	},
	userSubType: {
		gas: true,
		water: false,
	},
	meterNo: {
		gas: true,
		water: true,
	},
	archivesStatus: {
		gas: true,
		water: true,
	},
	orgName: {
		gas: true,
		water: true,
	},
	priceName: {
		gas: true,
		water: true,
	},
	meterBalanceAmount: {
		gas: true,
		water: true,
	},
	meterBalanceQuantity: {
		gas: true,
		water: false,
	},
	cardBalance: {
		gas: true,
		water: false,
	},
	meterCreditAmount: {
		gas: true,
		water: true,
	},
	totalRechargeAmount: {
		gas: true,
		water: true,
	},
	openPerson: {
		gas: true,
		water: true,
	},
	openDate: {
		gas: true,
		water: true,
	},
	archivesTime: {
		gas: true,
		water: true,
	},
	createStaffName: {
		gas: true,
		water: true,
	},
	certificateNo: {
		gas: true,
		water: true,
	},
	meterModel: {
		gas: true,
		water: true,
	},
	remark: {
		gas: true,
		water: true,
	},
	lastChargingUsePrice: {
		gas: true,
		water: true,
	},
	payTypeName: {
		gas: true,
		water: true,
	},
	meterTypeName: {
		gas: true,
		water: true,
	},
	meterReading: {
		gas: true,
		water: true,
	},
	valveStatusDesc: {
		gas: true,
		water: true,
	},
}
