<template>
	<gc-el-dialog :show="isShow" title="册本位数调整" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { updateBusinessHallDigits } from '@/api/basicConfig.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				residentsDigits: undefined,
				enterpriseDigits: undefined,
			},
			formItems: [
				{
					type: 'el-input-number',
					label: '居民册本号位数',
					prop: 'residentsDigits',
					attrs: {
						col: 24,
						controls: false,
						clearable: true,
						placeholder: '请输入居民册本号位数',
					},
				},
				{
					type: 'el-input-number',
					label: '企业册本号位数',
					prop: 'enterpriseDigits',
					attrs: {
						col: 24,
						controls: false,
						clearable: true,
						placeholder: '请输入企业册本号位数',
					},
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					residentsDigits: [
						{
							required: true,
							message: '请输入居民册本号位数',
							trigger: 'blur',
						},
						{
							min: 6,
							message: '居民册本号位数最小为6',
							trigger: 'blur',
							type: 'number',
						},
						{
							max: 10,
							message: '居民册本号位数最大为10',
							trigger: 'blur',
							type: 'number',
						},
					],
					enterpriseDigits: [
						{
							required: true,
							message: '请输入企业册本号位数',
							trigger: 'blur',
						},
						{
							min: 5,
							message: '企业册本号位数最小为5',
							trigger: 'blur',
							type: 'number',
						},
						{
							max: 10,
							message: '企业册本号位数最大为10',
							trigger: 'blur',
							type: 'number',
						},
					],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.$confirm('将影响新建册本位数，是否确认修改？', '册本位数调整').then(async () => {
					await updateBusinessHallDigits(this.formData)
					this.$message.success('册本位数调整成功')
					this.$emit('success')
					this.isShow = false
				})
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.el-input-number {
		width: 100%;
		.el-input__inner {
			text-align: left;
		}
	}
}
</style>
