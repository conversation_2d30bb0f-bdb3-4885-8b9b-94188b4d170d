<!-- 上传可将index.js作为mixin引入，包含了上传文件的整个记录 -->
<gc-upload-file
    :upload-step.sync="uploadStep" -上传进度 Number-非必传 默认值为1  1-未选择文件，初始状态，2-选择文件，3-上传成功，4-上传失败
    :msg="msg" -提示语 String-非必传 默认为空（主要控制上传完成后成功和失败的提示语显示）
    :loading-text.sync="loadingText" -加载text String-非必传
    :loading.sync="loading" -加载loading的显示/隐藏 Boolean-非必传 默认为false
    @before-upload-file="beforeUploadFile"  选择文件前的校验 -说明 cb为函数，判断是否向下进行，为true则继续，false停止 主要为了判断批量建档表单是否填写
      beforeUploadFile(data, cb) {
        console.log(this.$refs.paramsForm, "this.$refs.paramsForm");
        this.$refs.paramsForm.submitForm().then((res) => {
          if (res) {
            cb(true);
          } else {
            this.$message.error("请完善公共参数");
            cb(false);
          }
        });
      },
    @upload-confirm="uploadConfirm" --点击上传按钮后调用的函数
    uploadConfirm(val) {
      let middleObj = val;
      let obj = {
        address: {
          regionCode: 111,
          addressAreaName: 222,
        },
        price: {
          id: 1,
        },
      };
      this.uploadLogic(middleObj["list"], middleObj["type"], obj); -index.js中的上传逻辑
    },
    @upload-done="uploadDone" --点击完成/关闭按钮后调用的方法 主要可用来进行数据的初始化
    >
    <!-- <slot name="success-ops"> 上传成功后进行的操作按钮 -->
    <template #success-ops>
       <div class="jump" v-if="uploadStep == 3">
            <img src="@/assets/images/pic/create-a-file.png" alt="" />
            <span>去档案列表</span>
          </div>
    </template>
    <!-- <slot name="success-ops"> 上传失败后进行的操作按钮 -->
    <template #fail-ops>
       <div class="fail-download" v-if="uploadStep == 4">
            <img src="@/assets/images/pic/fail-xls.png" alt="" />
            <span>下载错误记录</span>
          </div>
    </template>
    </gc-upload-file>