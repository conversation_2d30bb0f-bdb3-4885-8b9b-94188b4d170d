<template>
	<div class="complex-search-panel" :data-count="innerValue.length">
		<template v-if="innerValue.length">
			<FieldItem
				v-for="(item, index) in innerValue"
				v-model="innerValue[index]"
				:key="`item_${index}`"
				:fields="fields"
				@add-item="handleItemAdd(index)"
				@remove-item="handleItemRemove(index)"
			></FieldItem>
		</template>
		<div class="add-item" @click="handleItemAdd(innerValue.length - 1)">
			<i class="el-icon-plus"></i>
			添加查询条件
		</div>
	</div>
</template>
<script>
import FieldItem from './FieldItem.vue'
export default {
	name: 'complex-search-panel',
	components: { FieldItem },
	props: {
		fields: {
			type: Array,
			default: () => [],
		},
		data: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			innerValue: JSON.parse(JSON.stringify(this.data)),
		}
	},
	methods: {
		handleItemAdd(index) {
			console.info(index)
			this.innerValue.splice(index + 1, 0, {
				field: '',
				operator: '',
				value: '',
			})
		},
		handleItemRemove(index) {
			this.innerValue.splice(index, 1)
		},
		getData() {
			return this.innerValue
		},
	},
	watch: {
		data(val) {
			if (val === this.innerValue) return
			this.innerValue = JSON.parse(JSON.stringify(val))
		},
		innerValue: {
			deep: true,
			immediate: true,
			handler(val) {
				this.$emit('change', val)
			},
		},
	},
}
</script>

<style lang="scss" scoped>
.complex-search-panel {
	position: relative;
	padding-right: 20px;
	max-height: 320px;
	overflow-x: hidden;
	display: flex;
	flex-wrap: wrap;
	gap: 10px 20px;
	.add-item {
		width: 100%;
		padding: 0 20px;
		text-align: center;
		background-color: #ffffff;
		color: #666666;
		position: sticky;
		bottom: 0;
		z-index: 2;
		border-radius: 4px;
		border: 1px solid #e9e9e9;
		cursor: pointer;
		&:hover {
			background-color: #fcfcfc;
		}
	}
	.complex-field-item {
		width: calc(50% - 10px);
	}
}
@media screen and (max-width: 1440px) {
	.complex-search-panel .complex-field-item {
		width: 100%;
	}
}
</style>
