<template>
	<div class="list-container">
		<el-input v-model="archivesIdentity" class="search-container" style="width: 250px" placeholder="请输入表卡编号">
			<el-button slot="append" icon="el-icon-search" @click="handlePageChange({ page: 1 })"></el-button>
		</el-input>
		<GcTable
			ref="tableRef"
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			showPage
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			@current-page-change="handlePageChange"
		>
			<template v-slot:operate="{ row }">
				<el-button type="text" @click="handleDelete(row)">删除</el-button>
			</template>
		</GcTable>
	</div>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getColumn } from './tableColumn'
import { apiGetListSubArchivesRecord, apiDeleteSubArchivesRecord } from '@/api/meterManage.api.js'
export default {
	props: {
		dmaArchivesId: {
			type: [Number, String],
		},
	},
	data() {
		return {
			archivesIdentity: '',
			columns: getColumn(this),
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	methods: {
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(
					removeNullParams({
						archivesIdentity: this.archivesIdentity,
						dmaArchivesId: this.dmaArchivesId,
					}),
				)
				Object.assign(formParams, {
					current,
					size,
				})
				const { total = 0, records = [] } = await apiGetListSubArchivesRecord(formParams)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleDelete(row) {
			this.$confirm('确定要删除所选择的分表吗?').then(async () => {
				try {
					await apiDeleteSubArchivesRecord(row.recordId)
					this.$message.success('删除成功')
					this.handlePageChange({ page: 1 })
				} catch (error) {
					console.log(error)
				}
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.list-container {
	display: flex;
	flex-direction: column;
	height: 100%;
}
.search-container {
	margin-bottom: 10px;
}
</style>
