<template>
	<el-dialog
		:id="dialogId"
		custom-class="gc-dialog-custom"
		:visible.sync="visible"
		:width="width"
		:top="customTop || top"
		:close-on-click-modal="false"
		:append-to-body="true"
		@open="open"
		@close="close"
	>
		<div slot="title" class="title">
			<slot v-if="$slots.icon" name="icon" />
			<svg v-else class="icon" aria-hidden="true">
				<use :xlink:href="`#${icon}`"></use>
			</svg>
			<span class="title-content">{{ title }}</span>
		</div>
		<slot />
		<template v-if="showFooter">
			<div v-if="$slots.footer" slot="footer">
				<slot name="footer" />
			</div>
			<div v-else slot="footer" class="footer">
				<!-- <el-button
          :class="{ 'is-border-light': cancelBtn.isBorder }"
          round
          :type="cancelBtn.type"
          @click="handleCancel"
        >
          {{ cancelBtn.value }}
        </el-button> -->
				<!-- <el-button
          :class="{ 'is-border-light': okBtn.isBorder }"
          round
          :type="okBtn.type"
          @click="handleOk"
        >
          {{ okBtn.value }}
        </el-button> -->
				<button class="gc-button gc-button-three" v-if="!hiddenCancelBtn" @click="handleCancel">
					{{ cancelBtn.value }}
				</button>
				<button class="gc-button gc-button-one" v-if="okBtn.value" @click="handleOk">
					{{ okBtn.value }}
				</button>
			</div>
		</template>
	</el-dialog>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'

const OK = { value: '确 定', type: 'primary', isBorder: false }
const CANCEL = { value: '取 消', type: 'default', isBorder: false }
export default {
	name: 'GcElDialog',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		icon: {
			type: String,
			default: 'icon-popup-general-icon',
		},
		title: String,
		showFooter: {
			type: Boolean,
			default: true,
		},
		okText: {
			type: [String, Object],
			default: () => OK,
		},
		cancelText: {
			type: [String, Object],
			default: () => {
				return CANCEL
			},
		},
		width: {
			type: String,
			default: '600px',
		},
		// 自定义margin-top值
		customTop: {
			type: String,
		},
		hiddenCancelBtn: {
			type: Boolean,
			default: false,
		},
	},
	watch: {
		show(newVal) {
			if (newVal) {
				this.handleDialogBodyHeight()
			}
		},
	},
	computed: {
		visible: {
			get() {
				return this.show
			},
			set(val) {
				// visible 改变时通知父组件
				this.$emit('update:show', val)
			},
		},
		okBtn() {
			const value = this.okText
			if (typeof value === 'string') {
				return { ...OK, ...{ value } }
			}
			return value
		},
		cancelBtn() {
			const value = this.cancelText
			if (typeof value === 'string') {
				return { ...CANCEL, ...{ value } }
			}
			return value
		},
	},
	data() {
		return {
			top: '120px',
			clientHeight: 0,
			dialogId: `dialog-${uuidv4()}`,
			timeout: '',
		}
	},
	methods: {
		handleOk() {
			clearTimeout(this.timeout)
			this.timeout = setTimeout(() => {
				this.$emit('ok')
			}, 500)
		},
		handleCancel() {
			this.$emit('cancel')
		},
		open() {
			// Dialog 打开的回调
			this.$emit('open')
		},
		close() {
			// Dialog 关闭的回调
			this.$emit('close')
		},
		handleDialogBodyHeight() {
			this.$nextTick(() => {
				const clientHeight = document.body.clientHeight
				let dialog = this.querySelector('.gc-dialog-custom')
				let dialogHeader = this.querySelector('.el-dialog__header')
				let dialogBody = this.querySelector('.el-dialog__body')
				let dialogFooter = this.querySelector('.el-dialog__footer')
				let otherHeight = 120
				if (dialogHeader) {
					otherHeight += dialogHeader.offsetHeight
				}
				if (dialogFooter) {
					otherHeight += dialogFooter.offsetHeight
				}
				dialogBody.style.maxHeight = `calc(100vh - ${otherHeight}px)`
				if (clientHeight - dialog.offsetHeight <= 180) {
					this.top = '60px'
				} else {
					this.top = '120px'
				}
			})
		},
		querySelector(el, parent = '#' + this.dialogId) {
			return document.querySelector(`${parent} ${el}`)
		},
	},
}
</script>

<style scoped lang="scss">
::v-deep .el-dialog__wrapper {
	background: rgba(0, 0, 0, 0.2);

	+ .v-modal {
		display: none;
	}
}

::v-deep .gc-dialog-custom {
	border-radius: 4px;
	display: flex;
	flex-direction: column;
	max-width: 100%;
	margin-bottom: 0;

	.el-dialog__header {
		padding: 20px 20px 10px 25px;
	}

	.title {
		display: flex;
		align-items: center;
		.icon {
			width: 20px;
			height: 20px;
			margin-right: 8px;
		}
		&-content {
			font-size: 20px;
			font-weight: 400;
			color: #282c42;
		}
	}

	.el-dialog__body {
		@include base-scrollbar(4px);
		padding: 15px 24px;
		overflow: auto;
		min-height: 58px;
		max-height: calc(100vh - 232px);
		box-sizing: border-box;
	}

	.footer {
		.gc-button:first-child {
			margin-right: 10px;
		}
		// .el-button {
		//   min-width: 100px;
		//   height: 32px;
		//   line-height: 32px;
		//   padding: 0 12px;
		//   &--primary {
		//     background-color: $base-color-blue;
		//     border-color: $base-color-blue;
		//   }
		//   &.is-border-light {
		//     color: $base-color-blue;
		//     border-color: $base-color-blue;
		//   }
		// }
	}
}
</style>
