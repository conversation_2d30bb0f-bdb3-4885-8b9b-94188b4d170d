<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-09 10:01:31
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 19:14:40
-->
<template>
	<div>
		<gc-detail-card :detail-card-info="detailCardInfo" :header-num="headerNum">
			<template #card-content>
				<div class="card-content">
					<div class="content-item" v-for="(item, index) in displayList" :key="index">
						<p class="field">{{ item.label }}</p>
						<p class="value">
							{{ infoData[item.key] }}
						</p>
					</div>
				</div>
			</template>
			<template #card-footer>
				<div class="card-footer">
					<el-button
						v-has="'plan-collection_meterReadingBook_updateBook2'"
						type="text"
						class="blue"
						@click="handleEdit"
					>
						<i class="iconfontCis icon-modify"></i>
						修改
					</el-button>
					<el-button v-has="'meterReadingBook_transfer_meterCardOpsPage'" type="text" @click="handleTransfer">
						<i class="iconfontCis icon-xls-table"></i>
						移交
					</el-button>
				</div>
			</template>
		</gc-detail-card>
		<!-- 册本修改弹窗 -->
		<RecordDialog
			ref="recordDialogRef"
			permissions="plan-collection_meterReadingBook_updateBook2"
			:show.sync="showRecord"
			editType="edit"
			@success="getInfo"
		/>
	</div>
</template>

<script>
import RecordDialog from '@/views/meter-reading/record-manage/components/recordDialog.vue'
import { getBookDetailById } from '@/api/meterReading.api.js'

export default {
	name: '',
	components: { RecordDialog },
	data() {
		return {
			showRecord: false,
			headerNum: {
				key: '表册编号',
				value: '--',
			},
			detailCardInfo: {
				bgUrl: require('@/assets/images/bg/pic-file.png'), //背景图url
				signUrl: require('@/assets/images/icon/title-file.png'), //header中的标志的url
				cardName: '册本信息', //卡片名
			},
			displayList: [
				{
					label: '册本类型',
					key: 'bookTypeDesc',
				},
				{
					label: '抄表员',
					key: 'meterReadingStaffName',
				},
				{
					label: '抄表员电话',
					key: 'meterReadingStaffPhone',
				},
				{
					label: '创建时间',
					key: 'createTime',
				},
				{
					label: '坊别',
					key: 'alleyName',
				},
				{
					label: '抄表周期',
					key: 'meterReadingCycleDesc',
				},
				{
					label: '抄表范围',
					key: 'archivesNoRange',
				},
			],
			infoData: {
				bookTypeDesc: '--',
				meterReadingStaffName: '--',
				meterReadingStaffPhone: '--',
				meterIsVirtual: '--',
				alleyName: '--',
				meterReadingCycleDesc: '--',
				archivesNoRange: '--',
			},
		}
	},
	created() {
		this.getInfo()
	},
	methods: {
		async getInfo() {
			const bookId = this.$route.query.bookId
			if (!bookId) return
			try {
				const res = await getBookDetailById({
					bookId,
				})
				this.headerNum.value = res.bookNo ?? '--'
				this.infoData = {
					...res,
					staffPhone: res.meterReadingStaffPhone,
				}
			} catch (error) {
				console.error(error)
				this.headerNum.value = '--'
				this.infoData = {
					bookTypeDesc: '--',
					meterReadingStaffName: '--',
					meterReadingStaffPhone: '--',
					meterIsVirtual: '--',
					alleyName: '--',
					meterReadingCycleDesc: '--',
					archivesNoRange: '--',
				}
			}
		},
		handleEdit() {
			this.showRecord = true
			this.$nextTick(() => {
				this.$refs.recordDialogRef.setFormData(this.infoData)
			})
		},
		// 移交
		handleTransfer() {
			const { orgCode, bookType, bookNo } = this.infoData
			this.$router.push({
				path: '/meterReading/meterReadingTransfer',
				query: {
					orgCode,
					bookType,
					bookNo,
				},
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.detail-card {
	.card-footer {
		padding: 0;
		display: flex;
		justify-content: space-around;
		::v-deep .el-button {
			font-size: 14px;
			color: $base-color-yellow;
			padding-bottom: 0;
			i {
				padding-right: 3px;
				font-size: 14px;
			}
			span {
				display: flex;
				align-items: center;
			}
		}
		.el-button + .el-button {
			margin-left: 2px;
			margin-right: 2px;
		}
		.blue {
			color: $base-color-blue;
		}
	}
}
</style>
