<template>
	<div class="left-container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
		<div class="btn-group">
			<el-button style="width: 50%" round @click="handleReset">重置</el-button>
			<el-button type="primary" style="width: 50%" round @click="handlSearch">筛选</el-button>
		</div>
	</div>
</template>

<script>
import { bookTypeOptions, readingStatusOptions, checkStatusOptions } from '@/consts/optionList.js'
import { getAlleyMap } from '@/api/meterReading.api.js'

export default {
	name: '',
	components: {},
	data() {
		return {
			formData: {
				taskYear: this.dayjs().format('YYYY'),
				orgCode: '',
				alleyId: '',
				bookType: '',
				bookNo: '',
				checkStatus: '',
				readingStatus: '',
				readDate: [],
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '抄表年份',
					prop: 'taskYear',
					attrs: {
						col: 24,
						clearable: false,
						type: 'year',
						valueFormat: 'yyyy',
					},
				},
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						col: 24,
						clearable: false,
						placeholder: '请选择营业分公司',
					},
					events: {
						change: () => {
							this.formData.alleyId = ''
							this.getAlleyMapData()
						},
					},
				},
				{
					type: 'el-select',
					label: '坊别',
					prop: 'alleyId',
					options: [],
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择坊别',
					},
				},
				{
					type: 'el-select',
					label: '册本类型',
					prop: 'bookType',
					options: bookTypeOptions,
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择册本类型',
					},
				},
				{
					type: 'el-input',
					label: '表册编号',
					prop: 'bookNo',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入表册编号',
					},
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
				{
					type: 'el-select',
					label: '抄表状态',
					prop: 'readingStatus',
					options: readingStatusOptions,
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择抄表状态',
					},
				},
				{
					type: 'el-select',
					label: '抄表情况',
					prop: 'checkStatus',
					options: checkStatusOptions,
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择抄表情况',
					},
				},
				{
					type: 'el-date-picker',
					label: '抄表时间',
					prop: 'readDate',
					attrs: {
						col: 24,
						type: 'daterange',
						defaultTime: ['00:00:00', '23:59:59'],
						valueFormat: 'yyyy-MM-dd HH:mm:ss',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
					},
				},
			],
			formAttrs: {
				rules: {
					taskYear: {
						required: true,
						message: '请选择抄表年份',
						trigger: 'change',
					},
					orgCode: {
						required: true,
						message: '请选择营业分公司',
						trigger: 'change',
					},
				},
			},
		}
	},
	created() {
		const { orgCode = '', bookNo = '', taskYear = '' } = this.$route.query
		if (orgCode && bookNo) {
			this.formData.orgCode = orgCode
			this.formData.bookNo = bookNo
			this.formData.taskYear = taskYear || this.dayjs().format('YYYY')
			this.$emit('search', this.formData)
			this.getAlleyMapData()
		}
	},
	computed: {
		orgOptions() {
			return this.formItems[1].options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.getAlleyMapData()
					this.$emit('search', this.formData)
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		handleReset() {
			this.$refs.formRef.resetFormSmooth()
			this.$emit('reset')
		},
		async handlSearch() {
			const valid = await this.$refs.formRef.validate()

			if (valid) {
				const readDate = this.formData.readDate
				if (!readDate) this.formData.readDate = []
				this.$emit('search', {
					...this.formData,
					readDateBegin: readDate?.length ? readDate[0] : '',
					readDateEnd: readDate?.length ? readDate[1] : '',
				})
			}
		},
		// 获取坊别数据
		async getAlleyMapData() {
			try {
				const res = await getAlleyMap({
					orgCode: this.formData.orgCode,
				})
				if (res) {
					this.formItems[2].options = res.map(item => {
						return {
							value: item.id,
							label: item.alleyName,
						}
					})
				}
			} catch (error) {
				console.error(error)
				this.formItems[2].options = []
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.left-container {
	display: flex;
	flex-direction: column;
	flex: 0 0 270px;
	margin-right: 20px;
	padding: 20px;
	background-color: #fff;
}
.el-form {
	flex: 1;
	overflow-y: auto;
	overflow-x: hidden;
}
.btn-group {
	flex: 0 0 52px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	.el-button {
		height: 30px;
	}
}
</style>
