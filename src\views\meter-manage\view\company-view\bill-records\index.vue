<template>
	<div class="container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch">
					<i class="iconfontCis icon-small-search"></i>
					筛选
				</el-button>
				<el-button @click="handleReset">
					<i class="iconfontCis icon-reset"></i>
					重置
				</el-button>
			</el-form-item>
		</GcFormSimple>
		<div class="btn-box">
			<el-button type="primary" :disabled="!tableData.length" @click="handleExport">导出</el-button>
			<el-button
				v-has="'payment_invoice_merge-open-invoice3'"
				type="primary"
				:disabled="invoiceOpenDisabled"
				@click="handleOpenInvoice"
			>
				合并开票
			</el-button>
			<el-button type="primary" :disabled="!selectBillData.length" @click="showDialog = true">催缴登记</el-button>
		</div>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handleChangePage"
				needType="selection"
				@selectChange="selectChange"
			/>
		</div>
		<!-- 催缴登记 -->
		<CheckInDialog :show.sync="showDialog" :data="selectBillData" @success="$emit('refresh')" />
	</div>
</template>

<script>
import { apiGetBillRecordList1, apiBillRecordExportExcel } from '@/api/meterManage.api'
import { removeNullParams, trimParams, exportBlob } from '@/utils/index.js'
import CheckInDialog from '@/views/arrearage-manage/components/checkInDialog.vue'

export default {
	components: { CheckInDialog },
	props: {
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		const _t = this
		const pickerOptions = {
			shortcuts: [
				{
					text: '最近一年',
					onClick(picker) {
						const end = new Date(_t.dayjs().endOf('year').format('YYYY-MM-DD'))
						const start = new Date(_t.dayjs().startOf('year').format('YYYY-MM-DD'))
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近三年',
					onClick(picker) {
						const end = new Date(_t.dayjs().endOf('year').format('YYYY-MM-DD'))
						const start = new Date(_t.dayjs().subtract(2, 'year').startOf('year').format('YYYY-MM-DD'))
						picker.$emit('pick', [start, end])
					},
				},
			],
		}
		return {
			formData: {
				openDate: [
					this.dayjs().subtract(2, 'year').startOf('year').format('YYYY-MM-DD'),
					this.dayjs().endOf('year').format('YYYY-MM-DD'),
				],
				callDays: undefined,
				userName: '',
				billNo: '',
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '开账日期',
					prop: 'openDate',
					attrs: {
						type: 'daterange',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						style: {
							width: '250px',
						},
						pickerOptions,
					},
				},
				{
					type: 'el-input-number',
					label: '距离上次催缴超过天数',
					prop: 'callDays',
					attrs: {},
				},
				{
					type: 'el-input',
					label: '用户名称',
					prop: 'userName',
					attrs: {
						style: {
							width: '100px',
						},
					},
				},
				{
					type: 'el-input',
					label: '账单编号',
					prop: 'billNo',
					attrs: {
						style: {
							width: '150px',
						},
					},
				},
			],
			formAttrs: {
				inline: true,
				rules: {
					openDate: [{ required: true, message: '请选择开账日期', trigger: 'change' }],
				},
			},
			columns: [
				{
					key: 'billNo',
					name: '账单编号',
					minWidth: '220px',
					tooltip: true,
				},
				{
					key: 'billDate',
					name: '账期',
					tooltip: true,
				},
				{
					key: 'useAmount',
					name: '抄表水量',
					tooltip: true,
				},
				{
					key: 'priceCode',
					name: '价格编号',
					tooltip: true,
				},
				{
					key: 'priceDesc',
					name: '价格',
					width: '250px',
					render: (h, row) => {
						const str = row.priceDesc ? row.priceDesc.replace(/<br\/>/g, '\n') : ''
						return h('span', { style: { whiteSpace: 'pre-line' } }, str)
					},
				},
				{
					key: 'priceBillItemList',
					name: '附加价格',
					tooltip: true,
					render: (h, row) => {
						let str = ''
						if (row.priceBillItemList?.length > 0) {
							str = this.surchangePrice(row.priceBillItemList)
						}
						return h('span', { style: { whiteSpace: 'pre-line' } }, str)
					},
				},
				{
					key: 'cycSurplusDesc',
					name: '阶梯剩余量',
					width: '250px',
					render: (h, row) => {
						const str = row.cycSurplusDesc ? row.cycSurplusDesc.replace(/<br\/>/g, '\n') : ''
						return h('span', { style: { whiteSpace: 'pre-line' } }, str)
					},
				},
				{
					key: 'useAmt',
					name: '水费',
					tooltip: true,
				},
				{
					key: 'billItemAmt',
					name: '附加费',
					tooltip: true,
				},
				{
					key: 'receivableAmount',
					name: '应缴金额',
					tooltip: true,
				},
				{
					key: 'userName',
					name: '用户名称',
					tooltip: true,
				},
				{
					key: 'userMobile',
					name: '手机',
					tooltip: true,
				},
				{
					key: 'callDays',
					name: '距离上次催缴（天）',
					tooltip: true,
				},
				{
					key: 'callTime',
					name: '上次催缴时间',
					tooltip: true,
				},
				{
					key: 'payTime',
					name: '缴费时间',
					tooltip: true,
				},
				{
					key: 'paidAmount',
					name: '缴费金额',
					tooltip: true,
					render: (h, row) => {
						const str = row.payTime ? row.paidAmount : ''
						return h('span', {}, str)
					},
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
			selectBillData: [],
			showDialog: false,
		}
	},
	computed: {
		invoiceOpenDisabled() {
			if (!this.selectBillData.length) return true
			return this.selectBillData.some(({ billStatus, invoiceStatus }) => {
				return billStatus !== 4 || (invoiceStatus !== 0 && invoiceStatus !== 2) // 已销账，未开票或者已冲红的才能开票
			})
		},
	},
	methods: {
		// 附加费
		surchangePrice(data) {
			const arr = data.map(item => {
				return `${item.itemName}: ${item.billItemPrice}元/吨`
			})
			return arr ? arr.join('\n') : ''
		},
		async getList() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) {
				return
			}
			this.loading = true
			try {
				const extractedData = Object.assign({}, ...Object.values(this.tabData))
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					archivesId: extractedData?.archivesId,
					current,
					size,
				})
				if (formParams.openDate && formParams.openDate.length > 1) {
					formParams.openStartDate = this.dayjs(formParams.openDate[0]).format('YYYY-MM-DD')
					formParams.openEndDate = this.dayjs(formParams.openDate[1]).format('YYYY-MM-DD')
				}
				delete formParams.openDate
				const { records, total } = await apiGetBillRecordList1(formParams)
				this.tableData = records.map(item => {
					if (item.priceDesc) {
						item['priceDescFilter'] = item.priceDesc ? item.priceDesc.replace(/<br\/>/g, '\n') : ''
					}

					return {
						...item,
						archivesNo: extractedData?.archivesIdentity,
						arrearsAmount: item.receivableAmount,
						billYear: this.dayjs(item.billDate).year(),
						waterAmount: item.useAmount,
					}
				})
				this.pageData.total = total
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleSearch() {
			this.handleChangePage({ page: 1 })
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.handleSearch()
		},
		selectChange(arr) {
			this.selectBillData = arr
		},
		async handleExport() {
			const extractedData = Object.assign({}, ...Object.values(this.tabData))
			const { current, size } = this.pageData
			const formParams = trimParams(removeNullParams(this.formData))

			Object.assign(formParams, {
				archivesId: extractedData?.archivesId,
				current,
				size,
				openStartDate: this.dayjs(formParams.openDate[0]).format('YYYY-MM-DD'),
				openEndDate: this.dayjs(formParams.openDate[1]).format('YYYY-MM-DD'),
			})
			delete formParams.openDate
			const res = await apiBillRecordExportExcel(formParams)
			exportBlob(res, '账单记录')
		},
		handleOpenInvoice() {
			this.$emit('openInvoice', {
				type: 'merge',
				data: this.selectBillData,
				onSuccess: this.handleSearch,
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.btn-box {
	margin-bottom: 10px;
	display: flex;
	justify-content: flex-end;
}
</style>
