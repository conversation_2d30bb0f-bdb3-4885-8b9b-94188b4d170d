import { userTypeOptions } from '@/consts/optionList.js'
export function getColumn(instance) {
	let residentColumns = [
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'userType',
			name: '表卡类型',
			tooltip: true,
			render: (h, row) => {
				const typeDes = (userTypeOptions.find(type => type.value === row.userType) || {}).label || '--'
				return h('span', {}, typeDes)
			},
		},
		{
			key: 'userName',
			name: '用户名称',
			tooltip: true,
		},
		{
			key: 'addressFullName',
			name: '地址',
			tooltip: true,
			minWidth: 250,
		},
		{
			key: 'meterBalanceAmount',
			name: '表卡余额',
			tooltip: true,
			align: 'right',
			width: 150,
			render: function (h, row) {
				const balance = row.meterBalanceAmount - 0
				return h('span', {}, balance.toFixed(2))
			},
		},
		{
			key: 'operate',
			name: '操作',
			fixed: 'right',
			width: 120,
		},
	]
	let companyColumns = [
		{
			key: 'enterpriseNumber',
			name: '企业编号',
			tooltip: true,
		},
		{
			key: 'userName',
			name: '企业名称',
			tooltip: true,
		},
		{
			key: 'companyAddress',
			name: '企业地址',
			tooltip: true,
			minWidth: 250,
		},
		{
			key: 'meterBalanceAmount',
			name: '预存余额',
			tooltip: true,
			align: 'right',
			width: 150,
			render: function (h, row) {
				const balance = row.meterBalanceAmount - 0
				return h('span', {}, balance.toFixed(2))
			},
		},
		{
			key: 'operate',
			name: '操作',
			fixed: 'right',
			width: 120,
		},
	]
	const columns = instance.activeTab === 'resident' ? residentColumns : companyColumns
	return columns
}
