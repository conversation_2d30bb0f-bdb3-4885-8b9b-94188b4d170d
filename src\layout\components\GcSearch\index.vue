<template>
	<div :class="'search-container-' + theme.layout">
		<gc-icon
			v-has="['cpm_archives_list', 'cpm_archives_list2']"
			icon="icon-overall-search"
			@click="showSearchDialog = true"
		/>
		<GcSearchDialogGlobal
			title=""
			:dialogVisible.sync="showSearchDialog"
			:search-condition="searchCondition"
			:active-condition.sync="activeCondition"
			dialog-width="900px"
		/>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
	name: 'GcSearch',
	data() {
		return {
			showSearchDialog: false,
			searchCondition: [
				{
					key: 'enterpriseNumber',
					label: '企业编号',
				},
				{
					key: 'archivesIdentity',
					label: '表卡编号',
				},
				{
					key: 'userName',
					label: '用户名称',
				},
				{
					key: 'bookNo',
					label: '表册编号',
				},
				{
					key: 'userMobile',
					label: '手机号',
				},
				{
					key: 'certificateNo',
					label: '证件号码',
				},
				{
					key: 'addressName',
					label: '地址描述',
				},
				{
					key: 'oldArchivesIdentity',
					label: '旧表卡编号',
				},
				{
					key: 'tapWaterNo',
					label: '自来水编号',
				},
				{
					key: 'meterNo',
					label: '水表编号',
				},
				{
					key: 'antiTheftCode',
					label: '防盗编号',
				},
			],
			activeCondition: 'enterpriseNumber',
		}
	},
	computed: {
		...mapGetters({
			theme: 'settings/theme',
		}),
	},
	watch: {},
	methods: {},
}
</script>

<style lang="scss" scoped>
.icon-overall-search {
	margin-left: 26px;
	cursor: pointer;
}
.search-container-horizontal {
	color: $base-color-white;
}
</style>
