<template>
	<div class="container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch">
					<i class="iconfontCis icon-small-search"></i>
					筛选
				</el-button>
				<el-button @click="handleReset">
					<i class="iconfontCis icon-reset"></i>
					重置
				</el-button>
			</el-form-item>
		</GcFormSimple>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handleChangePage"
			></GcTable>
		</div>
	</div>
</template>

<script>
import { getfilterName } from '@/utils'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiGetBillPayRecordListUser } from '@/api/meterManage.api'
export default {
	data() {
		const _t = this
		const pickerOptions = {
			shortcuts: [
				{
					text: '最近一年',
					onClick(picker) {
						const end = new Date(_t.dayjs().endOf('year').format('YYYY-MM-DD'))
						const start = new Date(_t.dayjs().startOf('year').format('YYYY-MM-DD'))
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近三年',
					onClick(picker) {
						const end = new Date(_t.dayjs().endOf('year').format('YYYY-MM-DD'))
						const start = new Date(_t.dayjs().subtract(2, 'year').startOf('year').format('YYYY-MM-DD'))
						picker.$emit('pick', [start, end])
					},
				},
			],
		}
		return {
			formData: {
				invoiceStatus: '',
				payMode: '',
				archivesIdentity: '',
				payDate: [
					this.dayjs().subtract(2, 'year').startOf('year').format('YYYY-MM-DD'),
					this.dayjs().endOf('year').format('YYYY-MM-DD'),
				],
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '缴费日期',
					prop: 'payDate',
					attrs: {
						type: 'daterange',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						style: {
							width: '250px',
						},
						pickerOptions,
					},
				},
				{
					type: 'el-select',
					label: '缴费方式',
					prop: 'payMode',
					options:
						this.$store.getters?.dataList?.payMode?.map(item => {
							return {
								label: item.sortName,
								value: item.sortValue,
							}
						}) || [],
					attrs: {
						style: {
							width: '120px',
						},
					},
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '90px',
				rules: {
					payDate: [{ required: true, message: '请选择缴费日期', trigger: 'change' }],
				},
			},
			columns: [
				{
					key: 'payTime',
					name: '缴费时间',
					tooltip: true,
				},
				{
					key: 'payMode',
					name: '缴费方式',
					tooltip: true,
					render: (h, row, total, scope) => {
						const valueStr = getfilterName(
							this.$store.getters.dataList.payMode,
							row[scope.column.property],
							'sortValue',
							'sortName',
						)
						return h('span', {}, valueStr)
					},
				},
				{
					key: 'paidAmount',
					name: '缴费金额',
					tooltip: true,
				},
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'billDate',
					name: '账期',
					tooltip: true,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
		}
	},
	methods: {
		async getList() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) {
				return
			}
			this.loading = true
			try {
				const { size, current } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
					userId: this.$route.query.userId || '',
				})

				if (formParams.payDate && formParams.payDate.length > 1) {
					formParams['payDateBegin'] = this.dayjs(formParams.payDate[0]).format('YYYY-MM-DD')
					formParams['payDateEnd'] = this.dayjs(formParams.payDate[1]).format('YYYY-MM-DD')
					delete formParams.payDate
				}

				const { records, total } = await apiGetBillPayRecordListUser(formParams)
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 点击开票
		goInvoice(row) {
			console.log(row)
			this.$message.error('开票功能暂未开放')
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleSearch() {
			this.handleChangePage({ page: 1 })
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.handleSearch()
		},
	},
}
</script>
