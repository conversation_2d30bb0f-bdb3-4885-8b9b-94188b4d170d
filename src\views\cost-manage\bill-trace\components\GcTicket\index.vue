<template>
	<div class="gc-ticket" :style="{ minWidth: minWidth }">
		<div class="gc-title">{{ title }}</div>
		<div
			v-for="(item, index) in list"
			class="gc-wrapper"
			:class="{ highlight: item.billNo === currentBillNo }"
			:key="index"
		>
			<div class="gc-circle-top"></div>
			<div class="gc-circle-bottom"></div>
			<div class="gc-content">
				<h1>{{ item.title }}</h1>
				<div v-for="(subItem, subIndex) in item.subList" :key="index + '-' + subIndex">
					<div class="label">{{ subItem.label }}</div>
					<div class="value">{{ subItem.value }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'GcTicket',
	props: {
		title: {
			type: String,
			default: '标题',
		},
		list: {
			type: Array,
			default: () => [1],
		},
		minWidth: {
			type: String,
			default: '152px',
		},
		currentBillNo: String,
	},
}
</script>

<style lang="scss" scoped>
.gc-ticket {
	display: flex;
	flex-direction: column;
	border-radius: 16px;
	flex-grow: 0;
	flex-shrink: 0;
	width: fit-content;
	height: fit-content;
	box-shadow: 0px 8px 24px -4px #18274b14;
	box-shadow: 0px 10px 20px -6px #18274b3b;

	.gc-title {
		position: relative;
		padding: 8px;
		border-radius: 8px 8px 0 0;
		background-color: #fff;
		font-family: Source Han Sans CN;
		font-size: 14px;
		font-weight: 700;
		color: #1d59ad;
	}
	.gc-title:first-of-type {
		background-color: #e2eeff;
	}
	.gc-wrapper {
		min-height: 130px;
		background-color: #fff;
	}
	.gc-circle-top {
		position: relative;
		height: 6px;
		background-color: #fff;
		background-image: radial-gradient(circle at left bottom, #f4f5fb, #f4f5fb 6px, transparent 7px),
			radial-gradient(circle at right bottom, #f4f5fb, #f4f5fb 6px, transparent 7px);
	}
	.gc-circle-top::after {
		content: '';
		--c: #d9d9d9; /* color */
		--r: 2px; /* circle size */
		--s: 2px; /* space bettwen circles */
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		margin: auto;
		width: calc(100% - 13px);
		height: 1px;
		--g: radial-gradient(circle closest-side, var(--c) 85%, transparent);
		background: var(--g) calc(var(--s) / -2) 0 / calc(var(--r) + var(--s)) var(--r) repeat-x;
		transform: scaleY(-1);
	}
	.gc-circle-bottom {
		position: relative;
		height: 6px;
		background-color: #fff;
		background-image: radial-gradient(circle at left top, #f4f5fb, #f4f5fb 6px, transparent 7px),
			radial-gradient(circle at right top, #f4f5fb, #f4f5fb 6px, transparent 7px);
	}
	.gc-content {
		padding: 7px 10px;
		h1 {
			margin-bottom: 10px;
			font-size: 14px;
			font-weight: 700;
			color: #000000;
		}
		.label {
			margin-bottom: 10px;
			font-size: 12px;
			font-weight: 400;
			color: #999999;
		}
		.value {
			margin-bottom: 10px;
			font-size: 14px;
			font-weight: 400;
			color: #4e4e4e;
		}
	}
	.gc-content > div:last-child .value {
		margin-bottom: 0;
	}
	.gc-wrapper:last-child .gc-content {
		padding-bottom: 15px;
	}
	.gc-wrapper.highlight {
		h1,
		.value {
			color: #567ced;
		}
	}
}
</style>
