import { bookTypeOptions } from '@/consts/optionList'
export const getFormItems = _this => {
	return {
		leftFormItems: [
			{
				type: 'el-select',
				label: '营业所',
				prop: 'orgCode',
				options: _this.$store.getters.orgList,
				attrs: {
					style: {
						width: '150px',
					},
				},
				events: {
					change: _this._getAlleyMap,
				},
			},
			{
				type: 'el-select',
				label: '表册类型',
				prop: 'bookType',
				options: bookTypeOptions,
				attrs: {
					style: {
						width: '150px',
					},
					disabled: !_this.$route.query.archivesId,
				},
			},
			{
				type: 'el-select',
				label: '坊别',
				prop: 'alleyId',
				options: [],
				attrs: {
					style: {
						width: '150px',
					},
				},
			},
			{
				type: 'el-input',
				label: '表册编号',
				prop: 'bookNo',
				attrs: {
					style: {
						width: '150px',
					},
				},
			},
		],
		rightFormItems: [
			{
				type: 'el-input',
				label: '表册编号',
				prop: 'bookNo',
				attrs: {
					disabled: true,
				},
			},
			{
				type: 'el-input',
				label: '表册类型',
				prop: 'bookTypeDesc',
				attrs: {
					disabled: true,
				},
			},
			{
				type: 'el-input',
				label: '坊别',
				prop: 'alleyName',
				attrs: {
					disabled: true,
				},
			},
			{
				type: 'el-input',
				label: '抄表员',
				prop: 'meterReadingStaffName',
				attrs: {
					disabled: true,
				},
			},
			{
				type: 'el-input',
				label: '抄表员电话',
				prop: 'meterReadingStaffPhone',
				attrs: {
					disabled: true,
				},
			},
			{
				type: 'el-input',
				label: '抄表周期',
				prop: 'meterReadingCycleDesc',
				attrs: {
					disabled: true,
				},
			},
			{
				type: 'el-input',
				label: '抄表范围',
				prop: 'archivesNoRange',
				attrs: {
					disabled: true,
				},
			},
		],
	}
}
