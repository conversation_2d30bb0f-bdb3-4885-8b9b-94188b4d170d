<template>
	<div class="tag-input-container" :class="{ 'is-focus': isFocus }">
		<el-tag v-for="tag in displayTags" :key="tag" closable :type="tagType" :size="size" @close="handleClose(tag)">
			{{ tag }}
		</el-tag>

		<div class="tag-input-add-container">
			<el-input
				v-if="inputVisible"
				class="tag-input-new-tag-input"
				v-model="inputValue"
				ref="saveTagInput"
				:size="size"
				@keyup.enter.native="handleInputConfirm"
				@blur="handleInputConfirm"
				@focus="handleFocus"
			></el-input>
			<el-button v-else class="tag-input-new-tag-button" :size="size" @click="showInput">
				{{ addButtonText }}
			</el-button>
		</div>
	</div>
</template>

<script>
export default {
	name: 'TagInput',
	props: {
		value: {
			type: [Array, String],
			default: () => [],
		},
		size: {
			type: String,
			default: 'mini',
			validator: value => ['medium', 'small', 'mini'].includes(value),
		},
		tagType: {
			type: String,
			default: 'info',
			validator: value => ['success', 'info', 'warning', 'danger', ''].includes(value),
		},
		addButtonText: {
			type: String,
			default: '+ 添加',
		},
		maxTags: {
			type: Number,
			default: Infinity,
		},
		disabled: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			displayTags: [],
			inputVisible: false,
			inputValue: '',
			isFocus: false,
			confirmTimer: null,
		}
	},
	watch: {
		value: {
			immediate: true,
			handler(newVal) {
				this.displayTags = typeof newVal === 'string' ? newVal.split(',').filter(Boolean) : [...newVal]
			},
		},
	},
	methods: {
		updateTags(newTagsArray) {
			if (this.maxTags && newTagsArray.length > this.maxTags) {
				this.$emit('exceed', newTagsArray[newTagsArray.length - 1])
				newTagsArray = newTagsArray.slice(0, this.maxTags)
			}
			this.$emit('input', newTagsArray) // 始终emit数组
			this.$emit('change', newTagsArray) // 同时触发change事件
		},
		handleClose(tag) {
			if (this.disabled) return
			const newTags = this.displayTags.filter(t => t !== tag)
			this.updateTags(newTags)
		},

		showInput() {
			if (this.disabled || (this.maxTags && this.displayTags.length >= this.maxTags)) return
			this.inputVisible = true
			this.$nextTick(() => {
				this.$refs.saveTagInput?.$refs.input?.focus()
			})
		},

		handleInputConfirm() {
			this.handleBlur()
		},

		handleBlur() {
			if (this.confirmTimer) clearTimeout(this.confirmTimer)

			this.confirmTimer = setTimeout(() => {
				if (this.inputValue.trim()) {
					const newTags = [...new Set([...this.displayTags, this.inputValue.trim()])]
					this.updateTags(newTags)
				}

				this.inputVisible = false
				this.inputValue = ''
				this.isFocus = false
			}, 150)
		},

		handleFocus() {
			this.isFocus = true
		},

		clear() {
			this.updateTags([])
		},

		addTag(tag) {
			if (!this.disabled) {
				const newTags = [...this.displayTags, tag]
				this.updateTags(newTags)
			}
		},

		removeTag(tag) {
			this.handleClose(tag)
		},
	},
}
</script>
<style lang="scss" scoped>
.tag-input-container {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	border: 1px solid #dcdfe6;
	background-color: #ffffff;
	border-radius: 4px;
	min-height: 32px;
	transition: border-color 0.2s;

	&.is-focus {
		border-color: #409eff;
	}

	&:hover {
		border-color: #c0c4cc;
	}

	.el-tag {
		margin: 2px 0 2px 6px;
	}
}

.tag-input-new-tag-button {
	margin: 2px 0 2px 6px;
	height: 24px;
	line-height: 22px;
	padding: 0 8px;
}

.tag-input-new-tag-input {
	margin: 2px 0 2px 6px;
	width: 90px;

	/deep/ .el-input__inner {
		height: 24px;
		line-height: 24px;
		padding: 0 8px;
		min-height: 24px;
	}
}
</style>
