<template>
	<div class="tag-input-container" :class="{ 'is-focus': isFocus }">
		<div class="tag-input-content">
			<el-tag
				v-for="tag in displayTags"
				:key="tag"
				closable
				:type="tagType"
				size="mini"
				@close="handleClose(tag)"
			>
				{{ tag }}
			</el-tag>

			<div class="tag-input-add-container">
				<el-input
					v-if="inputVisible"
					class="tag-input-new-tag-input"
					v-model="inputValue"
					ref="saveTagInput"
					size="mini"
					@keyup.enter.native="handleInputConfirm"
					@blur="handleInputConfirm"
					@focus="handleFocus"
				></el-input>
				<el-button v-else class="tag-input-new-tag-button" :disabled="disabled" size="mini" @click="showInput">
					{{ addButtonText }}
				</el-button>
			</div>
		</div>

		<div class="tag-input-extra">
			<div class="tag-input-upload">
				<el-button class="tag-input-upload-button" type="text" @click="handleUploadButtonClick">
					<i class="tag-input-upload-icon el-icon-upload"></i>
				</el-button>
				<input
					ref="uploadInputRef"
					type="file"
					name="file"
					accept=".xlsx, .xls, .csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
					class="tag-input-upload-input"
					@change="handleFileChange"
				/>
			</div>
			<!-- <el-upload
				accept=".xlsx, .xls, .csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
				:show-file-list="false"
				:multiple="false"
				@on-change="handleFileChange"
			>
				<el-button class="tag-input-upload-button" type="text">
					<i class="tag-input-upload-icon el-icon-upload"></i>
				</el-button>
			</el-upload> -->

			<!-- -->
		</div>
	</div>
</template>

<script>
import XLSX from 'xlsx'

const validateMap = {
	number: input => {
		return /^-?\d+(\.\d+)?$/.test(input)
	},
}

export default {
	name: 'TagInput',
	props: {
		value: {
			type: [Array, String],
			default: () => [],
		},
		tagType: {
			type: String,
			default: 'info',
			validator: value => ['success', 'info', 'warning', 'danger', ''].includes(value),
		},
		addButtonText: {
			type: String,
			default: '+ 添加',
		},
		maxTags: {
			type: Number,
			default: Infinity,
		},
		disabled: {
			type: Boolean,
			default: false,
		},
		type: {
			type: String,
			default: 'string',
		},
	},
	data() {
		return {
			displayTags: [],
			inputVisible: false,
			inputValue: '',
			isFocus: false,
			confirmTimer: null,
		}
	},
	watch: {
		value: {
			immediate: true,
			handler(newVal) {
				this.displayTags = typeof newVal === 'string' ? newVal.split(',').filter(Boolean) : [...newVal]
			},
		},
	},
	methods: {
		updateTags(newTagsArray) {
			if (this.maxTags && newTagsArray.length > this.maxTags) {
				this.$emit('exceed', newTagsArray[newTagsArray.length - 1])
				newTagsArray = newTagsArray.slice(0, this.maxTags)
			}
			this.$emit('input', newTagsArray) // 始终emit数组
			this.$emit('change', newTagsArray) // 同时触发change事件
		},
		handleClose(tag) {
			if (this.disabled) return
			const newTags = this.displayTags.filter(t => t !== tag)
			this.updateTags(newTags)
		},

		showInput() {
			if (this.disabled || (this.maxTags && this.displayTags.length >= this.maxTags)) return
			this.inputVisible = true
			this.$nextTick(() => {
				this.$refs.saveTagInput?.$refs.input?.focus()
			})
		},

		handleInputConfirm() {
			this.handleBlur()
		},
		validate(value) {
			const { type } = this

			const validateMethod = validateMap[type]
			if (validateMethod) {
				return validateMethod(value)
			}

			return true
		},
		handleBlur() {
			if (this.confirmTimer) clearTimeout(this.confirmTimer)

			this.confirmTimer = setTimeout(() => {
				const value = this.inputValue.trim()
				if (value && this.validate(value)) {
					const newTags = [...new Set([...this.displayTags, value])]
					this.updateTags(newTags)
				}

				this.inputVisible = false
				this.inputValue = ''
				this.isFocus = false
			}, 150)
		},

		handleFocus() {
			this.isFocus = true
		},

		clear() {
			this.updateTags([])
		},

		addTag(tag) {
			if (!this.disabled) {
				tag = Array.isArray(tag) ? tag : [tag]
				const newTags = [...new Set([...this.displayTags, ...tag])]
				this.updateTags(newTags)
			}
		},

		removeTag(tag) {
			this.handleClose(tag)
		},
		handleUploadButtonClick() {
			this.$refs.uploadInputRef.click()
		},
		parseExcelData(data) {
			data = data || []
			return data.reduce((acc, item) => {
				const [value] = item || []

				if (value !== undefined) {
					acc.push(value + '')
				}
				return acc
			}, [])
		},
		handleFileChange(event) {
			const file = event.target.files[0]
			if (!file) return

			const reader = new FileReader()

			reader.onload = e => {
				const data = new Uint8Array(e.target.result)
				const workbook = XLSX.read(data, { type: 'array' })

				// 读取第一个sheet
				const firstSheetName = workbook.SheetNames[0]
				const worksheet = workbook.Sheets[firstSheetName]

				// 转换为 json
				const json = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
				const formatedData = this.parseExcelData(json)

				this.addTag(formatedData)
			}

			reader.readAsArrayBuffer(file)
			event.target.value = ''
		},
	},
}
</script>
<style lang="scss" scoped>
.tag-input-container {
	display: flex;
	flex-wrap: wrap;
	align-items: flex-start;
	border: 1px solid #dcdfe6;
	background-color: #ffffff;
	border-radius: 4px;
	min-height: 32px;
	transition: border-color 0.2s;

	&.is-focus {
		border-color: #409eff;
	}

	&:hover {
		border-color: #c0c4cc;
	}

	.el-tag {
		margin: 2px 0 2px 6px;
	}
}

.tag-input-content {
	flex: 1;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	max-height: 100px;
	overflow: auto;
}

.tag-input-add-container {
	height: 30px;
	overflow: hidden;
}
.tag-input-new-tag-button {
	margin: 2px 0 2px 6px;
	height: 24px;
	line-height: 22px;
	padding: 0 8px;
}

.tag-input-new-tag-input {
	margin: 2px 0 2px 6px;
	width: 90px;
	line-height: 24px;
	/deep/ .el-input__inner {
		height: 24px;
		line-height: 24px;
		padding: 0 8px;
		min-height: 24px;
	}
}

.tag-input-extra {
	line-height: 1;
}
.tag-input-upload-button {
	padding: 0 8px;
	height: 30px;
}
.tag-input-upload-icon {
	font-size: 16px;
	line-height: 1;
}

.tag-input-upload-input {
	display: none;
}
</style>
