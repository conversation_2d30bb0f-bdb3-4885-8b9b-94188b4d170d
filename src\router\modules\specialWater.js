import Layout from '@/layout'

export default [
	{
		path: '/specialWater',
		name: 'SpecialWater',
		component: Layout,
		redirect: '/specialWater/fine',
		meta: {
			title: '用水',
			icon: 'icon-cis_yj_specialwater',
			permissions: [
				'cpm_fines_querypage',
				'cpm_fines_add',
				'cpm_fines_update',
				'cpm_fines_delete',
				'cpm_fines_payment',
				'cpm_fines_invoice',
				'cpm_planUsage_queryPage',
				'cpm_planUsage_recovery',
				'cpm_planUsage_import',
				'cpm_planUsage_downloadExcel',
				'cpm_planWaste_queryPage',
				'cpm_planWaste_import',
				'cpm_planWaste_add',
				'cpm_planWaste_remove',
				'cpm_planWaste_downloadExcel',
			],
		},
		children: [
			{
				path: 'fine',
				name: 'Fine',
				component: () => import('@/views/special-water/fine/index.vue'),
				meta: {
					title: '罚没款列表',
					keepAlive: true,
					icon: 'icon-cis_ej_fakuanliebiao',
					permissions: [
						'cpm_fines_querypage',
						'cpm_fines_add',
						'cpm_fines_update',
						'cpm_fines_delete',
						'cpm_fines_payment',
						'cpm_fines_invoice',
					],
				},
			},
			{
				path: 'planWaterManage',
				name: 'PlanWaterManage',
				component: () => import('@/views/special-water/plan-water-manage/index.vue'),
				meta: {
					title: '计划用水管理',
					keepAlive: true,
					icon: 'icon-cis_erji_jihuayongshuiguanli',
					permissions: [
						'cpm_planUsage_queryPage',
						'cpm_planUsage_recovery',
						'cpm_planUsage_import',
						'cpm_planUsage_downloadExcel',
					],
				},
			},
			{
				path: 'planFreeSewageChargesManage',
				name: 'PlanFreeSewageChargesManage',
				component: () => import('@/views/special-water/plan-free-sewage-charges/index.vue'),
				meta: {
					title: '计划免污水费用管理',
					keepAlive: true,
					icon: 'icon-cis_ej_jihuamianwushui',
					permissions: [
						'cpm_planWaste_queryPage',
						'cpm_planWaste_import',
						'cpm_planWaste_add',
						'cpm_planWaste_remove',
						'cpm_planWaste_downloadExcel',
					],
				},
			},
		],
	},
]
