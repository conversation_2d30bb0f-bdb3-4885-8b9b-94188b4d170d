<template>
	<GcElDialog :show="isShow" title="文件明细" width="1200px" :showFooter="false" @close="handleClose">
		<div class="container-table">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
			/>
		</div>
	</GcElDialog>
</template>
<script>
import { queryUnionSendFileDetail } from '@/api/costManage.api'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		rowData: {
			type: Object,
			default: () => ({}),
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'fileMonth',
					name: '文件生成月',
					tooltip: true,
				},
				{
					key: 'sendFileName',
					name: '文件名称',
					tooltip: true,
				},
				{
					key: 'sendFileType',
					name: '文件类型',
					tooltip: true,
				},
				{
					key: 'statusDesc',
					name: '文件状态',
					tooltip: true,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	watch: {
		isShow(val) {
			if (val) {
				try {
					if (val) {
						this.getList()
					}
				} catch (error) {
					console.log(error)
				}
			}
		},
	},
	methods: {
		async getList() {
			this.loading = true
			try {
				const params = {
					batchId: this.rowData.batchId,
				}
				const data = await queryUnionSendFileDetail(params).catch(e => {
					this.tableData = []
					this.pageData = {
						current: 1,
						size: 10,
						total: 0,
					}
					this.$message.error(e.message || '文件明细查询失败！')
				})
				if (data) {
					this.pageData.total = data.length
					this.tableData = data
					return
				}
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleClose() {
			this.isShow = false
			this.tableData = []
		},
	},
}
</script>
<style lang="scss" scoped>
.distributionMode {
	margin-bottom: 10px;
}
.container-table {
	height: 500px;
}
</style>
