import { getTagsViews, setTagsViews } from '@/utils/storage'

const filterCachedViews = views => {
	if (!views || views.length == 0) return []
	let cached = []
	for (const view of views) {
		if (view.meta.keepAlive && !cached.includes(view.name)) {
			cached.push(view.name)
		}
	}
	return cached
}

export default {
	namespaced: true,
	state: {
		tags: getTagsViews() || [],
		cachedViews: filterCachedViews(getTagsViews()),
		keepAlive: {
			keys: [],
			cache: Object.create(null),
		},
	},
	mutations: {
		// 添加路由tag
		ADD_VIEW: (state, view) => {
			const exist = state.tags.some(v => {
				return v.fullPath === view.fullPath
			})
			if (exist) return

			state.tags.push(
				Object.assign({}, view, {
					title: (view.query && view.query.title) || view.meta.title || 'no-name',
					matched: [],
				}),
			)
			setTagsViews(state.tags)
		},
		// 删除单个路由
		DEL_VIEW: (state, view) => {
			for (const [i, v] of state.tags.entries()) {
				if (v.fullPath === view.fullPath) {
					state.tags.splice(i, 1)
					break
				}
			}
			setTagsViews(state.tags)
		},
		// 删除所有路由，固定的页面（affix）排除在外
		DEL_ALL_VIEWS: state => {
			const affixTags = state.tags.filter(tag => tag.meta.affix)
			state.tags = affixTags
			setTagsViews(affixTags)
		},
		// 路由参数不同时更新
		UPDATE_VIEW: (state, view) => {
			for (let v of state.tags) {
				if (v.fullPath === view.fullPath) {
					v = Object.assign(v, view)
					break
				}
			}
		},

		// 缓存view相关
		ADD_CACHED_VIEW: state => {
			state.cachedViews = filterCachedViews(state.tags)
		},
		DEL_CACHED_VIEW: state => {
			state.cachedViews = filterCachedViews(state.tags)
		},
		DEL_ALL_CACHED_VIEWS: state => {
			state.cachedViews = filterCachedViews(state.tags)
		},
		// 缓存 keep-alive 相关
		SET_KEEPALIVE_KEYS: (state, keys) => {
			state.keepAlive.keys = keys
		},
		SET_KEEPALIVE_CACHE: (state, cache) => {
			state.keepAlive.cache = cache
		},
	},
	actions: {
		addView({ commit }, view) {
			commit('ADD_VIEW', view)
			commit('ADD_CACHED_VIEW', view)
		},
		delView({ commit, state }, view) {
			return new Promise(resolve => {
				commit('DEL_VIEW', view)
				commit('DEL_CACHED_VIEW')
				resolve([...state.tags])
			})
		},
		delAllViews({ commit, state }) {
			return new Promise(resolve => {
				commit('DEL_ALL_VIEWS')
				commit('DEL_ALL_CACHED_VIEWS')
				resolve([...state.tags])
			})
		},
		updateVisitedView({ commit }, view) {
			commit('UPDATE_VIEW', view)
		},
	},
}
