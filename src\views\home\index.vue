<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-10-12 14:09:20
 * @LastEditors: houyan
 * @LastEditTime: 2024-10-16 16:27:45
-->
<template>
	<div class="box-wrapper">
		<!-- 左侧快捷操作 -->
		<div class="quickentry-wrap firstlevel">
			<!-- 时间 -->
			<TimeCom />
			<!-- 问候语 -->
			<WelcomeCom />
			<!-- 天气 -->
			<!-- <weather-com /> -->
			<div class="img-wrap">
				<img src="@/assets/images/bg/workben-day.png" alt="" />
			</div>
			<!-- 快捷入口 -->
			<QuickEntry />
		</div>
		<!-- 主内容区域 -->
		<!-- <div class="main-wrap nodata">
			<img src="@/assets/images/empty.svg" alt="" />
		</div> -->
		<MainContent />
	</div>
</template>

<script>
import TimeCom from './components/TimeCom.vue'
import WelcomeCom from './components/WelcomeCom.vue'
// import WeatherCom from "./components/WeatherCom.vue";
import QuickEntry from './components/QuickEntry.vue'
import MainContent from './components/MainContent.vue'

export default {
	name: 'home',
	components: {
		TimeCom,
		WelcomeCom,
		// WeatherCom,
		QuickEntry,
		MainContent,
	},
}
</script>
<style lang="scss" scoped>
@import './index.scss';
</style>
