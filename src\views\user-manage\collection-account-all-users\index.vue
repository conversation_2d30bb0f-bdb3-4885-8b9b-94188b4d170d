<template>
	<div class="wrapper">
		<div class="title">托收账户所有企业</div>
		<div class="subTitle">托收协议号：{{ $route.query.num || '--' }}</div>
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			@current-page-change="handlePageChange"
		/>
	</div>
</template>

<script>
import { apiGetCollectionAccountUserList } from '@/api/userManage.api'
export default {
	name: 'CollectionAccountAllUsers',
	data() {
		return {
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'userName',
					name: '企业名称',
					tooltip: true,
				},
				{
					key: 'enterpriseNumber',
					name: '企业编号',
					tooltip: true,
				},
				{
					key: 'contactPeople',
					name: '联系人',
					tooltip: true,
				},
				{
					key: 'contact<PERSON>hone',
					name: '联系电话',
					tooltip: true,
				},
				{
					key: 'userMobile',
					name: '联系手机',
					tooltip: true,
				},
				{
					key: 'archivesCount',
					name: '实表表卡数量',
					tooltip: true,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	activated() {
		if (this.$route.query.recordId) {
			this.handlePageChange({ page: 1 })
		}
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		async getList() {
			this.loading = true
			try {
				const { recordId } = this.$route.query
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await apiGetCollectionAccountUserList({
					size,
					current,
					recordId,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	padding: 20px;
}

.title,
.subTitle {
	margin-bottom: 10px;
	color: black;
	font-weight: 900;
	font-size: 15px;
}
.subTitle {
	font-weight: 700;
	font-size: 14px;
}
</style>
