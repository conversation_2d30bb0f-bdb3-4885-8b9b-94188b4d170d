<template>
	<div class="select-container">
		<div class="page-layout">
			<div class="page-left">
				<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
					<template v-slot:otherInfo>
						<el-input v-model="formData.archivesIdentityStart" placeholder="起始表卡编号"></el-input>
						<span>-</span>
						<el-input v-model="formData.archivesIdentityEnd" placeholder="结束表卡编号"></el-input>
					</template>
				</GcFormSimple>

				<div class="btn-group">
					<el-button type="primary" style="width: 100%" round @click="handleSearch">筛选</el-button>
				</div>
			</div>

			<div class="page-right" v-loading="loading">
				<div class="table-choose">
					<span>表卡已选择：{{ checkedTotal }}个</span>
					<el-button type="primary" :disabled="!tableData.length" @click="handleClickAll">
						{{ checkAll ? '取消全部' : '选择全部表卡' }}
					</el-button>
				</div>
				<div class="table-wrapper">
					<div class="table-container">
						<el-table
							v-show="tableData.length"
							ref="tableRef"
							:data="tableData"
							:header-cell-style="{
								background: '#F0F4FA',
								color: '#222222',
								'font-weight': 600,
							}"
							height="100%"
							border
							@select="handleSelect"
							@select-all="handleSelectAll"
						>
							<el-table-column type="selection" width="55"></el-table-column>
							<el-table-column
								v-for="item in columns"
								:key="item.key"
								:prop="item.key"
								:label="item.name"
								:show-overflow-tooltip="item.tooltip"
								:width="item.width"
								:min-width="item.minWidth || 150"
								:align="item.align || 'left'"
							></el-table-column>
						</el-table>
						<gc-empty v-show="!tableData.length"></gc-empty>
					</div>
					<GcPagination
						v-show="tableData.length"
						:pageSize="pageData.size"
						:total="pageData.total"
						:currentPage="pageData.current"
						@current-page-change="handlePageChange"
					></GcPagination>
				</div>
			</div>
		</div>

		<div class="button-group">
			<el-button type="primary" round :disabled="checkedTotal == 0" @click="handleBind">绑 定</el-button>
		</div>
	</div>
</template>

<script>
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getColumn } from './tableColumn'
import { getFormItems } from './formItem.js'
import { getAlleyMap, getStaffMap } from '@/api/meterReading.api.js'
import { apiGetDmaArchivesList, apiSaveSubArchivesRecord } from '@/api/meterManage.api.js'
export default {
	props: {
		dmaArchivesId: {
			type: [Number, String],
		},
	},
	data() {
		return {
			// 左侧查询
			formData: {
				orgCode: '',
				alleyId: '',
				bookNo: '',
				meterReadingStaffId: '',
				archivesIdentityStart: '',
				archivesIdentityEnd: '',
			},
			columns: getColumn(this),
			formItems: getFormItems(this),
			formAttrs: {
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
				},
			},
			// 右侧列表
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			selectData: [], // 全局选中的数据
			selectedRows: [], // 当前页选中的数据
			unSelectData: [], // 全局未选中的数据
			unSelectedRows: [], // 当前页未选中的数据
			checkAll: false,
			checkedTotal: 0,
		}
	},
	methods: {
		// 获取坊别
		async _getAlleyMap() {
			try {
				const res = await getAlleyMap({
					orgCode: this.formData.orgCode,
				})
				if (res) {
					this.formItems[1].options = res.map(item => {
						return {
							value: item.id,
							label: item.alleyName,
							...item,
						}
					})
				}
			} catch (error) {
				console.error(error)
				this.formItems[1].options = []
			}
		},
		// 获取抄表员工数据
		async _getStaffMapData() {
			try {
				const res = await getStaffMap({
					orgCode: this.formData.orgCode,
				})
				this.formItems[3].options = res.map(item => {
					const { staffId, staffName } = item
					return {
						value: staffId,
						label: staffName,
					}
				})
			} catch (error) {
				this.formItems[3].options = []
			}
		},
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
				})
				const { total = 0, records = [] } = await apiGetDmaArchivesList(formParams)
				this.pageData.total = total
				this.tableData = records
				// 选中全部  勾选复选框
				if (this.checkAll) {
					// 筛选出当前页未勾选数据
					const matchedData = this.unSelectData.filter(item =>
						this.tableData.some(t => t.archivesId === item.archivesId),
					)
					if (matchedData.length) {
						this.selectedRows = this.tableData.filter(
							item => !matchedData.some(m => m.archivesId == item.archivesId),
						)
					} else {
						this.selectedRows = this.tableData
					}
					this.addGlobalSelectData()
				}
				this.updateSelection()
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		clearData() {
			this.unSelectData = []
			this.unSelectedRows = []
			this.selectData = []
			this.selectedRows = []
			this.checkedTotal = 0
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.clearData()
			this.checkAll = false
			this.handlePageChange({ page: 1 })
		},
		// 点击选择全部或者取消全部
		handleClickAll() {
			this.checkAll = !this.checkAll
			if (this.checkAll) {
				this.selectedRows = this.tableData
				this.addGlobalSelectData()
				this.updateSelection()
				this.checkedTotal = this.pageData.total
			} else {
				this.clearData()
				this.$refs.tableRef.clearSelection()
			}
		},
		// 手动勾选全部
		handleSelectAll(selection) {
			// 选中
			if (selection.length) {
				// 之前已经选中过, 总数减去之前选中数目
				const pastArr = this.selectData.filter(i => selection.some(s => s.archivesId === i.archivesId))
				this.checkedTotal = this.checkedTotal - pastArr.length + this.tableData.length
				this.unSelectData = this.unSelectData.filter(i => !selection.some(s => s.archivesId === i.archivesId))
				this.selectedRows = selection
				this.addGlobalSelectData()
			} else {
				// 取消选中
				this.selectData = this.selectData.filter(i => !this.tableData.some(t => t.archivesId === i.archivesId))
				this.checkedTotal -= this.tableData.length
				this.unSelectedRows = this.tableData
				this.addGlobalUnSelectData()
			}
		},
		// 手动勾选单个
		handleSelect(selection, row) {
			const ifChecked = selection.find(item => item.archivesId === row.archivesId)
			if (ifChecked) {
				// 选中
				this.unSelectData = this.unSelectData.filter(item => item.archivesId !== row.archivesId)
				this.selectedRows = selection
				this.addGlobalSelectData()
				this.checkedTotal += 1
			} else {
				//取消选中
				this.selectData = this.selectData.filter(item => item.archivesId !== row.archivesId)
				this.unSelectedRows = [row]
				this.addGlobalUnSelectData()
				this.checkedTotal -= 1
			}
		},
		mergeAndDeduplicateArrays(array1, array2) {
			const mergedArray = [...array1, ...array2]
			return Array.from(new Map(mergedArray.map(item => [item.archivesId, item])).values())
		},
		addGlobalSelectData() {
			this.selectData = this.mergeAndDeduplicateArrays(this.selectData, this.selectedRows)
		},
		addGlobalUnSelectData() {
			this.unSelectData = this.mergeAndDeduplicateArrays(this.unSelectData, this.unSelectedRows)
		},
		// 更新选中状态
		updateSelection() {
			this.$refs.tableRef.clearSelection()
			this.selectData.forEach(item => {
				const row = this.tableData.find(row => row.archivesId === item.archivesId)
				if (row) {
					this.$nextTick(() => {
						this.$refs.tableRef.toggleRowSelection(row, true)
					})
				}
			})
		},
		// 绑定
		async handleBind() {
			const { total } = this.pageData
			const params = {}
			const archivesBookQueryReqVO = trimParams(removeNullParams(this.formData))
			Object.assign(archivesBookQueryReqVO, {
				dmaArchivesId: this.dmaArchivesId,
			})
			params['archivesBookQueryReqVO'] = archivesBookQueryReqVO
			if (this.checkAll) {
				params['total'] = total
				params['all'] = true
				if (this.checkedTotal !== this.pageData.total) {
					params['notArchivesIds'] = this.unSelectData.map(i => i.archivesId)
				}
			} else {
				params['archivesIds'] = this.selectData.map(i => i.archivesId)
			}

			if (this.checkedTotal > 50000) {
				this.$message.error('不能超过5万条')
				return
			}
			await apiSaveSubArchivesRecord(params)
			this.$message.success('绑定成功')
			this.handleSearch()
		},
	},
}
</script>

<style lang="scss" scoped>
.select-container {
	display: flex;
	flex-direction: column;
	height: 100%;
}
.page-layout {
	border: 1px solid #e7e9ee;
	.page-left {
		border-right: 1px solid #e7e9ee;
		border-radius: 0;
	}
	.table-choose {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}
	.table-wrapper {
		flex: 1;
		overflow: hidden;
		.table-container {
			height: calc(100% - 64px);
		}
		::v-deep .el-table--border {
			border: none;
		}
		::v-deep .table-container {
			.el-table tr {
				th:nth-child(1) {
					border-left: 1px solid rgba(170, 178, 193, 0.2);
				}
				td:nth-child(1) {
					border-left: 1px solid rgba(170, 178, 193, 0.2);
				}
				th:last-child {
					border-right: 2px solid rgba(170, 178, 193, 0.2);
				}
				td:last-child {
					border-right: 2px solid rgba(170, 178, 193, 0.2);
				}
			}
			.el-table td {
				border-right: 1px solid rgba(170, 178, 193, 0.2);
			}
			.el-table th {
				border-top: 1px solid rgba(170, 178, 193, 0.2);
				border-right: 1px solid rgba(170, 178, 193, 0.2);
				&.gutter {
					border: 1px solid transparent !important;
				}
			}
			.el-table.el-table--scrollable-y {
				.el-table__body-wrapper.is-scrolling-none {
					padding-right: 2px;
					tr {
						th:last-child {
							border-right: 1px solid rgba(170, 178, 193, 0.2);
						}
						td:last-child {
							border-right: 1px solid rgba(170, 178, 193, 0.2);
						}
					}
				}
			}
		}
	}
}
.button-group {
	// width: 280px;
	margin: 20px 0 0 0;
	display: flex;
	justify-content: flex-end;
}
</style>
