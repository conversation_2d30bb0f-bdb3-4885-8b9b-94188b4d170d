export function getFormItems(_this) {
	const arr = [
		{
			type: 'el-select',
			label: '营业分公司',
			prop: 'orgCode',
			options: _this.$store.getters.orgList,
			attrs: {
				clearable: false,
				placeholder: '请选择',
			},
		},
		{
			type: 'el-select',
			label: '区/县',
			prop: 'regionCode',
			options: [],
			events: {
				change: value => _this.handleChangeAddress(value, 'regionCode'),
			},
		},
		{
			type: 'el-select',
			label: '街道/乡镇',
			prop: 'streetCode',
			options: [],
			events: {
				change: value => _this.handleChangeAddress(value, 'streetCode'),
			},
		},
		{
			type: 'el-select',
			label: '小区/村庄',
			prop: 'areaCode',
			options: [],
			events: {
				change: value => _this.handleChangeAddress(value, 'areaCode'),
			},
		},
		{
			type: 'el-select',
			label: '楼栋',
			prop: 'buildingCode',
			options: [],
		},
		{
			type: 'el-input',
			label: '表卡编号',
			prop: 'archivesIdentity',
		},
		{
			type: 'el-select',
			label: '档案类型',
			prop: 'userType',
			options:
				_this.$store.getters?.dataList?.userType?.map(item => {
					return {
						label: item.sortName,
						value: item.sortValue,
					}
				}) || [],
		},
		{
			type: 'el-date-picker',
			label: '销卡日期',
			prop: 'cancelTime',
			attrs: {
				type: 'daterange',
				startPlaceholder: '开始日期',
				endPlaceholder: '结束日期',
				valueFormat: 'yyyy-MM-dd',
			},
		},
	]
	return arr
}
