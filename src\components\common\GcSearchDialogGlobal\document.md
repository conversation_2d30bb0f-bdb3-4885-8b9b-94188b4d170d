<search-dialog 
    :dialogVisible.sync="dialogVisible"  弹窗的显示/隐藏（必传-Boolean）
    :title="'搜索档案'"                   弹窗的header名（必传-String）
    :valueObj='valueObj'                 传值（Object）-主要用来做清空的联动
    :search-condition="searchCondition"  tab显示的搜索条件（必传-Array）
    数据格式：(表具地址的key必须使用‘/addressName/’ -样式特殊，便于判断)
    [
      {
        key:'archivesNo',
        label:'档案编号'
      },
      {
        key:'userName',
        label:'用户名称'
      },
      {
        key:'addressName',
        label:'表具地址'
      },
      {
        key:'meterNo',
        label:'表具编号'
      },
      {
        key:'userMobile',
        label:'手机号'
      },
    ],
    :active-condition.sync="activeCondition" 当前active的tab（必传 String-便于重置还原，初始传值为搜索条件的第一个） 
    数据格式：''archivesNo''
    :selected-condition="selectedCondition"  当前选中的其他筛选条件（非必传，默认为[]） 
    数据格式：
    selectedCondition:[
      {
        key:'archiveStatus',
        value:'已建档'
      },
      {
        key:'userType',
        value:'工商'
      }
    ],
    :filter-condition-visible="true"         是否显示其他筛选条件（非必传 Boolean，默认为true）-缴费不显示
    :page="page"                            页数，总条数，当前页数（非必传 Object，默认为{}）-存在menu-list的必传
    数据格式：（如果不想用这些key，需要同步修改组件中的取值）
    page:{
      total:20, //总条数
      current:1, //当前页数
      pages:10  //总页数
    },
    :menu-visible="false"                   是否在弹窗内展示菜单项（非必传 Boolean，默认为false）-缴费
    :menu-list.sync="menuList"              菜单list（非必传 Boolean，默认为[]）-缴费
    数据格式：-影响显示，需key与下列展示保持一致
    menuList:[
      {
        archivesId:'111111123',
        archivesNo:'JK233456789',
        userName:'测试测试用户名称',
        archivesStatus:0,
        userMobile:'12344879456',
        meterNo:'123456789120',
        addressName:'杭州市江干区金卡智能集团股份有限公司'
      },
      {
        archivesId:'111111123',
        archivesNo:'JK233456789',
        userName:'测试测试用户名称',
        archivesStatus:1,
        userMobile:'12344879456',
        meterNo:'123456789120',
        addressName:'杭州市江干区金卡智能集团股份有限公司'
      }
    ]
    @delete-one="deleteOne"                方法，筛选条件的单个删除
    @empty="deleteAll"                     方法，筛选条件的清空
    @public-search="searchList"            方法，点击搜索按钮后触发
    @dialog-close="dialogClose"            方法，关闭弹窗触发，主要为了不点击搜索，但是有值做值的回显
    @go-up="goUp"                          方法，上一页  -缴费
    @go-down="goDown"                      方法，下一页  -缴费
    @go-archives="goArchives"              方法，选中菜单中某一项档案 -缴费
></search-dialog>