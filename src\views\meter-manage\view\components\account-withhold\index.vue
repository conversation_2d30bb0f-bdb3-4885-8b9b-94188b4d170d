<template>
	<GcElDialog
		:show="show"
		title="设置邮储代扣信息"
		okText="确认"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
	</GcElDialog>
</template>

<script>
import { apiQueryCollectionBankList } from '@/api/userManage.api'
import { apiGetWithholdAccount, apiGetWithholdAccount2, apiSaveOrUpdateWithholdAccount } from '@/api/meterManage.api'
import { getFormItems } from './formItem.js'
import { ruleRequired, ruleMaxLength } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
export default {
	name: 'AccountWithhold',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		archivesId: {
			type: String,
			default: () => ({}),
		},
		meterNo: {
			type: String,
			default: '',
		},
		permissionCode: {
			type: String,
			default: 'cpm_archives_withholdAccount',
		},
	},
	computed: {
		formItems() {
			return getFormItems(this)
		},
	},
	watch: {
		show: {
			handler(val) {
				console.log(
					'watch show:',
					val,
					', archivesId=',
					this.archivesId,
					', meterNo=',
					this.meterNo,
					', permissionCode=',
					this.permissionCode,
				)
				if (val) {
					this._apiGetAccountName()
					this._apiGetWithholdAccount()
				}
			},
		},
		meterNo: {
			handler(val) {
				this.formData.meterNo = val
			},
		},
	},
	data() {
		return {
			bankOptions: [],
			formData: {
				accountId: '',
				archivesId: '',
				meterNo: '',
				bankCode: '',
				accountName: '',
				accountAddr: '',
				accountNo: '',
				accountBalanceAmount: '',
				remark: '',
				isWithhold: false,
			},
			formAttrs: {
				rules: {
					meterNo: [ruleRequired('必填'), ruleMaxLength(32)],
					bankCode: [ruleRequired('必填')],
					remark: [ruleMaxLength(255)],
					accountNo: [ruleRequired('必填'), ruleMaxLength(64)],
					accountAddr: [ruleMaxLength(64)],
				},
			},
		}
	},
	methods: {
		async _apiGetAccountName() {
			const data = await apiQueryCollectionBankList()
			this.bankOptions = data.map(item => {
				return {
					label: item.bankName,
					value: item.bankCode,
				}
			})
		},
		async _apiGetWithholdAccount() {
			const apiMethods = {
				cpm_archives_withholdAccount: apiGetWithholdAccount,
				cpm_archives_withholdAccount2: apiGetWithholdAccount2,
			}
			const data = await apiMethods[this.permissionCode]({ archivesId: this.archivesId })
			Object.assign(this.formData, data)
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			let formObj = trimParams(removeNullParams(this.formData))
			await apiSaveOrUpdateWithholdAccount(formObj)
			this.$message.success('修改成功')
			this.handleClose()
			this.$emit('refresh')
		},
	},
}
</script>
