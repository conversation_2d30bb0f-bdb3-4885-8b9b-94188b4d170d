<template>
	<GcElDialog
		:show="isShow"
		title="设置邮储代扣信息"
		okText="确认"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
	</GcElDialog>
</template>

<script>
import { apiQueryCollectionBankList } from '@/api/userManage.api'
import { apiGetWithholdAccount, apiGetWithholdAccount2, apiSaveOrUpdateWithholdAccount } from '@/api/meterManage.api'
import { getFormItems } from './formItem.js'
import { ruleRequired, ruleMaxLength } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
export default {
	name: 'AccountWithhold',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		archivesId: {
			type: String,
			default: () => ({}),
		},
		archivesIdentity: {
			type: String,
			default: '',
		},
		permissionCode: {
			type: String,
			default: 'cpm_archives_withholdAccount',
		},
	},
	computed: {
		formItems() {
			return getFormItems(this)
		},
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	watch: {
		show: {
			handler(val) {
				if (val) {
					this._apiGetAccountName()
					this._apiGetWithholdAccount()
					this.formData.archivesId = this.archivesId
				}
			},
			immediate: true,
		},
		archivesIdentity: {
			handler(val) {
				this.formData.archivesIdentity = val
			},
			immediate: true,
		},
	},
	data() {
		return {
			bankOptions: [],
			formData: {
				accountId: '',
				archivesId: '',
				archivesIdentity: '',
				bankCode: '',
				accountName: '',
				accountAddr: '',
				accountNo: '',
				accountBalanceAmount: '',
				remark: '',
				isWithhold: false,
			},
			formAttrs: {
				rules: {
					archivesIdentity: [ruleRequired('必填'), ruleMaxLength(32)],
					bankCode: [ruleRequired('必填')],
					remark: [ruleMaxLength(255)],
					accountNo: [ruleRequired('必填'), ruleMaxLength(64)],
					accountAddr: [ruleMaxLength(64)],
				},
			},
		}
	},
	methods: {
		async _apiGetAccountName() {
			const data = await apiQueryCollectionBankList()
			this.bankOptions = data.map(item => {
				return {
					label: item.bankName,
					value: item.bankCode,
				}
			})
		},
		async _apiGetWithholdAccount() {
			const apiMethods = {
				cpm_archives_withholdAccount: apiGetWithholdAccount,
				cpm_archives_withholdAccount2: apiGetWithholdAccount2,
			}
			const data = await apiMethods[this.permissionCode]({ archivesId: this.archivesId })
			// 只将formData中已有的字段从data中获取对应的值填充
			Object.keys(this.formData).forEach(key => {
				if (Object.prototype.hasOwnProperty.call(data, key)) {
					this.formData[key] = data[key]
				}
			})
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			let formObj = trimParams(removeNullParams(this.formData))
			delete formObj.archivesIdentity
			delete formObj.accountBalanceAmount
			await apiSaveOrUpdateWithholdAccount(formObj)
			this.$message.success('修改成功')
			this.handleClose()
			this.$emit('refresh')
		},
	},
}
</script>
