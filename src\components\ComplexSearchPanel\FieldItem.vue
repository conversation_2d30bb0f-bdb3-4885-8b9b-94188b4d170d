<template>
	<div class="complex-field-item">
		<div class="field-select">
			<el-select v-model="config.key" placeholder="请选择" :clearable="true" :filterable="true">
				<el-option
					v-for="field in fields"
					:key="field.key"
					:label="field.label"
					:value="field.key"
					:disabled="field.disabled"
				></el-option>
			</el-select>
		</div>
		<div class="operator-select">
			<el-select v-model="config.operator" placeholder="请选择">
				<el-option
					v-for="operator in operators"
					:key="operator.value"
					:label="operator.label"
					:value="operator.value"
				></el-option>
			</el-select>
		</div>
		<div class="value-input">
			<InputItem :field="fieldInput" v-model="config.value" />
		</div>
		<el-button-group class="item-actions">
			<el-button icon="el-icon-plus" @click="$emit('add-item')"></el-button>
			<el-button icon="el-icon-minus" @click="$emit('remove-item')"></el-button>
		</el-button-group>
	</div>
</template>
<script>
import InputItem from './InputItem.vue'

const OPERATOR_MAP = {
	'=': { label: '=', value: '=' },
	'!=': { label: '!=', value: '!=' },
	'>': { label: '>', value: '>' },
	'>=': { label: '>=', value: '>=' },
	'<': { label: '<', value: '<' },
	'<=': { label: '<=', value: '<=' },
	in: { label: 'in', value: 'in' },
	'not in': { label: 'not in', value: 'not in' },
	like: { label: '包含', value: 'like' },
	'not like': { label: '不包含', value: 'not like' },
	'is NULL': { label: 'is NULL', value: 'is NULL' },
	'is not NULL': { label: 'is not NULL', value: 'is not NULL' },
}

export default {
	name: 'complex-field-item',
	components: { InputItem },
	props: {
		fields: {
			type: Array,
			default: () => [],
		},
		value: {
			type: Object,
			default: () => {
				return {
					field: '',
					operator: '',
					value: '',
				}
			},
		},
	},
	data() {
		this.dOperator = ['=', '!=', '>', '>=', '<', '<=', 'in', 'not in', 'like', 'not like', 'is NULL', 'is not NULL']
		this.dInput = { type: 'el-input' }
		return {
			config: this.value,
		}
	},
	computed: {
		currentFieldConfig() {
			const key = this.config.key
			return key ? this.fields.find(field => field.key === key) || null : null
		},
		operators() {
			const field = this.currentFieldConfig
			const operators = field ? field.operator || this.dOperator : this.dOperator
			return operators.map(key => {
				const operator = OPERATOR_MAP[key]

				return operator ? operator : { label: key, value: key }
			})
		},
		fieldInput() {
			const field = this.currentFieldConfig
			return field ? field.input || this.dInput : this.dInput
		},
	},
	watch: {
		value(val) {
			this.config = val
		},
		config: {
			handler: function (val) {
				if (this.value === val) return
				this.$emit('input', { ...val })
			},
			deep: true,
		},
		'config.key'() {
			this.config.operator = ''
			this.config.value = ''
		},
	},
}
</script>

<style lang="scss" scoped>
.complex-field-item {
	display: flex;
	gap: 10px;
	.operator-select {
		display: inline-flex;
		flex-grow: 0;
		width: 100px;
	}
	.field-select {
		display: inline-flex;
		flex-basis: 130px;
		flex-grow: 0;
	}
	.value-input {
		flex-basis: 0;
		flex-grow: 3;
	}
	.field-select,
	.complex-input-item {
		display: flex;
		::v-deep {
			& > div {
				width: 100%;
			}
		}
	}
	.el-button {
		padding: 9px 10px;
	}
}
</style>
