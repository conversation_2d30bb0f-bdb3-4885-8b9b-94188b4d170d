import { getLodop } from '../lodop/LodopFuncs'

export const collectionVoucherBatchPrint = async recordsList => {
	try {
		generateCollectionVoucherTemplate(recordsList)
	} catch (error) {
		console.error(error)
	}
}

export const generateCollectionVoucherTemplate = (data, type = 'preview') => {
	const LODOP = getLodop()

	LODOP.PRINT_INIT('同城特约委托收款')
	LODOP.SET_PRINT_MODE('POS_BASEON_PAPER', true) //设置以纸张边缘为基点
	LODOP.SET_PRINT_PAGESIZE(1, '192mm', '102mm', '')

	data.forEach((item, index) => {
		// 使用负边距来消除打印边距，调整图片位置和尺寸以填满整个页面
		LODOP.ADD_PRINT_IMAGE(0, 0, '192mm', '102mm', `${window.location.origin}/print-template/fp.jpg`)
		LODOP.SET_PRINT_STYLEA(0, 'Stretch', 2)
		console.log(index, item)
		LODOP.ADD_PRINT_TEXT(0, 0, '180mm', '20mm', '啊啊发发发发啊发发发撒发发发阿福')

		if (index !== data.length - 1) {
			LODOP.NEWPAGE()
		}
	})

	if (type === 'preview') {
		LODOP.PREVIEW()
	} else {
		LODOP.PRINT()
	}
}
