import { getLodop } from '../lodop/LodopFuncs'

export const collectionVoucherBatchPrint = async recordsList => {
	try {
		generateCollectionVoucherTemplate(recordsList)
	} catch (error) {
		console.error(error)
	}
}

export const generateCollectionVoucherTemplate = (data, type = 'preview') => {
	const LODOP = getLodop()

	LODOP.PRINT_INIT('同城特约委托收款')
	LODOP.SET_PRINT_MODE('PROGRAM_CONTENT_BYVAR', true)
	LODOP.SET_PRINT_PAGESIZE(1, '192mm', '102mm', '')
	LODOP.SET_PRINT_STYLE('FontSize', 12)
	LODOP.SET_PRINT_STYLE('FontColor', '#808080')

	data.forEach((item, index) => {
		LODOP.ADD_PRINT_SETUP_BKIMG(`<img border='0' src='${window.location.origin}/print-template/fp.jpg'>`)
		LODOP.SET_SHOW_MODE('BKIMG_WIDTH', 1920)
		LODOP.SET_SHOW_MODE('BKIMG_HEIGHT', 1020)

		// 预览、打印时包含背景图片
		LODOP.SET_SHOW_MODE('BKIMG_IN_PREVIEW', 1)
		LODOP.SET_SHOW_MODE('BKIMG_PRINT', true)

		console.log(index, item)

		if (index !== data.length - 1) {
			LODOP.NEWPAGE()
		}
	})

	if (type === 'preview') {
		LODOP.PREVIEW()
	} else {
		LODOP.PRINT()
	}
}
