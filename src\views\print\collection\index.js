import { getLodop } from '../lodop/LodopFuncs'

export const collectionVoucherBatchPrint = async recordsList => {
	try {
		generateCollectionVoucherTemplate(recordsList)
	} catch (error) {
		console.error(error)
	}
}

export const generateCollectionVoucherTemplate = (data, type = 'preview') => {
	const LODOP = getLodop()

	LODOP.PRINT_INIT('同城特约委托收款')
	LODOP.SET_PRINT_MODE('POS_BASEON_PAPER', true) //设置以纸张边缘为基点 按测量的物理尺寸定位
	LODOP.SET_PRINT_PAGESIZE(1, '192mm', '102mm', '')

	data.forEach((item, index) => {
		//以下居中对齐
		LODOP.SET_PRINT_STYLE('Alignment', 2) // 对齐方式 1左 2居中 3右
		//年月日
		LODOP.ADD_PRINT_TEXT('13mm', '72mm', '18mm', '4mm', '2025')
		LODOP.ADD_PRINT_TEXT('13mm', '90mm', '10mm', '4mm', '12')
		LODOP.ADD_PRINT_TEXT('13mm', '102mm', '10mm', '4mm', '30')
		//委托金额 小写 千百十万千百十元角分
		LODOP.ADD_PRINT_TEXT('50mm', '120mm', '5mm', '9mm', '9')
		LODOP.ADD_PRINT_TEXT('50mm', '125mm', '5mm', '9mm', '9')
		LODOP.ADD_PRINT_TEXT('50mm', '130mm', '5mm', '9mm', '9')
		LODOP.ADD_PRINT_TEXT('50mm', '135mm', '5mm', '9mm', '9')
		LODOP.ADD_PRINT_TEXT('50mm', '140mm', '5mm', '9mm', '9')
		LODOP.ADD_PRINT_TEXT('50mm', '145mm', '5mm', '9mm', '9')
		LODOP.ADD_PRINT_TEXT('50mm', '150mm', '5mm', '9mm', '9')
		LODOP.ADD_PRINT_TEXT('50mm', '155mm', '5mm', '9mm', '9')
		LODOP.ADD_PRINT_TEXT('50mm', '160mm', '5mm', '9mm', '9')
		LODOP.ADD_PRINT_TEXT('50mm', '165mm', '5mm', '9mm', '9')
		//款项内容
		LODOP.ADD_PRINT_TEXT('60mm', '29mm', '42mm', '10mm', '水费+污水费+罚款')
		//单证张数
		LODOP.ADD_PRINT_TEXT('60mm', '91mm', '24mm', '10mm', '100')
		//合同名称或号码
		LODOP.ADD_PRINT_TEXT('60mm', '140mm', '30mm', '10mm', '22200210000000069999')
		//以下左对齐
		LODOP.SET_PRINT_STYLE('Alignment', 1)
		//付款人全称
		LODOP.ADD_PRINT_TEXT('17mm', '43mm', '47mm', '9mm', '大连三叶食品有限公司')
		//付款人账号
		LODOP.ADD_PRINT_TEXT('26mm', '43mm', '47mm', '10mm', '22200210000000069999')
		//付款人开户行
		LODOP.ADD_PRINT_TEXT('36mm', '43mm', '47mm', '9mm', '中国银行大连市分行')
		//收款人全称
		LODOP.ADD_PRINT_TEXT('17mm', '115mm', '55mm', '10mm', '大连市自来水集团有限公司')
		//收款人账号
		LODOP.ADD_PRINT_TEXT('26mm', '115mm', '55mm', '10mm', '22200210000000069999')
		//收款人开户行名称
		LODOP.ADD_PRINT_TEXT('36mm', '115mm', '55mm', '10mm', '中国工商银行大连甘井子支行营业部')
		//委收金额 大写
		LODOP.ADD_PRINT_TEXT('46mm', '43mm', '77mm', '14mm', '玖仟捌佰柒拾陆万伍仟肆佰叁拾贰元壹分')
		LODOP.SET_PRINT_STYLEA(0, 'Bold', 1) // 加粗
		LODOP.SET_PRINT_STYLEA(0, 'FontSize', 10) // 字号
		if (index !== data.length - 1) {
			LODOP.NEWPAGE()
		}
	})

	if (type === 'preview') {
		LODOP.PREVIEW()
	} else {
		LODOP.PRINT()
	}
}
