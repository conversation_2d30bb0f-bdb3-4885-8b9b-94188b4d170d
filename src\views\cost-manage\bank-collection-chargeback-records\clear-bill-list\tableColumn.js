export function getColumn() {
	return [
		{
			key: 'billDate',
			name: '账期',
			tooltip: true,
			width: '100px',
		},
		{
			key: 'billNo',
			name: '账单编号',
			tooltip: true,
			width: '200px',
		},
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'userName',
			name: '用户',
			tooltip: true,
		},
		{
			key: 'addressFullName',
			name: '地址',
			minWidth: 250,
			tooltip: true,
		},
		{
			key: 'priceCode',
			name: '价格编号',
			tooltip: true,
		},
		{
			key: 'useAmt',
			name: '水费(元)',
			tooltip: true,
			align: 'right',
			render: function (h, row) {
				const amount = row.useAmt - 0
				return h('span', {}, amount.toFixed(2))
			},
		},
		{
			key: 'billItemAmt',
			name: '污水费(元)',
			tooltip: true,
			align: 'right',
			render: function (h, row) {
				const amount = row.billItemAmt - 0
				return h('span', {}, amount.toFixed(2))
			},
		},
		{
			key: 'receivableAmount',
			name: '应缴金额(元)',
			tooltip: true,
			align: 'right',
			render: function (h, row) {
				const amount = row.receivableAmount - 0
				return h('span', {}, amount.toFixed(2))
			},
		},
		{
			key: 'pushTime',
			name: '锁定时间',
			tooltip: true,
			width: '170px',
		},
		{
			key: 'fileDay',
			name: '对账时间',
			tooltip: true,
			width: '170px',
		},
		{
			key: 'successAmount',
			name: '对账金额(元)',
			tooltip: true,
			align: 'right',
			render: function (h, row) {
				const amount = row.successAmount - 0
				return h('span', {}, amount.toFixed(2))
			},
		},
	]
}
