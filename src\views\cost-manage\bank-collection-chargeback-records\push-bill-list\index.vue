<template>
	<div class="wrapper">
		<div class="bill-total">
			<span>账单笔数：{{ billSum.count }} 笔</span>
			<span>总金额：{{ billSum.amount }} 元</span>
		</div>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				needType="selection"
				@selectChange="selectChange"
				@current-page-change="handleChangePage"
			/>
		</div>
	</div>
</template>

<script>
import { getColumn } from './tableColumn.js'
export default {
	props: {
		loading: {
			type: Boolean,
			default: false,
		},
		tableData: {
			type: Array,
			default: () => [],
		},
		billSum: {
			type: Object,
			default: () => ({
				count: 0,
				amount: 0,
			}),
		},
		billClosable: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			columns: getColumn(),
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	methods: {
		selectable(row) {
			return this.billClosable && [1, 3].includes(row.status) && [2, 5].includes(row.billStatus)
		},
		pageDataReset() {
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.$emit('page-change', this.pageData)
		},
		selectChange(arr) {
			this.$emit('select-change', arr)
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	flex: 1;
	height: calc(100% - 109px);
	display: flex;
	flex-direction: column;
	padding: 0 20px 10px;
}
.table-container {
	flex: 1;
	height: 0;
}
.bill-total {
	margin-bottom: 10px;
	display: flex;
	gap: 20px;
}
</style>
