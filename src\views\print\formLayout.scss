.form-layout {
	display: flex;
	flex-wrap: wrap;
	.adjust-length {
		width: 100% !important;
		::v-deep {
			.el-input__inner {
				width: 100% !important;
			}
			.el-date-editor.el-input,
			.el-date-editor.el-input__inner {
				width: 100% !important;
			}
			.el-select {
				width: 100%;
			}
		}
	}
	.adjust-distance {
		margin-bottom: 18px;
	}
	.devide {
		border-bottom: 1px dashed #cccccc;
		margin-bottom: $base-margin;
	}
	.title {
		padding-bottom: 18px;
		font-size: 14px;
		color: #ababab;
	}
	::v-deep {
		.el-form-item {
			width: 33.33%;
			display: flex;
			flex-direction: column;
			.el-form-item__label {
				text-align: left;
				position: relative;
				color: $base-color-2;
				font-weight: 600;
			}
			.el-input__inner {
				width: 260px;
			}
			.el-textarea__inner {
				resize: none;
				min-height: 96px !important;
			}
			.el-input__count {
				background: rgba(255, 255, 255, 0);
				bottom: 0;
			}
		}
		.el-form-item.company {
			.el-input__suffix {
				left: 200px;
				i {
					font-size: $base-font-size-default;
					color: #aab2c1;
				}
			}
		}
		.el-form-item.limit-length {
			.el-input__suffix {
				right: 25px;
			}
		}
	}
}
