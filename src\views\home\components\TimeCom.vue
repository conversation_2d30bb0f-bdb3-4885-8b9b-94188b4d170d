<template>
	<div class="time">
		{{ time }}
		<span>{{ weekDay }}</span>
	</div>
</template>

<script>
export default {
	name: 'TimeCom',
	data() {
		return {
			time: this.dayjs().format('HH:mm:ss'),
			weekDay: '',
		}
	},
	mounted() {
		//创建定时器更新最新的时间
		var _this = this
		this.timeId = setInterval(function () {
			_this.time = _this.dayjs().format('HH:mm:ss')
		}, 1000)
		//  获取星期信息
		this.getweekDay()
	},
	methods: {
		getweekDay() {
			let w = this.dayjs().day()
			let zh_w = ['日', '一', '二', '三', '四', '五', '六']
			this.weekDay = `星期${zh_w[w]}`
		},
	},
	beforeDestroy: function () {
		//实例销毁前青出于定时器
		if (this.timeId) {
			clearInterval(this.timeId)
		}
	},
}
</script>
<style lang="scss" scoped>
.time {
	padding: 20px 0 0 20px;
	margin-bottom: 10px;
	font-size: 20px;
	color: #595959;
	font-weight: 600;
	span {
		margin-left: 10px;
	}
}
</style>
