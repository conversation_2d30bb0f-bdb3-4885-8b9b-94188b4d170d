<template>
	<div class="step-two-wrapper">
		<GcFormSimple ref="formRef" v-model="form" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleChangePage({ page: 1 })">查询</el-button>
			</el-form-item>
		</GcFormSimple>
		<div class="table-container">
			<GcTable
				style="height: 400px"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@click="handleRowClick"
				@current-page-change="handleChangePage"
			/>
		</div>
	</div>
</template>

<script>
import { bookTypeOptions } from '@/consts/optionList'
import { ruleRequired } from '@/utils/rules'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getAlleyMap, getBookList } from '@/api/meterReading.api.js'
export default {
	name: '',
	data() {
		return {
			form: {
				orgCode: '',
				bookType: 2,
				alleyId: '',
				bookNo: '',
			},
			formAttrs: {
				inline: true,
				labelWidth: '80px',
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
				},
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业所',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						style: {
							width: '150px',
						},
					},
					events: {
						change: this._getAlleyMap,
					},
				},
				{
					type: 'el-select',
					label: '表册类型',
					prop: 'bookType',
					options: bookTypeOptions,
					attrs: {
						style: {
							width: '150px',
						},
						disabled: true,
					},
				},
				{
					type: 'el-select',
					label: '坊别',
					prop: 'alleyId',
					options: [],
					attrs: {
						style: {
							width: '150px',
						},
					},
				},
				{
					type: 'el-input',
					label: '表册编号',
					prop: 'bookNo',
					attrs: {
						style: {
							width: '150px',
						},
					},
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			currentBookInfo: {},
		}
	},
	computed: {
		columns() {
			return [
				{
					width: 50,
					key: 'bookId',
					name: '',
					render: (h, row) => {
						return h(
							'el-radio',
							{
								props: {
									value: this.currentBookInfo.bookId,
									label: row.bookId,
								},
								on: {
									input: value => {
										this.currentBookInfo.bookId = value ? row.bookId : ''
									},
								},
							},
							'',
						)
					},
				},
				{
					key: 'bookNo',
					name: '表册编号',
					tooltip: true,
				},
				{
					key: 'archivesNoRange',
					name: '抄表范围',
					tooltip: true,
				},
				{
					key: 'meterReadingStaffName',
					name: '抄表员',
					tooltip: true,
				},
			]
		},
	},
	methods: {
		// 获取坊别
		_getAlleyMap() {
			const alleyObj = this.formItems.find(item => item.prop === 'alleyId')
			if (!this.form.orgCode) {
				this.form.alleyId = ''
				alleyObj.options = []
				return
			}
			getAlleyMap({
				orgCode: this.form.orgCode,
			}).then(data => {
				if (alleyObj) {
					alleyObj.options = data.map(item => {
						return {
							value: item.id,
							label: item.alleyName,
							...item,
						}
					})
				}
			})
		},
		// 获取册本
		async _getBookList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.form))
				Object.assign(formParams, { current, size })
				const { records, total } = await getBookList(formParams)

				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.handleSearch()
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			await this._getBookList()
		},
		handleRowClick({ row, event }) {
			if (event.target.tagName.toLowerCase() === 'input' && event.target.type === 'radio') {
				return
			}
			this.currentBookInfo = row
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.currentBookInfo = {}
		},
	},
	mounted() {},
}
</script>

<style lang="scss" scoped>
.step-two-wrapper {
	flex: 1;
	width: 1200px;
}
.el-form {
	text-align: center;
}
</style>
