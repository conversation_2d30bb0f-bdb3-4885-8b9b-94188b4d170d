<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>

			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:deal="{ row }">
					<el-button
						v-has="'meterReadingBook_record_preview'"
						type="text"
						size="medium"
						@click="handleCheck(row)"
					>
						查看
					</el-button>
					<el-button
						v-has="'meterReadingBook_transfer_export_excel'"
						type="text"
						size="medium"
						@click="handleExport(row)"
					>
						导出移交清单
					</el-button>
				</template>
			</GcTable>
		</div>

		<!-- 查看弹窗 -->
		<CheckDialog ref="checkDialogRef" :show.sync="showCheck" :data="currentRowData" />
	</div>
</template>

<script>
import { getColumn } from './tableColumn'
import { exportBlob } from '@/utils/index.js'
import { getAlleyMap, queryTransferRecordPage, meterCardTransferExport } from '@/api/meterReading.api.js'
import CheckDialog from './components/CheckDialog.vue'

export default {
	name: '',
	components: { CheckDialog },
	data() {
		return {
			// 左侧查询
			formData: {
				orgCode: '',
				alleyId: '',
				bookNo: '',
				handEndRange: [],
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						col: 24,
						clearable: false,
						placeholder: '请选择营业分公司',
					},
					events: {
						change: () => {
							this.formData.alleyId = ''
							this.getAlleyMapData()
						},
					},
				},
				{
					type: 'el-select',
					label: '坊别',
					prop: 'alleyId',
					options: [],
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择坊别',
					},
				},
				{
					type: 'el-input',
					label: '表册编号',
					prop: 'bookNo',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入表册编号',
					},
				},
				{
					type: 'el-date-picker',
					label: '移交时间',
					prop: 'handEndRange',
					attrs: {
						col: 24,
						type: 'daterange',
						defaultTime: ['00:00:00', '23:59:59'],
						valueFormat: 'yyyy-MM-dd HH:mm:ss',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
					},
				},
			],
			formAttrs: {
				rules: {
					orgCode: {
						required: true,
						message: '请选择营业分公司',
						trigger: 'change',
					},
				},
			},
			// 右侧列表
			loading: false,
			columns: getColumn(this),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 查看弹窗
			showCheck: false,
			currentRowData: {},
		}
	},
	computed: {
		orgOptions() {
			return this.formItems[0].options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.getAlleyMapData()
					this.$nextTick(() => {
						this.handleSearch()
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		// 获取坊别数据
		async getAlleyMapData() {
			try {
				const res = await getAlleyMap({
					orgCode: this.formData.orgCode,
				})
				if (res) {
					this.formItems[1].options = res.map(item => {
						return {
							value: item.id,
							label: item.alleyName,
						}
					})
				}
			} catch (error) {
				console.error(error)
				this.formItems[1].options = []
			}
		},

		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetFormSmooth()
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { handEndRange = [], ...rest } = this.formData
				const { total = 0, records = [] } = await queryTransferRecordPage({
					size,
					current,
					...rest,
					handStartTime: handEndRange?.length ? handEndRange[0] : '',
					handEndTime: handEndRange?.length ? handEndRange[1] : '',
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 查看
		handleCheck(data) {
			this.currentRowData = data
			this.showCheck = true
		},
		// 导出
		async handleExport(data) {
			try {
				const res = await meterCardTransferExport({
					recordId: data.recordId,
					orgCodeName: data.orgCodeNames,
					bookType: data.bookType,
				})
				exportBlob(res, '移交清单', 'xlsx')
			} catch (error) {
				const reader = new FileReader()
				reader.onload = () => {
					const response = JSON.parse(reader.result)
					this.$message.error(response.message)
				}
				reader.readAsText(error.response.data)
			}
		},
	},
}
</script>

<style lang="scss" scoped></style>
