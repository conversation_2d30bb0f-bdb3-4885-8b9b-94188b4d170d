<template>
	<GcElDialog
		:show="isShow"
		title="修改开票信息"
		width="500px"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
	</GcElDialog>
</template>

<script>
import { ruleMaxLength, RULE_INT_ENGLISH, RULE_INTEGERONLY } from '@/utils/rules'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getFormItem } from './form.js'
import { apiModifyUserInvoiceInfo } from '@/api/userManage.api.js'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		data: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	watch: {
		isShow: {
			async handler(val) {
				if (val) {
					this.formItems = getFormItem(this)
					this.assignForm(this.data?.user || {})
				}
			},
			immediate: true,
		},
	},
	data() {
		return {
			formData: { invoiceType: '', taxpayerIdentity: '', openBank: '', bankAccount: '' },
			formItems: [],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					openBank: [ruleMaxLength(32)],
					taxpayerIdentity: [RULE_INT_ENGLISH, ruleMaxLength(20)],
					bankAccount: [RULE_INTEGERONLY, ruleMaxLength(32)],
				},
			},
		}
	},
	methods: {
		assignForm(obj) {
			const keys = Object.keys(this.formData)

			keys.forEach(key => {
				if (obj && Object.prototype.hasOwnProperty.call(obj, key)) {
					this.formData[key] = obj[key] ? obj[key] + '' : obj[key]
				}
			})
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return

			const userVO = trimParams(removeNullParams(this.formData))
			Object.assign(userVO, {
				userId: this.data?.user?.userId,
				userType: this.data?.user?.userType,
				userName: this.data?.user?.userName,
			})
			await apiModifyUserInvoiceInfo({ userVO })
			this.$message.success('修改成功')
			this.$emit('success')
			this.isShow = false
		},
		handleClose() {
			this.$refs.formRef.resetForm()
			this.$refs.formRef.clearValidate()
			this.isShow = false
		},
	},
}
</script>
<style lang="scss" scoped>
::v-deep {
	.el-dialog__body {
		max-height: 600px !important;
	}
}
</style>
