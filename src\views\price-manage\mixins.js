import { mapState } from 'vuex'
import getFieldName from '@/mixin/getFieldName.js'
export default {
	mixins: [getFieldName],
	data() {
		return {
			gasNatureMap: {}, // 用气性质map映射关系
			billingTypeMap: {}, // 计费类型map映射关系
			timeUnitMap: {}, // 时间单位对应映射关系
			allowDefineCycSurplus:
				this.$ls.get('Tenant') && this.$ls.get('Tenant').business_config
					? this.$ls.get('Tenant').business_config.is_allow_define_cyc_surplus
					: false, // 是否重置余量
			isAllowMonthAvg:
				this.$ls.get('Tenant') && this.$ls.get('Tenant').business_config
					? this.$ls.get('Tenant').business_config.is_allow_month_avg
					: false, // 是否按月均摊
			isAllowPopulationLadder:
				this.$ls.get('Tenant') && this.$ls.get('Tenant').business_config
					? this.$ls.get('Tenant').business_config.is_allow_population_ladder
					: false, // 是否开启人口阶梯
			pricedata: {
				row: 5,
				list: [],
			},
			levelTableData: [],
			timeTableData: [],
			versionTableData: [],
			pagination: {
				pageNo: 1,
				pageSize: 10,
				total: 0,
			},
		}
	},

	computed: {
		...mapState({
			gasNatureOptions: state => state.dataList.dataList.priceNature,
			billingTypeOptions: state => state.dataList.dataList.billingType,
			timeUnitOptions: state => state.dataList.dataList.timeUnitCode,
			realm: state => state.user.userInfo?.realm || 'gas', // 业务领域 gas-燃气 water-水务
		}),
	},

	created() {
		// this.getGasNatureMap();
		this.getBillingTypeMap()
		this.getTimeUnitMap()
	},

	methods: {
		/* 获取用气性质映射表 */
		getGasNatureMap() {
			this.gasNatureMap = this.gasNatureOptions.reduce((prev, cur) => {
				prev[cur.sortValue] = cur.sortName
				return prev
			}, {})
		},

		/* 获取计费类型map映射关系 */
		getBillingTypeMap() {
			this.billingTypeMap = this.billingTypeOptions.reduce((prev, cur) => {
				prev[cur.sortValue] = cur.sortName
				return prev
			}, {})
		},

		/* 获取时间单位map映射关系 */
		getTimeUnitMap() {
			this.timeUnitMap = this.timeUnitOptions.reduce((prev, cur) => {
				prev[cur.sortValue] = cur.sortName
				return prev
			}, {})
		},

		/* 根据当前选中价格信息整理价格展示详情列表数据 */
		packagePriceInfoFomat() {
			const currentPriceInfo = this.currentPriceInfo
			this.pricedata.list = [
				{ key: '价格版本', value: currentPriceInfo.priceVersion, show: true },
				{
					key: '状态',
					value: currentPriceInfo.statusMask,
					slot: 'status',
					show: true,
				},
				{
					key: `${this.fieldName.baseName}性质`,
					value: currentPriceInfo.natureName,
					show: true,
				},
				{
					key: '计费类型',
					value: this.billingTypeMap[currentPriceInfo.billingTypeId],
					show: true,
				},
				{
					key: '单一价格',
					value: `${currentPriceInfo.singlePrice || '--'}元/${this.fieldName.baseUnit}`,
					show: currentPriceInfo.billingTypeId === 1 || currentPriceInfo.billingTypeId === 3,
				},
				{
					key: '时间单位',
					value: this.timeUnitMap[currentPriceInfo.timeUnitCode],
					show: currentPriceInfo.billingTypeId === 3,
				},
				{ key: '生效日期', value: currentPriceInfo.effectiveTime, show: true },
				{
					key: '周期开始日期',
					value: currentPriceInfo.cycleStartTime ? currentPriceInfo.cycleStartTime.split(' ')[0] : '',
					show: currentPriceInfo.billingTypeId === 2,
				},
				{
					key: '计费周期 / 月',
					value: currentPriceInfo.billingCycle,
					show: currentPriceInfo.billingTypeId === 2,
				},
				{
					key: '人口基数',
					value: `${currentPriceInfo.populationBase || 0}人`,
					show: currentPriceInfo.billingTypeId === 2 && currentPriceInfo.ladderPopulation === 1,
				},
				{
					key: '人口递增值',
					value: `${currentPriceInfo.populationIncrease || 0} ${this.fieldName.baseUnit}/人`,
					show: currentPriceInfo.billingTypeId === 2 && currentPriceInfo.ladderPopulation === 1,
				},
				{
					key: '是否人口阶梯',
					value:
						currentPriceInfo.ladderPopulation === 1
							? '是'
							: currentPriceInfo.ladderPopulation === 0
							? '否'
							: '',
					show: currentPriceInfo.billingTypeId === 2 && this.isAllowPopulationLadder,
				},
				{
					key: '是否按月均摊',
					value: currentPriceInfo.adjustLadder === 1 ? '是' : currentPriceInfo.adjustLadder === 0 ? '否' : '',
					show: currentPriceInfo.billingTypeId === 2 && this.isAllowMonthAvg,
				},
				{
					key: '是否重置余量',
					value: currentPriceInfo.resetSurplus === 0 ? '是' : '否',
					show:
						currentPriceInfo.billingTypeId === 2 &&
						currentPriceInfo.priceVersion !== 1 &&
						this.allowDefineCycSurplus,
				},
			].filter(o => o.show)
		},
	},
}
