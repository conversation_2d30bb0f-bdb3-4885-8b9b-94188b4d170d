<template>
	<div class="alarm-list-box">
		<div class="table-box" v-if="alarmList.length > 0">
			<el-table
				class="alarm-list"
				:data="alarmList"
				:header-cell-style="{ background: '#ffafaf', color: '#222' }"
				:cell-style="{ borderColor: '#ffafaf' }"
				size="medium"
			>
				<el-table-column prop="alarmName" label="报警原因"></el-table-column>
				<el-table-column label="报警等级">
					<template slot-scope="scope">
						<span v-if="scope.row.alarmLevel" class="alarm-level">
							{{ nameConversion(scope.row.alarmLevel, alarmLevelArr) }}
							<span
								v-for="item in Number(scope.row.alarmLevel)"
								:key="item"
								class="iconfontCis icon-caution"
							></span>
						</span>
					</template>
				</el-table-column>
			</el-table>
			<!-- -->
			<!--  -->
			<el-pagination
				class="alarm-list-pagination"
				small
				:page-size="8"
				:total="total"
				:is-right-align="true"
				:hide-on-single-page="true"
				:current="current"
				:layout="'total, prev, pager, next'"
				@current-change="handleCurrentChange"
			></el-pagination>
		</div>
		<gc-empty v-else text="暂无报警信息"></gc-empty>
	</div>
</template>

<script>
import { nameConversion } from '@/utils/index.js'
import dictionaryValue from '@/utils/dictionaryValue'

const mock_alarm_list = [
	{
		alarmRecordId: 179,
		alarmId: 3,
		meterId: null,
		deviceId: 2011288066,
		alarmStatus: 1,
		alarmTime: '2021-01-13T15:17:59',
		recoverTime: '2021-01-15T06:50:36',
		tenantId: 3,
		callResult: 0,
		alarmLevel: '2',
		alarmName: '读数异常变大',
		deviceNo: '994027100901',
		deviceType: 0,
		communicationNo: '994027100901',
	},
	{
		alarmRecordId: 171,
		alarmId: 3,
		meterId: null,
		deviceId: 2011288066,
		alarmStatus: 1,
		alarmTime: '2021-01-13T15:17:59',
		recoverTime: '2021-01-15T06:50:36',
		tenantId: 3,
		callResult: 0,
		alarmLevel: '1',
		alarmName: '阀门关位检测到异常',
		deviceNo: '994027100901',
		deviceType: 0,
		communicationNo: '994027100901',
	},
	{
		alarmRecordId: 172,
		alarmId: 3,
		meterId: null,
		deviceId: 2011288066,
		alarmStatus: 1,
		alarmTime: '2021-01-13T15:17:59',
		recoverTime: '2021-01-15T06:50:36',
		tenantId: 3,
		callResult: 0,
		alarmLevel: '3',
		alarmName: '多天未通讯',
		deviceNo: '994027100901',
		deviceType: 0,
		communicationNo: '994027100901',
	},
]
export default {
	name: 'gcAlarmList',
	mixins: [dictionaryValue],
	props: {
		alarmList: {
			type: Array,
			default: () => mock_alarm_list,
		},
		// 总条数
		total: {
			type: Number,
			default: 0,
		},
		current: {},
	},
	methods: {
		nameConversion,
		handleCurrentChange(val) {
			this.$emit('current-change', val)
		},
	},
}
</script>
<style lang="scss" scoped>
.alarm-list-box {
	height: 100%;
}
.alarm-list {
	border-radius: 4px;
	border: 1px solid #ec958e;
	.alarm-level {
		font-size: 14px;
		color: #ec6b60;
		.icon-caution {
			padding-left: 6px;
			font-size: 13px;
		}
	}
}
.alarm-list-pagination {
	margin-top: 20px;
}
.el-pagination.el-pagination--small {
	display: flex;
	justify-content: flex-end;
}
</style>
