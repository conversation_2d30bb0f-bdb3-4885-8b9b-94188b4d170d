<template>
	<div
		class="gc-arrow"
		:style="{
			'--c': color,
			'--r': circleSize,
			'--s': space,
			flex: `0 0 ${width}`,
		}"
	></div>
</template>

<script>
export default {
	name: 'GcArrow',
	props: {
		color: {
			type: String,
			default: '#1D59AD',
		},
		circleSize: {
			type: String,
			default: '2px',
		},
		space: {
			type: String,
			default: '2px',
		},
		width: {
			type: String,
			default: '64px',
		},
	},
	components: {},
	data() {
		return {}
	},
	computed: {},
	created() {},
	methods: {},
}
</script>

<style lang="scss" scoped>
.gc-arrow {
	--c: red; /* color */
	--r: 10px; /* circle size */
	--s: 10px; /* space bettwen circles */
	height: 5px;
	display: inline-block;
	margin: 10px 10px;
	position: relative;
	--g: radial-gradient(circle closest-side, var(--c) 85%, transparent);
	background: var(--g) calc(var(--s) / -2) 0 / calc(var(--r) + var(--s)) var(--r) repeat-x;
	transform: scaleY(-1);
}

.gc-arrow::after {
	content: '';
	position: absolute;
	top: calc(var(--r) / 2);
	left: 100%;
	width: 6px;
	height: 8px;
	transform: translateY(-50%);
	background: var(--c);
	clip-path: polygon(0 0, 100% 50%, 0 100%);
}
</style>
