<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-02 20:32:16
 * @LastEditors: houyan
 * @LastEditTime: 2024-10-28 14:36:33
-->
<template>
	<ul>
		<li
			v-for="(tab, index) in tabList"
			:key="index"
			:class="{ active: activeTab === tab.value, disabled: tab.disabled }"
			@click="handleClick(tab)"
		>
			<span>{{ tab.label }}</span>
			<i v-if="tab.status === 2 && !tab.disabled" class="icon el-icon-check"></i>
			<i v-if="tab.status === 1 && !tab.disabled" class="tip">{{ tab.tip || '待完善' }}</i>
		</li>
	</ul>
</template>

<script>
export default {
	name: 'GcTab',
	props: {
		tabList: {
			type: Array,
			default: () => [],
		},
		defaultTab: {
			type: [String, Number],
			default: '',
		},
	},
	components: {},
	watch: {
		tabList: {
			handler(v) {
				if (v && v.length && (this.defaultTab == null || this.defaultTab === '')) {
					this.activeTab = v[0].value
					this.$emit('changeTab', this.activeTab)
				}
			},
			immediate: true,
		},
		defaultTab(v) {
			if (v) {
				this.activeTab = v
			}
		},
	},
	data() {
		return {
			activeTab: this.defaultTab,
		}
	},
	methods: {
		handleClick(item) {
			if (item.disabled) return
			this.activeTab = item.value
			this.$emit('changeTab', this.activeTab)
		},
	},
}
</script>

<style lang="scss" scoped>
li {
	display: flex;
	align-items: center;
	padding: 0 10px;
	height: 40px;
	// line-height: 40px;
	cursor: pointer;
	span {
		min-width: 60px;
	}
	.tip {
		margin-left: 10px;
		font-size: $base-font-size-small;
		color: $base-color-red;
	}
	.icon {
		margin-left: 10px;
		font-weight: bold;
		color: $base-color-green;
	}
}
.active {
	position: relative;
	background-color: #eff5fe;
	color: #2f87fe;
}
.active:after {
	position: absolute;
	right: 0;
	top: 0;
	content: '';
	display: block;
	width: 2px;
	height: 100%;
	background-color: #2f87fe;
}
.disabled {
	cursor: not-allowed;
	color: #ccc;
}
</style>
