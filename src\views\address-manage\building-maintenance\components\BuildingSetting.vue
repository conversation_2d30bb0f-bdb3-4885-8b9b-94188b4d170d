<template>
	<GcElDialog :show="isShow" :title="title" @close="handleClose" @cancel="handleClose" @ok="handleSave">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
	</GcElDialog>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import {
	ruleRequired,
	RULE_POSITIVEINTEGERONLY,
	ruleMaxLength,
	validateMinValue,
	validateMaxValue,
} from '@/utils/rules.js'
import { getFormItems } from './formItem'
import { apiAddBuilding, apiBatchAddBuilding, apiUpdateBuilding } from '@/api/addressManage.api'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		editType: {
			type: String,
			default: '',
		},
		data: {
			type: Object,
			default: () => {},
		},
	},
	watch: {
		show(val) {
			if (val) {
				if (['edit', 'add'].includes(this.editType)) {
					this.formItems = getFormItems(this).filter(item => item.prop !== 'num')
				} else {
					this.formItems = getFormItems(this)
				}
				this.formItems.forEach(item => {
					this.$set(this.formData, item.prop, '')
				})

				this.$nextTick(() => {
					this.$refs.formRef.$refs.formRef.clearValidate()
				})
				if (this.editType === 'edit') {
					this.assignForm(this.data)
				}
				this.formData.neighbourhoodName = this.$route.query.addressAreaName
			}
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		title() {
			if (this.editType === 'add') {
				return `新增楼栋`
			} else if (this.editType === 'batchAdd') {
				return `批量新增楼栋`
			} else if (this.editType === 'edit') {
				return `修改楼栋`
			}
			return ``
		},
	},
	data() {
		return {
			formData: {},
			formItems: [],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					buildingNo: [
						ruleRequired('必填'),
						RULE_POSITIVEINTEGERONLY,
						validateMinValue(1),
						validateMaxValue(999),
					],
					num: [ruleRequired('必填'), RULE_POSITIVEINTEGERONLY, validateMinValue(1), validateMaxValue(999)],
					unit: [ruleRequired('必填'), ruleMaxLength(16)],
				},
			},
		}
	},
	methods: {
		// 新增单个楼栋
		async _apiAddBuilding() {
			try {
				const params = trimParams(removeNullParams(this.formData))
				delete params.neighbourhoodName
				Object.assign(params, {
					neighbourhoodCode: this.$route.query.addressAreaCode,
				})

				await apiAddBuilding(params)
				this.$message.success('新增成功')
				this.$emit('success')
				this.handleClose()
			} catch (error) {
				console.error(error)
			}
		},
		// 批量新增楼栋
		async _apiBatchAddBuilding() {
			try {
				const params = trimParams(removeNullParams(this.formData))
				delete params.neighbourhoodName
				Object.assign(params, {
					neighbourhoodCode: this.$route.query.addressAreaCode,
				})

				await apiBatchAddBuilding(params)
				this.$message.success('新增成功')
				this.$emit('success')
				this.handleClose()
			} catch (error) {
				console.error(error)
			}
		},
		// 修改楼栋
		async _apiUpdateBuilding() {
			try {
				const params = trimParams(removeNullParams(this.formData))
				delete params.neighbourhoodName
				Object.assign(params, {
					addressAreaId: this.data.addressAreaId,
					neighbourhoodCode: this.$route.query.addressAreaCode,
				})

				await apiUpdateBuilding(params)
				this.$message.success('修改成功')
				this.$emit('success')
				this.handleClose()
			} catch (error) {
				console.error(error)
			}
		},
		assignForm(obj) {
			const keys = Object.keys(this.formData)

			keys.forEach(key => {
				if (obj && Object.prototype.hasOwnProperty.call(obj, key)) {
					this.formData[key] = obj[key] + ''
				}
			})
		},
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const apiMethods = {
				add: this._apiAddBuilding,
				batchAdd: this._apiBatchAddBuilding,
				edit: this._apiUpdateBuilding,
			}

			const apiMethod = apiMethods[this.editType]
			if (apiMethod) {
				apiMethod.call(this)
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
	},
}
</script>
