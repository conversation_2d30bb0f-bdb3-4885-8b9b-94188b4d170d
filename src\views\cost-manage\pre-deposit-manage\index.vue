<template>
	<div class="page-layout">
		<div class="page-left">
			<el-tabs v-model="activeTab" type="border-card" @tab-click="handleChangeTab">
				<el-tab-pane v-for="(tab, index) in tabs" :key="index" :label="tab.label" :name="tab.name">
					<GcFormSimple
						:ref="'formRef' + index"
						v-model="formData"
						:formItems="formItems"
						:formAttrs="formAttrs"
					/>
				</el-tab-pane>
			</el-tabs>
			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:operate="row">
					<el-button
						v-has="'billing_pay-record_refund-balance'"
						v-if="row.row.meterBalanceAmount > 0"
						type="text"
						@click="handleCancel(row)"
					>
						取消预存款
					</el-button>
					<el-button v-has="'billing_pay-record_charge-balance'" type="text" @click="handleRecharge(row)">
						充值
					</el-button>
				</template>
			</GcTable>
		</div>
		<CancelPreDeposit ref="cancelPreDepositRef" :show.sync="showCancelDialog" @success="getList" />
		<Recharge ref="reChargeRef" :show.sync="showRechargeDialog" @success="getList"></Recharge>
	</div>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getColumn } from './tableColumn'
import { getFormItems } from './formItem.js'
import CancelPreDeposit from './components/CancelPreDeposit.vue'
import Recharge from './components/Recharge.vue'
import { getBalanceList } from '@/api/costManage.api'
export default {
	name: 'PreDepositManage',
	components: { CancelPreDeposit, Recharge },
	data() {
		return {
			// 左侧查询
			tabs: [
				{
					label: '居民',
					name: 'resident',
				},
				{
					label: '企业',
					name: 'company',
				},
			],
			activeTab: 'resident',
			formData: {
				orgCode: '',
				archivesIdentity: '',
				enterpriseNumber: '',
			},
			formItems: getFormItems(this),
			formAttrs: {
				rules: {
					orgCode: {
						required: true,
						message: '请选择营业分公司',
						trigger: 'change',
					},
				},
			},
			// 右侧列表
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			showCancelDialog: false,
			showRechargeDialog: false,
		}
	},
	mounted() {
		const defaultOrgCode = this.$store.getters.orgList[0].value
		if (defaultOrgCode) {
			this.formData.orgCode = defaultOrgCode
			this.getList()
		}
	},
	computed: {
		columns() {
			return getColumn(this)
		},
		refName() {
			return `formRef${this.tabs.findIndex(item => item.name === this.activeTab)}`
		},
	},
	created() {},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		handleReset() {
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.$refs[this.refName][0].resetFields()
		},
		async handleSearch() {
			const valid = await this.$refs[this.refName][0].validate()
			if (!valid) return
			this.pageData.current = 1
			this.pageData.total = 0
			this.getList()
		},
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				const isResident = this.activeTab === 'resident'
				if (isResident) {
					delete formParams.enterpriseNumber
				} else {
					delete formParams.archivesIdentity
				}
				Object.assign(formParams, {
					current,
					size,
					userType: isResident ? 3 : 4,
				})
				const { records, total } = await getBalanceList(formParams)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleCancel(row) {
			this.showCancelDialog = true
			this.$nextTick(() => {
				this.$refs.cancelPreDepositRef.assignForm(row)
			})
		},
		handleRecharge(row) {
			this.showRechargeDialog = true
			this.$nextTick(() => {
				this.$refs.reChargeRef.assignForm(row)
			})
		},
		handleChangeTab() {
			const isResidentTab = this.activeTab === 'resident'
			const newItem = isResidentTab
				? {
						type: 'el-input',
						label: '表卡编号',
						prop: 'archivesIdentity',
						attrs: {
							clearable: true,
						},
				  }
				: {
						type: 'el-input',
						label: '企业编号',
						prop: 'enterpriseNumber',
						attrs: {
							clearable: true,
						},
				  }
			if (isResidentTab) {
				this.formData.archivesIdentity = ''
			} else {
				this.formData.enterpriseNumber = ''
			}
			this.formItems.splice(1, 1, newItem)
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.handleSearch()
		},
	},
}
</script>
<style lang="scss" scoped>
.page-left {
	padding-top: 0;
	padding-left: 0;
	padding-right: 0;
	.btn-group {
		padding: 0 20px;
	}
	// 隐掉tab的边框
	::v-deep {
		.el-tabs {
			width: 100%;
			height: 100%;
			box-shadow: none;
			border: none;
			border-radius: 4px 4px 0 0;
			overflow: hidden;
			.el-tabs__content {
				padding: 0;
				height: calc(100% - 38px);
				.el-tab-pane {
					height: 100%;
				}
			}
		}
		.el-tabs--border-card > .el-tabs__header {
			border: none;
			height: 38px;
			margin-bottom: 10px;
			.el-tabs__nav {
				display: flex;
				align-items: center;
				width: 100%;
				border: none;
				height: 38px;
				.el-tabs__item {
					margin: 0;
					background: #e1ebfa;
					border: none;
					font-size: 14px;
					color: #6d7480;
					flex: 1;
					padding: 0;
					text-align: center;
					height: 38px;
					&.is-active {
						background: #ffffff;
						font-weight: 500;
						color: #2f87fe;
					}
				}
			}
		}
		.el-tabs__item:focus.is-active.is-focus:not(:active) {
			-webkit-box-shadow: none;
			box-shadow: none;
		}
		.el-form {
			padding: 0 20px;
		}
	}
}
</style>
