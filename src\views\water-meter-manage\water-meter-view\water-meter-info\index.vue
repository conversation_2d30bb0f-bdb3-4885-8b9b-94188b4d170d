<template>
	<div>
		<gc-detail-card
			:detail-card-info="detailCardInfo"
			:header-num="{
				key: '水表编号',
				value: data.meterNo,
			}"
		>
			<template #tag>
				<span
					v-if="data.meterStatus || data.meterStatus === 0"
					:class="`tag ${data.meterStatus !== 1 ? 'error' : ''}`"
				>
					<i></i>
					{{ meterStatusName || '--' }}
				</span>
			</template>
			<template #card-content>
				<div class="card-content">
					<div class="content-item" v-for="(item, index) in displayList" :key="index">
						<p class="field">{{ item.label }}</p>
						<p class="value">
							{{ item.key !== 'meterStatus' ? data[item.key] || '--' : meterStatusName || '--' }}
						</p>
					</div>
				</div>
			</template>
			<template #card-footer>
				<div v-if="editable" v-show="data.meterId" class="card-footer">
					<el-button v-has="'cpm_meter_modify-meter'" type="text" class="blue" @click="handleEdit">
						<i class="iconfontCis icon-modify"></i>
						修改
					</el-button>
					<!-- 检修状态 -->
					<el-button
						v-has="'cpm_meter_repair-meter'"
						v-if="data.meterStatus === 2"
						type="text"
						@click="handleOverhaul"
					>
						<i class="iconfontCis icon-erji-zuhupeizhi"></i>
						检修
					</el-button>
					<!-- 检修状态 才可以报废 -->
					<el-button
						v-has="'cpm_meter_scrap-meter'"
						v-if="data.meterStatus === 2"
						type="text"
						@click="handleScrap"
					>
						<i class="iconfontCis icon-xls-table"></i>
						报废
					</el-button>
				</div>
			</template>
		</gc-detail-card>
		<!-- 水表修改弹窗 -->
		<UpdateMeterDialog
			ref="updateDialogRef"
			:show.sync="showUpdateDialog"
			@success="handleRefresh"
			editType="edit"
		/>
		<!-- 水表检修弹窗 -->
		<OverhaulMeterDialog
			ref="overhaulDialogRef"
			:data="data"
			@success="handleRefresh"
			:show.sync="showOverhaulDialog"
		/>
		<!-- 水表报废弹窗 -->
		<ScrapMeterDialog ref="scrapDialogRef" :data="data" @success="handleRefresh" :show.sync="showScrapDialog" />
	</div>
</template>

<script>
import UpdateMeterDialog from '../../components/update-meter-dialog.vue'
import ScrapMeterDialog from '../../components/scrap-meter-dialog.vue'
import OverhaulMeterDialog from '../../components/overhaul-meter-dialog.vue'
import { getfilterName } from '@/utils'

export default {
	name: '',
	components: { UpdateMeterDialog, OverhaulMeterDialog, ScrapMeterDialog },
	props: {
		data: Object,
		editable: Boolean,
	},
	data() {
		return {
			detailCardInfo: {
				bgUrl: require('@/assets/images/bg/pic-file.png'), //背景图url
				signUrl: require('@/assets/images/icon/title-file.png'), //header中的标志的url
				cardName: '水表信息', //卡片名
			},
			displayList: [
				{
					label: '水表仓库编号',
					key: 'meterWarehouseCode',
				},
				{
					label: '水表类型',
					key: 'meterTypeName',
				},
				{
					label: '口径',
					key: 'caliber',
				},
				{
					label: '量程',
					key: 'ranges',
				},
				{
					label: '厂商',
					key: 'manufacturerName',
				},
				{
					label: '水表型号',
					key: 'meterModel',
				},
				{
					label: '水表状态',
					key: 'meterStatus',
				},
				{
					label: '首次安装时间',
					key: 'useTime',
				},
				{
					label: '服役年限',
					key: 'useYears',
				},
			],
			// 修改弹窗
			showUpdateDialog: false,
			// 检修弹窗
			showOverhaulDialog: false,
			// 报废弹窗
			showScrapDialog: false,
		}
	},
	computed: {
		meterStatusName() {
			return getfilterName(
				this.$store.getters?.dataList?.meterStatus || [],
				this.data.meterStatus,
				'sortValue',
				'sortName',
			)
		},
	},
	methods: {
		// 修改
		handleEdit() {
			this.showUpdateDialog = true
			this.$nextTick(() => {
				this.$refs.updateDialogRef.setFormData(this.data)
			})
		},
		// 检修
		handleOverhaul() {
			this.showOverhaulDialog = true
		},
		// 报废
		handleScrap() {
			this.showScrapDialog = true
		},
		// 刷新
		handleRefresh() {
			this.$emit('refresh')
		},
	},
}
</script>

<style lang="scss" scoped>
.detail-card {
	.card-footer {
		padding: 0;
		display: flex;
		justify-content: space-around;
		::v-deep .el-button {
			font-size: 14px;
			color: $base-color-yellow;
			padding-bottom: 0;
			i {
				padding-right: 3px;
			}
			span {
				display: flex;
				align-items: center;
			}
		}
		.el-button + .el-button {
			margin-left: 2px;
			margin-right: 2px;
		}
		.blue {
			color: $base-color-blue;
		}
	}
}

.tag {
	background: #ecf4ff;
	color: $base-color-blue;
	i {
		background: $base-color-blue;
	}
	&.error {
		background: #faecd8;
		color: #ff9d57;
		i {
			background: #ff9d57;
		}
	}
}
</style>
