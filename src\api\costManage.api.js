import service from './request'
import { BILLING, PAYMENT } from '@/consts/moduleNames'
import fileDownload from '@/utils/fileDownload'

// 账单列表
export function apiGetBillList(data) {
	return service({
		url: `${BILLING}/bill/list`,
		method: 'post',
		data,
	})
}
// 查询待缴费账单列表
export function apiGetBillArrearsList(data) {
	return service({
		url: `${BILLING}/bill-arrears/list`,
		method: 'post',
		data,
	})
}
// 用户欠费记录
export function apiGetBillArrearsList1(data) {
	return service({
		url: `${BILLING}/bill-arrears/list1`,
		method: 'post',
		data,
	})
}

// 待开账账单列表
export function apiGetBillPendingList(data) {
	return service({
		url: `${BILLING}/bill/pending-list`,
		method: 'post',
		data,
	})
}

// 开账
export function apiOpenBill(parameter) {
	return service({
		url: `${BILLING}/bill/open`,
		method: 'PUT',
		data: parameter,
	})
}
// 全部开账
export function apiOpenAllBill(parameter) {
	return service({
		url: `${BILLING}/bill/search-open`,
		method: 'PUT',
		data: parameter,
	})
}

// 柜台缴费列表
export function apiGetCounterList(data) {
	return service({
		url: `${PAYMENT}/bill-pay-record/counter-list`,
		method: 'post',
		data,
	})
}

// 销账
export function apiBillClear(data) {
	return service({
		url: `${BILLING}/bill/clear`,
		method: 'post',
		data,
	})
}
// 批量销账
export function apiBillClearBatch(data) {
	return service({
		url: `${BILLING}/bill/account-clear-batch`,
		method: 'post',
		data,
	})
}

// 冲正
export function apiBillPayRecordRevers(data) {
	return service({
		url: `${PAYMENT}/bill-pay-record/revers`,
		method: 'post',
		data,
	})
}

// 销账列表
export function apiGetAccountClearList(data) {
	return service({
		url: `${BILLING}/bill/account-clear-list`,
		method: 'post',
		data,
	})
}
// 销账列表导出
export function apiExportAccountClearList(data) {
	return service({
		url: `${BILLING}/bill/account-clear-list/export/excel`,
		method: 'POST',
		data,
		responseType: 'blob',
	})
}

// 账户销账
export function apiClearAccount(data) {
	return service({
		url: `${BILLING}/bill/account-clear`,
		method: 'post',
		data,
	})
}

// 账单详情
export function apiGetBillDetail(params) {
	return service({
		url: `${BILLING}/bill/detail`,
		method: 'GET',
		params,
	})
}

//减免
export function apiBillAdjustReduction(data) {
	return service({
		url: `${BILLING}/bill-adjust/reduction`,
		method: 'POST',
		data,
	})
}
// 费用调整
export function apiBillAdjustment(data) {
	return service({
		url: `${BILLING}/bill-adjust/adjustment`,
		method: 'POST',
		data,
	})
}
export function apiBillAdjustment1(data) {
	return service({
		url: `${BILLING}/bill-adjust/adjustment1`,
		method: 'POST',
		data,
	})
}
export function apiBillAdjustment2(data) {
	return service({
		url: `${BILLING}/bill-adjust/adjustment2`,
		method: 'POST',
		data,
	})
}

// 部分缴费
export function apiBillPartial(data) {
	return service({
		url: `${BILLING}/bill-adjust/partial-payment`,
		method: 'POST',
		data,
	})
}
// 获取调整记录
export function apiGetBillAdjust(params) {
	return service({
		url: `${BILLING}/bill-adjust/record`,
		method: 'GET',
		params,
	})
}
// 获取调整账单详情
export function apiGetRecordBillInfo(params) {
	return service({
		url: `${BILLING}/bill-adjust/record-bill-info`,
		method: 'GET',
		params,
	})
}

// 预存额列表
export function getBalanceList(data) {
	return service({
		url: `${BILLING}/pay-record/balance-list`,
		method: 'POST',
		data,
	})
}

// 取消预存款
export function refundBalanceRecord(data) {
	return service({
		url: `${BILLING}/pay-record/refund-balance`,
		method: 'POST',
		data,
	})
}
// 充值
export function reChargeRecord(data) {
	return service({
		url: `${BILLING}/pay-record/charge-balance`,
		method: 'POST',
		data,
	})
}

// 预存记录列表
export function getPayRecordList(data) {
	return service({
		url: `${BILLING}/pay-record/list`,
		method: 'POST',
		data,
	})
}

// 查询关联账单
export function getPayRecordBillList(params) {
	return service({
		url: `${BILLING}/pay-record/bill-list`,
		method: 'GET',
		params,
	})
}

// 离线缴费/批量充值
export function batPayment(data) {
	return service({
		url: `${BILLING}/charge-batch`,
		method: 'POST',
		data,
	})
}

// 离线缴费记录列表
export function getBatPaymentRecords(data) {
	return service({
		url: `${BILLING}/charge-batch/record/list`,
		method: 'POST',
		data,
	})
}

// 获取离线缴费记录上传失败数据
export function getBatRechargeUploadFailRecords(params) {
	return service({
		url: `${BILLING}/charge-batch/fail`,
		method: 'GET',
		params,
	})
}

// 联合收费管理-取消送盘文件
export function cancelUnionSendFile(params) {
	return service({
		url: `${BILLING}/unionCollection/cancelUnionSendFile`,
		method: 'GET',
		params,
	})
}

// 联合收费管理-完成对账
export function completeUnionCollection(params) {
	return service({
		url: `${BILLING}/unionCollection/complete`,
		method: 'GET',
		params,
	})
}

// 联合收费管理-推送送盘文件
export function sendUnionSendFile(params) {
	return service({
		url: `${BILLING}/unionCollection/sendUnionSendFile`,
		method: 'GET',
		params,
	})
}

// 联合收费管理-生成送盘文件
export function createUnionSendFile(data) {
	return service({
		url: `${BILLING}/unionCollection/createUnionSendFile`,
		method: 'POST',
		data,
	})
}

// 联合收费管理-查看新增用户
export function queryUnionCard(data) {
	return service({
		url: `${BILLING}/unionCollection/queryUnionCard`,
		method: 'POST',
		data,
	})
}

// 联合收费管理-回盘文件列表查询
export function queryUnionReturnFile(data) {
	return service({
		url: `${BILLING}/unionCollection/queryUnionReturnFile`,
		method: 'POST',
		data,
	})
}

// 联合收费管理-送盘文件列表查询
export function queryUnionSendFile(data) {
	return service({
		url: `${BILLING}/unionCollection/queryUnionSendFile`,
		method: 'POST',
		data,
	})
}

// 联合收费管理-送盘文件明细
export function queryUnionSendFileDetail(data) {
	return service({
		url: `${BILLING}/unionCollection/queryUnionSendFileDetail`,
		method: 'POST',
		data,
	})
}

// 银行托收管理-取消送盘文件
export function cancelBankSendFile(params) {
	return service({
		url: `${BILLING}/bankCollection/cancelBankSendFile`,
		method: 'GET',
		params,
	})
}

// 银行托收管理-完成对账
export function completeBankCollection(params) {
	return service({
		url: `${BILLING}/bankCollection/complete`,
		method: 'GET',
		params,
	})
}

// 银行托收管理-生成送盘文件
export function createBankSendFile(data) {
	return service({
		url: `${BILLING}/bankCollection/createBankSendFile`,
		method: 'POST',
		data,
	})
}

// 银行托收管理-列表查询
export function queryBankSendFile(data) {
	return service({
		url: `${BILLING}/bankCollection/queryBankSendFile`,
		method: 'POST',
		data,
	})
}

// 银行托收管理-导入回盘文件
export function importBankReturnFile(data) {
	return service({
		url: `${BILLING}/bankCollection/importBankReturnFile`,
		method: 'POST',
		data,
	})
}

// 银行托收扣管理-导出送盘文件
export function bankExportSendFile(params, fileName) {
	const path = `${BILLING}/bankCollection/exportBankSendFile`
	return fileDownload({
		path,
		fileName,
		params,
		useBrowserDownload: true,
	})
}

// 联合收费扣款记录-手动销账
export function closeUnionBill(data) {
	return service({
		url: `${BILLING}/unionCollection/closeUnionBill`,
		method: 'POST',
		data,
	})
}

// 联合收费扣款记录-账单列表查询
export function queryUnionBillList(data) {
	return service({
		url: `${BILLING}/unionCollection/queryUnionBillList`,
		method: 'POST',
		data,
	})
}

// 联合收费扣款记录-账单统计
export function queryUnionBillStatistics(data) {
	return service({
		url: `${BILLING}/unionCollection/queryUnionBillStatistics`,
		method: 'POST',
		data,
	})
}

// 联合收费扣款记录-导出对账记录
export function unionCollectionExportBill(params) {
	const path = `${BILLING}/unionCollection/exportBill`
	return fileDownload({
		path,
		fileName: '联合收费扣款记录.xlsx',
		params,
	})
}

// 银行托收扣款记录-导出对账记录
export function bankCollectionExportBill(params) {
	const path = `${BILLING}/bankCollection/exportBill`
	return fileDownload({
		path,
		fileName: '银行托收扣款记录.xlsx',
		params,
	})
}

// 银行托收扣款记录-手动销账
export function closeBankBill(data) {
	return service({
		url: `${BILLING}/bankCollection/closeBankBill`,
		method: 'POST',
		data,
	})
}

// 银行托收扣款记录-账单列表查询
export function queryBankBillList(data) {
	return service({
		url: `${BILLING}/bankCollection/queryBankBillList`,
		method: 'POST',
		data,
	})
}

// 合并开票：账单管理 难以置信×1
export function mergeOpenInvoice(data) {
	return service({
		url: `${PAYMENT}/invoice/merge-open-invoice`,
		method: 'POST',
		data,
	})
}

// 合并开票：居民表卡视图 难以置信×2
export function mergeOpenInvoice2(data) {
	return service({
		url: `${PAYMENT}/invoice/merge-open-invoice2`,
		method: 'POST',
		data,
	})
}

// 合并开票：企业表卡视图 难以置信×3
export function mergeOpenInvoice3(data) {
	return service({
		url: `${PAYMENT}/invoice/merge-open-invoice3`,
		method: 'POST',
		data,
	})
}

// 合并开票：用户视图 难以置信×4
export function mergeOpenInvoice4(data) {
	return service({
		url: `${PAYMENT}/invoice/merge-open-invoice4`,
		method: 'POST',
		data,
	})
}

// 合并开票：企业视图 难以置信×5
export function mergeOpenInvoice5(data) {
	return service({
		url: `${PAYMENT}/invoice/merge-open-invoice5`,
		method: 'POST',
		data,
	})
}

// 合并开票：缴费 难以置信×6
export function mergeOpenInvoice6(data) {
	return service({
		url: `${PAYMENT}/invoice/merge-open-invoice6`,
		method: 'POST',
		data,
	})
}

// 批量开票：账单管理
export function batchOpenInvoice(data) {
	return service({
		url: `${PAYMENT}/invoice/batch-open-invoice`,
		method: 'POST',
		data,
	})
}

// 批量开票：缴费
export function batchOpenInvoice2(data) {
	return service({
		url: `${PAYMENT}/invoice/batch-open-invoice2`,
		method: 'POST',
		data,
	})
}

// 开票: 账单管理
export function singleOpenInvoice(data) {
	return service({
		url: `${PAYMENT}/invoice/open-invoice`,
		method: 'POST',
		data,
	})
}

// 开票fine: 罚没款
export function singleOpenInvoiceFines(data) {
	return service({
		url: `${PAYMENT}/invoice/open-invoice-fines`,
		method: 'POST',
		data,
	})
}

// 查询账户余额
export function apiGetAccountBalance(params) {
	return service({
		url: `${BILLING}/account/by-archivesId`,
		method: 'GET',
		params,
	})
}
