<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handleCurrentChange"
			/>
			<!-- 弹窗 -->
			<VirtualPreview :show.sync="showPreview" :currentRow="currentRow" />
		</div>
	</div>
</template>

<script>
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { distributionModeOptions } from '@/consts/optionList.js'
import { getAlleyMap } from '@/api/meterReading.api.js'
import {
	apiGetVirtualArchivesList,
	apiConvertNormalArchives,
	apiQueryConvertNormalArchives,
} from '@/api/meterManage.api'
import VirtualPreview from './virtual-preview/index.vue'
export default {
	components: { VirtualPreview },
	data() {
		return {
			formData: {
				orgCode: '',
				alleyId: '',
				bookNo: '',
				archivesStatus: '',
				archivesIdentity: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					events: {
						change: () => {
							this.formData.alleyId = ''
							this._getAlleyMap()
						},
					},
				},
				{
					type: 'el-select',
					label: '坊别',
					prop: 'alleyId',
					options: [],
				},
				{
					type: 'el-input',
					label: '表册编号',
					prop: 'bookNo',
				},
				{
					type: 'el-select',
					label: '表卡状态',
					prop: 'archivesStatus',
					options: (this.$store.getters.dataList.archiveState || []).map(item => {
						return {
							label: item.sortName,
							value: Number(item.sortValue),
						}
					}),
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
				},
			],
			formAttrs: {
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
				},
			},
			columns: [
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'addressFullName',
					name: '地址',
					tooltip: true,
					minWidth: 200,
				},
				{
					key: 'userName',
					name: '用户',
					tooltip: true,
				},
				{
					key: 'bookNo',
					name: '表册编号',
					tooltip: true,
				},
				{
					key: 'virtualCount',
					name: '虚表数量',
					tooltip: true,
				},
				{
					key: 'distributionMode',
					name: '水量分配方式',
					tooltip: true,
					render: (h, row) => {
						const enums = distributionModeOptions.reduce((acc, option) => {
							acc[option.value] = option.label
							return acc
						}, {})
						return h('span', {}, enums[row.distributionMode])
					},
				},
				{
					key: '',
					name: '操作',
					fixed: 'right',
					minWidth: 200,
					render: (h, row) => {
						return h('div', {}, [
							this.$has('cpm-archives-virtual-archives') &&
								h(
									'el-button',
									{
										props: {
											type: 'text',
											size: 'medium',
										},
										on: {
											click: this.clickTableBtn(row, 'preview'),
										},
									},
									'查看虚表',
								),
							this.$has('cpm-archives-update-virtual-records') &&
								h(
									'el-button',
									{
										props: {
											type: 'text',
											size: 'medium',
										},
										on: {
											click: this.clickTableBtn(row, 'edit'),
										},
									},
									'编辑虚表',
								),
							this.$has('cpm-archives-convert-normal-archives') &&
								h(
									'el-button',
									{
										props: {
											type: 'text',
											size: 'medium',
										},
										on: {
											click: this.clickTableBtn(row, 'change'),
										},
									},
									'转正常表',
								),
						])
					},
					hide: !this.$has([
						'cpm-archives-virtual-archives',
						'cpm-archives-update-virtual-records',
						'cpm-archives-convert-normal-archives',
					]),
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			searchParams: {},
			showPreview: false,
			currentRow: {},
			loading: false,
		}
	},
	computed: {
		orgOptions() {
			return this.formItems[0].options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this._getAlleyMap()
					this.$nextTick(() => {
						this.handleSearch()
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	activated() {
		this.handleReset()
	},
	methods: {
		async _getAlleyMap() {
			const res = await getAlleyMap({
				orgCode: this.formData.orgCode,
			})
			const arr = res.map(item => {
				return {
					value: item.id,
					label: item.alleyName,
					...item,
				}
			})
			this.formItems[1].options = arr
		},
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
				})

				const { records, total } = await apiGetVirtualArchivesList(formParams)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.currentRow = {}
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.handleCurrentChange({ page: 1 })
		},
		clickTableBtn(row, type) {
			return async () => {
				if (type === 'edit') {
					this.$router.push({
						path: '/meterManage/virtualMeterSplitPerfect',
						query: {
							archivesIdentity: row.archivesIdentity,
							archivesId: row.archivesId,
						},
					})
				} else if (type === 'preview') {
					this.currentRow = row
					this.showPreview = true
				} else if (type === 'change') {
					const params = {
						archivesId: row.archivesId,
						tenantId: this.$store.getters.userInfo?.tenantId,
					}
					const res = await apiQueryConvertNormalArchives(params)
					this.$confirm(res, '转正常表提示').then(() => {
						this._apiConvertNormalArchives(params)
					})
				}
			}
		},
		handleCurrentChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		async _apiConvertNormalArchives(params) {
			await apiConvertNormalArchives(params)
			this.$message.success('转换成功')
			this.handleSearch()
		},
	},
}
</script>
