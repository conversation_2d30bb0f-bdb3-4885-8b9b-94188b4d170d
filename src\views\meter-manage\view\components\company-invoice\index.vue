<template>
	<GcElDialog :show="isShow" title="开票信息" width="500px" :showFooter="false" @close="isShow = false">
		<GcGroupDetail :data="userInfo"></GcGroupDetail>
	</GcElDialog>
</template>

<script>
import { getfilterName } from '@/utils'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		userInfo() {
			const list = [
				{
					key: '开票类型',
					value: '--',
					field: 'invoiceType',
				},
				{
					key: '纳税人识别号',
					value: '--',
					field: 'taxpayerIdentity',
				},
				{
					key: '开户银行',
					value: '--',
					field: 'openBank',
				},
				{
					key: '银行账号',
					value: '--',
					field: 'bankAccount',
				},
			]
			const extractedData = Object.assign({}, ...Object.values(this.tabData))
			const getValue = (field, value) => {
				const { invoiceType = [] } = this.$store.getters.dataList || {}

				switch (field) {
					case 'invoiceType':
						return getfilterName(invoiceType, value, 'sortValue', 'sortName')
					default:
						return value
				}
			}
			list.forEach(item => {
				item.value = getValue(item.field, extractedData[item.field])
			})
			return {
				list,
			}
		},
	},
}
</script>
