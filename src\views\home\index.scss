.box-wrapper {
    height: 100%;
    box-sizing: border-box;
    background: #eceff8;
    // padding: 20px;
    overflow: hidden;
    display: flex;
    .firstlevel {
      float: left;
      height: 100%;
    }
    .quickentry-wrap {
      max-width: 460px;
      min-width: 320px;
      width: 25%;
      margin-right: 20px;
      background: #fff;
      .img-wrap {
        margin: 0 20px;
        padding: 22px 0 0 0;
        text-align: center;
        border-bottom: 1px solid #d8d8d8;
        font-size: 0;
        img {
          width: 53%;
        }
      }
    }
    .main-wrap {
      min-width: 714px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      overflow: auto;
      &.nodata {
        background: #fff;
      }
      .secondlevel {
        margin-bottom: 20px;
        overflow: hidden;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    .secondlevel:first-child {
      min-height: 160px;
      display: flex;
      justify-content: space-between;
      .income-wrap {
        min-width: 289px;
        width: 42%;
      }
    }
    .secondlevel:nth-child(2) {
      height: 42%;
      min-height: 224px;
      display: flex;
      justify-content: space-between;
      .chart-wrap {
        width: 49%;
        min-width: 49%;
        max-width: 49%;
        background: #fff;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
        border-radius: 4px;
      }
    }
    .secondlevel:last-child {
      height: 42%;
      min-height: 213px;
      display: flex;
      justify-content: space-between;
      .meter-trend-wrap {
        min-width: 228px;
        width: 32%;
        background: #fff;
        margin-right: 20px;
      }
      .monitor-kpi-wrap {
        min-width: 212px;
        width: 30%;
        background: #fff;
        margin-right: 20px;
      }
      .mix-wrap {
        flex-grow: 2;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .mix-item {
          width: 30%;
          background: #fff;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          &:nth-child(1),
          &:nth-child(2),
          &:nth-child(3) {
            margin-bottom: 20px;
          }
          .iconfont {
            font-size: 42px;
            margin-bottom: 8px;
          }
          .icon-wrap{
            position: relative;
            .ssc{
                position: absolute;
                width: 14px;
                height: 14px;
                background: #EC6B60;
                color: #FFFFFF;
                border-radius: 50%;
                top: 0px;
                right: 0px;
                text-align: center;
            }
          }
          .icon {
            width: 1em;
            height: 1em;
            vertical-align: -0.15em;
            fill: currentColor;
            overflow: hidden;
            font-size: 42px;
            margin-bottom: 8px;
          }
          span {
            font-size: 12px;
            font-weight: 400;
            color: #4a4a4a;
          }
        }
      }
    }
}