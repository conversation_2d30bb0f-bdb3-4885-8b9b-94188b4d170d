<template>
	<div class="statistics-container">
		<div class="flex head">
			<div class="first">
				<h1 class="year-title">账单统计</h1>
				<div>账单数(笔)</div>
				<div>总金额(元)</div>
			</div>
			<div>
				<h3 class="cell-label">已推送</h3>
				<div class="cell-value">{{ totalSum.billNum1 }}</div>
				<div class="cell-value">{{ totalSum.billAmount1 }}</div>
			</div>
			<div>
				<h3 class="cell-label">已销账</h3>
				<div class="cell-value">{{ totalSum.billNum2 }}</div>
				<div class="cell-value">{{ totalSum.billAmount2 }}</div>
			</div>
			<div>
				<h3 class="cell-label">异常</h3>
				<div class="cell-value">{{ totalSum.billNum3 }}</div>
				<div class="cell-value">{{ totalSum.billAmount3 }}</div>
			</div>
		</div>
		<div class="list-wrapper">
			<div
				v-for="(item, index) in yearsSum"
				:key="index"
				class="flex"
				:class="{ active: activeIndex == index }"
				@click="handleClick(index)"
			>
				<div class="first">
					<h1 class="year-title">{{ item.billYear }}</h1>
					<div>账单数(笔)</div>
					<div>总金额(元)</div>
				</div>
				<div>
					<h3 class="cell-label">已推送</h3>
					<div class="cell-value" :class="{ 'font-bold': activeIndex == index }">
						{{ item.billNum1 }}
					</div>
					<div class="cell-value" :class="{ 'font-bold': activeIndex == index }">
						{{ item.billAmount1 }}
					</div>
				</div>
				<div>
					<h3 class="cell-label">已销账</h3>
					<div class="cell-value" :class="{ 'font-bold': activeIndex == index }">
						{{ item.billNum2 }}
					</div>
					<div class="cell-value" :class="{ 'font-bold': activeIndex == index }">
						{{ item.billAmount2 }}
					</div>
				</div>
				<div>
					<h3 class="cell-label">异常</h3>
					<div class="cell-value" :class="{ 'font-bold': activeIndex == index }">
						{{ item.billNum3 }}
					</div>
					<div class="cell-value" :class="{ 'font-bold': activeIndex == index }">
						{{ item.billAmount3 }}
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: '',
	components: {},
	props: {
		statisticData: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			activeIndex: 0,
		}
	},
	computed: {
		formattedData() {
			const keyPrefix = 'billAmount'
			const keySuffix = ['1', '2', '3']
			return this.statisticData.map(item => {
				const copyItem = { ...item }
				for (const suffix of keySuffix) {
					const key = keyPrefix + suffix
					copyItem[key] = copyItem[key].toFixed(2)
				}
				return copyItem
			})
		},
		totalSum() {
			return (
				this.formattedData.find(item => !item.billYear) || {
					billNum1: '--',
					billAmount1: '--',
					billNum2: '--',
					billAmount2: '--',
					billNum3: '--',
					billAmount3: '--',
				}
			)
		},
		yearsSum() {
			return this.formattedData.filter(item => item.billYear)
		},
	},
	watch: {
		yearsSum: {
			handler(list) {
				if (list.length) {
					this.$emit('year-data-select', list[this.activeIndex])
				}
			},
		},
		activeIndex: {
			handler(index) {
				if (this.yearsSum.length) {
					this.$emit('year-data-select', this.yearsSum[index])
				}
			},
		},
	},
	methods: {
		handleClick(index) {
			if (this.activeIndex === index) return
			this.activeIndex = index
		},
	},
}
</script>

<style lang="scss" scoped>
.statistics-container {
	display: flex;
	flex-direction: column;
	gap: 10px;
	padding-bottom: 10px;
	flex: 0 0 450px;
	height: 100%;
	border-right: 10px solid #eceff8;
}
.list-wrapper {
	display: flex;
	flex-direction: column;
	gap: 10px;
	padding: 0 10px;
	width: 100%;
	overflow-y: auto;
	height: 100%;
	scrollbar-width: none;
	-ms-overflow-style: none;
	.list-wrapper::-webkit-scrollbar {
		display: none;
	}
	& > .flex {
		cursor: pointer;
	}
}
.year-title {
	font-family: 'Source Han Sans CN', 'microsoft yahei', 'PingFang SC', 'Hiragino Sans GB';
	font-size: 16px;
	color: #000000;
	font-weight: 700;
}
.flex {
	border-radius: 5px;
	width: 100%;
	border: 1px solid #eceff8;
	display: flex;
	gap: 7px;
	padding: 0 10px;
	.flex > div {
		flex: 0 0 26%;
		display: flex;
		flex-direction: column;
		gap: 10px;
		padding: 10px 0;
		word-break: break-all;
	}
	.flex > div:first-child {
		flex: 0 0 76px;
		position: relative;
	}
	.flex > div:first-child::after {
		content: '';
		position: absolute;
		left: 100%;
		top: 50%;
		transform: translate(-50%, -50%);
		width: 1px;
		height: 90%;
		background-color: #e1e2e3;
	}
}
.first {
	padding: 0;
}
.head {
	border: none !important;
	border-radius: 0;
	padding: 0 21px;
	background-color: #f4f7fa;
}
.active {
	background-color: #d0e4ff;
	border-color: #2f87fe;
	h3 {
		color: #003c8d;
	}
	.cell-value {
		font-weight: 500;
		color: #000000;
	}
}
.cell-label {
	font-size: 12px;
	color: #999999;
	line-height: 16px;
}
.cell-value {
	font-size: 14px;
	color: #4e4e4e;
}
</style>
