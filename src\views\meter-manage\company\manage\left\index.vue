<template>
	<div class="left-container">
		<div class="left-search-container">
			<GcSearchFake
				@handleclick="handleClickFakeSearch"
				@clear="clearFormComponent"
				:valueObj="valueObj"
			></GcSearchFake>
			<GcSearchDialogNew
				title="搜索"
				:dialogVisible.sync="showSearchPopou"
				:search-condition="searchCondition"
				:active-condition.sync="activeCondition"
				:selected-condition="selectedCondition"
				:valueObj="valueObj"
				width="1100px"
				@delete-one="deleteOne"
				@empty="deleteAll"
				@dialog-close="closeDialog"
				@public-search="searchTable"
			/>
		</div>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
		<div class="btn-group">
			<el-button round @click="handleReset">重置</el-button>
			<el-button type="primary" round @click="handleSearch">筛选</el-button>
		</div>
	</div>
</template>

<script>
import { ruleRequired } from '@/utils/rules'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { isBlank } from '@/utils/validate.js'
import { apiGetPriceList_all } from '@/api/meterManage.api.js'
export default {
	activated() {
		this._apiGetPriceList_all()
	},
	data() {
		return {
			formData: {
				orgCode: '',
				archivesStatus: '',
				priceId: '',
				virtualMeterType: '',
				archivesTime: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
				},
				{
					type: 'el-select',
					label: '表卡状态',
					prop: 'archivesStatus',
					options: (this.$store.getters.dataList.archiveState || []).map(item => {
						return {
							label: item.sortName,
							value: Number(item.sortValue),
						}
					}),
				},
				{
					type: 'el-select',
					label: '价格',
					prop: 'priceId',
					options: [],
				},
				{
					type: 'el-select',
					label: '表卡类型',
					prop: 'virtualMeterType',
					options: (this.$store.getters.dataList.virtualMeterType || []).map(item => {
						return {
							label: item.sortName,
							value: item.sortValue,
						}
					}),
				},
				{
					type: 'el-date-picker',
					label: '建档日期',
					prop: 'archivesTime',
					attrs: {
						type: 'daterange',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						valueFormat: 'yyyy-MM-dd',
					},
				},
			],
			formAttrs: {
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
				},
			},
			valueObj: {
				value: null,
			},
			showSearchPopou: false,
			searchCondition: [
				{
					key: 'archivesIdentity',
					label: '表卡编号',
				},
				{
					key: 'enterpriseNumber',
					label: '企业编号',
				},
				{
					key: 'userName',
					label: '用户名称',
				},
				{
					key: 'bookNo',
					label: '表册编号',
				},
				{
					key: 'collectionAgreementNumber',
					label: '托收协议号',
				},
				{
					key: 'userMobile',
					label: '手机号',
				},
				{
					key: 'certificateNo',
					label: '证件号码',
				},
					{
					key: 'addressName',
					label: '地址描述',
				},
				{
					key: 'oldArchivesIdentity',
					label: '旧表卡编号',
				},
				{
					key: 'tapWaterNo',
					label: '自来水编号',
				},
				{
					key: 'meterNo',
					label: '水表编号',
				},
				{
					key: 'antiTheftCode',
					label: '防盗编号',
				},
			],
			activeCondition: 'archivesIdentity',
			selectedCondition: [],
		}
	},
	computed: {
		orgOptions() {
			return this.formItems[0].options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.$nextTick(() => {
						this.handleSearch()
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		async _apiGetPriceList_all() {
			const priceObj = this.formItems.find(item => item.prop === 'priceId')
			const { records } = await apiGetPriceList_all()

			if (priceObj) {
				priceObj.options = records.map(item => {
					return {
						label: item.priceName,
						value: item.priceId,
						...item,
					}
				})
			}
		},
		handleClickFakeSearch() {
			this.showSearchPopou = true
			this.getSearchData()
		},
		getSearchData() {
			const data = trimParams(removeNullParams(this.formData))
			const newArr = []
			Object.keys(data).forEach(key => {
				const obj = this.formItems.find(item => item.prop === key)
				if (key == 'archivesTime') {
					newArr.push({
						key,
						value: data[key].join('~'),
					})
				} else {
					if (obj && obj.options) {
						const selectItem = obj.options.find(subItem => subItem.value === data[key])
						newArr.push({
							key,
							value: selectItem.label,
						})
					} else {
						newArr.push({
							key,
							value: data[key],
						})
					}
				}
			})
			this.selectedCondition = newArr
		},
		clearFormComponent() {
			this.valueObj = { value: null }
			this.activeCondition = 'archivesIdentity'
		},
		deleteOne(key) {
			this.selectedCondition = this.selectedCondition.filter(item => item.key !== key)
			this.formData[key] = ''
		},
		deleteAll() {
			this.$refs.formRef.resetForm()
			this.selectedCondition = []
		},
		closeDialog(params) {
			this.valueObj = params
		},
		searchTable(params) {
			this.valueObj = params
			this.handleSearch()
		},
		handleReset() {
			this.$refs.formRef.resetFormSmooth()
			this.selectedCondition = []
			this.clearFormComponent()
			this.$emit('clearTable')
		},
		async handleSearch() {
			let flag = false
			let tip = ''
			if (
				!flag &&
				this.valueObj.key === 'userName' &&
				this.valueObj.value.length < 2 &&
				!isBlank(this.valueObj.value)
			) {
				flag = true
				tip = '用户名称搜索关键字请至少输入两位'
			}
			if (flag) return this.$message.error(tip)
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const formParams = trimParams(removeNullParams(this.formData))
			if (this.valueObj.value !== null) {
				formParams[this.valueObj.key] = this.valueObj.value
			}
			if (this.formData.archivesTime?.length > 0) {
				delete formParams.archivesTime
				formParams.createStartTime = this.formData.archivesTime[0]
				formParams.createEndTime = this.formData.archivesTime[1]
			}
			this.$emit('getParams', formParams)
		},
	},
}
</script>

<style lang="scss" scoped>
.left-container {
	display: flex;
	flex-direction: column;
	flex: 0 0 270px;
	margin-right: 20px;
	padding: 20px;
	background-color: #fff;
}
.left-search-container {
	margin-bottom: 20px;
}
.el-form {
	flex: 1;
	padding: 0 10px;
	overflow: auto;
}
.btn-group {
	flex: 0 0 52px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	.el-button {
		width: 50%;
		height: 30px;
	}
}
</style>
