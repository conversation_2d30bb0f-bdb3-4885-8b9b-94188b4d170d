# 复杂查询组件

通过预设的字段设定，生成复杂查询条件。目前仅支持`与`查询，不支持`或`查询。

## 组件使用

```vue
<template>
  <ComplexSearchPanel
    ref="complexSearchPanel"
    :fields="fields"
    :data="paramsSource"
    @change="handleParamsChange"
    />
</template>
<script>
import ComplexSearchPanel from 'complex-search-panel'
export default {
  components: {
    ComplexSearchPanel
  },
  data() {
    this.fields = [
      {
        "key": "archivesIdentity",
        "label": "表卡编号",
        "useBy": [
          "meter",
          "reading",
          "bill",
          "payment"
        ],
        "operator": [
          "=",
          "!=",
          "包含",
          "不包含"
        ],
        "input": {
          "type": "el-input",
          "attrs": {
            "clearable": true
          }
        }
      },
      {
        "key": "payTime",
        "label": "缴费时间",
        "useBy": [
          "payment"
        ],
        "operator": [
          "=",
          "!=",
          ">",
          ">=",
          "<",
          "<="
        ],
        "input": {
          "type": "el-date-picker",
          "attrs": {
            "format": "yyyy-MM-dd",
            "valueFormat": "yyyy-MM-dd",
            "placeholder": "选择日期",
            "clearable": true,
            "type": "datetime"
          }
        }
      },
      {
        "key": "userType",
        "label": "用户类型",
        "useBy": [
          "meter",
          "reading",
          "bill",
          "payment"
        ],
        "operator": [
          "=",
          "!="
        ],
        "input": {
          "type": "el-select",
          "options": [
            {
              "label": "企业",
              "value": 1
            },
            {
              "label": "居民",
              "value": 2
            }
          ],
          "attrs": {
            "multiple": false,
            "clearable": true,
            "filterable": true
          }
        }
      },
      {
        "key": "meterTypeId",
        "label": "水表类型",
        "useBy": [
          "meter",
          "reading",
          "bill",
          "payment"
        ],
        "operator": [
          "=",
          "!="
        ],
        "input": {
          "type": "el-select",
          "options": [],
          "attrs": {
            "multiple": false,
            "clearable": true,
            "filterable": true
          }
        },
        // 须动态查询并插入选项
        __putOptions: function (options) {
          this.input.options = options
        }
      },
    ]
    return {
      paramsSource: [
        {
          key: 'archivesIdentity',
          operator: '=',
          value: '123456789'
        },
        {
          key: 'payTime',
          operator: '=',
          value: '2020-01-01'
        },
        {
          key: 'userType',
          operator: '=',
          value: '1'
        },
        {
          key: 'meterTypeId',
          operator: '=',
          value: '1'
        }
      ]
    }
  },
  methods: {
    // 通过 change 事件获取查询参数配置
    handleParamsChange(params) {
      this.paramsSource = params
    }
  },
  mounted() {
    // 也可直接获取查询参数配置
    const paramsSource = this.$refs.complexSearchPanel.getData()
  }
}
</script>
```
