<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>

			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div v-has="'cpm_fines_add'" class="right-top">
				<el-button v-has="'cpm_fines_add'" type="primary" @click="handleAdd">新增罚没款</el-button>
			</div>
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:deal="{ row }">
					<el-button
						v-if="!row.billId || row.billStatus === 2"
						v-has="'cpm_fines_delete'"
						type="text"
						size="medium"
						@click="handleDelete(row)"
					>
						删除
					</el-button>
					<el-button
						v-if="row.billStatus !== 4"
						v-has="'cpm_fines_payment'"
						type="text"
						size="medium"
						@click="handlePay(row)"
					>
						缴费
					</el-button>
					<el-button
						v-if="row.billStatus === 4 && (row.invoiceStatus === 0 || row.invoiceStatus === 2)"
						v-has="'payment_invoice_open-invoice-fines'"
						type="text"
						size="medium"
						@click="handleOpenInvoice(row)"
					>
						开票
					</el-button>
				</template>
			</GcTable>
		</div>

		<!-- 新增弹窗 -->
		<UpdateDialog ref="updateDialogRef" :show.sync="showUpdate" @success="getList(1)" />
		<!-- 开票 -->
		<PaymentInvoiceDialog
			ref="openInvoiceDialog"
			:billList="billList"
			:show.sync="openInvoiceDialogShow"
			:queryExtraParams="queryInvoiceExtraParams"
			:showRemarkAddress="false"
			@success="handleOpenInvoiceSuccess"
			@submit="handleOpenInvoiceSubmit"
		/>
	</div>
</template>

<script>
import { getColumn } from './tableColumn'
import { RULE_PHONE } from '@/utils/rules'
import { queryFinesPage, deleteFine, createBill } from '@/api/specialWater.api'
import UpdateDialog from './components/UpdateDialog.vue'
import PaymentInvoiceDialog from '@/components/PaymentInvoiceDialog'
import { singleOpenInvoiceFines } from '@/api/costManage.api'

export default {
	name: 'Fine',
	components: { UpdateDialog, PaymentInvoiceDialog },
	data() {
		return {
			// 左侧查询
			formData: {
				orgCode: '',
				registerDateRange: [],
				payDateRange: [],
				userName: '',
				archivesNo: '',
				phoneNum: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						col: 24,
						clearable: false,
						placeholder: '请选择营业分公司',
					},
				},
				{
					type: 'el-date-picker',
					label: '登记日期',
					prop: 'registerDateRange',
					attrs: {
						col: 24,
						type: 'daterange',
						valueFormat: 'yyyy-MM-dd',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
					},
				},
				{
					type: 'el-date-picker',
					label: '缴费日期',
					prop: 'payDateRange',
					attrs: {
						col: 24,
						type: 'daterange',
						valueFormat: 'yyyy-MM-dd',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
					},
				},
				{
					type: 'el-input',
					label: '用户名称',
					prop: 'userName',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入用户名称',
					},
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesNo',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
				{
					type: 'el-input',
					label: '手机号码',
					prop: 'phoneNum',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入手机号码',
					},
				},
			],
			formAttrs: {
				rules: {
					orgCode: {
						required: true,
						message: '请选择营业分公司',
						trigger: 'change',
					},
					phone: RULE_PHONE,
				},
			},
			// 右侧列表
			loading: false,
			columns: getColumn(this),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 新建弹窗
			showUpdate: false,

			// 缴费弹窗
			showPayment: false,
			billList: [],
			openInvoiceDialogShow: false,
			queryInvoiceExtraParams: {},
		}
	},
	computed: {
		orgOptions() {
			return this.formItems[0].options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
				}
			},
			deep: true,
			immediate: true,
		},
	},
	activated() {
		if (!this.formData.orgCode) return
		this.$nextTick(() => {
			this.handleSearch()
		})
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetFields()
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			this.billList = []
			try {
				const { current, size } = this.pageData
				const { registerDateRange, payDateRange, ...rest } = this.formData
				const { total = 0, records = [] } = await queryFinesPage({
					size,
					current,
					...rest,
					startRegisterDate: registerDateRange?.length > 0 ? registerDateRange[0] : '',
					endRegisterDate: registerDateRange?.length > 0 ? registerDateRange[1] : '',
					startPayDate: payDateRange?.length > 0 ? payDateRange[0] : '',
					endPayDate: payDateRange?.length > 0 ? payDateRange[1] : '',
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},

		// 新增罚没款
		handleAdd() {
			this.showUpdate = true
		},
		// 删除
		handleDelete(row) {
			this.$confirm('确定要删除吗？').then(async () => {
				await deleteFine({ recordId: row.recordId })
				this.$message.success('删除成功')
				this.getList(1)
			})
		},
		// 缴费
		async handlePay(row) {
			if (!row.priceId) {
				return this.$message.error('当前价格为空，请重新创建罚没款缴费')
			}
			let billId = row.billId
			if (!billId) {
				// 创建账单
				billId = await createBill({
					archivesId: row.archivesId,
					archivesNo: row.archivesNo,
					recordId: row.recordId,
				})
			}
			this.$router.push({
				path: '/costManage/paymentPage',
				query: {
					ids: billId,
				},
			})
		},
		handleOpenInvoice(row) {
			this.openInvoiceDialogShow = true
			this.queryInvoiceExtraParams = {
				recordId: row.recordId,
			}
			this.billList = [row]
		},
		handleOpenInvoiceSuccess() {
			this.handleSearch()
			this.openInvoiceDialogShow = false
		},
		handleOpenInvoiceSubmit({ invoiceInfo, loading }) {
			const { phoneNumber, ...rest } = invoiceInfo
			const data = {
				finesRecordId: this.billList[0].recordId,
				...rest,
				userMobile: phoneNumber,
			}
			singleOpenInvoiceFines(data)
				.then(() => {
					this.$message.success('开票成功')
					this.handleSearch()
					this.openInvoiceDialogShow = false
				})
				.catch(e => {
					console.error(e)
					this.$message.error(e.message || '开票失败')
				})
				.finally(() => {
					if (loading) loading.close()
				})
		},
	},
}
</script>

<style lang="scss" scoped>
.right-top {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-bottom: 12px;
}
</style>
