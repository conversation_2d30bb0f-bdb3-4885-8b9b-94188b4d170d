export function getFormItems(_this) {
	return [
		{
			type: 'slot',
			slotName: 'dmaArchivesIdentity',
			label: '表卡编号',
			prop: 'dmaArchivesIdentity',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-select',
			label: 'DMA表类型',
			prop: 'dmaArchivesType',
			options:
				_this.$store.getters?.dataList?.dmaArchivesType?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '监控栋数',
			prop: 'dmaMonitorBuildingNum',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '地址',
			prop: 'dmaAddress',
			attrs: {
				col: 10,
			},
		},
		{
			type: 'el-input',
			label: '流量计口径',
			prop: 'caliber',
			attrs: {
				col: 6,
			},
		},
		{
			type: 'el-input',
			label: '站号',
			prop: 'dmaStationNo',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '备注',
			prop: 'dmaRemark',
			attrs: {
				col: 12,
				type: 'textarea',
				placeholder: '请输入备注',
				maxlength: '255',
				showWordLimit: true,
				autosize: {
					minRows: 4,
					maxRows: 8,
				},
			},
		},
		{
			type: 'el-input',
			label: '监控范围',
			prop: 'dmaMonitoringRange',
			attrs: {
				col: 12,
				type: 'textarea',
				placeholder: '请输入监控范围',
				maxlength: '255',
				showWordLimit: true,
				autosize: {
					minRows: 4,
					maxRows: 8,
				},
			},
		},
	]
}
