@mixin gradient-color-blue {
  // 主渐变色
  background: linear-gradient(135deg, #4d9aff 0%, #1771e6 100%);
}
@mixin gradient-color-yellow {
  background: linear-gradient(135deg, #f5da94 0%, #e5a410 100%);
}
@mixin gradient-color-light-blue {
  background: linear-gradient(135deg, #1ec8d9 0%, #209ae5 100%);
}
@mixin gradient-color-purple {
  background: linear-gradient(135deg, #789fff 0%, #3565df 100%);
}
@mixin gradient-color-green {
  background: linear-gradient(135deg, #92d2ca 0%, #59c7b9 100%);
}

// 卡片样式
@mixin base-card() {
  background: #ffffff;
  box-shadow: 0px 6px 23px 0px rgba(0, 0, 0, 0.03);
  border-radius: 4px;
}

// 默认阴影，$dir: "right" 设置当前元素的阴影处于右侧
@mixin base-box-shadow(
  $dir: "right",
  $size: "2px",
  $blur: "4px",
  $color: "rgba(41, 46, 51, 0.1)"
) {
  @if $dir == "right" {
    box-shadow: $size 0px $blur 0px $color;
  } @else if $dir == "bottom" {
    box-shadow: 0px $size $blur 0px $color;
  } @else {
    box-shadow: $size $size $blur 0px $color;
  }
}

// 默认滚动条，$offset: 4px 设置滚动条距离右侧偏移量为4px
@mixin base-scrollbar($offset: 0) {
  // 滚动条背景，宽高
  &::-webkit-scrollbar {
    width: 8px + $offset;
    height: 8px;
    background-color: #f5f5f5;
  }
  // 滚动条轨道
  &::-webkit-scrollbar-track {
    border: none;
    background-color: #fff;
  }
  // 滚动条滑块
  &::-webkit-scrollbar-thumb {
    border-radius: 4px;
    cursor: pointer;
    background: rgba($base-color-scrollbar,1);
    box-shadow: -$offset 0 0 #fff inset;
    background-clip: padding-box;
    border-color: transparent;
    border-width: 1px;// 实际滚动条的宽度是 8-2*1 即 6px
    border-style: dashed;
  }
  &::-webkit-scrollbar-thumb:hover {
    border-width: 0;
    background-color: rgba($base-color-scrollbar, 1);
  }
}

@mixin flex-center() {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin text-overflow() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 统一字体加粗
@mixin base-bold($size: 18) {
  color: #444;
  font-size: $size + px;
  font-weight: 600;
}
