<template>
	<div class="comWrap">
		<!-- 不同搜索条件下展示不同的档案卡片 -->
		<p class="curFilterName">
			{{ item[curTab] }}
			<el-button
				round
				size="mini"
				class="perfectBtn"
				v-if="item.archivesStatus == 4"
				@click="gotoPerfect(item.archivesId)"
			>
				去完善档案
			</el-button>
			<el-button round size="mini" class="detailBtn" v-else @click="gotoDetail(item.archivesId)">
				去档案详情
			</el-button>
		</p>
		<p class="otherName" v-for="it in otherNames" :key="it.key">{{ it['label'] }}：{{ item[it['key']] || '--' }}</p>
	</div>
</template>

<script>
import { searchKeys as filterNames } from '@/consts/search'
export default {
	name: 'ArchiveTempCard',
	components: {},
	props: {
		curTab: String,
		item: Object,
	},
	data() {
		return {
			filterNames,
		}
	},
	computed: {
		otherNames() {
			return this.filterNames.filter(it => !it.isHide && it.key !== this.curTab)
		},
	},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		gotoPerfect() {
			this.$router.push('/archives/perfect?archivesId=' + this.item.archivesId)
			this.$emit('close')
		},
		gotoDetail() {
			this.$router.push('/archives/detail?archivesId=' + this.item.archivesId)
			this.$emit('close')
		},
	},
}
</script>
<style lang="scss" scoped>
.comWrap {
	flex: 1;
}
.curFilterName {
	font-size: 14px;
	font-weight: 500;
	color: #2f87fe;
	line-height: 21px;
	overflow: hidden;
	.el-button {
		float: right;
		&.perfectBtn {
			color: #ff9d57;
			border-color: #ff9d57;
		}
		&.detailBtn {
			color: #2f87fe;
			border-color: #2f87fe;
		}
	}
}
.otherName {
	font-size: 12px;
	font-weight: 400;
	color: #999999;
	line-height: 18px;
}
</style>
