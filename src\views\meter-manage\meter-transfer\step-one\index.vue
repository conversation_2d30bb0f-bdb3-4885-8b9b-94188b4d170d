<template>
	<div class="step-one-wrapper">
		<div
			v-for="(item, index) in list"
			:key="index"
			class="card"
			:class="{
				selected: selectedType === item.type,
			}"
			@click="selectCard(item.type)"
		>
			<img v-show="selectedType === item.type" class="done" src="@/assets/images/pic/done.png" />
			<div class="before">
				<div class="label">
					{{ item.before.label }}
				</div>
				<div class="icon">
					<img :src="selectedType === item.type ? item.before.activeIcon : item.before.icon" />
				</div>
			</div>
			<img :src="selectedType === item.type ? item.arrow.activeIcon : item.arrow.icon" />
			<div class="after">
				<div class="label">
					{{ item.after.label }}
				</div>
				<div class="icon">
					<img :src="selectedType === item.type ? item.after.activeIcon : item.after.icon" />
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'TransferType',
	props: {
		detail: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			selectedType: 4,
		}
	},
	computed: {
		list() {
			const juminIcon = require('@/assets/images/pic/jumin.png')
			const juminActiveIcon = require('@/assets/images/pic/jumin-active.png')
			const qiyeIcon = require('@/assets/images/pic/qiye.png')
			const qiyeActiveIcon = require('@/assets/images/pic/qiye-active.png')
			const arrowIcon = require('@/assets/images/pic/arrow.png')
			const arrowActiveIcon = require('@/assets/images/pic/arrow-active.png')
			const jumin = [
				{
					before: {
						label: '居民表卡',
						icon: juminIcon,
						activeIcon: juminActiveIcon,
					},
					arrow: {
						icon: arrowIcon,
						activeIcon: arrowActiveIcon,
					},
					after: {
						label: '企业表卡',
						icon: qiyeIcon,
						activeIcon: qiyeActiveIcon,
					},
					type: 4,
				},
			]
			const qiye = [
				{
					before: {
						label: '企业表卡',
						icon: qiyeIcon,
						activeIcon: qiyeActiveIcon,
					},
					arrow: {
						icon: arrowIcon,
						activeIcon: arrowActiveIcon,
					},
					after: {
						label: '企业表卡',
						icon: qiyeIcon,
						activeIcon: qiyeActiveIcon,
					},
					type: 4,
				},
				{
					before: {
						label: '企业表卡',
						icon: qiyeIcon,
						activeIcon: qiyeActiveIcon,
					},
					arrow: {
						icon: arrowIcon,
						activeIcon: arrowActiveIcon,
					},
					after: {
						label: '居民表卡',
						icon: juminIcon,
						activeIcon: juminActiveIcon,
					},
					type: 3,
				},
			]
			const userType = this.detail?.user?.userType
			return userType === 3 ? jumin : qiye
		},
	},
	methods: {
		selectCard(type) {
			this.selectedType = type
		},
	},
}
</script>

<style lang="scss" scoped>
.step-one-wrapper {
	flex-basis: 200px;
	display: flex;
	justify-content: center;
	gap: 40px;
}

.card {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15px;
	width: 320;
	height: 168px;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	padding: 20px;
	cursor: pointer;
	transition: background-color 0.3s;
	background: #f4f5fb;
}
.done {
	position: absolute;
	top: 2px;
	right: 2px;
}

.before,
.after {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	width: 100px;
	height: 120px;
	padding: 12px 13.5px 20px 14.5px;
	border-radius: 4px 0px 0px 0px;
	background: #ffffff;
}

.icon {
	text-align: center;
}

.label {
	font-family: Source Han Sans CN;
	font-size: 18px;
	font-weight: 500;
	color: #5f627d;
}

.selected {
	border-color: #007bff;
	background: #e8f3ff;
	.label {
		color: #2f87fe;
	}
}
</style>
