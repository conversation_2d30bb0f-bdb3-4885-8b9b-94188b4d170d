import service from './request'
import { Message } from 'element-ui'

// 报表导出公共方法
export function exportReport(data, type, others, loading) {
	service({
		method: data.method,
		url: data.url,
		data: data.data,
		responseType: 'blob',
		timeout: 300000,
		...others,
	})
		.then(res => {
			if (loading) {
				loading.close && loading.close()
				loading.clear && loading.clear()
			}
			const link = document.createElement('a')
			if (type == 'pdf') {
				let blob = new Blob([res], { type: 'application/pdf' })
				link.style.display = 'none'
				link.href = URL.createObjectURL(blob)
				// link.download = res.headers['content-disposition'] //下载后文件名
				link.download = data.fileName //下载的文件名
				document.body.appendChild(link)
				link.click()
				document.body.removeChild(link)
			} else if (type == 'excel') {
				let blob = new Blob([res], { type: 'application/vnd.ms-excel' })
				link.style.display = 'none'
				link.href = URL.createObjectURL(blob)
				// link.download = res.headers['content-disposition'] //下载后文件名
				link.download = data.fileName //下载的文件名
				document.body.appendChild(link)
				link.click()
				document.body.removeChild(link)
			}
		})
		.catch(() => {
			if (loading) {
				loading.close && loading.close()
				loading.clear && loading.clear()
			}
			// console.log(`导出报表失败-${error}`);
			Message.error('导出报表失败')
		})
}
// 获取组织机构详情-系统级
export const apiGetOrgDetail_sys = params => {
	return service({
		url: '/v1/tos/organization/sys/detail',
		method: 'get',
		params: params,
	})
}

export const apiGetOrgDetail = params => {
	return service({
		url: 'v1/tos/organization/detail',
		method: 'get',
		params: params,
	})
}

//获取所有价格包含禁用
export const apiGetPriceList_all = params => {
	return service({
		url: '/cpm/prices/list-price',
		method: 'get',
		params: params,
	})
}

//查询表具类型
export const apiGetMeterTypeList = params => {
	return service({
		url: '/cpm/device-type/list',
		method: 'get',
		params: params,
	})
}

// 获取员工列表
export const apiGetStaffList = params => {
	return service({
		url: '/v1/tos/staff/list',
		method: 'get',
		params: params,
	})
}

// 查询租户列表
export const apiGetTenantList = params => {
	return service({
		url: 'v1/tos/tenant/list',
		method: 'get',
		params: params,
	})
}

// 获取租户组织机构树
export const apiGetOrgTree = (params, others) => {
	let obj = {
		url: '/v1/tos/organization/tree',
		method: 'get',
		params: params,
	}
	if (others && others.constructor === Object) {
		obj = { ...obj, ...others }
	}
	return service(obj)
}

// 获取租户组织机构结构列表
export const apiGetOrgStructure = (params, others) => {
	let obj = {
		url: '/v1/tos/orgstruture/list',
		method: 'get',
		params: params,
	}
	if (others && others.constructor === Object) {
		obj = { ...obj, ...others }
	}
	return service(obj)
}
