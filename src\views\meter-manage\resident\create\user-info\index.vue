<template>
	<div class="container-wrapper">
		<GcModelHeader
			class="info-title"
			title="用户信息"
			:icon="require('@/assets/images/icon/title-common-parameters.png')"
		>
			<template slot="right">
				<Search :userType="3" :active-tab="{ id: 2 }" @use="handleSearchUser" />
			</template>
		</GcModelHeader>
		<div class="container">
			<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<template v-slot:otherInfo>
					<h5 class="gap-title">开票信息</h5>
				</template>
				<template v-slot:contract>
					<GcUploadFile v-model="formData.purchaseContractUrl" />
				</template>
				<template v-slot:otherMobile>
					<el-form-item class="other-mobile" label="其他手机" prop="otherMobile">
						<AddOtherMobile v-model="formData.otherMobile" :mobileList.sync="formData.mobileList" />
					</el-form-item>
				</template>
				<template v-slot:taxpayerIdentity>
					<el-autocomplete
						style="width: 100%"
						v-model="formData.taxpayerIdentity"
						:fetch-suggestions="queryTaxpayerIdentity"
						placeholder="请输入"
						@select="handleTaxpayerIdentitySelect"
						@change="handleTaxpayerIdentityChange"
					>
						<template slot-scope="{ item }">
							<div class="billing-information-item">
								<p class="billing-information-item-row">纳税人识别号：{{ item.taxpayerIdentity }}</p>
								<p class="billing-information-item-row">开票抬头：{{ item.userName }}</p>
							</div>
						</template>
					</el-autocomplete>
				</template>
			</GcFormRow>
			<div class="button-group">
				<button class="gc-button gc-button-three" type="button" @click="handleReset">重 置</button>
				<button class="gc-button gc-button-two" type="button" @click="changeTab('addressChoose')">
					上一项
				</button>
				<button class="gc-button gc-button-two" type="button" @click="changeTab('meterInfo')">下一项</button>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash'
import getFormItems from './userForm.js'
import AddOtherMobile from '../../../components/AddOtherMobile'
import {
	ruleRequired,
	RULE_INTEGERONLY,
	RULE_PHONE,
	RULE_INCORRECTEMAIL,
	ruleMaxLength,
	RULE_INT_ENGLISH,
	RULE_INCORRECTIDCARD,
	RULE_POSTALCODE,
} from '@/utils/rules'
import Search from '@/views/meter-manage/view/components/search'
import { apiQueryInvoiceBuyer } from '@/api/userManage.api'
export default {
	name: '',
	props: {
		activeTab: {
			type: String,
			default: '',
		},
		detailData: {
			type: Object,
			default: () => {},
		},
	},
	components: { AddOtherMobile, Search },
	data() {
		return {
			formData: {
				userName: '',
				nameUsedBefore: '',
				contractNum: '',
				contactPeople: '',
				userMobile: '',
				contactPhone: '',
				households: '',
				resiPopulation: '',
				propertyOwner: '',
				zipCode: '',
				certificateNo: '',
				certificateType: '',
				otherCertificateNo: '',
				email: '',
				userSubType: '',
				chargingMethod: '',
				mailingAddress: '',
				otherMobile: '', // 其他手机号
				mobileList: [], // 其他手机号
				purchaseContractUrl: [],
				taxpayerIdentity: '',
				openBank: '',
				bankAccount: '',
				userId: '',
				buyerName: '',
			},
			billingInfoDisabled: false,
			// formItems: getFormItems(this),
			formAttrs: {
				labelWidth: '110px',
				labelPosition: 'right',
				rules: {
					userName: [ruleRequired('必填'), ruleMaxLength(32)],
					userMobile: [RULE_PHONE],
					chargingMethod: [ruleRequired('必填')],
					nameUsedBefore: [ruleMaxLength(32)],
					contractNum: [ruleMaxLength(64)],
					contactPeople: [ruleMaxLength(64)],
					contactPhone: [ruleMaxLength(32)],
					households: [
						{
							pattern: /^(?:[0-9]{1,2}|30)$/,
							message: '请输入0-30的整数',
							trigger: '',
						},
					],
					resiPopulation: [
						{
							pattern: /^(?:[0-9]{1,2}|64)$/,
							message: '请输入0-64的整数',
							trigger: '',
						},
					],
					propertyOwner: [ruleMaxLength(32)],
					zipCode: [RULE_POSTALCODE],
					certificateNo: [RULE_INCORRECTIDCARD],
					otherCertificateNo: [ruleMaxLength(32)],
					email: [RULE_INCORRECTEMAIL],
					mailingAddress: [ruleMaxLength(64)],
					otherMobile: [RULE_PHONE],
					openBank: [ruleMaxLength(32)],
					taxpayerIdentity: [RULE_INT_ENGLISH, ruleMaxLength(20)],
					bankAccount: [RULE_INTEGERONLY, ruleMaxLength(32)],
				},
			},
		}
	},
	computed: {
		formItems() {
			return getFormItems(this)
		},
	},
	watch: {
		formData: {
			handler: 'validateForm',
			deep: true,
		},
		detailData: {
			handler(v) {
				if (v) {
					this.assignForm(v)
				}
			},
			deep: true,
		},
	},
	created() {
		this.queryTaxpayerIdentity = _.debounce(this.queryTaxpayerIdentity, 250)
	},
	methods: {
		assignForm(obj) {
			this.formData = Object.assign(this.formData, obj)
			// 其他手机号回显
			this.formData.mobileList = obj.otherContactPhone ? obj.otherContactPhone.split(',') : []
			// 购房合同回显
			this.formData.purchaseContractUrl = obj.purchaseContractUrl ? JSON.parse(obj.purchaseContractUrl) : []
		},
		async validateForm() {
			const valid = await this.$refs.formRef.validate()
			this.$emit('getValid', 'userInfo', valid)
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.validateForm()
		},
		changeTab(v) {
			this.$emit('changeTab', v)
		},
		handleSearchUser(obj) {
			this.assignForm(obj)
		},
		async queryTaxpayerIdentity(taxpayerIdentity, callback) {
			taxpayerIdentity = taxpayerIdentity || ''
			try {
				let result = await apiQueryInvoiceBuyer({
					taxpayerIdentity,
				})
				result = result || []
				const list = result.map(item => {
					const { taxpayerIdentity } = item
					return { ...item, value: taxpayerIdentity }
				})

				callback(list)
			} catch (e) {
				console.log(e)
				callback([])
			}
		},
		handleTaxpayerIdentitySelect(item) {
			const { userName, openBank, bankAccount } = item
			this.formData.openBank = openBank
			this.formData.buyerName = userName
			this.formData.bankAccount = bankAccount
			this.billingInfoDisabled = true
		},
		handleTaxpayerIdentityChange() {
			this.billingInfoDisabled = false
		},
	},
	mounted() {
		this.validateForm()
	},
}
</script>

<style lang="scss" scoped>
.container-wrapper {
	height: 100%;
}
.container {
	padding-right: 20px;
	height: calc(100% - 60px);
	overflow-y: auto;
	overflow-x: hidden;
}
.gap-title {
	padding: 0 20px;
	color: #222222;
	font-size: 14px;
	font-weight: bold;
}
.button-group {
	margin-top: 20px;
	padding: 0 20px;
	.gc-button {
		margin-right: 8px;
	}
}
.other-mobile {
	::v-deep {
		.el-form-item__error {
			position: absolute;
			top: 36px;
		}
	}
}
.billing-information-item {
	padding: 8px 0;
}
</style>
