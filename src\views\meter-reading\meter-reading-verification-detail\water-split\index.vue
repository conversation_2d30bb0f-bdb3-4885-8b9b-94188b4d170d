<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-11 15:00:52
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 16:08:20
-->
<template>
	<div class="wrapper">
		<div class="left">
			<RealMeter :type="type" @click="handleRowClick" />
		</div>
		<div class="right">
			<VirtualMeter :data="meterRowData" />
		</div>
	</div>
</template>

<script>
import RealMeter from './real-meter/index.vue'
import VirtualMeter from './virtual-meter/index.vue'
export default {
	name: '',
	props: {
		type: Number,
	},
	components: { RealMeter, VirtualMeter },
	data() {
		return {
			meterRowData: null,
		}
	},
	computed: {},
	created() {},
	methods: {
		handleRowClick(data) {
			this.meterRowData = data
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	flex: 1;
	display: flex;
	height: 0;
	gap: 10px;
	.left {
		width: 0;
		flex: 1;
	}
	.right {
		width: 0;
		flex: 1;
	}
}
</style>
