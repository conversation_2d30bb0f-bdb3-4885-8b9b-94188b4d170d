<template>
	<gc-el-dialog
		:show="isShow"
		title="选择企业"
		custom-top="120px"
		width="960px"
		@open="handleOpen"
		@close="handleClose"
		:showFooter="false"
	>
		<div class="search-container">
			<el-select v-model="formData.searchKey" placeholder="请选择">
				<el-option label="企业编号" value="enterpriseNumber"></el-option>
				<el-option label="企业名称" value="enterpriseName"></el-option>
			</el-select>
			<el-input v-model="formData.searchValue" placeholder="请输入"></el-input>
			<gc-button @click.native="handleSearch">查询</gc-button>
		</div>

		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			showPage
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			@current-page-change="handleChangePage"
		>
			<template v-slot:operate="{ row }">
				<el-button type="text" @click="handleSelect(row)">选择</el-button>
			</template>
		</GcTable>
	</gc-el-dialog>
</template>

<script>
import { queryEnterprisePage } from '@/api/userManage.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		orgCode: {
			type: String,
			default: '',
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				searchKey: 'enterpriseNumber',
				searchValue: '',
				orgCode: '',
			},
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'enterpriseNumber',
					name: '企业编号',
					tooltip: true,
				},
				{
					key: 'enterpriseName',
					name: '企业名称',
					tooltip: true,
				},
				{
					key: 'enterpriseAddress',
					name: '企业地址',
					tooltip: true,
				},
				{
					key: 'operate',
					name: '操作',
					width: 100,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	created() {},
	methods: {
		handleSearch() {
			this.pageData.current = 1
			this.getList()
		},
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = {
					[this.formData.searchKey]: this.formData.searchValue,
					orgCode: this.orgCode,
				}
				Object.assign(formParams, {
					current,
					size,
				})
				const { records, total } = await queryEnterprisePage(formParams)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleSelect(row) {
			this.$emit('select', {
				id: row.id,
				enterpriseNumber: row.enterpriseNumber,
				enterpriseName: row.enterpriseName,
				collectionAccountId: row.collectionAccountId,
				chargingMethod: row.chargingMethod,
			})
			this.isShow = false
		},
		handleOpen() {
			this.getList()
		},
		handleClose() {
			this.formData = {
				searchKey: 'enterpriseNumber',
				searchValue: '',
				orgCode: '',
			}
			this.tableData = []
			this.isShow = false
		},
	},
}
</script>
<style lang="scss" scoped>
.search-container {
	display: flex;
	align-items: center; // 上下居中
	gap: 16px; // 元素之间留白
	margin: 0 0 16px 0;
	.el-input {
		flex: 1; // 输入框撑满剩余空间
	}
}

.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.el-dialog__body {
		height: 560px;
	}
	.el-select {
		width: 160px !important;
	}
	.gc-table {
		height: calc(100% - 48px);
	}
}

.data-box {
	.data-item {
		display: flex;
		align-items: center;
		margin-bottom: 12px;
		.label {
			width: 100px;
			margin-right: 10px;
			line-height: 1.5;
		}
		.value {
			flex: 1;
		}
	}
}
.el-tag {
	margin: 2px 0;
	& + .el-tag {
		margin-left: 4px;
	}
}
</style>
