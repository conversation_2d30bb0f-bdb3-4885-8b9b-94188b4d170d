<template>
	<div class="step-five-wrapper">
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:archivesIdentity>
				<el-input v-model="formData.archivesIdentity" placeholder="请输入" @input="debouncedInputHandler">
					<img slot="append" src="@/assets/images/icon/get-num.svg" @click="_apiGetArchivesIdentity" />
				</el-input>
			</template>
			<template v-slot:natureName>
				<span>{{ formData.natureName }}</span>
			</template>
			<template v-slot:billingType>
				<span>{{ formData.billingType }}</span>
			</template>
		</GcFormRow>
	</div>
</template>

<script>
import _ from 'lodash'
import { getfilterName } from '@/utils'
import { apiEffectivePrice, apiGetArchivesIdentity, apiVerifyArchives } from '@/api/meterManage.api.js'
import { ruleRequired, RULE_INTEGERONLY } from '@/utils/rules'
export default {
	props: {
		userType: {
			type: Number,
			default: 3,
		},
	},
	data() {
		return {
			priceNameList: [],
			priceCodeList: [],
			formData: {
				archivesIdentity: '',
				priceId: '',
				priceName: '',
				priceCode: '',
				natureName: '',
				billingType: '',
			},
			formAttrs: {
				labelWidth: '140px',
				labelPosition: 'right',
				rules: {
					archivesIdentity: [ruleRequired('必填'), RULE_INTEGERONLY],
					priceName: [ruleRequired('必填')],
					priceCode: [ruleRequired('必填')],
				},
			},
			params: {
				orgCode: '',
				bookId: '',
				alleyCode: '',
				enterpriseNumber: '',
			},
		}
	},
	computed: {
		formItems() {
			return [
				{
					type: 'slot',
					label: '表卡编号',
					prop: 'archivesIdentity',
					slotName: 'archivesIdentity',
					attrs: {
						col: 16,
					},
				},
				{
					type: 'el-select',
					label: '价格名称',
					prop: 'priceName',
					options: this.priceNameList,
					attrs: {
						col: 16,
					},
					events: {
						change: this.handleChangePrice,
					},
				},
				{
					type: 'el-select',
					label: '价格编号',
					prop: 'priceCode',
					options: this.priceCodeList,
					attrs: {
						filterable: true,
						col: 16,
					},
					events: {
						change: this.handleChangePrice,
					},
				},
				{
					type: 'slot',
					label: '用水性质',
					prop: 'natureName',
					slotName: 'natureName',
					attrs: {
						col: 16,
					},
				},
				{
					type: 'slot',
					label: '计费类型',
					prop: 'billingType',
					slotName: 'billingType',
					attrs: {
						col: 16,
					},
				},
			]
		},
	},
	watch: {
		formData: {
			handler: 'validateForm',
			deep: true,
		},
	},
	methods: {
		debouncedInputHandler: _.debounce(async function () {
			const valid = await this.$refs.formRef.validateField('archivesIdentity')
			if (valid !== '') return
			try {
				await apiVerifyArchives({
					archivesIdentity: this.formData.archivesIdentity,
				})
			} catch (error) {
				this.$message.error(error.message)
			}
		}, 500),
		setArchivesIdentityRule() {
			// 清除之前的校验规则
			this.$refs.formRef.clearValidate()
			// 根据用户类型动态设置表卡编号校验规则
			if (this.userType === 3) {
				// 居民表卡编号校验规则：前三位必须是坊别code，总共9位
				const alleyCode = this.params.alleyCode
				this.setRuleForArchivesIdentity(alleyCode, 6, `必须为9位, 且前三位必须为${alleyCode}`)
			} else {
				// 企业表卡编号校验规则：企业编号 + 可输入四位，总共11位
				const enterpriseNumber = this.params.enterpriseNumber
				this.setRuleForArchivesIdentity(enterpriseNumber, 4, `必须为11位, 且前七位必须为${enterpriseNumber}`)
			}
			// 执行表单验证
			this.validateForm()
		},
		// 动态生成表卡编号校验规则的方法
		setRuleForArchivesIdentity(prefix, length, message) {
			this.formAttrs.rules.archivesIdentity[2] = {
				pattern: new RegExp(`^${prefix}\\d{${length}}$`),
				message,
				trigger: '',
			}
		},
		// 获取表卡编号
		async _apiGetArchivesIdentity() {
			try {
				const { bookId, orgCode, enterpriseNumber } = this.params
				let params = {
					bookId,
					orgCode,
					userType: this.userType,
				}
				if (this.userType === 4) {
					params.enterpriseNumber = enterpriseNumber
				}
				const { archivesIdentity } = await apiGetArchivesIdentity(params)
				this.formData.archivesIdentity = archivesIdentity
				this.$message.success('获取表卡编号成功')
			} catch (error) {
				console.log(error)
			}
		},
		// 获取价格编号
		async _apiEffectivePrice() {
			const data = await apiEffectivePrice()
			this.priceNameList = data.map(item => {
				return {
					label: item.priceName,
					value: item.priceId,
					...item,
				}
			})
			this.priceCodeList = data.map(item => {
				return {
					label: item.priceCode,
					value: item.priceId,
					...item,
				}
			})
		},
		handleChangePrice(v) {
			if (v) {
				const billingType = this.$store.getters.dataList.billingType
				const priceObj = this.priceNameList.find(item => item.priceId === v)
				this.formData.priceName = priceObj.priceName
				this.formData.priceCode = priceObj.priceCode
				this.formData.priceId = priceObj.priceId
				this.formData.natureName = priceObj.natureName
				this.formData.billingType = getfilterName(billingType, priceObj.billingTypeId, 'sortValue', 'sortName')
			} else {
				this.formData.priceName = ''
				this.formData.priceCode = ''
				this.formData.priceId = ''
				this.formData.natureName = ''
				this.formData.billingType = ''
			}
		},
		handleReset() {
			this.$refs.formRef.resetFields()
		},
		async validateForm() {
			const valid = await this.$refs.formRef.validate()
			return valid
		},
	},
	mounted() {
		this._apiEffectivePrice()
	},
}
</script>

<style lang="scss" scoped>
.step-five-wrapper {
	flex: 1;
	width: 800px;
}
::v-deep {
	.el-row {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
	.el-input-group__append {
		padding: 0;
		img {
			display: block;
			padding: 0 10px;
			height: 30px;
			object-fit: none;
			cursor: pointer;
		}
	}
}
</style>
