<template>
	<div class="wrapper">
		<div class="right-top">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" @click="handleSearch(false)">筛选</el-button>
					<el-button @click="handleReset">重置</el-button>
				</el-form-item>
			</GcFormSimple>
			<el-button v-has="'cpm_bill_item_add'" type="primary" @click="handleAdd">新增账项</el-button>
		</div>
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			@current-page-change="handlePageChange"
		>
			<template #itemType="{ row }">
				<span>{{ getfilterNameFn($store.getters.dataList.billItemType, row.itemType) }}</span>
			</template>
			<template #generateType="{ row }">
				<span>{{ getfilterNameFn($store.getters.dataList.expenseGenerateWay, row.generateType) }}</span>
			</template>
			<template #isSurcharge="{ row }">
				<span>{{ row.isSurcharge === 1 ? '是' : '否' }}</span>
			</template>
			<template #itemStatus="{ row }">
				<span>{{ row.itemStatus === 1 ? '在用' : '停用' }}</span>
			</template>
			<template v-slot:deal="{ row }">
				<el-button v-has="'cpm_bill_item_modify'" type="text" size="medium" @click="handleAdjust(row)">
					修改
				</el-button>
			</template>
		</GcTable>

		<!-- 新增、编辑弹窗 -->
		<UpdateDialog ref="updateDialogRef" :show.sync="showUpdate" :editType="editType" @success="getList(1)" />
	</div>
</template>

<script>
import { getfilterName } from '@/utils'
import UpdateDialog from './components/UpdateDialog.vue'
import { queryBillItemList } from '@/api/basicConfig.api'

export default {
	name: 'AccountMaintenance',
	components: { UpdateDialog },
	data() {
		return {
			formData: {
				itemType: '',
				itemName: '',
				generateType: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '账项类别',
					prop: 'itemType',
					options:
						this.$store.getters?.dataList?.billItemType?.map(item => {
							return {
								label: item.sortName,
								value: Number(item.sortValue),
							}
						}) || [],
					attrs: {
						clearable: true,
						placeholder: '请选择账项类别',
					},
				},
				{
					type: 'el-input',
					label: '账项名称',
					prop: 'itemName',
					attrs: {
						clearable: true,
						placeholder: '请输入账项名称',
					},
				},
				{
					type: 'el-select',
					label: '生成方式',
					prop: 'generateType',
					options:
						this.$store.getters?.dataList?.expenseGenerateWay?.map(item => {
							return {
								label: item.sortName,
								value: Number(item.sortValue),
							}
						}) || [],
					attrs: {
						clearable: true,
						placeholder: '请选择生成方式',
					},
				},
			],
			formAttrs: { inline: true, labelWidth: '80px' },

			loading: false,
			tableData: [],
			columns: [
				{
					key: 'itemType',
					name: '账项类别',
					tooltip: true,
				},
				{
					key: 'itemName',
					name: '账项名称',
					tooltip: true,
				},
				{
					key: 'generateType',
					name: '费用生成方式',
					tooltip: true,
				},
				{
					key: 'itemAmount',
					name: '默认金额（元）',
					tooltip: true,
				},
				{
					key: 'isSurcharge',
					name: '是否附加费',
					tooltip: true,
				},
				{
					key: 'itemStatus',
					name: '状态',
					tooltip: true,
				},
				{
					hide: !this.$has('cpm_bill_item_modify'),
					key: 'deal',
					name: '操作',
					fixed: 'right',
					width: 100,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 新增、编辑弹窗
			showUpdate: false,
			editType: 'add',
		}
	},

	created() {
		this.getList()
	},
	methods: {
		getfilterNameFn(options = [], val, key = 'sortValue', label = 'sortName') {
			return getfilterName(options, val, key, label)
		},
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.getList(1)
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await queryBillItemList({
					size,
					current,
					...this.formData,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 新增
		handleAdd() {
			this.editType = 'add'
			this.showUpdate = true
		},
		// 修改
		handleAdjust(data) {
			this.editType = 'edit'
			this.showUpdate = true
			this.$nextTick(() => {
				this.$refs.updateDialogRef.setFormData(data)
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	padding: 20px;
}

.right-top {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 12px;
	::v-deep {
		.el-form-item {
			margin-bottom: 0;
		}
	}
}
</style>
