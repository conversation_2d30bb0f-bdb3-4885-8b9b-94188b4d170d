<template>
	<div class="authority-container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
		<div class="tree-wrapper" v-loading="loading">
			<h5>权限分配</h5>
			<div class="tree-container">
				<vue-scroll :ops="{ bar: { background: '#e3e3e3' } }">
					<el-tree
						ref="roleManageRef"
						:data="permissionList"
						highlight-current
						show-checkbox
						node-key="permissionId"
						:default-checked-keys="defaultPermissionKeys"
						:expand-on-click-node="false"
						:check-on-click-node="true"
						:props="{
							children: 'children',
							label: 'description',
							disabled: () => !edidState,
						}"
					></el-tree>
				</vue-scroll>
			</div>
		</div>
		<div class="button-wrapper" v-if="!selectRole.noEdit">
			<el-button
				v-has="'v1_tos_role_modify'"
				type="primary"
				v-show="!edidState && permissionList.length"
				@click="edidState = true"
			>
				编辑
			</el-button>
			<el-button v-has="'v1_tos_role_delete'" v-show="!edidState && permissionList.length" @click="deleteRole">
				删除
			</el-button>
			<el-button type="primary" v-show="edidState" @click="handleSave">保存</el-button>
			<el-button v-show="edidState" @click="resetAuthority">取消</el-button>
		</div>
		<div class="button-wrapper" v-else>
			<el-button type="primary" @click="handleSave">保存</el-button>
			<el-button @click="$emit('operate-success')">取消</el-button>
		</div>
	</div>
</template>

<script>
import { ruleRequired, ruleMaxLength } from '@/utils/rules'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiRoleTree, apiModifyRole, apiCreateRole, apiDeleteRole } from '@/api/organizationManage.api'
export default {
	props: {
		selectRole: {
			type: Object,
		},
	},
	data() {
		return {
			formData: {
				name: '',
			},
			formAttrs: {
				inline: true,
				rules: {
					name: [ruleRequired('请输入角色名称'), ruleMaxLength(20)],
				},
			},
			defaultPermissionKeys: [],
			permissionList: [],
			edidState: false,
			submitLoading: false,
			loading: false,
		}
	},
	computed: {
		formItems() {
			return [
				{
					type: 'el-input',
					label: '角色名称',
					prop: 'name',
					attrs: {
						disabled: !this.edidState,
					},
				},
			]
		},
	},
	watch: {
		selectRole: {
			handler() {
				this.resetAuthority()
				// 新增
				if (this.selectRole.noEdit) {
					this.edidState = true
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		// 获取权限树
		async getTree() {
			this.loading = true
			const { orgName, noEdit, copyId, id, tenant_id, ref_type, src_template_id } = this.selectRole
			const searchParams = {
				isBusiness: orgName && orgName === '营业厅' ? true : false,
				roleId: noEdit ? copyId : id,
				tenantId: tenant_id,
				refType: ref_type,
				srcTemplateId: src_template_id,
			}
			try {
				const res = await apiRoleTree(searchParams)
				this.defaultPermissionKeys = []
				this.permissionList = this.packageUniteFields(res.children || [])
				this.packageSelectKeys(this.permissionList)
			} catch (error) {
				console.log(error)
			} finally {
				this.loading = false
			}
		},
		/**
		 * 递归权限列表树统一子集字段显示为children
		 * @param { Array } data 数据列表
		 */
		packageUniteFields(data) {
			return data.map(o => {
				let combinedChildren = []
				if (o.permissionList && o.permissionList.length) {
					combinedChildren = combinedChildren.concat(o.permissionList)
				}
				if (o.children && o.children.length) {
					combinedChildren = combinedChildren.concat(o.children)
				}
				o.children = this.packageUniteFields(combinedChildren)
				return {
					...o,
					children: o.children,
				}
			})
		},
		/**
		 * 递归权限树形列表整理数据中已选中数据key
		 * @param { Array } data 数据列表
		 */
		packageSelectKeys(data = []) {
			data.forEach(o => {
				o.permissionId && o.check && this.defaultPermissionKeys.push(o.permissionId)
				if (o.children && o.children.length) {
					this.packageSelectKeys(o.children)
				}
			})
		},
		deleteRole() {
			this.$confirm('删除后数据将无法恢复', '确定删除角色吗？', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(() => {
				apiDeleteRole({
					id: this.selectRole.id,
					tid: this.selectRole.tenant_id,
				}).then(() => {
					this.$message({
						type: 'success',
						message: '删除角色成功',
					})
					this.$emit('operate-success')
				})
			})
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const permissionList = (this.$refs.roleManageRef.getCheckedKeys() || []).filter(Boolean)
			if (!permissionList.length) {
				this.$message.error('权限不能为空')
				return
			}

			try {
				this.submitLoading = true
				const { noEdit, tenant_id, id } = this.selectRole
				const params = trimParams(removeNullParams(this.formData))
				Object.assign(params, {
					permission_list: permissionList,
					tid: tenant_id,
					id,
				})
				const API = noEdit ? apiCreateRole : apiModifyRole
				await API({ id }, params)
				this.$message.success(noEdit ? '新增角色权限成功' : '编辑权限成功')
				this.$emit('operate-success')
			} catch (error) {
				console.log(error)
			} finally {
				this.submitLoading = false
			}
		},
		resetAuthority() {
			this.formData.name = this.selectRole.name
			this.getTree()
			this.edidState = false
		},
	},
}
</script>

<style lang="scss" scoped>
.authority-container {
	flex: 1;
	height: 0;
	display: flex;
	flex-direction: column;
}
.el-form {
	position: relative;
	&::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 20px;
		right: 20px;
		border-top: 1px dashed #efeaea;
	}
}
.tree-wrapper {
	flex: 1;
	height: 0;
	display: flex;
	flex-direction: column;
	h5 {
		margin: 20px 0 10px 0;
	}
	.tree-container {
		flex: 1;
		overflow: auto;
	}
	::v-deep .el-tree {
		padding: 0 12px;
		.is-current {
			& > .el-tree-node__content {
				& > .el-tree-node__label {
					color: #2080f7;
				}
			}
		}
		.el-tree-node__content {
			line-height: 34px;
			height: 34px;
			.el-checkbox__inner {
				border-radius: 0;
			}
			.el-tree-node__expand-icon {
				&:not(.is-leaf) {
					color: #666;
				}
			}
		}
	}
}
.button-wrapper {
	display: flex;
	gap: 10px;
	.el-button + .el-button {
		margin: 0;
	}
}
</style>
