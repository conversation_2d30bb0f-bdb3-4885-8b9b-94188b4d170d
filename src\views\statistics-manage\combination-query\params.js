import { userTypeOptions } from '@/consts/optionList.js'

const baseSelect = (opt = {}) => {
	const { multiple = true, options } = opt
	const config = {
		operator: multiple ? ['in', 'not in'] : ['=', '!='],
		input: {
			type: 'el-select',
			options: options || [],
			attrs: {
				multiple,
				clearable: true,
				filterable: true,
			},
		},
		dataType: 'String',
		dataFormat: 'Array',
	}
	if (!options) {
		config.__putOptions = function (options) {
			this.input.options = options
		}
	}
	return config
}

const baseInput = {
	operator: ['=', '!=', 'like', 'not like'],
	input: {
		type: 'el-input',
		attrs: {
			clearable: true,
		},
	},
	dataType: 'String',
	dataFormat: 'Object',
}

const baseNumber = {
	operator: ['=', '!=', '>', '>=', '<', '<='],
	input: {
		type: 'el-input-number',
	},
	dataType: 'String',
	dataFormat: 'Object',
}

const baseDate = (opt = {}) => {
	const { format = 'yyyy-MM-dd', type = 'date' } = opt
	return {
		operator: ['=', '!=', '>', '>=', '<', '<='],
		input: {
			type: 'el-date-picker',
			attrs: {
				format,
				valueFormat: format,
				placeholder: '选择日期',
				clearable: true,
				type,
			},
		},
		dataType: 'Date',
		dataFormat: 'Object',
	}
}

// 默认项、不可删除
// 校验规则
export const VIEW_CONFIGS = {
	// 表卡视图
	meter: {
		defaultParams: () => {
			return [{ key: 'org_code', operator: '=', value: '' }]
		},
		validationRules: [
			{
				type: 'required',
				key: 'org_code',
				message: '请配置营业所分公司',
			},
		],
	},
	// 抄表视图
	reading: {
		defaultParams: () => {
			return [
				{ key: 'org_code', operator: '=', value: '' },
				{ key: 'task_year', operator: '', value: '' },
			]
		},
		validationRules: [
			{
				type: 'required',
				key: 'org_code',
				message: '请配置营业所分公司',
			},
			{
				type: 'required',
				key: 'task_year',
				message: '请配置抄表年',
			},
		],
	},
	// 账单视图
	bill: {
		defaultParams: () => {
			return [
				{ key: 'org_code', operator: '=', value: '' },
				{ key: 'bill_date', operator: '', value: '' },
			]
		},
		validationRules: [
			{
				type: 'required',
				key: 'org_code',
				message: '请配置营业所分公司',
			},
			{
				type: 'required',
				key: 'bill_date',
				message: '请配置账单账期',
			},
		],
	},
	// 缴费视图
	payment: {
		defaultParams: () => {
			return [
				{ key: 'org_code', operator: '=', value: '' },
				{ key: 'pay_time', operator: '', value: '' },
				{ key: 'pay_date', operator: '', value: '' },
			]
		},
		validationRules: [
			{
				type: 'required',
				key: 'org_code',
				message: '请配置营业所分公司',
			},
			{
				type: 'required_one_of',
				key: ['pay_time', 'pay_date'],
				message: '请配置收费账期或缴费时间',
			},
		],
	},
}
// 字段值变更，联动更新可选项
export const FIELD_CHANGE_CONFIG = [
	// {
	// 	source: 'org_code',
	// 	target: 'meter_reading_staff_id',
	// },
]

export default instance => {
	const { virtualMeterType = [], payMode = [], payChannel = [], billStatus = [], archiveState = [], billType = [] } =
		instance.$store.getters.dataList || {}
	return [
		{
			key: 'org_code',
			label: '营业所分公司',
			view: ['meter', 'reading', 'bill', 'payment'],
			...baseSelect({ options: instance.$store.getters.orgList, multiple: true }),
			dataFormat: 'Array',
			// onlyOne: true,
		},
		{
			key: 'archives_identity',
			label: '表卡编号',
			view: ['meter', 'reading', 'bill', 'payment'],
			...baseInput,
		},
		{
			key: 'book_no',
			label: '表册编号',
			view: ['meter', 'reading', 'bill', 'payment'],
			input: {
				type: 'el-tag',
			},
			operator: ['in', 'not in'],
			dataType: 'String',
			dataFormat: 'Array',
		},
		{
			key: 'alley_code',
			label: '坊别',
			view: ['meter', 'reading', 'bill', 'payment'],
			input: {
				type: 'el-tag',
			},
			operator: ['in', 'not in'],
			dataType: 'String',
			dataFormat: 'Array',
		},
		{
			key: 'nature_no',
			label: '用水性质',
			view: ['meter', 'bill', 'payment'],
			operator: ['in', 'not in'],
			input: {
				type: 'el-cascader',
				options: [],
				attrs: {
					clearable: true,
					filterable: true,
					showAllLevels: false,
				},
				props: {
					multiple: true,
					label: 'natureName',
					value: 'priceNatureId',
					emitPath: false,
					checkStrictly: true,
				},
			},
			dataType: 'String',
			dataFormat: 'Array',
			optionMethod: 'priceNatureId',
			__putOptions: function (options) {
				this.input.options = options
			},
		},
		{
			key: 'tap_water_no',
			label: '自来水编号',
			view: ['meter'],
			...baseInput,
		},
		{
			key: 'user_type',
			label: '用户类型',
			view: ['meter', 'reading', 'bill', 'payment'],
			...baseSelect({ multiple: false, options: userTypeOptions }),
			dataType: 'Integer',
			dataFormat: 'Object',
		},
		{
			key: 'virtual_meter_type',
			label: '表卡类型',
			view: ['meter', 'reading', 'bill', 'payment'],
			...baseSelect({
				multiple: false,
				options: virtualMeterType.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}),
			}),
			dataFormat: 'Object',
		},
		{
			key: 'archives_status',
			label: '表卡状态',
			view: ['meter', 'reading'],
			...baseSelect({
				options: archiveState.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}),
			}),
			dataType: 'Integer',
		},
		{
			key: 'meter_type_id',
			label: '水表类型',
			view: ['meter'],
			...baseSelect({ multiple: false }),
			optionMethod: 'meterTypeId',
			dataType: 'Integer',
			dataFormat: 'Object',
		},
		{
			key: 'archives_time',
			label: '创建日期',
			view: ['meter'],
			...baseDate(),
		},
		{
			key: 'enterprise_number',
			label: '企业编号',
			view: ['meter', 'reading', 'bill', 'payment'],
			input: {
				type: 'el-tag',
			},
			operator: ['in', 'not in'],
			dataType: 'String',
			dataFormat: 'Array',
		},
		{
			key: 'base_meter_no',
			label: '水表标号',
			view: ['meter'],
			...baseInput,
		},
		{
			key: 'stop_date',
			label: '停用日期',
			view: ['meter'],
			...baseDate(),
		},
		{
			key: 'cancellation_date',
			label: '销卡日期',
			view: ['meter'],
			...baseDate(),
		},
		{
			key: 'bill_date',
			label: '账单账期',
			view: ['bill', 'payment'],
			...baseDate({ format: 'yyyy-MM' }),
			dataType: 'String',
		},
		{
			key: 'pay_date',
			label: '收费账期',
			view: ['payment'],
			...baseDate({ format: 'yyyy-MM' }),
			dataType: 'String',
		},
		{
			key: 'bill_open_time',
			label: '开账日期',
			view: ['bill'],
			...baseDate(),
		},
		{
			key: 'bill_status',
			label: '账单状态',
			view: ['bill'],
			...baseSelect({
				options: billStatus.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}),
			}),
		},
		{
			key: 'bill_type',
			label: '账单类型',
			view: ['bill'],
			...baseSelect({
				options: billType.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}),
			}),
		},
		{
			key: 'cleared_type',
			label: '销账类型',
			view: ['payment'],
			...baseSelect({
				multiple: false,
				options: [
					{
						label: '销账',
						value: 1,
					},
					{
						label: '预存余额销账',
						value: 2,
					},
				],
			}),
			dataFormat: 'Object',
		},
		{
			key: 'task_year',
			label: '抄表年',
			view: ['reading'],
			...baseDate({ format: 'yyyy', type: 'year' }),
			dataType: 'Integer',
		},
		{
			key: 'task_month',
			label: '抄表月',
			view: ['reading'],
			...baseSelect({
				options: [
					{
						value: '1',
						label: '1月',
					},
					{
						value: '2',
						label: '2月',
					},
					{
						value: '3',
						label: '3月',
					},
					{
						value: '4',
						label: '4月',
					},
					{
						value: '5',
						label: '5月',
					},
					{
						value: '6',
						label: '6月',
					},
					{
						value: '7',
						label: '7月',
					},
					{
						value: '8',
						label: '8月',
					},
					{
						value: '9',
						label: '9月',
					},
					{
						value: '10',
						label: '10月',
					},
					{
						value: '11',
						label: '11月',
					},
					{
						value: '12',
						label: '12月',
					},
				],
			}),
			dataType: 'Integer',
		},
		{
			key: 'meter_reading_staff_id',
			label: '抄表员',
			view: ['reading', 'bill', 'payment'],
			...baseSelect(),
			optionMethod: 'meterReadingStaffIds',
		},
		{
			key: 'use_amount',
			label: '水量',
			view: ['reading'],
			...baseNumber,
		},
		{
			key: 'pay_mode',
			label: '付款方式',
			view: ['payment'],
			...baseSelect({
				options: payMode.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}),
			}),
		},
		{
			key: 'pay_channel',
			label: '缴费渠道',
			view: ['payment'],
			...baseSelect({
				options: payChannel.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}),
			}),
		},
		{
			key: 'pay_time',
			label: '缴费时间',
			view: ['payment'],
			...baseDate(),
		},
	]
}
