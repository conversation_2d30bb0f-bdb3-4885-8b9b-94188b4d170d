<template>
	<div class="pay-input">
		<div class="row-input">
			<div class="box-icon" :disabled="disabled">¥</div>
			<div class="box-input">
				<el-input
					class="input-true"
					:style="{ opacity: showInput || !showNumberTip ? 1 : 0 }"
					type="number"
					clearable
					:value="value"
					:disabled="disabled"
					:step="0.01"
					@input="inputChange($event)"
					@focus="inputFocus($event)"
					@blur="inputBlur($event)"
					@keyup.enter.native="inputEnter($event)"
				/>
				<div
					class="input-false"
					:disabled="disabled"
					:style="{ opacity: showInput || !showNumberTip ? 0 : 1 }"
					@click="showInput = true"
				>
					{{ formatNumber(value) | comdify }}
				</div>
			</div>
		</div>
		<div class="box-tip" v-show="showNumberTip && value >= 100">
			<span class="arrow"></span>
			<span class="tip">{{ value | numberTip }}</span>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		value: [String, Number],
		disabled: {
			type: Boolean,
			default: false,
		},
		showNumberTip: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			showInput: false, // 是否展示输入框
		}
	},
	filters: {
		// 金额量级提示(大于等于100提示)
		numberTip(val) {
			val = val * 1
			val += ''
			const integer = val.split('.')[0]
			const unit = ['百', '千', '万', '十万', '百万', '千万', '亿', '十亿']
			const index = integer > 0 ? integer.length : 0
			return integer >= 100 ? unit[index - 3] : ''
		},
		// 添加千分位
		comdify(val) {
			let reg = /\d{1,3}(?=(\d{3})+$)/g
			let number = val.replace(/^(\d+)((\.\d+)?)$/, function (s, s1, s2) {
				return s1.replace(reg, '$&,') + s2
			})
			return number
		},
	},
	methods: {
		// 精确的四舍五入方法，避免toFixed精度丢失
		formatNumber(value) {
			if (value === null || value === undefined || isNaN(value)) {
				return '0.00'
			}
			// 使用Math.round避免toFixed的精度问题
			const rounded = Math.round((parseFloat(value) + Number.EPSILON) * 100) / 100
			return rounded.toFixed(2)
		},
		inputChange(val) {
			this.$emit('input', val)
		},
		inputBlur() {
			this.showInput = false
			this.$emit('blur')
		},
		inputFocus() {
			this.showInput = true
			this.$emit('focus')
		},
		inputEnter(event) {
			if (event) {
				event.target.blur()
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.row-input {
	position: relative;
	display: flex;
	height: 37px;
	.box-icon {
		width: 14px;
		font-size: 22px;
		font-weight: 600;
		color: #222222;
		line-height: 34px;
		&[disabled] {
			color: #ccc;
		}
	}
	.box-input {
		flex: 1;
		margin-left: 14px;
		position: relative;
		.input-true,
		.input-false {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			&[disabled] {
				color: #ccc;
			}
		}
		.input-true {
			z-index: 999;
		}
		.input-false {
			font-size: 30px;
			font-family: Arial-BoldMT, Arial;
			color: #222222;
			line-height: 34px;
			padding-left: 8px;
			position: relative;
			top: -1px;
		}
	}
}
.box-tip {
	position: relative;
	width: 100%;
	&::before {
		content: '';
		width: 100%;
		height: 1px;
		background-color: #ccc;
		transform: scaleY(0.5);
		position: absolute;
		left: 0;
		top: 0;
	}
	.arrow {
		position: relative;
		left: 18px;
		&::before {
			content: '';
			width: 0;
			height: 0;
			border-width: 0 5px 5px;
			border-style: solid;
			border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #ccc;
			position: absolute;
			left: 8px;
			bottom: 0;
		}
		&::after {
			content: '';
			width: 0;
			height: 0;
			border-width: 0 5px 5px;
			border-style: solid;
			border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #fff;
			position: absolute;
			left: 8px;
			bottom: -1px;
		}
	}

	.tip {
		position: absolute;
		left: 18px;
		width: 30px;
		text-align: center;
		color: #d9d9d9;
	}
}
::v-deep {
	.el-form-item__error + .pay-input {
		background-color: red;
	}
	.el-input--small .el-input__inner,
	.input {
		border: none;
		font-size: 30px;
		font-family: Arial-BoldMT, Arial;
		color: #222222;
		line-height: 34px;
		padding-left: 8px;
	}
}
</style>
