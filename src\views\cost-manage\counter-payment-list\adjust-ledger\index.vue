<template>
	<GcElDialog
		:show="isShow"
		title="销账调整"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
	</GcElDialog>
</template>

<script>
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getFormItem } from './form.js'
import { apiBillPayRecordRevers } from '@/api/costManage.api'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
				payTime: this.dayjs().format('YYYY-MM-DD HH:mm:ss'),
				reason: '',
				billPayRecordId: '',
				year: '',
			},
			formItems: getFormItem(),
			formAttrs: {
				rules: {
					payTime: [ruleRequired('必填')],
				},
			},
		}
	},
	methods: {
		handleClose() {
			this.isShow = false
			this.handleReset()
		},
		handleReset() {
			this.$refs.formRef.resetFields()
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const formParams = trimParams(removeNullParams(this.formData))
			delete formParams.archivesIdentity
			await apiBillPayRecordRevers(formParams)
			this.$message.success(`账单销账成功`)
			this.$emit('success')
			this.handleClose()
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
</style>
