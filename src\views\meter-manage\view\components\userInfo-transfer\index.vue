<template>
	<GcElDialog
		:show="isShow"
		title="过户"
		custom-top="50px"
		width="1200px"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:dividLine>
				<div class="divid-line"></div>
			</template>
			<template v-slot:userInfo>
				<Search
					v-show="newDetailData.userType === 3"
					:userType="newDetailData.userType"
					style="float: right"
					:active-tab="{ id: 2 }"
					@use="handleSearchUser"
				/>
			</template>
			<template v-slot:businessLicenseUrl>
				<GcUploadFile v-model="formData.businessLicenseUrl" />
			</template>
			<template v-slot:purchaseContractUrl>
				<GcUploadFile v-model="formData.purchaseContractUrl" />
			</template>
			<template v-slot:otherMobile>
				<el-form-item class="other-mobile" label="其他手机" prop="otherMobile">
					<AddOtherMobile v-model="formData.otherMobile" :mobileList.sync="formData.mobileList" />
				</el-form-item>
			</template>
			<template v-slot:taxpayerIdentity>
				<el-autocomplete
					style="width: 100%"
					v-model="formData.taxpayerIdentity"
					:fetch-suggestions="queryTaxpayerIdentity"
					placeholder="请输入"
					@select="handleTaxpayerIdentitySelect"
					@change="handleTaxpayerIdentityChange"
				>
					<template slot-scope="{ item }">
						<div class="billing-information-item">
							<p class="billing-information-item-row">纳税人识别号：{{ item.taxpayerIdentity }}</p>
							<p class="billing-information-item-row">开票抬头：{{ item.userName }}</p>
						</div>
					</template>
				</el-autocomplete>
			</template>
		</GcFormRow>
	</GcElDialog>
</template>

<script>
import _ from 'lodash'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { isBlank } from '@/utils/validate.js'
import {
	ruleRequired,
	RULE_INTEGERONLY,
	RULE_PHONE,
	RULE_INCORRECTEMAIL,
	ruleMaxLength,
	RULE_INT_ENGLISH,
	RULE_INCORRECTIDCARD,
	RULE_POSTALCODE,
} from '@/utils/rules.js'
import { getFormItems } from './form.js'
import { apiEffectivePrice, apiArchivesTransferUser, apiArchivesTransferUser1 } from '@/api/meterManage.api.js'
import { queryEnterprisePage, apiQueryInvoiceBuyer } from '@/api/userManage.api'
import AddOtherMobile from '../../../components/AddOtherMobile'
import Search from '../search/index.vue'

export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		detailData: {
			type: Object,
			default: () => {
				return {}
			},
		},
		permissionCode: {
			type: String,
			default: 'cpm_archives_transfer-user',
		},
	},
	created() {
		this.queryTaxpayerIdentity = _.debounce(this.queryTaxpayerIdentity, 250)
	},
	components: { AddOtherMobile, Search },
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		newDetailData() {
			return Object.assign({}, ...Object.values(this.detailData))
		},
	},
	watch: {
		isShow: {
			async handler(val) {
				if (val) {
					await this._apiEffectivePrice()

					this.formData.userType = this.newDetailData.userType
					this.formItems = getFormItems(this)
					this.resetFormData()
					if (this.formData.userType === 4) {
						await this._apiGetCompany(this.newDetailData.orgCode)
						this.formData.enterpriseNumber = this.newDetailData.enterpriseNumber
						this.handleChangeEnterprise(this.formData.enterpriseNumber)
					}
				}
			},
		},
		billingInfoDisabled: {
			handler(flag) {
				const keys = ['bankAccount', 'openBank', 'buyerName']

				keys.forEach(key => {
					const item = this.formItems.find(item => item.prop === key)
					if (item) {
						item.attrs = {
							...(item.attrs || {}),
							disabled: flag,
						}
					}
				})
			},
			immediate: true,
		},
	},
	data() {
		return {
			formItems: [],
			formData: {
				operatorPerson: this.$store.getters.userInfo.staffName,
				userType: '',
				mobileList: [], // 其他手机号
				priceId: '',
				collectionAccountId: '', // 企业：页面不展示，但是后端需传
				chargingMethod: '', //  企业： 页面不展示，但是后端需传
			},
			formAttrs: {
				labelWidth: '120px',
				rules: {
					userType: [ruleRequired('必填')],
					userName: [ruleRequired('必填'), ruleMaxLength(32)],
					userMobile: [RULE_PHONE],
					chargingMethod: [ruleRequired('必填')],
					nameUsedBefore: [ruleMaxLength(32)],
					contractNum: [ruleMaxLength(64)],
					contactPeople: [ruleMaxLength(64)],
					contactPhone: [ruleMaxLength(128)],
					households: [
						{
							pattern: /^(?:[0-9]{1,2}|30)$/,
							message: '请输入0-30的整数',
							trigger: '',
						},
					],
					resiPopulation: [
						{
							pattern: /^(?:[0-9]{1,2}|64)$/,
							message: '请输入0-64的整数',
							trigger: '',
						},
					],
					propertyOwner: [ruleMaxLength(32)],
					zipCode: [RULE_POSTALCODE],
					certificateNo: [RULE_INCORRECTIDCARD],
					otherCertificateNo: [ruleMaxLength(32)],
					email: [RULE_INCORRECTEMAIL],
					mailingAddress: [ruleMaxLength(64)],
					otherMobile: [RULE_PHONE],
					openBank: [ruleMaxLength(32)],
					taxpayerIdentity: [RULE_INT_ENGLISH, ruleMaxLength(20)],
					bankAccount: [RULE_INTEGERONLY, ruleMaxLength(32)],
					priceName: [ruleRequired('必填')],
					priceCode: [ruleRequired('必填')],
					enterpriseNumber: [
						ruleRequired('必填'),
						{
							pattern: /^\d{7}$/,
							message: '必须为数字且7位',
							trigger: '',
						},
					],
					buyerName: [ruleMaxLength(32)],
				},
			},
			priceNameList: [],
			priceCodeList: [],
			collectionAccountList: [],
			userId: '',
			billingInfoDisabled: false,
		}
	},
	methods: {
		assignBaseForm(key) {
			if (
				[
					'archivesIdentity',
					'arrearsTotalAmount',
					'meterBalanceAmount',
					'taxpayerIdentity',
					'openBank',
					'bankAccount',
					'invoiceType',
					'buyerName',
				].includes(key)
			) {
				this.formData[key] = this.newDetailData[key] === undefined ? '' : this.newDetailData[key]
			} else if (['priceCode', 'priceName'].includes(key)) {
				this.priceCodeList.find(item => {
					if (item.priceId === this.newDetailData.priceId) {
						this.formData[key] = item.priceId
						this.formData.priceId = item.priceId
					}
				})
			}
			this.formData.currentUserName = this.detailData.user?.userName
		},
		resetFormData() {
			const { formItems } = this
			formItems.forEach(item => {
				if (item.prop !== 'userType' && item.prop) {
					if (item.prop === 'businessLicenseUrl' || item.prop === 'purchaseContractUrl') {
						this.$set(this.formData, item.prop, [])
					} else if (item.prop !== 'operatorPerson') {
						this.$set(this.formData, item.prop, '')
					}
					this.assignBaseForm(item.prop)
				}
			})
		},
		// 获取价格编号
		async _apiEffectivePrice() {
			const data = await apiEffectivePrice()
			this.priceNameList = data.map(item => {
				return {
					label: item.priceName,
					value: item.priceId,
					...item,
				}
			})
			this.priceCodeList = data.map(item => {
				return {
					label: item.priceCode,
					value: item.priceId,
					...item,
				}
			})
		},
		// 获取企业
		async _apiGetCompany(orgCode) {
			const { records } = await queryEnterprisePage({
				current: 1,
				size: 99999,
				orgCode,
			})
			const obj = this.formItems.find(item => item.prop === 'enterpriseNumber')
			if (obj) {
				obj.options = records.map(item => ({
					label: item.enterpriseNumber + '',
					value: item.enterpriseNumber + '',
					obj: item,
				}))
			}
		},
		handleChangeEnterprise(v) {
			const obj = this.formItems.find(item => item.prop === 'enterpriseNumber')
			obj.options.forEach(item => {
				if (item.value === v) {
					this.formData.enterpriseName = item.obj.enterpriseName
					this.formData.collectionAccountId = item.obj.collectionAccountId
					this.formData.chargingMethod = item.obj.chargingMethod
				}
			})
		},
		handleSearchUser(obj) {
			const keys = Object.keys(obj)
			keys.forEach(key => {
				if (obj && Object.prototype.hasOwnProperty.call(obj, key) && !isBlank(obj[key])) {
					if (['businessLicenseUrl', 'purchaseContractUrl'].includes(key)) {
						this.formData[key] = obj[key] ? JSON.parse(obj[key]) : []
					} else if (key === 'otherContactPhone') {
						this.formData.mobileList = obj.otherContactPhone ? obj.otherContactPhone.split(',') : ''
					} else {
						this.formData[key] = obj[key]
					}
					this.userId = obj.userId
				}
			})
		},
		// 改变用户类型
		handleChangeUserType() {
			this.formItems = getFormItems(this)
			if (this.formData.userType === 4) {
				this.getCollectionAccountList()
			}
		},
		handleChangePrice(v) {
			if (v) {
				const priceObj = this.priceNameList.find(item => item.priceId === v)
				this.formData.priceName = priceObj.priceName
				this.formData.priceCode = priceObj.priceCode
				this.formData.priceId = priceObj.priceId
			} else {
				this.formData.priceName = ''
				this.formData.priceCode = ''
				this.formData.priceId = ''
			}
		},
		async handleSave() {
			if (this.formData.arrearsTotalAmount) {
				this.$message.error('当前表卡存在欠费，无法过户')
				return
			}

			const valid = await this.$refs.formRef.validate()
			if (!valid) {
				this.$message.error('表单信息未完善')
				return
			}
			const params = trimParams(removeNullParams(this.formData))
			const otherContactPhone = params.mobileList ? params.mobileList.join(',') : ''
			const businessLicenseUrl =
				params.businessLicenseUrl && params.businessLicenseUrl.length
					? JSON.stringify(
							params.businessLicenseUrl.map(item => {
								return {
									name: item.name,
									url: item.url,
								}
							}),
					  )
					: ''
			const purchaseContractUrl =
				params.purchaseContractUrl && params.purchaseContractUrl.length
					? JSON.stringify(
							params.purchaseContractUrl.map(item => {
								return {
									name: item.name,
									url: item.url,
								}
							}),
					  )
					: ''
			delete params.mobileList

			const residentParams = {
				archives: {
					archivesId: this.newDetailData.archivesId,
					households: params.households,
					resiPopulation: params.resiPopulation,
					propertyOwner: params.propertyOwner,
					contractNum: params.contractNum,
					purchaseContractUrl,
				},
				user: {
					userName: params.userName,
					userSubType: params.userSubType,
					userType: 3,
					userMobile: params.userMobile,
					contactPeople: params.contactPeople,
					contactPhone: params.contactPhone,
					otherContactPhone,
					userId: this.userId,
					nameUsedBefore: params.nameUsedBefore,
					certificateNo: params.certificateNo,
					certificateType: params.certificateType,
					otherCertificateNo: params.otherCertificateNo,
					zipCode: params.zipCode,
					email: params.email,
					mailingAddress: params.mailingAddress,
					taxpayerIdentity: params.taxpayerIdentity,
					openBank: params.openBank,
					bankAccount: params.bankAccount,
					buyerName: params.buyerName,
					chargingMethod: params.chargingMethod,
				},
				price: {
					priceId: params.priceId,
				},
			}
			const companyParams = {
				archives: {
					archivesId: this.newDetailData.archivesId,
					contractNum: params.contractNum,
					purchaseContractUrl,
					businessLicenseUrl,
				},
				user: {
					userName: params.userName,
					userSubType: params.userSubType,
					userType: 4,
					userMobile: params.userMobile,
					contactPeople: params.contactPeople,
					contactPhone: params.contactPhone,
					otherContactPhone,
					userId: this.userId,
					companyAddress: params.companyAddress,
					enterpriseNumber: params.enterpriseNumber,
					chargingMethod: params.chargingMethod,
					collectionAccountId: params.collectionAccountId,
					zipCode: params.zipCode,
					email: params.email,
					mailingAddress: params.mailingAddress,
					invoiceType: params.invoiceType,
					taxpayerIdentity: params.taxpayerIdentity,
					openBank: params.openBank,
					buyerName: params.buyerName,
					bankAccount: params.bankAccount,
				},
				price: {
					priceId: params.priceId,
				},
			}
			const newParams = params.userType == 3 ? residentParams : companyParams
			try {
				const apiMethods = {
					'cpm_archives_transfer-user': apiArchivesTransferUser,
					'cpm_archives_transfer-user1': apiArchivesTransferUser1,
				}
				await apiMethods[this.permissionCode](newParams)
				this.$message.success('过户成功')
				this.handleClose()
				this.$emit('success')
			} catch (error) {
				console.log(error)
			}
		},
		async queryTaxpayerIdentity(taxpayerIdentity, callback) {
			taxpayerIdentity = taxpayerIdentity || ''
			try {
				let result = await apiQueryInvoiceBuyer({
					taxpayerIdentity,
				})
				result = result || []
				const list = result.map(item => {
					const { taxpayerIdentity } = item
					return { ...item, value: taxpayerIdentity }
				})

				callback(list)
			} catch (e) {
				console.log(e)
				callback([])
			}
		},
		handleTaxpayerIdentitySelect(item) {
			const { userName, openBank, bankAccount } = item
			console.log(userName, openBank, bankAccount)
			this.formData.openBank = openBank
			this.formData.buyerName = userName
			this.formData.bankAccount = bankAccount
			this.billingInfoDisabled = true
		},
		handleTaxpayerIdentityChange() {
			this.billingInfoDisabled = false
		},
		handleClose() {
			this.isShow = false
			this.userId = ''
		},
	},
}
</script>
<style lang="scss" scoped>
.other-mobile {
	::v-deep {
		.el-form-item__error {
			position: absolute;
			top: 36px;
		}
	}
}
.divid-line {
	height: 1px;
	background-color: #eceff8;
}
::v-deep {
	.title {
		.el-form-item__label {
			font-weight: 500 !important;
			color: #000000 !important;
		}
	}
	.el-input-group__append {
		padding: 0;
		img {
			display: block;
			padding: 0 10px;
			height: 30px;
			object-fit: none;
			cursor: pointer;
		}
	}
}
</style>
