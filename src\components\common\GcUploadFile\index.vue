<template>
	<div class="gc-upload-file">
		<el-upload
			ref="uploadRef"
			:action="apiUrl"
			:limit="limit"
			:file-list="fileList"
			:show-file-list="showFileList"
			:accept="accept"
			:on-progress="handleProgress"
			:auto-upload="autoUpload"
			:on-exceed="handleExceed"
			:before-upload="beforeUpload"
			:on-success="handleSuccess"
			:on-error="handleError"
			:on-remove="handleRemove"
			:disabled="isDisabled"
		>
			<template #trigger>
				<slot name="trigger">
					<el-button type="primary" :loading="loading" :disabled="isDisabled">{{ buttonName }}</el-button>
				</slot>
			</template>
			<template #tip>
				<slot name="tip">
					<div class="gc-tip" v-show="showTip">
						<i class="el-icon-warning-outline"></i>
						.doc、.docx、.pdf、.jpg、.png，文件大小{{ limit }}M以内
					</div>
				</slot>
			</template>
		</el-upload>
	</div>
</template>

<script>
export default {
	name: 'GcUploadFile',
	props: {
		autoUpload: {
			type: Boolean,
			default: true,
		},
		accept: {
			type: String,
			default: '.doc,.docx,.pdf,.jpg,.png',
		},
		limit: {
			type: Number,
			default: 1,
		},
		showFileList: {
			type: Boolean,
			default: true,
		},
		value: {
			type: Array,
			default: () => [],
		},
		isDisabled: {
			type: Boolean,
			default: false,
		},
		buttonName: {
			type: String,
			default: '选择文件',
		},
		showTip: {
			type: Boolean,
			default: true,
		},
	},
	components: {},
	data() {
		return {
			progress: 0,
			loading: false,
			file: null,
		}
	},
	computed: {
		fileList: {
			get() {
				return this.value
			},
			set(newVal) {
				this.$emit('input', newVal)
			},
		},
		apiUrl() {
			const IS_PROD = ['production', 'prod'].includes(process.env.NODE_ENV)
			const preUrl = IS_PROD ? '' : 'api'
			return preUrl + '/cpm/file/manage/upload'
		},
	},
	created() {},
	methods: {
		beforeUpload(file) {
			const fileSuffix = file.name.substring(file.name.lastIndexOf('.') + 1)
			const whiteList = ['pdf', 'jpg', 'png', 'doc', 'docx']

			if (whiteList.indexOf(fileSuffix) === -1) {
				this.$message.error('上传文件只能是 pdf、doc、docx、jpg、png格式')
				return false
			}

			if (file.size / 1024 / 1024 > this.limit) {
				this.$message.error(`文件大小最大为${this.limit}M`)
				return false
			}
			this.loading = true
			this.file = { file: file, name: file.name, uid: file.uid }
		},
		handleSuccess(res) {
			this.loading = false
			this.fileList.push({
				...this.file,
				url: res.data,
			})
			this.$emit('on-success', {
				...this.file,
				url: res.data,
			})
		},
		handleError() {
			this.loading = false
		},
		handleRemove(file) {
			let index = this.fileList.findIndex(item => item.uuid === file.uuid)
			this.fileList.splice(index, 1)
			this.$emit('on-remove', file)
		},
		handleExceed() {
			this.$message.error('最多上传' + this.limit + '个附件')
		},
		handleProgress() {},
	},
}
</script>

<style lang="scss" scoped>
.gc-tip {
	margin-top: 6px;
	color: #babbca;
}
.gc-upload-file {
	width: 100%;
}
</style>
