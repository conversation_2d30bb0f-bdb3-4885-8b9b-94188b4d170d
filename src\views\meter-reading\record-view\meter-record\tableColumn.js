export function getColumn(_) {
	return [
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'addressFullName',
			name: '地址',
			tooltip: true,
		},
		{
			key: 'userName',
			name: '用户',
			tooltip: true,
		},
		{
			key: 'meterReadingNumber',
			name: '抄表次数',
			tooltip: true,
		},
		{
			key: 'archivesStatusDesc',
			name: '表卡状态',
			tooltip: true,
		},
		{
			key: 'lastChargingMeterReading',
			name: '指针数',
			tooltip: true,
		},
		{
			key: 'recordSeq',
			name: '册内序号',
			tooltip: true,
		},
		{
			key: 'handOverDesc',
			name: '是否移交',
			tooltip: true,
		},
		{
			key: 'handTypeDesc',
			name: '移交渠道',
			tooltip: true,
		},
		{
			key: 'takeOverDesc',
			name: '小区是否接管',
			tooltip: true,
		},
		{
			key: 'summaryArchivesDesc',
			name: '是否小区总表',
			tooltip: true,
		},
		{
			key: 'archivesMeterTypeDesc',
			name: '贸易结算类型',
			tooltip: true,
		},
		{
			hide: !_.$has([
				'plan-collection_meterReadingBook_updateArchivesSeq',
				'plan-collection_meterReadingBook_meterPrint',
			]),
			key: 'deal',
			width: 190,
			name: '操作',
			fixed: 'right',
		},
	]
}
