{"name": "ui_new", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.21.1", "core-js": "^3.6.5", "dayjs": "^1.10.4", "echarts": "^5.1.1", "element-resize-detector": "^1.2.2", "element-ui": "^2.15.1", "file-saver": "^2.0.5", "idle-vue": "^2.0.5", "jsencrypt": "^3.3.2", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "remixicon": "^4.2.0", "screenfull": "^5.1.0", "swiper": "^5.4.5", "underscore": "^1.13.1", "uuid": "^8.3.2", "v-click-outside": "^3.1.2", "vue": "^2.6.11", "vue-awesome-swiper": "^4.1.1", "vue-clipboard2": "^0.3.1", "vue-json-viewer": "^2.2.19", "vue-ls": "^3.2.2", "vue-native-websocket": "^2.0.14", "vue-router": "^3.2.0", "vue-seamless-scroll": "^1.1.23", "vuescroll": "4.17.3", "vuex": "^3.4.0", "xlsx": "^0.17.0", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@commitlint/cli": "^9.1.2", "@commitlint/config-conventional": "^9.1.2", "@e-cloud/es-commitlint": "0.0.2", "@prettier/plugin-pug": "^1.20.1", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "eslint": "^6.8.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^6.2.2", "git-revision-webpack-plugin": "^5.0.0", "husky": "^4.3.8", "lint-staged": "^10.5.4", "mockjs": "^1.1.0", "node-sass": "^6.0.1", "prettier": "^2.2.1", "sass-loader": "^10.2.0", "script-loader": "^0.7.2", "style-resources-loader": "^1.4.1", "vue-template-compiler": "^2.6.11"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx,html,css,vue,less,scss}": "prettier  --plugin-search-dir ./node_modules --write"}}