<template>
	<div class="flex">
		<div class="bill-info">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" @click="handleSearch">
						<i class="iconfontCis icon-small-search"></i>
						筛选
					</el-button>
					<el-button @click="handleReset">
						<i class="iconfontCis icon-reset"></i>
						重置
					</el-button>
				</el-form-item>
			</GcFormSimple>
			<GcModelHeader title="账单信息" :icon="require('@/assets/images/icon/title-cash.png')"></GcModelHeader>
			<GcGroupDetail :data="recordsData"></GcGroupDetail>
		</div>
		<div class="trace-panel">
			<GcModelHeader title="账单追溯" :icon="require('@/assets/images/icon/title-cash.png')"></GcModelHeader>
			<div class="top-gradient-edge"></div>
			<div class="bill-list" v-if="billList.length">
				<template v-for="(item, index) in billList">
					<GcTicket
						:current-bill-no="detailData.billNo"
						:title="item.name"
						:list="item.list"
						:key="'ticket-' + index"
					></GcTicket>
					<GcArrow v-show="index !== billList.length - 1" :key="'arrow-' + index" space="3px" />
				</template>
			</div>
			<div class="bill-list" v-else>
				<GcEmpty></GcEmpty>
			</div>
		</div>
	</div>
</template>

<script>
import { getfilterName } from '@/utils'
import GcTicket from './components/GcTicket'
import GcArrow from './components/GcArrow'
import { apiGetRecordBillInfo, apiGetBillAdjust } from '@/api/costManage.api'
import { accSub } from '@/utils/calc.js'
export default {
	name: 'BillTrace',
	components: { GcTicket, GcArrow },
	data() {
		return {
			formData: {
				billNo: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '账单编号',
					prop: 'billNo',
					attrs: {
						style: {
							width: '280px',
						},
					},
				},
			],
			formAttrs: {
				inline: true,
			},
			billList: [],
			detailData: {},
		}
	},
	computed: {
		recordsData() {
			const list = [
				{
					key: '账单编号',
					value: '--',
					field: 'billNo',
				},
				{
					key: '账期',
					value: '--',
					field: 'billDate',
				},
				{
					key: '表卡编号',
					value: '--',
					field: 'archivesIdentity',
				},
				{
					key: '用户名称',
					value: '--',
					field: 'userName',
				},
				{
					key: '创建时间',
					value: '--',
					field: 'createTime',
				},
				{
					key: '账单状态',
					value: '--',
					field: 'billStatus',
				},
				{
					key: '价格编号',
					value: '--',
					field: 'priceCode',
				},
				{
					key: '价格版本号',
					value: '--',
					field: 'priceVersion',
				},
				{
					key: '水量',
					value: '--',
					field: 'useAmount',
				},
				{
					key: '水费',
					value: '--',
					field: 'useAmt',
				},
				{
					key: '污水费',
					value: '--',
					field: 'billItemAmt',
				},
				{
					key: '附加费',
					value: '--',
					field: 'billItemAmt2',
				},
				{
					key: '应缴金额',
					value: '--',
					field: 'receivableAmount',
				},
			]
			const { billStatus = [] } = this.$store.getters.dataList || {}
			list.forEach(item => {
				if (item.field === 'billStatus') {
					item.value = getfilterName(billStatus, this.detailData[item.field], 'sortValue', 'sortName')
				} else if (item.field === 'billItemAmt2') {
					//  附加费=应缴金额-水费-污水费
					const { receivableAmount, useAmt, billItemAmt } = this.detailData || {}
					item.value = [useAmt || 0, billItemAmt || 0].reduce((acc, current) => {
						return accSub(acc, current)
					}, receivableAmount || 0)
				} else {
					item.value = this.detailData[item.field]
				}
			})
			return {
				list,
				row: 6,
			}
		},
	},
	created() {},
	activated() {
		const { billNo } = this.$route.query
		if (billNo) {
			this.formData.billNo = billNo
			this.handleSearch()
		}
	},
	methods: {
		getTicketCellValue(obj, key) {
			const value = obj[key]
			if (value === null || value === undefined) {
				return '--'
			}
			return value
		},
		async getList() {
			if (!this.formData.billNo) return
			const data = await apiGetBillAdjust({
				billNo: this.formData.billNo,
			})
			this.billList = []
			data.reverse().forEach((item, index) => {
				if (index === 0) {
					const beforeContent = JSON.parse(item.beforeContent)
					const { receivableAmount, useAmt, billItemAmt } = beforeContent || {}
					beforeContent.billItemAmt2 = [useAmt || 0, billItemAmt || 0].reduce((acc, current) => {
						return accSub(acc, current)
					}, receivableAmount || 0)
					this.billList.push({
						name: '原始账单',
						list: [
							{
								title: '原始账单',
								billNo: this.getTicketCellValue(beforeContent, 'billNo'),
								subList: [
									{
										label: '账单编号',
										value: this.getTicketCellValue(beforeContent, 'billNo'),
									},
									{
										label: '创建时间',
										value: this.getTicketCellValue(beforeContent, 'createTime'),
									},
									{
										label: '应缴金额',
										value: this.getTicketCellValue(beforeContent, 'receivableAmount'),
									},
									{
										label: '水量',
										value: this.getTicketCellValue(beforeContent, 'useAmount'),
									},
									{
										label: '水费',
										value: this.getTicketCellValue(beforeContent, 'useAmt'),
									},
									{
										label: '污水费',
										value: this.getTicketCellValue(beforeContent, 'billItemAmt'),
									},
									{
										label: '附加费',
										value: this.getTicketCellValue(beforeContent, 'billItemAmt2'),
									},
								],
							},
						],
					})
				}
				const afterContent = JSON.parse(item.afterContent)
				if (item.adjustType === 2) {
					const isNotByPrice = afterContent.adjustMode !== '按水价'
					this.billList.push({
						name: '减免',
						list: [
							{
								title: '减免',
								subList: [
									{
										label: '减免方式',
										value: this.getTicketCellValue(afterContent, 'adjustMode'),
									},
									{
										label: '减免时间',
										value: this.getTicketCellValue(item, 'createTime'),
									},
									...(isNotByPrice
										? [
												{
													label:
														afterContent.adjustMode == '按污水费'
															? '减免污水费用'
															: '减免水量',
													value: this.getTicketCellValue(afterContent, 'adjustInput'),
												},
										  ]
										: [
												{
													label: '价格编号',
													value: this.getTicketCellValue(afterContent, 'priceCode'),
												},
												{
													label: '价格版本号',
													value: this.getTicketCellValue(afterContent, 'priceVersion'),
												},
										  ]),
								],
							},
							{
								title: '减免后账单',
								billNo: this.getTicketCellValue(afterContent, 'billNo'),
								subList: [
									{
										label: '账单编号',
										value: this.getTicketCellValue(afterContent, 'billNo'),
									},
									{
										label: '应缴金额',
										value: this.getTicketCellValue(afterContent, 'receivableAmount'),
									},
								],
							},
						],
					})
					return
				}
				if (item.adjustType === 3) {
					const isNotByPrice = afterContent.adjustMode !== '按水价'
					this.billList.push({
						name: '费用调整',
						list: [
							{
								title: '调整',
								subList: [
									{
										label: '调整方式',
										value: this.getTicketCellValue(afterContent, 'adjustMode'),
									},
									{
										label: '调整时间',
										value: this.getTicketCellValue(item, 'createTime'),
									},
									...(isNotByPrice
										? [
												{
													label: '调整水量',
													value: this.getTicketCellValue(afterContent, 'adjustInput'),
												},
										  ]
										: [
												{
													label: '价格编号',
													value: this.getTicketCellValue(afterContent, 'priceCode'),
												},
												{
													label: '价格版本号',
													value: this.getTicketCellValue(afterContent, 'priceVersion'),
												},
										  ]),
								],
							},
							{
								title: '调整后账单',
								billNo: this.getTicketCellValue(afterContent, 'billNo'),
								subList: [
									{
										label: '账单编号',
										value: this.getTicketCellValue(afterContent, 'billNo'),
									},
									{
										label: '应缴金额',
										value: this.getTicketCellValue(afterContent, 'receivableAmount'),
									},
								],
							},
						],
					})
					return
				}
				if (item.adjustType === 4) {
					this.billList.push({
						name: '部分缴费',
						list: [
							{
								title: '已缴费',
								billNo: this.getTicketCellValue(afterContent, 'billNo1'),
								subList: [
									{
										label: '账单编号',
										value: this.getTicketCellValue(afterContent, 'billNo1'),
									},
									{
										label: '缴费时间',
										value: this.getTicketCellValue(item, 'createTime'),
									},
									{
										label: '缴费金额',
										value: this.getTicketCellValue(afterContent, 'receivableAmount1'),
									},
								],
							},
							{
								title: '剩余账单',
								billNo: this.getTicketCellValue(afterContent, 'billNo'),
								subList: [
									{
										label: '账单编号',
										value: this.getTicketCellValue(afterContent, 'billNo'),
									},
									{
										label: '应缴金额',
										value: this.getTicketCellValue(afterContent, 'receivableAmount'),
									},
								],
							},
						],
					})
				}
			})

			setTimeout(() => {
				const el = this.$el.querySelector('.bill-list')
				if (el.scrollWidth > el.clientWidth) {
					el.scrollLeft = el.scrollWidth - el.clientWidth
				}
			}, 0)
		},
		async getDetail() {
			if (!this.formData.billNo) return
			const data = await apiGetRecordBillInfo({
				billNo: this.formData.billNo,
			})
			this.detailData = data
		},
		handleSearch() {
			this.getList()
			this.getDetail()
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.billList = []
			this.detailData = {}
		},
	},
}
</script>

<style lang="scss" scoped>
.flex {
	display: flex;
	flex-direction: column;
	height: 100%;
}
.bill-info {
	background-color: #fff;
	margin-bottom: 12px;
	.el-form {
		margin: 20px 20px 0 20px;
		border-bottom: 1px dashed #eef0f3;
	}
}
.trace-panel {
	position: relative;
	background-color: #fff;
	height: calc(100% - 272px);
}
.bill-list {
	position: relative;
	padding: 24px;
	display: flex;
	height: calc(100% - 84px);
	margin: 0 24px;
	border-radius: 4px;
	border: 24px solid #f4f5fb;
	border-top-width: 12px;
	border-bottom-width: 12px;
	overflow: auto;
	background-color: #f4f5fb;
	scroll-behavior: smooth;
}

.trace-panel::before,
.trace-panel::after {
	content: '';
	width: 24px;
	background: linear-gradient(to left, rgba(244, 245, 251, 0), #f4f5fb);
	position: absolute;
	top: 60px;
	bottom: 48px;
	left: 48px;
	z-index: 1;
}
.trace-panel::after {
	right: 48px;
	left: auto;
	background: linear-gradient(to right, rgba(244, 245, 251, 0), #f4f5fb);
}

.bill-list::-webkit-scrollbar:horizontal {
	height: 12px;
	background-color: #e1e2ec;
}

::-webkit-scrollbar:vertical {
	width: 0;
}

.bill-list::-webkit-scrollbar-track {
	background-color: #e1e2ec;
}

.bill-list::-webkit-scrollbar-thumb {
	background-color: #7b7e97;
}

.top-gradient-edge {
	position: relative;
	height: 0;
	width: 100%;
	z-index: 2;
}
.top-gradient-edge::before {
	content: '';
	position: absolute;
	top: 12px;
	left: 24px;
	right: 24px;
	height: 24px;
	background: linear-gradient(to top, rgba(244, 245, 251, 0), #f4f5fb);
}
</style>
