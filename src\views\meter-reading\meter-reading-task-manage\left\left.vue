<template>
	<div class="left-container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
		<div class="btn-group">
			<el-button style="width: 50%" round @click="handleReset">重置</el-button>
			<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
		</div>
	</div>
</template>

<script>
import { bookTypeOptions, taskStatusOptions, handOverOptions } from '@/consts/optionList.js'
import { getAlleyMap } from '@/api/meterReading.api.js'

export default {
	name: '',
	components: {},
	data() {
		return {
			formData: {
				orgCode: '',
				alleyId: '',
				bookNo: '',
				bookType: '',
				taskStatus: '',
				handOver: '',
				taskYear: '',
				taskMonth: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						col: 24,
						clearable: false,
						placeholder: '请选择营业分公司',
					},
					events: {
						change: () => {
							this.formData.alleyId = ''
							this.getAlleyMapData()
						},
					},
				},
				{
					type: 'el-select',
					label: '坊别',
					prop: 'alleyId',
					options: [],
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择坊别',
					},
				},
				{
					type: 'el-input',
					label: '表册编号',
					prop: 'bookNo',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入表册编号',
					},
				},
				{
					type: 'el-select',
					label: '册本类型',
					prop: 'bookType',
					options: bookTypeOptions,
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择册本类型',
					},
				},
				{
					type: 'el-select',
					label: '任务状态',
					prop: 'taskStatus',
					options: taskStatusOptions,
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择任务状态',
					},
				},
				{
					type: 'el-select',
					label: '是否全部移交',
					prop: 'handOver',
					options: handOverOptions,
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择是否全部移交',
					},
				},
				{
					type: 'el-date-picker',
					label: '抄表年',
					prop: 'taskYear',
					attrs: {
						col: 24,
						type: 'year',
						valueFormat: 'yyyy',
					},
				},
				{
					type: 'el-select',
					label: '抄表月',
					prop: 'taskMonth',
					options: [
						{
							value: '1',
							label: '1月',
						},
						{
							value: '2',
							label: '2月',
						},
						{
							value: '3',
							label: '3月',
						},
						{
							value: '4',
							label: '4月',
						},
						{
							value: '5',
							label: '5月',
						},
						{
							value: '6',
							label: '6月',
						},
						{
							value: '7',
							label: '7月',
						},
						{
							value: '8',
							label: '8月',
						},
						{
							value: '9',
							label: '9月',
						},
						{
							value: '10',
							label: '10月',
						},
						{
							value: '11',
							label: '11月',
						},
						{
							value: '12',
							label: '12月',
						},
					],
					attrs: {
						col: 24,
					},
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
			],
			formAttrs: {
				rules: {
					orgCode: {
						required: true,
						message: '请选择营业分公司',
						trigger: 'change',
					},
				},
			},
		}
	},
	computed: {
		orgOptions() {
			return this.formItems[0].options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.getAlleyMapData()
					// this.$emit('search', this.formData)
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		handleReset() {
			this.$refs.formRef.resetFormSmooth()
			this.$emit('reset')
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				const { taskYear, taskMonth } = this.formData
				if (taskMonth && !taskYear) {
					this.$message.warning('请选择抄表年')
					return
				}
				const formData = { ...this.formData }
				if (taskMonth) {
					formData.taskMonth = `${taskYear}-${taskMonth}`
				}
				this.$emit('search', formData)
			}
		},
		// 获取坊别数据
		async getAlleyMapData() {
			try {
				const res = await getAlleyMap({
					orgCode: this.formData.orgCode,
				})
				if (res) {
					this.formItems[1].options = res.map(item => {
						return {
							value: item.id,
							label: item.alleyName,
						}
					})
				}
			} catch (error) {
				console.error(error)
				this.formItems[1].options = []
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.left-container {
	display: flex;
	flex-direction: column;
	flex: 0 0 270px;
	margin-right: 20px;
	padding: 20px;
	background-color: #fff;
}
.el-form {
	flex: 1;
	padding: 0 10px;
	overflow: auto;
}
.btn-group {
	flex: 0 0 52px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	.el-button {
		height: 30px;
	}
}
</style>
