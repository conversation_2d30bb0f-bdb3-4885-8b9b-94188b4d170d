<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-02 19:08:26
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 19:09:51
-->
<template>
	<div class="archives-detail" v-loading.fullscreen.lock="loading">
		<!-- 表卡信息 -->
		<RecordsInfo />
		<!-- 详情tab切换区 -->
		<div class="archives-content">
			<gc-detail-tab
				ref="detailTabRef"
				:tab-list="tabList"
				:default-active-name.sync="defaultActiveName"
				@tab-change="handleTabChange"
			></gc-detail-tab>
		</div>
	</div>
</template>

<script>
import RecordsInfo from './records-info' // 表卡信息
import MeterRecord from './meter-record' // 表卡记录
import OperationHistory from './operation-history' // 操作历史
import ReadingTask from './reading-task' // 抄表任务
export default {
	name: 'meterDetail',
	components: {
		RecordsInfo,
	},
	data() {
		return {
			loading: false,
			defaultActiveName: 'meterRecord',
		}
	},
	computed: {
		tabList() {
			let tabArr = [
				{
					name: 'meterRecord',
					label: '表卡记录',
					component: MeterRecord,
				},
			]
			this.$has('plan-collection_meterReadingBook_getBookModifyRecordList') &&
				tabArr.push({
					name: 'operationHistory',
					label: '操作记录',
					component: OperationHistory,
				})
			this.$has('plan-collection_meterReadingTask_getTaskList') &&
				tabArr.push({
					name: 'readingTask',
					label: '抄表任务',
					component: ReadingTask,
				})
			return tabArr
		},
	},
	mounted() {},
	methods: {
		handleTabChange(data) {
			this.$refs.detailTabRef.$refs.componentRef[data.index].getList()
		},
	},
}
</script>
<style lang="scss" scoped>
.archives-detail {
	width: 100%;
	height: 100%;
	display: flex;
	.archives-content {
		width: calc(100% - 290px);
	}
}
</style>
