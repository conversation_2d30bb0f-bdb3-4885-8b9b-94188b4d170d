<template>
	<!-- 横向布局 -->
	<div class="gc-layout-horizontal fixed">
		<div class="gc-layout-header fixed-header">
			<gc-header />
			<div class="gc-tabs-horizontal">
				<div class="gc-main">
					<gc-tags-view />
				</div>
			</div>
		</div>
		<div class="gc-main main-padding">
			<gc-app-main />
		</div>
	</div>
</template>

<script>
export default {
	name: 'LayoutHorizontal',
	props: {
		collapse: {
			type: Boolean,
			default() {
				return false
			},
		},
		device: {
			type: String,
			default() {
				return 'desktop'
			},
		},
	},
}
</script>

<style lang="scss" scoped>
.gc-layout-horizontal {
	::v-deep {
		.gc-main {
			width: 92% !important;
			margin: auto;
		}
	}

	.gc-tabs-horizontal {
		background: $base-color-white;
		box-shadow: $base-box-shadow;
	}

	.gc-nav {
		.fold-unfold {
			display: none;
		}
	}
}
</style>
