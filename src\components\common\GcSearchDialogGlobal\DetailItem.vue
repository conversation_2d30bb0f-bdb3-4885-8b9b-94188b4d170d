<template>
	<div class="comWrap">
		<p class="curFilterName">
			{{ detailData.name || '--' }}
			<el-button round size="mini" class="detailBtn" @click="gotoDetail">去档案详情</el-button>
		</p>
		<div class="comWrap-content">
			<p class="otherName" v-for="(item, key) in detailData.list" :key="key">
				{{ item.label }}：{{ item.value || '--' }}
			</p>
		</div>
	</div>
</template>

<script>
export default {
	name: 'DetailItem',
	components: {},
	props: {
		detailData: Object,
	},
	data() {
		return {}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		gotoDetail() {
			const { archivesId, userType } = this.detailData
			if (!this.$has('cpm_archives_detail') && userType === 3) {
				this.$notify({
					message: '暂无权限访问，请联系管理员！',
					title: '提示',
					offset: 60,
					type: 'warning',
				})
				return
			}
			if (!this.$has('cpm_archives_detail5') && userType === 4) {
				this.$notify({
					message: '暂无权限访问，请联系管理员！',
					title: '提示',
					offset: 60,
					type: 'warning',
				})
				return
			}

			const path =
				userType === 3
					? '/meterManage/residentMeterView?archivesId=' + archivesId
					: '/meterManage/companyMeterView?archivesId=' + archivesId
			this.$router.push(path)
			this.$emit('close')
		},
	},
}
</script>
<style lang="scss" scoped>
.comWrap {
	flex: 1;
}
.curFilterName {
	font-size: 14px;
	font-weight: 500;
	color: #2f87fe;
	line-height: 21px;
	overflow: hidden;
	.el-button {
		float: right;
		&.perfectBtn {
			color: #ff9d57;
			border-color: #ff9d57;
		}
		&.detailBtn {
			color: #2f87fe;
			border-color: #2f87fe;
		}
	}
}
.otherName {
	font-size: 12px;
	font-weight: 400;
	color: #999999;
	line-height: 18px;
	width: 50%;
}

.comWrap-content {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
}
</style>
