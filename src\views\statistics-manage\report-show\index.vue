<template>
	<div class="page-layout">
		<div class="page-left">
			<gc-model-header
				class="info-title"
				title="报表列表"
				:icon="require(`@/assets/images/icon/title-multi-check.png`)"
			/>
			<div v-loading="treeLoading" class="tree-box">
				<el-tree
					v-show="reportList.length > 0"
					ref="treeRef"
					:data="reportList"
					node-key="id"
					highlight-current
					default-expand-all
					expand-on-click-node
					:props="defaultProps"
					@node-click="handleNodeClick"
				>
					<template #default="{ node }">
						<el-tooltip v-if="node.label && node.label.length > 12" :content="node.label" placement="top" effect="dark">
							<span class="tree-node-label">{{ node.label }}</span>
						</el-tooltip>
						<span v-else class="tree-node-label">{{ node.label }}</span>
					</template>
				</el-tree>
				<gc-empty v-show="reportList.length == 0" />
			</div>
		</div>
		<div class="page-right">
			<iframe v-if="currentReportUrl" :src="currentReportUrl" frameborder="0" width="100%" height="100%"></iframe>
			<gc-empty v-else />
		</div>
	</div>
</template>

<script>
import { reportTreeList } from '@/api/statisticsManage.api'

export default {
	name: 'ReportShow',
	components: {},
	data() {
		return {
			defaultProps: {
				children: 'children',
				label: 'label',
			},
			treeLoading: false,
			reportList: [],
			currentReportUrl: '',

			// 最后一次点击的节点key
			lastNodeKey: null,
		}
	},
	computed: {},
	beforeRouteEnter(to, from, next) {
		next(vm => {
			vm.$nextTick(() => {
				vm.getTypeList()
			})
		})
	},
	methods: {
		handleNodeClick(data) {
			if (data.isLeaf) {
				this.lastNodeKey = data.id
				const { orgCode, staffId } = this.$store.getters.userInfo || {}
				this.currentReportUrl = data.reportUrl + '&code=' + orgCode + '&ystaffId=' + staffId
			} else {
				this.$refs.treeRef.setCurrentKey(this.lastNodeKey)
			}
		},
		// 获取左侧报表列表
		async getTypeList() {
			this.treeLoading = true
			try {
				const data = await reportTreeList()
				this.reportList = this.handleTreeData(data || []) || []
				if (this.reportList.length > 0) {
					this.currentReportUrl = this.reportList[0].reportUrl
				}
			} catch (error) {
				console.error(error)
				this.reportList = []
				this.currentReportUrl = ''
			} finally {
				this.treeLoading = false
			}
		},

		// 处理tree数据
		handleTreeData(data = []) {
			return data.map((item, index) => {
				const { categoryId, categoryName: label, reportList = [] } = item
				return {
					id: `category${index}`,
					categoryId,
					label,
					children: reportList.map(it => {
						const { id, reportName: label, reportUrl } = it
						return {
							id,
							categoryId,
							label,
							reportUrl,
							isLeaf: true,
						}
					}),
				}
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.page-left {
	overflow: auto;
}
.info-title {
	height: auto;
	padding: 0;
	margin-bottom: 12px;
}
.tree-box {
	height: calc(100% - 45px);
}
.el-tree {
	height: 100%;
	overflow: auto;
	.tree-node-label{
		overflow: hidden;
		text-overflow: ellipsis;
	}
	::v-deep {
		.el-tree-node__content {
			height: 32px;
		}
		.el-tree-node__label {
			@include text-overflow;
		}
		.el-tree-node.is-current > .el-tree-node__content {
			background-color: rgba(196, 221, 255, 0.5);
			.el-tree-node__label {
				color: #2f87fe;
			}
		}
	}
}
</style>
