<template>
	<GcElDialog :show="isShow" title="小区接管登记" custom-top="220px" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:addressAreaIds>
				<el-tag
					v-for="(tag, index) in formData.addressAreaIds"
					:key="tag.addressAreaId"
					closable
					@close="handleCloseTag(index)"
				>
					{{ tag.name }}
				</el-tag>
			</template>
		</GcFormSimple>

		<template #footer>
			<GcButton btn-type="three" @click.native="handleClose">取消</GcButton>
			<GcButton @click.native="handleSave">保存</GcButton>
		</template>
	</GcElDialog>
</template>

<script>
import { ruleRequired } from '@/utils/rules.js'
import { apiRecordTakeOver } from '@/api/meterManage.api.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		data: {
			require: true,
			type: Array,
		},
	},
	watch: {
		show(val) {
			if (val) {
				this.formData.addressAreaIds = this.data.map(item => {
					return {
						name: item.areaName,
						addressAreaId: item.addressAreaId,
					}
				})
			}
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				addressAreaIds: [],
				receiver: '',
				receiveDate: '',
			},
			formItems: [
				{
					type: 'slot',
					slotName: 'addressAreaIds',
					label: '预接管小区',
					prop: 'addressAreaIds',
					attrs: {},
				},
				{
					type: 'el-input',
					label: '接管人',
					prop: 'receiver',
					attrs: {},
				},
				{
					type: 'el-date-picker',
					label: '接管时间',
					prop: 'receiveDate',
					attrs: {
						type: 'date',
						format: 'yyyy-MM-dd',
						valueFormat: 'yyyy-MM-dd',
					},
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					addressAreaIds: [ruleRequired('必填')],
					receiveDate: [ruleRequired('必填')],
					receiver: [ruleRequired('必填')],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const formParams = trimParams(removeNullParams(this.formData))
			if (formParams.addressAreaIds && formParams.addressAreaIds.length > 0) {
				formParams.addressAreaIds = formParams.addressAreaIds.map(item => item.addressAreaId)
			}
			await apiRecordTakeOver(formParams)
			this.$message.success('接管登记成功')
			this.$emit('success')
			this.handleClose()
		},
		handleCloseTag(index) {
			this.formData.addressAreaIds.splice(index, 1)
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
.el-tag {
	margin-right: 10px;
}
</style>
