# 排序池

传入一个对象数组，支持对数组项目拖拽排序，并返回排序后的数组。支持使用 `use-checked` 属性启用选择，此时仅返回选中的项目。

## 组件使用

```vue
<template>
  <SortPool ref="sortPool" :source="source" checkable @change="handleSortChange"></SortPool>
</template>
<script>
import SortPool from '@/components/SortPool/index.vue'
export default {
  components: {
    SortPool
  },
  data() {
    this.source = [
      {
        key: 'orgName',
        name: '营业所分公司',
        tooltip: true,
        useBy: ['meter', 'reading', 'bill', 'payment'],
      },
      {
        key: 'archivesIdentity',
        name: '表卡编号',
        tooltip: true,
        useBy: ['meter', 'reading', 'bill', 'payment'],
      },
      {
        key: 'bookNo',
        name: '表册编号',
        tooltip: true,
        useBy: ['meter', 'reading', 'bill', 'payment'],
      },
      {
        key: 'alleyCode',
        name: '坊别',
        tooltip: true,
        useBy: ['meter', 'reading', 'bill', 'payment'],
      },
    ]
    return {
      sortedData: [
        {
          key: 'archivesIdentity',
          name: '表卡编号',
          sort: 0,
          origin: {
            key: 'archivesIdentity',
            name: '表卡编号',
            tooltip: true,
            useBy: ['meter', 'reading', 'bill', 'payment'],
          },
        },
        {
          key: 'orgName',
          name: '营业所分公司',
          sort: 1,
          origin: {
            key: 'orgName',
            name: '营业所分公司',
            tooltip: true,
            useBy: ['meter', 'reading', 'bill', 'payment'],
          },
        },
        {
          key: 'alleyCode',
          name: '坊别',
          sort: 2,
          origin: {
            key: 'alleyCode',
            name: '坊别',
            tooltip: true,
            useBy: ['meter', 'reading', 'bill', 'payment'],
          },
        },
        {
          key: 'bookNo',
          name: '表册编号',
          sort: 3,
          origin: {
            key: 'bookNo',
            name: '表册编号',
            tooltip: true,
            useBy: ['meter', 'reading', 'bill', 'payment'],
          },
        },
      ]
    }
  },
  methods: {
    // 通过 change 事件获取排序后的数组
    handleSortChange(sortedData) {
      this.sortedData = sortedData
    }
  },
  mounted() {
    // 通过 setData 方法赋值数组
    this.$refs.sortPool.setData(this.sortedData)

    // 也可直接获取排序后的数组
    const sortedData = this.$refs.sortPool.getData()
  }
}
</script>
```
