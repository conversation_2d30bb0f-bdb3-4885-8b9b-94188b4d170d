/*
 * @Description
 * @Version:
 * @Autor: houyan
 * @Date: 2024-09-03 16:12:44
 * @LastEditors: houyan
 * @LastEditTime: 2024-10-28 15:53:51
 */
export function getFormItems(_this) {
	return [
		{
			type: 'el-input',
			label: '表卡编号',
			prop: 'archivesIdentity',
			attrs: {
				placeholder: '请输入',
				col: 6,
				disabled: <PERSON><PERSON><PERSON>(_this.$route.query.archivesId),
			},
		},
		{
			type: 'slot',
			slotName: 'button',
			attrs: {
				col: 2,
			},
		},
		{
			type: 'el-input',
			label: '坊别',
			prop: 'alleyCode',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '表册编号',
			prop: 'bookNo',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '区/县',
			prop: 'regionName',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '地址',
			prop: 'addressFullName',
			attrs: {
				col: 16,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '水表仓库编号',
			prop: 'meterWarehouseCode',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '用户/企业名称',
			prop: 'userName',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '联系人',
			prop: 'contactPeople',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '手机',
			prop: 'userMobile',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '价格编号',
			prop: 'priceCode',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '价格名称',
			prop: 'priceName',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '用水性质',
			prop: 'natureName',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
	]
}
