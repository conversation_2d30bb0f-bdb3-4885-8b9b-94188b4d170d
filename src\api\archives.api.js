import service from './request'
export const moduleNames = 'cpm/'
let userBaseUrl = process.env.VUE_APP_API_BASE_URL

//行政区域级联查询
export function apiGetRegion(parameter) {
	return service({
		url: moduleNames + 'region/children',
		method: 'GET',
		params: parameter,
	})
}

//行政区域级联查询
export function apiGetArchivesList(data) {
	return service({
		url: moduleNames + 'archives/list',
		method: 'POST',
		data: data,
	})
}

// 获取小区列表
export function apiGetArea(data) {
	return service({
		url: moduleNames + 'address-areas/list',
		method: 'POST',
		data: data,
	})
}

// 实时查询街道/小区
export function apiRealTimeQuery(data) {
	return service({
		url: moduleNames + 'address-polls/search',
		method: 'GET',
		params: data,
	})
}

//详细地址查询关联档案
export function apiByAdressList(parameter) {
	return service({
		url: moduleNames + 'archives/by-address',
		method: 'GET',
		params: parameter,
	})
}

// 获取表具类型
export function apiMeterTypeList(parameter) {
	return service({
		url: moduleNames + 'device-type/tenant/meter-type',
		method: 'GET',
		params: parameter,
	})
}

//获取dtu类型
export function apiDtuTypeList(parameter) {
	return service({
		url: moduleNames + 'device-type/tenant/dtu-type',
		method: 'get',
		params: parameter,
	})
}

// ES搜索DTU
export function apiDtuList(parameter) {
	return service({
		url: moduleNames + 'dtu/search',
		method: 'get',
		params: parameter,
	})
}

// 查询复用用户信息
export function apiUserSearch(parameter) {
	return service({
		url: moduleNames + 'user/search',
		method: 'POST',
		data: parameter,
	})
}

// 查询复用表具信息
export function apiMeterSearch(parameter) {
	return service({
		url: moduleNames + 'meter/waitInstallMeterList',
		method: 'get',
		params: parameter,
	})
}

//档案详情历史价格
export function apiHistoricalPrice(parameter) {
	return service({
		url: moduleNames + 'archives/history-price',
		method: 'GET',
		params: parameter,
	})
}

//获取生效的价格
export function apiEffectivePrice(parameter) {
	return service({
		url: moduleNames + 'prices/effective',
		method: 'GET',
		params: parameter,
	})
}

//获取所有价格包含禁用
export function apiAllPrices(parameter) {
	return service({
		url: moduleNames + 'prices/list-price',
		method: 'GET',
		params: parameter,
	})
}

// 换价
export function apiExchangePrice(parameter) {
	return service({
		url: moduleNames + 'archives/change-price',
		method: 'PUT',
		data: parameter,
	})
}

// 查询影响档案列表
export function apiArchivesAffect(parameter) {
	return service({
		url: moduleNames + 'archives/affect',
		method: 'get',
		params: parameter,
	})
}

// 单户建档
export function apiCreateArchives(parameter) {
	return service({
		url: moduleNames + 'archives',
		method: 'POST',
		data: parameter,
	})
}

//档案更新
export function apiUpdateArchives(parameter) {
	return service({
		url: moduleNames + 'archives',
		method: 'PUT',
		data: parameter,
	})
}

// ----批量建档 start----

// 批量建档上传Excel
export function apiBatchUpload(data) {
	let obj = {
		url: moduleNames + 'archives-batch',
		method: 'POST',
		data: data,
		timeout: 600000,
	}
	return service(obj)
}
// 批量建档入库
export function apiBatchRecordAdd(data) {
	let obj = {
		url: moduleNames + 'archives-batch/record',
		method: 'POST',
		data: data,
		timeout: 600000,
	}
	return service(obj)
}
// 批量缴费入库
export function apiBatchRechargeRecordAdd(data) {
	let obj = {
		url: moduleNames + 'charge-batch/record',
		method: 'POST',
		data: data,
		timeout: 600000,
	}
	return service(obj)
}
// 查询批量建档记录
export function apiBatchRecordList(data) {
	return service({
		url: moduleNames + 'archives-batch/record/list',
		method: 'POST',
		data: data,
	})
}
// 下载批量建档错误记录
export function apiDownFailRecord(params) {
	return service({
		url: moduleNames + 'archives-batch/fail',
		method: 'GET',
		params: params,
	})
}

// ----批量建档 End----

//  根据大类编码获取字典小类
export function apiGetSysDataList(data) {
	return service({
		url: moduleNames + 'sysdata/getsysdatalist',
		method: 'GET',
		params: data,
	})
}
// 获取租户组织结构列表
export function apiGetTenantOrgList(parameter, others) {
	let obj = {
		url: 'v1/tos/organization/sys/tree',
		method: 'GET',
		params: parameter,
	}
	if (others && others.constructor === Object) {
		obj = { ...obj, ...others }
	}
	return service(obj)
}
//获取租户所在省份系统级
export function apiGetTenantPlaceSys(parameter) {
	return service({
		url: '/v1/tos/organization/detail',
		method: 'GET',
		params: parameter,
		baseURL: userBaseUrl,
	})
}
// 档案详情查询
export function apiArchivesDetail(params) {
	return service({
		url: moduleNames + 'archives/detail',
		method: 'GET',
		params: params,
	})
}
// 其他费用明细查询
export function apiOtherExpensesList(parameter) {
	return service({
		url: moduleNames + 'other-cost/list',
		method: 'POST',
		data: parameter,
	})
}
//缴费明细
export function apiPaymentList(parameter) {
	return service({
		url: moduleNames + 'archives/costrecords',
		method: 'POST',
		data: parameter,
	})
}
// 获取最新抄表信息
export function apiLatestReadInfo(parameter) {
	return service({
		url: moduleNames + 'meter/latest-read-info',
		method: 'GET',
		params: parameter,
	})
}

// 查询告警记录
export function apiAlarmLog(parameter) {
	return service({
		url: moduleNames + 'archives/alarmrecords',
		method: 'POST',
		data: parameter,
	})
}

// 日志列表查询
export function apiDailyRecord(parameter) {
	return service({
		url: moduleNames + 'archives/modifyrecords',
		method: 'POST',
		data: parameter,
	})
}

// 结算明细列表查询
export function apiSettleList(parameter) {
	return service({
		url: moduleNames + 'archives/settle-detail',
		method: 'POST',
		data: parameter,
	})
}

// 结算明细折线图查询
export function apiSettleChart(parameter) {
	return service({
		url: moduleNames + 'archives/settle-records-group',
		method: 'POST',
		data: parameter,
	})
}

//修改小区
export function apiChangeAddressAreas(data) {
	return service({
		url: moduleNames + 'address-areas',
		method: 'PUT',
		data: data,
	})
}

//指令下发（开关阀）
export function apiValveControl(parameter) {
	return service({
		url: moduleNames + 'code/web/send',
		method: 'POST',
		data: parameter,
	})
}

//点火
export function apiIgnition(parameter) {
	return service({
		url: moduleNames + 'archives/build',
		method: 'PUT',
		data: parameter,
	})
}

// 更换模组
export function apiModuleReplace(parameter) {
	return service({
		url: moduleNames + 'archives/change-meter-type',
		method: 'PUT',
		data: parameter,
	})
}

// 过户
export function apiChangeUser(parameter) {
	return service({
		url: moduleNames + 'archives/change-user',
		method: 'PUT',
		data: parameter,
	})
}

//档案上次停用原因
export function apiLastDisabledReason(parameter) {
	return service({
		url: moduleNames + 'archives/last-disable-reason',
		method: 'GET',
		params: parameter,
	})
}

//停用
export function apiArchivesDisabled(parameter) {
	return service({
		url: moduleNames + 'archives/disable',
		method: 'PUT',
		data: parameter,
	})
}

//启用
export function apiArchivesEnable(parameter) {
	return service({
		url: moduleNames + 'archives/enable',
		method: 'PUT',
		data: parameter,
	})
}

//销档
export function apiArchivesClose(parameter) {
	return service({
		url: moduleNames + 'archives/close',
		method: 'PUT',
		data: parameter,
	})
}

//标记黑名单
export function apiLockBlacklist(parameter) {
	return service({
		url: moduleNames + 'user',
		method: 'POST',
		data: parameter,
	})
}

//移出黑名单
export function apiRemoveBlacklist(parameter) {
	return service({
		url: moduleNames + 'user',
		method: 'PUT',
		data: parameter,
	})
}

// es查询用户信息
export function apiReuseUser(parameter) {
	return service({
		url: moduleNames + 'user/search',
		method: 'POST',
		data: parameter,
	})
}

// 换表
export function apiChangeMeter(parameter) {
	return service({
		url: moduleNames + 'archives/change-meter',
		method: 'PUT',
		data: parameter,
	})
}

// es查询表具信息
export function apiReuseMeter(parameter) {
	return service({
		url: moduleNames + 'meter/waitInstallMeterList',
		method: 'GET',
		params: parameter,
	})
}

// 更换DTU
export function apiChangeDTU(parameter) {
	return service({
		url: moduleNames + 'archives/change-dtu',
		method: 'PUT',
		data: parameter,
	})
}

// es查询DTU信息
export function apiReuseDtu(parameter) {
	return service({
		url: moduleNames + 'dtu/search',
		method: 'GET',
		params: parameter,
	})
}

// 登记其他费用
export function apiAddOtherExpense(parameter) {
	return service({
		url: moduleNames + 'other-cost/register',
		method: 'POST',
		data: parameter,
	})
}

// 修改已登记的其他费用
export function apiEditOtherExpense(parameter) {
	return service({
		url: moduleNames + 'other-cost/modify',
		method: 'POST',
		data: parameter,
	})
}

// 撤销登记其他费用
export function apiCancelOtherExpense(parameter) {
	return service({
		url: moduleNames + 'other-cost/undo',
		method: 'POST',
		data: parameter,
	})
}

// 取消收费-其他费用
export function apiOtherExpenseCancleRecharge(parameter) {
	return service({
		url: moduleNames + 'other-cost/cancel',
		method: 'POST',
		data: parameter,
	})
}

// 取消收费-充值
export function apiCancleRecharge(parameter) {
	return service({
		url: moduleNames + 'charge/cancel-recharge',
		method: 'POST',
		data: parameter,
	})
}

// 取消收费-补气
export function apiCancleReissue(parameter) {
	return service({
		url: moduleNames + 'charge/cancel-reissue',
		method: 'POST',
		data: parameter,
	})
}

//缴费详情
export function apiFeeDetails(parameter) {
	return service({
		url: moduleNames + 'charge/charge-detail',
		method: 'POST',
		data: parameter,
	})
}

//缴费详情
export function apiInvoiceDetails(parameter) {
	return service({
		url: moduleNames + 'nuonuo-invoice/invoice-detail',
		method: 'GET',
		params: parameter,
	})
}

//新增小区
export function apiAddAddressAreas(data) {
	return service({
		url: moduleNames + 'address-areas/add',
		method: 'post',
		data: data,
	})
}

//查询街道/小区
export function apiGetAddressList(parameter) {
	return service({
		url: moduleNames + 'address-areas/list',
		method: 'post',
		data: parameter,
	})
}
// 获取分公司下所有营业厅的所属城市
export function apiGetAllbuzHallsList(parameter) {
	return service({
		url: moduleNames + 'region/org/city',
		method: 'get',
		params: parameter,
	})
}
// 禁用小区
export function apiAddressDisable(parameter) {
	return service({
		url: moduleNames + 'address-areas/disable',
		method: 'PUT',
		data: parameter,
	})
}
// 获取修改小区影响档案数
export function apiInfluencedArchives(parameter) {
	return service({
		url: moduleNames + 'address-areas/modify-count',
		method: 'get',
		params: parameter,
	})
}
//查询小区操作记录列表
export function apiAddressRecordList(parameter) {
	return service({
		url: moduleNames + 'address-areas/record-list',
		method: 'POST',
		data: parameter,
	})
}

// 获取员工列表
export const apiGetStaffList = params => {
	return service({
		url: '/v1/tos/staff/list',
		method: 'get',
		params: params,
	})
}

// 第三方采集表具重新注册
export function apiThirdRegisterDevice(data) {
	return service({
		url: '/cpm/archives/third-register-device',
		method: 'POST',
		data,
	})
}

// 费用调整记录保存
export function apiAdjustCostRecord(data) {
	return service({
		url: '/cpm/archives/add-adjust-cost-record',
		method: 'POST',
		data,
	})
}

// 费用调整历史查询
export function apiGetAdjustRecordList(data) {
	return service({
		url: '/cpm/archives/list-adjust-cost-record',
		method: 'POST',
		data,
	})
}

// 费用调整明细
export function apiGetAdjustRecordDetail(params) {
	return service({
		url: '/cpm/archives/adjust-cost-detail',
		method: 'GET',
		params,
	})
}

// 收费结算
export function apiGetSettlementFees(data) {
	return service({
		url: '/cpm/archives/settlementFees',
		method: 'POST',
		data,
	})
}

// 欠费档案查询
export function apiGetArrearsList(data) {
	return service({
		url: '/cpm/archives/arrears-list',
		method: 'POST',
		data,
	})
}

// 短信催收
export function apiSendArreasMsg(data) {
	return service({
		url: '/cpm/archives/send-arrears-msg',
		method: 'POST',
		data,
	})
}

// 短信催收进度查询
export function apiGetPushDetailProgress(data) {
	return service({
		url: '/cpm/archives/arrears-push-msg',
		method: 'POST',
		data,
		headers: {
			nomessageTip: true,
		},
	})
}

// 短信催收历史总览获取
export function apiGetPushRecordList(data) {
	return service({
		url: '/cpm/archives/push-msg-list',
		method: 'POST',
		data,
	})
}

// 短信催收历史明细查询
export function apiGetPushRecordDetail(data) {
	return service({
		url: '/cpm/archives/push-msg-detail-list',
		method: 'POST',
		data,
	})
}

// 欠费档案导出
export function apiExportArchives2Excel(data) {
	return service({
		url: '/cpm/report/arreas-archives/export/excel',
		method: 'POST',
		data,
		responseType: 'blob',
	})
}

/*月账单 start */
//月账单列表
export function apiMonthBillingList(parameter) {
	return service({
		url: moduleNames + 'archives/monthlyBill/list',
		method: 'POST',
		data: parameter,
	})
}

//月账单详情
export function apiMonthBillingDetails(parameter) {
	return service({
		url: moduleNames + 'archives/monthlyBill',
		method: 'GET',
		params: parameter,
	})
}

//月账单发票详情
export function apiMonthBillingInvoiceDetails(parameter) {
	return service({
		url: moduleNames + 'nuonuo-invoice/monthly-invoice-detail',
		method: 'GET',
		params: parameter,
	})
}

//发票开具
export function apiMonthBillingOpeningInvoice(parameter) {
	return service({
		url: 'cpm/nuonuo-invoice/monthly-opening-invoice ',
		method: 'POST',
		data: parameter,
	})
}

//发票红冲
export function apiMonthBillingRedhashInvoice(parameter) {
	return service({
		url: 'cpm/nuonuo-invoice/monthly-red-invoice',
		method: 'POST',
		data: parameter,
	})
}

export function apiGetVerificationList(data) {
	return service({
		url: '/cpm/metrological-verification/record-list',
		method: 'POST',
		data,
	})
}

export function apiGetVerificationRecordDetails(data) {
	return service({
		url: '/cpm/metrological-verification/record-batch-details',
		method: 'POST',
		data,
	})
}

export function apiBatchDeleteArchives(data) {
	return service({
		url: '/cpm/metrological-verification/batch',
		method: 'DELETE',
		data,
	})
}

/*月账单 end */

// 购方信息保存
export function apiSaveArchivesBuyer(parameter) {
	return service({
		url: moduleNames + 'invoice-buyer',
		method: 'PUT',
		data: parameter,
	})
}
