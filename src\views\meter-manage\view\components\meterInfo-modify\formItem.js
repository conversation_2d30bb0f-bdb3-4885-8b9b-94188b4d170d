export const getFormItems = function (_this) {
	return [
		{
			type: 'el-input',
			label: '水表编号',
			prop: 'meterNo',
		},
		{
			type: 'el-input',
			label: '水表仓库编号',
			prop: 'meterWarehouseCode',
		},
		{
			type: 'el-input',
			label: '防盗编号',
			prop: 'antiTheftCode',
		},
		{
			type: 'el-input',
			label: '水表标号',
			prop: 'baseMeterNo',
		},
		{
			type: 'el-select',
			label: '水表类型',
			prop: 'meterTypeId',
			options: _this.meterTypeOptions,
			attrs: {
				col: 12,
			},
			events: {
				change: value => {
					const data = _this.meterTypeOptions.find(item => item.value === value)
					if (data) {
						const { manufacturerName, meterRange, useYears } = data
						_this.formData.manufacturerName = manufacturerName
						_this.formData.ranges = meterRange
						_this.formData.useYears = useYears
					} else {
						_this.formData.manufacturerName = ''
						_this.formData.ranges = ''
						_this.formData.useYears = ''
					}
				},
			},
		},
		{
			type: 'el-input',
			label: '水表厂商',
			prop: 'manufacturerName',
			attrs: {
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '水表型号',
			prop: 'meterModel',
		},
		{
			type: 'el-input',
			label: '口径',
			prop: 'caliber',
		},
		{
			type: 'el-input',
			label: '量程',
			prop: 'ranges',
		},
		{
			type: 'el-select',
			label: '装表位置',
			prop: 'installPosition',
			options: _this.$store.getters.dataList.installPosition
				? _this.$store.getters.dataList.installPosition.map(item => {
						return {
							label: item.sortName,
							value: Number(item.sortValue),
						}
				  })
				: [],
		},
		{
			type: 'el-input',
			label: '表井位置',
			prop: 'tableWellLocation',
		},
		{
			type: 'el-input',
			label: '服役年限',
			prop: 'useYears',
			attrs: {
				disabled: true,
			},
		},
	]
}
