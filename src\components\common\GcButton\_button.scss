/* 按钮配置 */
$button-border-radius: 16px;
$button-font-size: $base-font-size-default;
$button-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
  border-color 0.15s ease-in-out !default;

// 默认状态
$button-color-one: $base-color-white!important;
$button-color-two: $base-color-blue !important;
$button-color-three: #4e4e4e !important;

$button-bg-color-one: $base-color-blue;
$button-bg-color-two: $base-color-white;
$button-bg-color-three: $base-color-white;

$button-border-color-one: $base-color-blue;
$button-border-color-two: $base-color-blue;
$button-border-color-three: #d8d8d8;

// 悬停状态
$button-hover-color-one: $base-color-white;
$button-hover-color-two: #599fff;
$button-hover-color-three: #727272;

$button-hover-bg-color-one: #599fff;
$button-hover-bg-color-two: #fcfcfc;
$button-hover-bg-color-three: $base-color-white;

$button-hover-border-color-one: #599fff;
$button-hover-border-color-two: #599fff;
$button-hover-border-color-three: #e0e0e0;

// 按下状态
$button-focus-color-one: $base-color-white;
$button-focus-color-two: $base-color-blue;
$button-focus-color-three: #4e4e4e;

$button-focus-bg-color-one: #2a7ae6;
$button-focus-bg-color-two: $base-color-white;
$button-focus-bg-color-three: $base-color-white;

$button-focus-border-color-one: #2a7ae6;
$button-focus-border-color-two: #2a7ae6;
$button-focus-border-color-three: #bfbfbf;
// 禁用状态
$button-disabled-color-one: $base-color-white;
$button-disabled-color-two: #accfff;
$button-disabled-color-three: #b9b9b9;

$button-disabled-bg-color-one: #accfff;
$button-disabled-bg-color-two: #accfff;
$button-disabled-bg-color-three: #f0f0f0;

$button-disabled-border-color-one: #accfff;
$button-disabled-border-color-two: #accfff;
$button-disabled-border-color-three: #f0f0f0;


$btnTypes: one, two, three;
$button-colors: $button-color-one, $button-color-two, $button-color-three;
$button-bg-colors: $button-bg-color-one, $button-bg-color-two,
  $button-bg-color-three;
$button-border-colors: $button-border-color-one, $button-border-color-two,
  $button-border-color-three;
//
$button-hover-colors: $button-hover-color-one, $button-hover-color-two,
  $button-hover-color-three;
$button-hover-bg-colors: $button-hover-bg-color-one, $button-hover-bg-color-two,
  $button-hover-bg-color-three;
$button-hover-border-colors: $button-hover-border-color-one,
  $button-hover-border-color-two, $button-hover-border-color-three;
//
$button-focus-colors: $button-focus-color-one, $button-focus-color-two,
  $button-focus-color-three;
$button-focus-bg-colors: $button-focus-bg-color-one, $button-focus-bg-color-two,
  $button-focus-bg-color-three;
$button-focus-border-colors: $button-focus-border-color-one,
  $button-focus-border-color-two, $button-focus-border-color-three;
//
$button-disabled-colors: $button-disabled-color-one, $button-disabled-color-two,
  $button-disabled-color-three;
$button-disabled-bg-colors: $button-disabled-bg-color-one,
  $button-disabled-bg-color-two, $button-disabled-bg-color-three;
$button-disabled-border-colors: $button-disabled-border-color-one,
  $button-disabled-border-color-two, $button-disabled-border-color-three;

@for $i from 1 through length($btnTypes) {
  .gc-button-#{nth($btnTypes, $i)} {
    color: nth($button-colors, $i);
    background-color: nth($button-bg-colors, $i);
    border-color: nth($button-border-colors, $i) !important;
    &:hover {
      color: nth($button-hover-colors, $i) !important;
      background-color: nth($button-hover-bg-colors, $i);
      border-color: nth($button-hover-border-colors, $i) !important;
    }
    &:focus {
      color: nth($button-focus-colors, $i);
      background-color: nth($button-focus-bg-colors, $i);
      border-color: nth($button-focus-border-colors, $i) !important;
    }
    &.disabled {
      color: nth($button-disabled-colors, $i)!important;
      background-color: nth($button-disabled-bg-colors, $i);
      border-color: nth($button-disabled-border-colors, $i) !important;
    }
  }
}

.gc-button {
  position: relative;
  display: inline-block;
  padding: 0 22px;
  height: 32px;
  line-height: 32px;
  border-radius: $button-border-radius;
  font-size: $button-font-size;
  border: none;
  outline: none;
  border: 1px solid;
  box-sizing: border-box;
  cursor: pointer;
  transition: $button-transition;
  white-space: nowrap;
  text-align: center;
  vertical-align: middle;
  background-image: none;
  transition: all ease-in-out 0.2s;
  &.disabled {
    cursor: not-allowed;
  }
}