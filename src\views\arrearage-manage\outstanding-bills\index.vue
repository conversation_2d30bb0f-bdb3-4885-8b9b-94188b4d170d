<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>

			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div
				v-has="['cpm_urge_payment_urgeRegister', 'billing_recall_ls']"
				v-if="tableData.length"
				class="right-top"
			>
				<div>
					<div v-show="selectedData.length">
						已勾选
						<span class="num">{{ selectedData.length }}</span>
						笔欠费账单，合计欠费金额
						<span class="num">{{ selectedDataAmount }}</span>
						元，欠费总水量
						<span class="num">{{ selectedDataWaterYield }}</span>
						吨
					</div>
				</div>
				<div>
					<el-button
						v-has="'billing_recall_ls'"
						type="primary"
						:disabled="selectedData.length === 0"
						@click="handlePrintLs(selectedData)"
					>
						打印旅顺水费通知单
					</el-button>
					<el-button
						v-has="'cpm_urge_payment_urgeRegister'"
						type="primary"
						:disabled="selectedData.length === 0"
						@click="handleCheckIn(selectedData)"
					>
						催缴登记
					</el-button>
				</div>
			</div>
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				:needType="$has('cpm_urge_payment_urgeRegister') ? 'selection' : ''"
				@current-page-change="handlePageChange"
				@selectChange="handleSelectChange"
			>
				<template #priceDetail="{ row }">
					<div v-html="row.priceDetail" />
				</template>
				<template #priceBillItemList="{ row }">
					<div v-html="surchangePrice(row.priceBillItemList)" />
				</template>
				<template v-slot:deal="{ row }">
					<el-button
						v-has="'cpm_urge_payment_urgeRegister'"
						type="text"
						size="medium"
						@click="handleCheckIn([row])"
					>
						催缴登记
					</el-button>
					<el-button
						v-has="'cpm_urge_payment_printNotice'"
						type="text"
						size="medium"
						@click="handlePrintNotification"
					>
						打印欠费通知单
					</el-button>
				</template>
			</GcTable>
		</div>

		<!-- 催缴登记 -->
		<CheckInDialog
			:show.sync="showCheckIn"
			:date-range="formData.dateRange"
			:data="chosedData"
			@success="getList(1)"
		/>
	</div>
</template>

<script>
import { getColumn } from './tableColumn'
import CheckInDialog from '../components/checkInDialog.vue'
import { accAdd } from '@/utils/calc'
import { arrearagePrint } from '@/views/print/arrearage/index.js'
import { getAlleyMap, getStaffMap } from '@/api/meterReading.api.js'
import { queryBillPage } from '@/api/arrearageManage.api'
import { billingLsBatchPrint } from '@/views/print/arrearage/index.js'

export default {
	name: 'OutStandingBills',
	components: { CheckInDialog },
	data() {
		return {
			// 左侧查询
			formData: {
				orgCode: '',
				alleyCode: '',
				bookNo: '',
				archivesNo: '',
				userName: '',
				meterReadingStaffId: '',
				dateRange: [
					this.dayjs().startOf('year').format('YYYY-MM-DD'),
					this.dayjs().endOf('year').format('YYYY-MM-DD'),
				],
				lastUrgeDays: undefined,
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						col: 24,
						clearable: false,
						placeholder: '请选择营业分公司',
					},
					events: {
						change: () => {
							this.formData.alleyCode = ''
							this.formData.meterReadingStaffId = ''
							this.getAlleyMapData()
							this.getStaffMapData()
						},
					},
				},
				{
					type: 'el-select',
					label: '坊别',
					prop: 'alleyCode',
					options: [],
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择坊别',
					},
				},
				{
					type: 'el-input',
					label: '表册编号',
					prop: 'bookNo',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入表册编号',
					},
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesNo',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
				{
					type: 'el-input',
					label: '用户名称',
					prop: 'userName',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入用户名称',
					},
				},
				{
					type: 'el-select',
					label: '抄表员',
					prop: 'meterReadingStaffId',
					options: [],
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择抄表员',
					},
				},
				{
					type: 'el-date-picker',
					label: '开账日期',
					prop: 'dateRange',
					attrs: {
						col: 24,
						clearable: false,
						type: 'daterange',
						valueFormat: 'yyyy-MM-dd',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
					},
				},
				{
					type: 'el-input-number',
					label: '距离上次催缴天数',
					prop: 'lastUrgeDays',
					attrs: {
						col: 24,
						min: 1,
						max: 9999,
						clearable: true,
						placeholder: '请输入距离上次催缴天数',
					},
				},
			],
			formAttrs: {
				rules: {
					orgCode: {
						required: true,
						message: '请选择营业分公司',
						trigger: 'change',
					},
					dateRange: {
						required: true,
						message: '请选择开账日期',
						trigger: 'change',
					},
				},
			},
			// 右侧列表
			loading: false,
			columns: getColumn(this),
			tableData: [{}],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 列表选中数据
			selectedData: [],

			// 催缴登记弹窗
			showCheckIn: false,
			chosedData: [],
		}
	},
	computed: {
		// 合计欠费金额
		selectedDataAmount() {
			return this.selectedData.reduce((accumulator, currentValue) => {
				return accAdd(accumulator, currentValue.arrearsAmount ?? 0)
			}, 0)
		},
		// 合计欠费总水量
		selectedDataWaterYield() {
			return this.selectedData.reduce((accumulator, currentValue) => {
				return accAdd(accumulator, currentValue.waterAmount ?? 0)
			}, 0)
		},
		orgOptions() {
			return this.formItems[0].options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.getAlleyMapData()
					this.getStaffMapData()
					this.$nextTick(() => {
						this.handleSearch()
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		// 附加费处理
		surchangePrice(data = []) {
			const arr = data?.map(item => {
				return `${item.itemName}: ${item.billItemPrice}元/吨`
			})
			return arr.length > 0 ? arr.join('<br />') : '--'
		},

		handleSelectChange(data) {
			this.selectedData = data
		},
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.formData.lastUrgeDays = undefined
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			this.selectedData = []
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { dateRange, ...rest } = this.formData
				const { total = 0, records = [] } = await queryBillPage({
					size,
					current,
					startBillTime: dateRange?.length > 0 ? dateRange[0] : '',
					endBillTime: dateRange?.length > 0 ? dateRange[1] : '',
					...rest,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.selectedData = []
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 获取坊别数据
		async getAlleyMapData() {
			const res = await getAlleyMap({
				orgCode: this.formData.orgCode,
			})
			try {
				if (res) {
					this.formItems[1].options = res.map(item => {
						return {
							value: item.alleyCode,
							label: item.alleyName,
						}
					})
				}
			} catch (error) {
				console.error(error)
				this.formItems[1].options = []
			}
		},

		// 打印欠费通知单
		handlePrintNotification() {
			arrearagePrint({})
		},
		// 催缴登记
		handleCheckIn(datas) {
			this.chosedData = datas
			this.showCheckIn = true
		},
		// 打印旅顺水费通知单
		handlePrintLs(datas) {
			if (datas && datas.length > 0) {
				billingLsBatchPrint(
					'billing_recall_ls',
					datas.map(item => {
						const { billId, billYear } = item
						return { billId, billYear }
					}),
				)
			}
		},
		// 获取抄表员工数据
		async getStaffMapData() {
			try {
				const res = await getStaffMap({
					orgCode: this.formData.orgCode,
				})
				if (res) {
					this.formItems[5].options = res.map(item => {
						const { staffId, staffName } = item
						return {
							value: staffId,
							label: staffName,
						}
					})
				}
			} catch (error) {
				console.error(error)
				this.formItems[5].options = []
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.right-top {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
}
.num {
	color: #ec6b60;
	margin: 0 4px;
}

::v-deep {
	.el-input-number {
		width: 100%;
	}
	.el-select {
		margin-bottom: 8px;
		.el-input__prefix {
			padding-left: 10px;
			color: #4e4e4e;
		}
		.el-input--prefix {
			.el-input__inner {
				padding-left: 55px;
			}
		}
	}
}
</style>
