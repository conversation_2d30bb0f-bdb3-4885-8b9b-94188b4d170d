export function getFormItems(_this) {
	const FORM_ITEM_LIST_MAP = {
		rename: [
			{
				type: 'el-select',
				label: '营业分公司',
				prop: 'orgCode',
				options: _this.$store.getters.orgList,
			},
			{
				type: 'el-input',
				label: '表卡编号',
				prop: 'archivesIdentity',
			},
			{
				type: 'el-select',
				label: '档案类型',
				prop: 'userType',
				options:
					_this.$store.getters?.dataList?.userType?.map(item => {
						return {
							label: item.sortName,
							value: item.sortValue,
						}
					}) || [],
			},
			{
				type: 'el-input',
				label: '更名前用户名称',
				prop: 'oldUserName',
			},
			{
				type: 'el-input',
				label: '更名后用户名称',
				prop: 'userName',
			},
			{
				type: 'el-input',
				label: '地址',
				prop: 'addressName',
			},
			{
				type: 'el-date-picker',
				label: '业务操作时间',
				prop: 'optTime',
				attrs: {
					type: 'daterange',
					startPlaceholder: '开始日期',
					endPlaceholder: '结束日期',
					rangeSeparator: 'L',
				},
			},
		],
		modify: [
			{
				type: 'el-select',
				label: '营业分公司',
				prop: 'orgCode',
				options: _this.$store.getters.orgList,
			},
			{
				type: 'el-date-picker',
				label: '业务操作时间',
				prop: 'optTime',
				attrs: {
					type: 'daterange',
					startPlaceholder: '开始日期',
					endPlaceholder: '结束日期',
					rangeSeparator: 'L',
				},
			},
			{
				type: 'el-input',
				label: '企业编号',
				prop: 'enterpriseNumber',
			},
		],
		transfer: [
			{
				type: 'el-select',
				label: '营业分公司',
				prop: 'orgCode',
				options: _this.$store.getters.orgList,
			},
			{
				type: 'el-input',
				label: '表卡编号',
				prop: 'archivesIdentity',
			},
			{
				type: 'el-select',
				label: '档案类型',
				prop: 'userType',
				options:
					_this.$store.getters?.dataList?.userType?.map(item => {
						return {
							label: item.sortName,
							value: item.sortValue,
						}
					}) || [],
			},
			{
				type: 'el-input',
				label: '过户前用户名称',
				prop: 'oldUserName',
			},
			{
				type: 'el-input',
				label: '过户后用户名称',
				prop: 'userName',
			},
			{
				type: 'el-input',
				label: '地址',
				prop: 'addressName',
			},
			{
				type: 'el-date-picker',
				label: '业务操作时间',
				prop: 'optTime',
				attrs: {
					type: 'daterange',
					startPlaceholder: '开始日期',
					endPlaceholder: '结束日期',
					rangeSeparator: 'L',
				},
			},
		],
	}

	const arr = FORM_ITEM_LIST_MAP[_this.activeTab] || []

	// if (_this.activeTab === 'modify') {
	// 	arr = [
	// 		{
	// 			type: 'el-select',
	// 			label: '营业分公司',
	// 			prop: 'orgCode',
	// 			options: _this.$store.getters.orgList,
	// 		},
	// 		{
	// 			type: 'el-date-picker',
	// 			label: '业务操作时间',
	// 			prop: 'optTime',
	// 			attrs: {
	// 				type: 'daterange',
	// 				startPlaceholder: '开始日期',
	// 				endPlaceholder: '结束日期',
	// 				rangeSeparator: 'L',
	// 			},
	// 		},
	// 		{
	// 			type: 'el-input',
	// 			label: '企业编号',
	// 			prop: 'enterpriseNumber',
	// 		},
	// 	]
	// } else if (_this.activeTab !== 'rename') {
	// 	arr.unshift({
	// 		type: 'el-select',
	// 		label: '营业分公司',
	// 		prop: 'orgCode',
	// 		options: _this.$store.getters.orgList,
	// 	})
	// 	arr[1] = {
	// 		type: 'el-select',
	// 		label: '档案类型',
	// 		prop: 'userType',
	// 		options:
	// 			_this.$store.getters?.dataList?.userType?.map(item => {
	// 				return {
	// 					label: item.sortName,
	// 					value: item.sortValue,
	// 				}
	// 			}) || [],
	// 	}
	// }

	arr.forEach(item => {
		if (item.prop === 'orgCode') {
			_this.$set(_this.formData, item.prop, _this.$store.getters.orgList[0].value)
		} else {
			_this.$set(_this.formData, item.prop, '')
		}
	})
	return arr
}
