<template>
	<div class="batch-layout">
		<div class="tab-switch">
			<div class="tab-block">
				<div
					class="tab-item"
					v-for="(item, index) in tabArr"
					:key="item.key"
					:class="{ active: index == activeIndex }"
					@click="tabSwitch(index, item.key)"
				>
					{{ item.value }}
				</div>
			</div>
			<!-- tab区操作 -->
			<slot name="info"></slot>
		</div>
		<div class="content">
			<div class="batch-ops" v-show="activeIndex == 0" key="ops">
				<!-- 操作区内容 -->
				<slot name="batch-ops"></slot>
			</div>
			<div class="batch-record" v-show="activeIndex == 1" key="record">
				<!-- 使用表格-->
				<slot name="batch-record"></slot>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'BatchLayout',
	props: {
		// tab {key:'archives',value:'批量建档'}
		tabArr: {
			type: Array,
			required: true,
			default: () => [],
		},
	},
	data() {
		return {
			activeIndex: 0,
		}
	},
	methods: {
		// tab切换
		tabSwitch(index, key) {
			this.activeIndex = index
			// 父组件可在切换后进行某些初始化或者初始请求
			this.$emit('tab-switch', key)
		},
	},
}
</script>
<style lang="scss" scoped>
div {
	box-sizing: border-box;
}
.batch-layout {
	height: 100%;
}
.tab-switch {
	height: 38px;
	background: #f4f7fa;
	line-height: 38px;
	display: flex;
	justify-content: space-between;
	.tab-block {
		display: flex;
		.tab-item {
			padding: 0 20px;
			color: #999999;
			cursor: pointer;
		}
		.tab-item.active {
			background: #fff;
			color: $base-color-blue;
		}
	}
	.info {
		padding-right: 20px;
		color: $base-color-blue;
		cursor: pointer;
	}
}
.content {
	height: calc(100% - 38px);
	background: #fff;
	.batch-ops,
	.batch-record {
		height: 100%;
		overflow: auto;
	}
	.batch-record {
		padding: 20px 24px;
	}
}
</style>
