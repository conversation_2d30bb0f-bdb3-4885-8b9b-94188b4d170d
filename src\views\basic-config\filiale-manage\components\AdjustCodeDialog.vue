<template>
	<gc-el-dialog :show="isShow" title="编码调整" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { RULE_INTEGERONLY, ruleMaxLength, ruleDigit, RULE_POSITIVEINTEGERONLY_STARTOFZERO } from '@/utils/rules.js'
import { updateBusinessHallCode } from '@/api/basicConfig.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				businessCode: '',
				collectionBusinessCode: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '营业分公司编码',
					prop: 'businessCode',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入营业分公司编码',
					},
				},
				{
					type: 'el-input',
					label: '营业分公司托收协议编码',
					prop: 'collectionBusinessCode',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入营业分公司托收协议编码',
					},
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					businessCode: [
						{
							required: true,
							message: '请输入营业分公司编码',
							trigger: 'blur',
						},
						ruleMaxLength(1, '营业分公司编码'),
						RULE_INTEGERONLY,
					],
					collectionBusinessCode: [
						{
							required: true,
							message: '请输入营业分公司托收协议编码',
							trigger: 'blur',
						},
						ruleDigit(4, '营业分公司托收协议编码只能输入'),
						RULE_POSITIVEINTEGERONLY_STARTOFZERO,
					],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.$confirm('将影响新建表卡编号、册本和托收协议编码，是否确认修改？', '编码规则调整确认').then(
					async () => {
						await updateBusinessHallCode(this.formData)
						this.$message.success('编码调整成功')
						this.$emit('success')
						this.isShow = false
					},
				)
			}
		},
		handleClose() {
			this.formData = {
				businessCode: '',
				collectionBusinessCode: '',
			}
			this.$refs.formRef.resetFields()
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
</style>
