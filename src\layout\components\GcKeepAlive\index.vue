<template>
	<v-keep-alive :include="cachedViews">
		<router-view :key="$route.fullPath" />
	</v-keep-alive>
</template>

<script>
export default {
	name: 'GcKeepAlive',
	computed: {
		cachedViews() {
			const { tags } = this.$store.state.tagsView
			const result = []
			tags.map(item => {
				if (item.meta && item.meta.keepAlive) {
					result.push(item.fullPath || item.path)
				}
			})
			return result
		},
	},
}
</script>
