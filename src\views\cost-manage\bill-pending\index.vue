<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div class="btn-container">
				<el-button
					v-has="'billing_bill_open'"
					type="primary"
					:disabled="!isBillDisabled"
					@click="handleOpenBill"
				>
					直接推送缴费
				</el-button>
				<el-button
					v-has="'billing_bill_search_open'"
					type="primary"
					:disabled="!isBillAllDisabled"
					@click="handleOpenAllBill"
				>
					全部推送缴费
				</el-button>
			</div>
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				needType="selection"
				@selectChange="selectChange"
				@current-page-change="handleChangePage"
			/>
		</div>
	</div>
</template>

<script>
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getAlleyMap } from '@/api/meterReading.api.js'
import { apiGetBillPendingList, apiOpenBill, apiOpenAllBill } from '@/api/costManage.api'
import { getColumn } from './tableColumn'
export default {
	data() {
		return {
			// 左侧查询
			formData: {
				orgCode: '',
				archivesNo: '',
				alleyId: '',
				archivesIdentity: '',
				userName: '',
				chargingMethod: '',
				billDate: '',
				year: this.dayjs().format('YYYY'),
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					events: {
						change: () => {
							this.formData.alleyId = ''
							this._getAlleyMap()
						},
					},
				},
				{
					type: 'el-date-picker',
					label: '年份',
					prop: 'year',
					attrs: {
						type: 'year',
						valueFormat: 'yyyy',
						clearable: false,
						pickerOptions: {
							disabledDate(time) {
								const currentYear = new Date().getFullYear()
								const minYear = 2007
								return time.getFullYear() < minYear || time.getFullYear() > currentYear + 1
							},
						},
					},
				},
				{
					type: 'el-input',
					label: '表册编号',
					prop: 'bookNo',
				},
				{
					type: 'el-select',
					label: '坊别',
					prop: 'alleyId',
					options: [],
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
				},
				{
					type: 'el-input',
					label: '用户名称',
					prop: 'userName',
				},
				{
					type: 'el-select',
					label: '收费方式',
					prop: 'chargingMethod',
					options: (this.$store.getters.dataList.chargingMethod || []).map(item => {
						return {
							label: item.sortName,
							value: item.sortValue,
						}
					}),
				},
				{
					type: 'el-date-picker',
					label: '账期',
					prop: 'billDate',
					attrs: {
						type: 'month',
						valueFormat: 'yyyy-MM',
					},
				},
			],
			formAttrs: {
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
					year: [ruleRequired('请选择年份')],
				},
			},
			// 右侧表格
			columns: getColumn(this),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			searchParams: {},
			billIds: [],
			loading: false,
		}
	},
	computed: {
		isBillDisabled() {
			return this.billIds.length
		},
		isBillAllDisabled() {
			return this.tableData.length
		},
		orgOptions() {
			return this.formItems[0].options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this._getAlleyMap()
					this.$nextTick(() => {
						this.handleSearch()
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		// 获取坊别
		_getAlleyMap() {
			getAlleyMap({
				orgCode: this.formData.orgCode,
			}).then(res => {
				const arr = res.map(item => {
					return {
						value: item.id,
						label: item.alleyName,
						...item,
					}
				})
				this.formItems[3].options = arr
			})
		},
		// 表格数据
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
				})
				const { records, total } = await apiGetBillPendingList(formParams)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.handleChangePage({ page: 1 })
		},
		selectChange(arr) {
			this.billIds = arr.map(item => item.billId)
		},
		async handleOpenBill() {
			const params = {
				billIds: this.billIds,
				year: this.formData.year,
			}
			await apiOpenBill(params)
			this.$message.success('开账成功')
			this.getList()
		},
		async handleOpenAllBill() {
			const { current, size } = this.pageData
			const formParams = trimParams(removeNullParams(this.formData))
			Object.assign(formParams, {
				current,
				size,
			})
			await apiOpenAllBill(formParams)
			this.$message.success('开账成功')
			this.getList()
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
	},
}
</script>

<style lang="scss" scoped>
.btn-container {
	margin-bottom: 10px;
}
</style>
