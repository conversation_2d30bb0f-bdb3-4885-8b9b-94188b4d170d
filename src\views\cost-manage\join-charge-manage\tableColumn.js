export function getColumn(_this) {
	if (_this.activeTab === 'give') {
		return [
			{
				key: 'fileMonth',
				name: '送盘生成月',
				tooltip: true,
			},
			{
				key: 'billNum',
				name: '账单数',
				tooltip: true,
				align: 'right',
			},
			{
				key: 'billAmount',
				name: '总金额(元)',
				tooltip: true,
				align: 'right',
				render: function (h, row) {
					const amount = row.billAmount - 0
					return h('span', {}, amount.toFixed(2))
				},
			},
			{
				key: 'statusDesc',
				name: '文件状态',
				tooltip: true,
			},
			{
				key: 'successNum',
				name: '已销账帐单数',
				tooltip: true,
				align: 'right',
			},
			{
				key: 'successAmount',
				name: '已销账总金额(元)',
				tooltip: true,
				align: 'right',
				render: function (h, row) {
					const amount = row.successAmount - 0
					return h('span', {}, amount.toFixed(2))
				},
			},
			{
				key: 'giveOperation',
				name: '操作',
				minWidth: 350,
				fixed: 'right',
			},
		]
	}
	return [
		{
			key: 'fileDay',
			name: '回盘日期',
			tooltip: true,
		},
		{
			key: 'fileMonth',
			name: '对账文件月份',
			tooltip: true,
		},
		{
			key: 'sendFileName',
			name: '文件名称',
			tooltip: true,
		},
		{
			key: 'statusDesc',
			name: '文件状态',
			tooltip: true,
		},
		{
			key: 'returnAmount',
			name: '回盘总金额(元)',
			tooltip: true,
			align: 'right',
			render: function (h, row) {
				const amount = row.returnAmount - 0
				return h('span', {}, amount.toFixed(2))
			},
		},
		{
			key: 'backOperation',
			name: '操作',
			fixed: 'right',
		},
	]
}
