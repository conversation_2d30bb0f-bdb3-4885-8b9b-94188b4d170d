<template>
	<div class="container-wrapper">
		<GcModelHeader
			class="info-title"
			title="档案信息"
			:icon="require('@/assets/images/icon/title-common-parameters.png')"
		></GcModelHeader>
		<div class="container">
			<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<template v-slot:baseInfo>
					<h5 class="gap-title">基本信息</h5>
				</template>
				<template v-slot:archivesIdentity>
					<el-input v-model="formData.archivesIdentity" @input="debouncedInputHandler" placeholder="请输入">
						<img slot="append" src="@/assets/images/icon/get-num.svg" @click="_apiGetArchivesIdentity" />
					</el-input>
				</template>
				<template v-slot:otherInfo>
					<h5 class="gap-title">价格信息</h5>
				</template>
			</GcFormRow>
			<!-- 阶梯计价策略 -->
			<div class="level-v-price" v-show="formData.billingTypeId == 2">
				<h5>阶梯计价策略</h5>
				<el-table
					:data="levelTableData"
					border
					:header-cell-style="{
						background: 'rgb(240, 244, 250)',
						color: '#222',
					}"
				>
					<el-table-column prop="levelName" label="阶梯"></el-table-column>
					<el-table-column prop="waterUsecount" label="用水量（吨）"></el-table-column>
					<el-table-column prop="warnLevelBorder" label="报警阈值（吨）"></el-table-column>
					<el-table-column prop="unitPrice" label="价格（元/吨）"></el-table-column>
				</el-table>
			</div>
			<!-- 附加费 -->
			<div class="level-v-price">
				<h5>附加费</h5>
				<el-table
					:data="billItemTableData"
					border
					:header-cell-style="{
						background: 'rgb(240, 244, 250)',
						color: '#222',
					}"
				>
					<el-table-column prop="itemName" label="附加费用名称"></el-table-column>

					<el-table-column prop="billItemPrice" label="价格（元/吨）"></el-table-column>
				</el-table>
			</div>
			<div class="button-group">
				<button class="gc-button gc-button-three" type="button" @click="handleReset">重 置</button>
				<button class="gc-button gc-button-two" type="button" @click="changeTab('meterInfo')">上一项</button>
			</div>
		</div>
	</div>
</template>
<script>
import _ from 'lodash'
import { getFormItems } from './recordsForm.js'
import { accAdd } from '@/utils/calc'
import { apiGetPriceList_all, apiGetArchivesIdentity, apiVerifyArchives } from '@/api/meterManage.api.js'
import { ruleRequired, RULE_INTEGERONLY, ruleMaxLength } from '@/utils/rules'
import { archivesMeterTypeOptions } from '@/consts/optionList'
export default {
	name: 'RecordsInfo',
	components: {},
	props: {
		detailData: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
				summaryArchives: 0,
				archivesMeterType: '',
				accountNumber: '',
				priceName: '',
				priceId: '',
				priceCode: '',
				natureName: '',
				billingTypeId: '',
			},
			formItems: getFormItems(this),
			formAttrs: {
				labelWidth: '120px',
				labelPosition: 'right',
				rules: {
					archivesIdentity: [
						ruleRequired('必填'),
						RULE_INTEGERONLY,
						{
							pattern: /^\d{9}$/,
							message: '必须为9位',
							trigger: '',
						},
					],
					summaryArchives: [ruleRequired('必填')],
					archivesMeterType: [ruleRequired('必填')],
					priceName: [ruleRequired('必填')],
					priceCode: [ruleRequired('必填')],
					accountNumber: [ruleMaxLength(32)],
				},
			},
			levelTableData: [],
			billItemTableData: [],
			priceList: [],
			archivesIdentityParams: {
				orgCode: '',
				bookId: '',
				userType: 3,
			},
			flag: true,
		}
	},
	watch: {
		formData: {
			handler: 'validateForm',
			deep: true,
		},
		detailData: {
			async handler(v) {
				if (v) {
					this.formData = Object.assign(this.formData, this.detailData)
					this.changeSummaryArchives(this.formData.summaryArchives)
				}
			},
			deep: true,
		},
	},
	created() {},
	methods: {
		debouncedInputHandler: _.debounce(async function () {
			const valid = await this.$refs.formRef.validateField('archivesIdentity')
			if (valid !== '') return
			try {
				await apiVerifyArchives({
					archivesIdentity: this.formData.archivesIdentity,
				})
			} catch (error) {
				this.$message.error(error.message)
			}
		}, 500),
		// 页面激活时, 价格信息更新
		async updatePrice() {
			await this._apiGetPriceList_all()
			const priceId = this.$route.name === 'ResidentMeterCreateModify' ? this.detailData.priceId : undefined
			this.handleChangePrice(priceId)
		},
		async _apiGetPriceList_all() {
			try {
				const { records } = await apiGetPriceList_all()

				this.priceList = records
				if (records && records.length > 0) {
					const priceNameObj = this.formItems.find(item => item.prop == 'priceName')
					const priceCodeObj = this.formItems.find(item => item.prop == 'priceCode')
					priceNameObj.options = records.map(item => {
						return {
							label: item.priceName,
							value: item.priceId,
							...item,
							disabled: [1, 9].includes(item.enableFlag) ? false : true,
						}
					})
					priceCodeObj.options = records.map(item => {
						// 单一价 拼接 单价
						const singlePrice = item.billingTypeId === 1 ? ' - ' + item.singlePrice : ''
						return {
							label: `${item.priceCode}${singlePrice}`,
							value: item.priceId,
							...item,
							disabled: [1, 9].includes(item.enableFlag) ? false : true,
						}
					})
				}
			} catch (error) {
				console.log(error)
			}
		},
		// 获取表卡编号
		async _apiGetArchivesIdentity() {
			try {
				const { archivesIdentity } = await apiGetArchivesIdentity(this.archivesIdentityParams)
				if (archivesIdentity) {
					this.formData.archivesIdentity = archivesIdentity
					this.$message.success('获取表卡编号成功')
				} else {
					this.$message.error('获取表卡编号失败')
				}
			} catch (error) {
				console.log(error)
			}
		},
		async validateForm() {
			const valid = await this.$refs.formRef.validate()
			this.$emit('getValid', 'recordsInfo', valid)
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.validateForm()
		},
		changeTab(v) {
			this.$emit('changeTab', v)
		},
		changeSummaryArchives(v) {
			this.formItems = this.formItems.filter(item => item.prop !== 'archivesMeterType')
			if (v) {
				this.formItems.splice(3, 0, {
					type: 'el-select',
					label: '贸易结算类型',
					prop: 'archivesMeterType',
					options: archivesMeterTypeOptions,
					attrs: {
						col: 8,
						clearable: true,
					},
				})
			}
		},
		handleChangePrice(v) {
			if (v) {
				const priceObj = this.priceList.find(item => item.priceId === v)
				this.packageLevelPriceTable(priceObj)
				this.billItemTableData = priceObj.priceBillItemList || []
				this.formData.priceName = priceObj.priceName
				this.formData.natureName = priceObj.natureName
				this.formData.priceCode = priceObj.priceCode
				this.formData.priceId = priceObj.priceId
				this.formData.singlePrice = priceObj.singlePrice
				this.formData.billingTypeId = priceObj.billingTypeId
				const index = this.formItems.findIndex(item => item.prop == 'singlePrice')
				// 阶梯价不展示 价格   单一价 展示价格
				if (this.formData.billingTypeId == 2 && index !== -1) {
					this.formItems.splice(index, 1)
				} else if (this.formData.billingTypeId == 1 && index === -1) {
					this.formItems.push({
						type: 'el-input',
						label: '价格（元/吨）',
						prop: 'singlePrice',
						attrs: {
							col: 8,
							disabled: true,
						},
					})
				}
			} else {
				this.levelTableData = []
				this.billItemTableData = []
				this.formData.priceName = ''
				this.formData.priceCode = ''
				this.formData.natureName = ''
				this.formData.priceId = ''
				this.formData.billingTypeId = ''
				this.formData.singlePrice = ''
			}
		},
		/**
		 * 根据价格详情返回levelBorder和levelPrice字段自定义处理阶梯计价策略表格数据
		 * levelBorder 12|18|26|33|99999999
		 * levelPrice 1.11|1.12|1.13|1.14|1.15
		 * 将levelBorder每个一|分割数据前求和得到类似[0, 12, 30, 56, 89, 99999999]格式的数据
		 */
		packageLevelPriceTable(currentPriceInfo) {
			const enumLevel = {
				0: '一',
				1: '二',
				2: '三',
				3: '四',
				4: '五',
			}
			const levelBorder = currentPriceInfo?.levelBorder
				? currentPriceInfo.levelBorder.split('|').map(o => parseInt(o))
				: []
			const levelPrice = currentPriceInfo?.levelPrice ? currentPriceInfo.levelPrice.split('|') : []
			const warnLevelBorder = currentPriceInfo?.warnLevelBorder ? currentPriceInfo.warnLevelBorder.split('|') : []
			// 计算数据前几个数据之和得到新的list
			const beforeSumLevel = levelBorder.reduce(
				(prev, cur) => {
					const sum = Math.min(prev[prev.length - 1] + cur, 99999999)
					prev.push(sum)
					return prev
				},
				[0],
			)
			this.levelTableData = levelPrice.map((o, index) => {
				return {
					unitPrice: o,
					waterUsecount: `${beforeSumLevel[index]} ~ ${beforeSumLevel[index + 1]}`,
					warnLevelBorder: warnLevelBorder[index]
						? index === levelPrice.length - 1
							? warnLevelBorder[index]
							: accAdd(beforeSumLevel[index], warnLevelBorder[index])
						: '--',
					levelName: `第${enumLevel[index]}阶梯`,
				}
			})
		},
		// 设置表卡编号校验规则:前三位必须是坊别code
		setArchivesIdentityRule(alleyCode) {
			this.$refs.formRef.clearValidate()
			this.formAttrs.rules.archivesIdentity[2] = {
				pattern: new RegExp(`^${alleyCode}\\d{6}$`),
				message: `必须为9位, 且前三位必须为${alleyCode}`,
				trigger: '',
			}
			this.validateForm()
		},
		setArchivesIdentityParams({ bookId, orgCode }) {
			bookId && (this.archivesIdentityParams.bookId = bookId)
			orgCode && (this.archivesIdentityParams.orgCode = orgCode)
		},
	},
	mounted() {
		this.validateForm()
	},
}
</script>

<style lang="scss" scoped>
.container-wrapper {
	height: 100%;
}
.container {
	padding-right: 20px;
	height: calc(100% - 60px);
	overflow-y: auto;
	overflow-x: hidden;
}
.gap-title {
	padding: 0 20px;
	color: #222222;
	font-size: 14px;
	font-weight: bold;
}

.level-v-price {
	margin-top: 20px;
	padding: 0 20px;
	h5 {
		color: #222222;
		font-size: 14px;
		margin-bottom: 20px;
		font-weight: bold;
	}
	::v-deep .el-table {
		.cell {
			font-size: 14px;
		}
	}
	::v-deep .el-table__empty-block {
		border-bottom: 1px solid #ebeef5 !important;
	}
}
.button-group {
	margin-top: 20px;
	padding: 0 20px;
	.gc-button {
		margin-right: 8px;
	}
}
::v-deep {
	.el-input-group__append {
		padding: 0;
		img {
			display: block;
			padding: 0 10px;
			height: 30px;
			object-fit: none;
			cursor: pointer;
		}
	}
}
</style>
