import service from './request'

//查询部门层级树
export function apiGetDepartmentTree(data) {
	return service({
		url: '/v1/tos/department/queryTree',
		method: 'POST',
		data,
	})
}
// 查询部门类型
export function apiGetDepartmentMap(data) {
	return service({
		url: '/v1/tos/department/queryMap',
		method: 'POST',
		data,
	})
}
// 查询组织层级下拉
export function apiQueryOrgMap(params) {
	return service({
		url: '/v1/tos/department/queryOrgMap',
		method: 'GET',
		params,
	})
}

// 新增部门
export function apiAddDepartment(data) {
	return service({
		url: '/v1/tos/department/addDepartment',
		method: 'POST',
		data,
	})
}

// 修改部门
export function apiUpdateDepartment(data) {
	return service({
		url: '/v1/tos/department/updateDepartment',
		method: 'POST',
		data,
	})
}

// 删除部门
export function apiDeleteDepartment(data) {
	return service({
		url: '/v1/tos/department/deleteDepartment',
		method: 'POST',
		data,
	})
}

// 新增员工
export function apiCreateStaff(data) {
	return service({
		url: `/v1/tos/staff/create`,
		method: 'POST',
		data,
	})
}

// 修改租户员工
export function apiModifyStaff(data, id) {
	return service({
		url: `/v1/tos/staff/modify?id=${id}`,
		method: 'put',
		data,
	})
}

// 查询营业厅下所有人员列表
export function apiStaffList(params) {
	return service({
		url: `/v1/tos/staff/list`,
		method: 'get',
		params,
	})
}

// 查询角色列表数据信息
export function apiRoleList(params) {
	return service({
		url: `/v1/tos/role/list`,
		method: 'GET',
		params,
	})
}

// 租户管理查看权限组数据
export function apiRoleTree(params) {
	return service({
		url: `/auth/authz/permission/group/tenant/role/tree`,
		method: 'POST',
		params,
	})
}

// 更新组织权限信息
export function apiModifyRole(params, data) {
	return service({
		url: `v1/tos/role/modify`,
		method: 'PUT',
		params,
		data,
	})
}

// 删除部门员工角色权限
export function apiDeleteRole(params) {
	return service({
		url: `/v1/tos/role/delete?id=${params.id}&tid=${params.tid}`,
		method: 'DELETE',
	})
}

// 创建权限
export function apiCreateRole(params, data) {
	return service({
		url: `/v1/tos/role/create`,
		method: 'post',
		data,
	})
}
// 租户账户修改自身的密码
export const apiTenantUpdatePass = parameter => {
	return service({
		url: '/auth/aggregation/password/tenant/update',
		method: 'POST',
		params: parameter,
	})
}

// 获取工种分类部分列表
export const apiGetBusinessHallCompanyMap = params => {
	return service({
		url: `/cpm/businessHall/queryBusinessHallCompanyMap`,
		method: 'GET',
		params,
	})
}
