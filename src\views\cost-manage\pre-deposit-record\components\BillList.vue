<template>
	<GcElDialog
		:show="isShow"
		:title="`查看相关${billName}账单`"
		width="1200px"
		:showFooter="false"
		@close="handleClose"
	>
		<div class="distributionMode">{{ billName }}总金额：{{ amountTotal }}元</div>
		<div class="container-table">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
			/>
		</div>
	</GcElDialog>
</template>
<script>
import { getPayRecordBillList } from '@/api/costManage.api'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		rowData: {
			type: Object,
			default: () => ({}),
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		billName() {
			const { costOperationType = 1 } = this.rowData
			const enums = {
				1: '销账',
				9: '冲正',
			}
			return enums[costOperationType]
		},
		amountTotal() {
			const amount = this.rowData.billAmount
			return amount || amount === 0 ? amount.toFixed(2) : '--'
		},
	},
	data() {
		return {
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'billDate',
					name: '账期',
					tooltip: true,
					width: '100px',
				},
				{
					key: 'billNo',
					name: '账单编号',
					tooltip: true,
					width: '240px',
				},
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'useAmount',
					name: '水量',
					tooltip: true,
					width: '80px',
				},
				{
					key: 'priceCode',
					name: '价格编号',
					tooltip: true,
					width: '100px',
				},
				{
					key: 'useAmt',
					name: '水费',
					tooltip: true,
					align: 'right',
					width: '120px',
					render: function (h, row) {
						const useAmt = row.useAmt - 0
						return h('span', {}, useAmt.toFixed(2))
					},
				},
				{
					key: 'billItemAmt',
					name: '污水费',
					tooltip: true,
					align: 'right',
					width: '120px',
					render: function (h, row) {
						const billItemAmt = row.billItemAmt - 0
						return h('span', {}, billItemAmt.toFixed(2))
					},
				},
				{
					key: 'billItemAmt2',
					name: '附加费',
					tooltip: true,
					align: 'right',
					width: '120px',
					render: function (h, row) {
						const billItemAmt = row.billItemAmt2 - 0
						return h('span', {}, billItemAmt.toFixed(2))
					},
				},
				{
					key: 'receivableAmount',
					name: '应缴金额',
					tooltip: true,
					align: 'right',
					width: '120px',
					render: function (h, row) {
						const receivableAmount = row.receivableAmount - 0
						return h('span', {}, receivableAmount.toFixed(2))
					},
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	watch: {
		isShow(val) {
			if (val) {
				try {
					if (val) {
						this.getList()
					}
				} catch (error) {
					console.log(error)
				}
			}
		},
	},
	methods: {
		async getList() {
			this.loading = true
			try {
				const params = {
					payRecordId: this.rowData.payRecordId,
				}
				const data = await getPayRecordBillList(params).catch(e => {
					this.tableData = []
					this.pageData = {
						current: 1,
						size: 10,
						total: 0,
					}
					this.$message.error(e.message || '关联账单查询失败！')
				})
				if (data) {
					this.pageData.total = data.length
					this.tableData = data
					return
				}
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleClose() {
			this.isShow = false
			this.tableData = []
		},
	},
}
</script>
<style lang="scss" scoped>
.distributionMode {
	margin-bottom: 10px;
}
.container-table {
	height: 500px;
}
</style>
