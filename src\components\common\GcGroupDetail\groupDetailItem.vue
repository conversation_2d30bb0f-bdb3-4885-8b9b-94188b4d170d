<template>
	<div class="group-detail-item">
		<div class="group-detail-name">
			<div v-if="$slots.head">
				<slot name="head"></slot>
			</div>
			<p v-else>{{ name }}</p>
		</div>
		<div class="group-detail-info">
			<div :class="{ ellipsis: isEllipsis }"><slot></slot></div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'GroupDetailItem',
	componentName: 'GroupDetailItem',
	props: {
		name: String,
		isDouble: Boolean,
		isEllipsis: Boolean,
	},

	data() {
		return {
			clientWidth: 0,
		}
	},
}
</script>

<style lang="scss" scoped>
.group-detail-item {
	width: 25%;
	margin-bottom: 20px;
	flex-shrink: 0;
	padding: 0 8px;
	box-sizing: border-box;
	.group-detail-name {
		font-size: 12px;
		color: #999;
	}
	.group-detail-info {
		margin-top: 10px;
		div {
			font-size: 14px;
			color: #4e4e4e;
			p {
				word-break: break-all;
			}
			&.ellipsis {
				p {
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}
	}
}
</style>
