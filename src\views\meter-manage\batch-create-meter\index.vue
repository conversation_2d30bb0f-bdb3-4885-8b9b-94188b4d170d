<template>
	<div class="batch-create">
		<BatchOpsLayout :tabArr="tabArr" @tab-switch="tabSwitch">
			<template #info>
				<div class="info" @click="usualErrorVisible = true">
					<i class="iconfontCis icon-commonerror"></i>
					常见错误解决方法
				</div>
			</template>
			<template #batch-ops>
				<div class="batch-archives">
					<div class="common-params" v-show="uploadStep == 1 || uploadStep == 2">
						<div class="header">
							<img src="@/assets/images/icon/title-common-parameters.png" alt="" />
							<span>公共参数</span>
						</div>
						<!-- 公共参数表单 -->
						<ParamsForm ref="paramsForm"></ParamsForm>
					</div>
					<div class="upload-area" :class="{ fill: uploadStep == 3 || uploadStep == 4 }">
						<div class="header" v-if="uploadStep == 1 || uploadStep == 2">
							<img src="@/assets/images/icon/title-uploading.png" alt="" />
							<span>上传文件</span>
						</div>
						<!-- 提醒 -->
						<UploadTip v-if="uploadStep == 1 || uploadStep == 2"></UploadTip>
						<!-- 模板下载 -->
						<TemplateDownload
							v-if="uploadStep == 1 || uploadStep == 2"
							:templateList="templateList"
						></TemplateDownload>
						<div class="upload-file-box">
							<!-- 上传文件 -->
							<UploadFile
								:select-text-inital.sync="selectTextInital"
								:upload-step.sync="uploadStep"
								:msg="msg"
								:loading-text.sync="loadingText"
								:loading.sync="loading"
								:default-img="defaultImg"
								@before-upload-file="beforeUploadFile"
								@upload-down-fail="uploadDownFail"
								@upload-confirm="uploadConfirm"
							>
								<template #success-ops>
									<div class="jump" v-if="uploadStep == 3">
										<!-- <span class="border-blue">
											<img src="@/assets/images/pic/create-success.png" alt="" />
											<span @click="goArchivesList">去档案列表</span>
										</span> -->
									</div>
								</template>
							</UploadFile>
						</div>
					</div>
				</div>
			</template>
			<template #batch-record>
				<div class="table-show">
					<GcTable
						:columns="columns"
						:table-data="recordList"
						:show-page="true"
						:total="page.total"
						@current-page-change="currentPageChange"
						:current-page.sync="page.current"
						:page-size="page.size"
					></GcTable>
				</div>
			</template>
		</BatchOpsLayout>
		<!-- 常见错误解决方案 -->
		<UsualError :show.sync="usualErrorVisible"></UsualError>
	</div>
</template>

<script>
import uploadMix from '@/components/UploadFile/index.js'
import BatchOpsLayout from '@/components/BatchOpsLayout/index.vue'
import UploadFile from '@/components/UploadFile/index.vue'
import UploadTip from '@/components/UploadTip/index.vue'
import TemplateDownload from '@/components/TemplateDownload/index.vue'
import UsualError from './components/UsualError.vue'
import ParamsForm from './components/ParamsForm.vue'
import identity from '@/mixin/identity.js'
import { excelVerList, excelVerListWater } from '@/consts/templateVersion.js'

export default {
	name: 'batchCreateMeter',
	mixins: [uploadMix, identity],
	components: {
		BatchOpsLayout,
		UploadFile,
		UsualError,
		UploadTip,
		TemplateDownload,
		ParamsForm,
	},
	data() {
		return {
			tabArr: [
				{
					key: 'archives',
					value: '批量建表',
				},
				{
					key: 'archivesRecord',
					value: '批量建表记录',
				},
			],
			usualErrorVisible: false,
			defaultImg: require('@/assets/images/pic/batch-create.png'),
			selectTextInital: '选择文件',
		}
	},
	computed: {
		templateList() {
			let arr = [
				{
					title: '批量建表-完整',
					url: `excel/批量建表-${this.version}.xlsx`,
				},
				{
					title: '批量建表-地址+用户',
					url: `excel/批量建表-地址+用户-${this.versionWithoutMeter}.xlsx`,
				},
			]
			if (this.realm === 'water') {
				arr = [
					{
						title: '批量建表-完整',
						url: `excel-water/批量建表-${this.version}.xlsx`,
					},
					{
						title: '批量建表-地址+用户',
						url: `excel-water/批量建表-地址+用户-${this.versionWithoutMeter}.xlsx`,
					},
				]
			}
			return arr
		},
		version() {
			if (this.realm === 'water') {
				return excelVerListWater['archives-bat-create']
			} else {
				return excelVerList['archives-bat-create']
			}
		},
		versionWithoutMeter() {
			if (this.realm === 'water') {
				return excelVerListWater['archives-bat-create-without-meter']
			} else {
				return excelVerList['archives-bat-create-without-meter']
			}
		},
	},
	watch: {
		uploadStep(newVal, oldVal) {
			if (oldVal == 3 && newVal == 1) {
				this.$nextTick(() => {
					this.$refs.paramsForm.resetForm()
					this.$refs.paramsForm.meterCheck = {}
				})
			}
		},
	},
	mounted() {
		this.getRecordList({ page: 1 }, 'archives')
	},
	methods: {
		// 选择文件前校验
		beforeUploadFile(data, cb) {
			this.$refs.paramsForm.submitForm().then(res => {
				if (res) {
					cb(true)
				} else {
					this.$message.error('请完善公共参数')
					cb(false)
				}
			})
		},
		// 上传
		uploadConfirm(val) {
			let excelObj = val
			const { regionCode, priceId, bookRecord, orgCode } = this.$refs.paramsForm.formData
			const sourceData = excelObj['list']
			let obj = {
				orgCode,
				address: {
					regionCode,
					addressFullName: this.$refs.paramsForm.addressFullName,
					addressAreaCode: this.$refs.paramsForm.addressAreaCode,
				},
				archives: {
					bookId: bookRecord.bookId,
				},
				price: {
					priceId,
				},
			}
			for (const item of sourceData) {
				const meterRecordNo = item.表卡编号.toString()
				if (meterRecordNo.length !== 9) {
					this.$message.error(`表卡编号长度应为 9, 行号: ${item.rowNum}`)
					return
				}
				if (meterRecordNo.indexOf(bookRecord.alleyCode) !== 0) {
					this.$message.error(
						`表卡编号前三位必须为所属表册坊别号(${bookRecord.alleyCode}), 行号: ${item.rowNum}`,
					)
					return
				}
			}

			this.uploadLogic(sourceData, excelObj['type'], obj)
		},
		tabSwitch(flag) {
			if (flag === 'archivesRecord') {
				this.getRecordList(
					{
						page: this.page.current,
					},
					'archives',
				)
			}
		},
		//翻页
		currentPageChange({ page, size }) {
			this.page.current = page
			this.page.size = size
			this.getRecordList(
				{
					page,
					size,
				},
				'archives',
			)
		},
		// 跳转至档案详情页
		goArchivesList() {
			this.$router.push({
				path: '/archives/list',
			})
		},
	},
	deactivated() {
		if (this.uploadStep == 3 || this.uploadStep == 4) {
			this.uploadStep = 1
		}
	},
}
</script>
<style lang="scss" scoped>
.batch-create {
	height: 100%;
	::v-deep {
		.batch-ops {
			padding: 20px 16px 0px 20px;
		}
	}
	.batch-archives {
		display: flex;
		height: 100%;
		justify-content: flex-end;
		position: relative;
		.common-params {
			width: 60%;
			height: 100%;
			position: absolute;
			left: 0;
			padding: 0 40px 20px 0;
		}
		.common-params::after {
			content: '';
			display: inline-block;
			width: 1px;
			height: calc(100% - 20px);
			position: absolute;
			right: 0;
			top: 0;
			border-left: 1px dashed #ccc;
		}
		.upload-area {
			position: sticky;
			top: 0;
			padding-left: 30px;
			width: 40%;
			display: flex;
			flex-direction: column;
			padding-bottom: 30px;
			.upload-file-box {
				flex: 1;
			}
		}
		.upload-area.fill {
			width: 100% !important;
			padding-left: 0;
		}
		.header {
			display: flex;
			align-items: center;
			padding-bottom: 34px;
			img {
				width: 22px;
				height: 22px;
				padding-right: 4px;
			}
			span {
				font-size: $base-font-size-bigger;
				color: $base-color-3;
				font-weight: 500;
			}
		}
	}
	.table-show {
		height: 100%;
		::v-deep {
			.status0::before,
			.status1::before {
				content: '';
				display: inline-block;
				width: 6px;
				height: 6px;
				border-radius: 50%;
				margin-right: 5px;
				vertical-align: middle;
			}
			.status0::before {
				background: #ec6b60;
			}
			.status1::before {
				background: #12b3c7;
			}
			.no-ops {
				color: #cccccc;
			}
			.down-fail-record {
				color: #ec6b60;
				font-size: 14px;
			}
		}
	}
}
.info {
	.icon-commonerror {
		padding-right: 4px;
	}
}
</style>
