<template>
	<div class="page-layout">
		<div class="page-right">
			<div class="role-config">
				<div class="list-wrap">
					<gc-model-header
						class="info-title"
						title="角色列表"
						:icon="require(`@/assets/images/icon/title-multi-check.png`)"
					>
						<template #right>
							<el-button v-has="'cpm_report_role_add'" type="primary" size="small" @click="handleAddRole">
								新增角色
							</el-button>
						</template>
					</gc-model-header>
					<div v-loading="roleLoading" class="list-box">
						<div v-show="roleList.length > 0" class="list-box">
							<div
								class="list-item"
								v-for="(item, index) in roleList"
								:key="item.id"
								:class="{ active: listActive === index }"
								@click="handleRoleClick(index)"
							>
								<div class="label">{{ item.roleName }}</div>
								<el-dropdown v-has="['cpm_report_role_update', 'cpm_report_role_delete']" @click.stop>
									<i class="icon-more el-icon-more" @click.stop></i>
									<el-dropdown-menu slot="dropdown">
										<el-dropdown-item
											v-has="'cpm_report_role_update'"
											@click.native="handleEditRole(item)"
										>
											修改
										</el-dropdown-item>
										<el-dropdown-item
											v-has="'cpm_report_role_delete'"
											@click.native="handleDeleteRole(item)"
										>
											删除
										</el-dropdown-item>
									</el-dropdown-menu>
								</el-dropdown>
							</div>
						</div>
						<gc-empty v-show="roleList.length === 0" />
					</div>
				</div>
				<div class="operation-box">
					<gc-model-header
						class="info-title"
						title="报表列表"
						:icon="require(`@/assets/images/icon/title-multi-check.png`)"
					>
						<template #right>
							<el-button
								v-has="'cpm_report_roleResource_savePermission'"
								type="primary"
								size="small"
								:disabled="!currentRoleData.id"
								@click="handleSaveRoleConfig"
							>
								保存
							</el-button>
						</template>
					</gc-model-header>
					<div v-loading="treeLoading" class="report-box">
						<el-tree
							v-show="treeData.length > 0"
							ref="treeRef"
							:data="treeData"
							node-key="id"
							show-checkbox
							check-on-click-node
							:expand-on-click-node="false"
							default-expand-all
							:props="defaultProps"
						/>
						<gc-empty v-show="treeData.length === 0" />
					</div>
					<gc-model-header
						class="info-title"
						title="用户绑定"
						:icon="require(`@/assets/images/icon/title-multi-check.png`)"
						style="margin-top: 12px"
					>
						<template #right>
							<el-button
								v-has="'cpm_report_roleUserRef_delete'"
								type="primary"
								size="small"
								:disabled="selectedData.length === 0"
								@click="
									handleUnbind(
										selectedData.map(item => item.userId),
										true,
									)
								"
							>
								批量解绑
							</el-button>
							<el-button
								v-has="'cpm_report_roleUserRef_add'"
								type="primary"
								size="small"
								:disabled="!currentRoleData.id"
								@click="handleChooseUsers"
							>
								选择用户
							</el-button>
						</template>
					</gc-model-header>
					<div class="user-config">
						<GcTable
							ref="tableRef"
							:loading="loading"
							:columns="columns"
							:table-data="tableData"
							:page-size="pageData.size"
							:total="pageData.total"
							:current-page="pageData.current"
							:needType="$has('cpm_report_roleUserRef_delete') ? 'selection' : ''"
							showPage
							@current-page-change="handlePageChange"
							@selectChange="handleSelectChange"
						>
							<template v-slot:deal="{ row }">
								<el-button
									v-has="'cpm_report_roleUserRef_delete'"
									type="text"
									size="medium"
									@click="handleUnbind([row.userId])"
								>
									解绑
								</el-button>
							</template>
						</GcTable>
					</div>
				</div>
			</div>
		</div>

		<!-- 新增、修改角色 -->
		<RoleUpdateDialog
			ref="roleUpdateDialogRef"
			:show.sync="showUpdate"
			:edit-type="editType"
			@success="getRoleList"
		/>
		<!-- 新增、编辑用户角色配置弹窗 -->
		<ChooseDialog ref="chooseDialogRef" :show.sync="showChoose" @success="getList(1)" />
	</div>
</template>

<script>
import { isBlank } from '@/utils/validate.js'
import RoleUpdateDialog from './components/RoleUpdateDialog.vue'
import ChooseDialog from './components/ChooseDialog.vue'
import {
	queryReportRoleList,
	queryCategoryReportList,
	deleteReportRole,
	saveReportsToRole,
	queryRoleUsers,
	removeRoleUsers,
} from '@/api/statisticsManage.api'

export default {
	name: 'ReportPermission',
	components: { RoleUpdateDialog, ChooseDialog },
	data() {
		return {
			// 左侧角色列表
			roleLoading: false,
			listActive: 0,
			roleList: [],

			// 角色新增、编辑弹窗
			editType: 'add',
			showUpdate: false,

			// 上方报表tree
			treeLoading: false,
			treeData: [],
			defaultProps: {
				children: 'children',
				label: 'label',
			},

			// 下方用户角色配置列表
			loading: false,
			columns: [
				{
					key: 'userName',
					name: '用户名',
					tooltip: true,
				},
				{
					hide: !this.$has('cpm_report_roleUserRef_delete'),
					key: 'deal',
					name: '操作',
					fixed: 'right',
					width: 100,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 用户列表选中的数据
			selectedData: [],

			// 选择用户弹窗
			showChoose: false,
		}
	},
	computed: {
		// 当前选中角色数据
		currentRoleData() {
			return this.roleList.length > 0
				? this.roleList[this.listActive]
				: { id: '', remark: '', reportIds: [], roleName: '' }
		},

		// 当前角色下 已选中的报表
		chosedReportIds() {
			return this.currentRoleData.reportIds || []
		},
	},
	beforeRouteEnter(to, from, next) {
		next(vm => {
			vm.$nextTick(() => {
				if (vm.roleList.length > 0) {
					vm.getReportTreeData()
				}
			})
		})
	},
	created() {
		this.getRoleList()
	},
	methods: {
		handleRoleClick(index) {
			if (this.listActive !== index) {
				this.listActive = index
				this.$nextTick(() => {
					this.$refs.treeRef.setCheckedKeys(this.currentRoleData.reportIds)
				})
				this.getList(1)
			}
		},
		roleInputValidator(value) {
			if (isBlank(value)) return '请输入角色名称'
			if (value.length > 16) return '角色名称不能超过16位字符'
		},
		// 新增角色
		handleAddRole() {
			this.editType = 'add'
			this.showUpdate = true
		},
		// 编辑角色
		handleEditRole(data) {
			this.editType = 'edit'
			this.showUpdate = true
			this.$nextTick(() => {
				this.$refs.roleUpdateDialogRef.setFormData(data)
			})
		},
		// 删除角色
		handleDeleteRole(data) {
			this.$confirm('确定要删除该角色吗？').then(async () => {
				await deleteReportRole({
					id: data.id,
				})
				this.$message.success('删除成功')
				this.getRoleList()
			})
		},
		// 获取角色列表数据
		async getRoleList(active = 0) {
			this.roleLoading = true
			try {
				const data = await queryReportRoleList()
				this.roleList = data || []
				this.listActive = active
				if (this.roleList.length > 0) {
					this.getReportTreeData()
					this.getList(1)
				}
			} catch (error) {
				console.error(error)
				this.roleList = []
				this.listActive = 0
			} finally {
				this.roleLoading = false
			}
		},
		// 获取报表tree数据
		async getReportTreeData() {
			this.treeLoading = true
			try {
				const data = await queryCategoryReportList()
				this.treeData = this.handleTreeData(data || []) || []
				this.$nextTick(() => {
					this.$refs.treeRef.setCheckedKeys(this.chosedReportIds)
				})
			} catch (error) {
				console.error(error)
				this.treeData = []
			} finally {
				this.treeLoading = false
			}
		},
		// 保存角色所配置的报表
		async handleSaveRoleConfig() {
			const checkedKeys = this.$refs.treeRef.getCheckedKeys(true)
			await saveReportsToRole({
				reportIds: checkedKeys,
				roleId: this.currentRoleData.id,
			})
			this.$message.success('保存成功')
			this.getRoleList()
		},

		async getList(curPage) {
			this.loading = true
			this.selectedData = []
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await queryRoleUsers({
					size,
					current,
					roleId: this.currentRoleData.id,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleSelectChange(data) {
			this.selectedData = data
		},

		// 处理tree数据
		handleTreeData(data = []) {
			return data.map((item, index) => {
				const { categoryId, categoryName: label, reportList = [] } = item
				return {
					id: `category${index}`,
					categoryId,
					label,
					disabled: reportList.length === 0,
					children: reportList.map(it => {
						const { id, reportName: label, reportUrl } = it
						return {
							id,
							categoryId,
							label,
							reportUrl,
						}
					}),
				}
			})
		},

		// 选择用户
		handleChooseUsers() {
			this.showChoose = true
			this.$nextTick(() => {
				this.$refs.chooseDialogRef.setFormData({
					...this.currentRoleData,
					roleId: this.currentRoleData.id,
				})
				this.$refs.chooseDialogRef.getUsers()
			})
		},
		// 解绑
		handleUnbind(userIds = [], isBatch = false) {
			this.$confirm(`确定要${isBatch ? '解绑这些' : '解绑这条'}数据吗？`).then(async () => {
				await removeRoleUsers({
					roleId: this.currentRoleData.id,
					userIds,
				})
				this.$message.success('解绑成功')
				this.getList(1)
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.info-title {
	height: auto;
	padding: 0;
	margin-bottom: 4px;
}
.role-config {
	flex: 1;
	display: flex;
	overflow: auto;
}
.list-wrap {
	display: flex;
	flex-direction: column;
	width: 300px;
	padding: 8px;
	border: 1px solid #eee;
}
.list-box {
	flex: 1;
	overflow: auto;
	margin-top: 8px;
	.list-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 42px;
		padding: 0 12px;
		cursor: pointer;
		.label {
			flex: 1;
			margin-right: 12px;
			@include text-overflow;
		}
		&.active {
			color: #2f87fe;
			background-color: rgba(196, 221, 255, 0.5);
			.el-dropdown {
				display: block;
			}
		}
	}
}
.operation-box {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: auto;
	margin-left: 16px;
}
.report-box {
	flex: 1;
	height: 40%;
	border: 1px solid #eee;
	padding: 8px;
}
.user-config {
	flex: 1.5;
	overflow: auto;
}
::v-deep {
	.gc-table .is-show-page {
		height: calc(100% - 40px);
	}
	.gc-table .pagination-box {
		margin-top: 12px;
	}
	.gc-cloud-pagination .gc-cloud-pagination-right .el-pagination.is-background {
		padding: 0 0 0 5px;
	}

	.el-dropdown-menu__item {
		min-width: auto;
	}
	.el-tree {
		height: 100%;
		overflow: auto;
	}
}
</style>
