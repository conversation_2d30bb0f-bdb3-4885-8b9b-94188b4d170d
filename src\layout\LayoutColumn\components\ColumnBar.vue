<template>
	<div class="column-bar-container" :class="{ 'z-index': highIndex }">
		<!-- 导航左侧（一级菜单） -->
		<div class="left">
			<el-scrollbar>
				<router-link to="/">
					<div class="box-logo">
						<img class="logo" :src="require('@/assets/images/logo-white.svg')" alt="" />
					</div>
				</router-link>
				<ul class="box-menu">
					<template v-for="(item, index) in handleRoutes">
						<el-tooltip
							popper-class="box-tooltip"
							placement="top"
							effect="dark"
							:key="item.name"
							:open-delay="200"
							:enterable="false"
							:content="item.meta.title"
						>
							<li
								class="menu-item"
								:class="{ active: item.name === extra.first }"
								@click="toggleMenu(item)"
							>
								<!-- 导航展开 -->
								<div v-show="!collapse" class="box-icon">
									<gc-icon
										class="menu-icon"
										font-family="iconfontCis"
										v-if="item.meta.icon"
										:icon="item.meta.icon"
									/>
									<span class="menu-font" v-if="item.meta.title">{{ item.meta.title }}</span>
								</div>
								<!-- 导航收起 -->
								<el-popover
									v-show="collapse"
									popper-class="box-popover"
									placement="right-start"
									width="180"
									trigger="hover"
									:visible-arrow="false"
									:open-delay="200"
								>
									<div class="box-icon" slot="reference" @mouseenter="handleMouseenter">
										<gc-icon
											v-if="item.meta.icon"
											font-family="iconfontCis"
											class="menu-icon"
											:icon="item.meta.icon"
										/>
										<span class="menu-font" v-if="item.meta.title">{{ item.meta.title }}</span>
									</div>
									<div
										class="box-hover"
										v-show="collapse && showHoverMenu"
										@mouseleave="handleMouseleave(index)"
									>
										<el-menu
											ref="hoverMenu"
											mode="vertical"
											:default-active="activeMenu"
											:unique-opened="uniqueOpened"
											@open="handleMenuClick(index, 1)"
											@close="handleMenuClick(index, 2)"
										>
											<template v-for="route in item.children">
												<gc-menu
													class="two-level-menu"
													v-if="!route.hidden"
													:key="route.fullPath"
													:item="route"
												/>
											</template>
										</el-menu>
									</div>
								</el-popover>
							</li>
						</el-tooltip>
					</template>
				</ul>
			</el-scrollbar>
		</div>
		<!-- 导航右侧（展开、子菜单） -->
		<div class="right" v-show="!collapse">
			<el-scrollbar>
				<div class="title">
					{{
						userLevel == 0
							? '大连市自来水集团有限公司'
							: realm !== 'water'
							? title
							: '大连市自来水集团有限公司'
					}}
				</div>
				<div class="group-title">{{ handleGroupTitle }}</div>
				<div class="box-submenu">
					<el-menu ref="kzMenu" mode="vertical" :default-active="activeMenu" :unique-opened="uniqueOpened">
						<template v-for="route in handlePartialRoutes">
							<gc-menu class="two-level-menu" v-if="!route.hidden" :key="route.fullPath" :item="route" />
						</template>
					</el-menu>
				</div>
			</el-scrollbar>
		</div>
	</div>
</template>

<script>
import variables from '@/styles/variables/variables.scss'
import { mapGetters, mapActions } from 'vuex'
import { openFirstMenu, uniqueOpened } from '@/config'
import { handleActivePath } from '@/utils/routes'
import identity from '@/mixin/identity.js'

export default {
	name: 'ColumnBar',
	mixins: [identity],
	data() {
		return {
			activeMenu: '',
			groupTitle: '',
			uniqueOpened,
			variables,
			highIndex: false,
		}
	},
	created() {
		this.$baseEventBus.$on('render-router', () => {
			this.$refs.hoverMenu.forEach(item => {
				item.activeIndex = item.defaultActive
			})
			this.$refs.kzMenu.activeIndex = this.$refs.kzMenu.defaultActive
		})
	},
	beforeDestroy() {
		this.$baseEventBus.$off('render-router')
	},
	computed: {
		...mapGetters({
			collapse: 'settings/collapse',
			routes: 'routes/routes',
			theme: 'settings/theme',
			extra: 'settings/extra',
			title: 'settings/title',
			showHoverMenu: 'settings/showHoverMenu',
		}),
		handleRoutes() {
			return this.routes.filter(item => item.hidden !== true && item.meta)
		},
		handlePartialRoutes() {
			const activeMenu = this.routes.find(_ => _.name === this.extra.first)
			return activeMenu ? activeMenu.children : []
		},
		handleGroupTitle() {
			const activeMenu = this.routes.find(_ => _.name === this.extra.first)
			return activeMenu ? activeMenu.meta.title : ''
		},
	},
	watch: {
		$route: {
			handler(route) {
				this.activeMenu = handleActivePath(route)
				const firstMenu = route.matched[0].name
				if (this.extra.first !== firstMenu) {
					this.extra.first = firstMenu
					this.handleTabClick(true)
				}
			},
			immediate: true,
		},
		// 点击展开按钮时，延时等待左侧菜单完全展开后提升z-index使其盖住右侧header，露出阴影
		collapse: {
			handler(val) {
				if (val) {
					this.highIndex = false
				} else {
					setTimeout(() => {
						this.highIndex = true
					}, 60)
				}
			},
			immediate: true,
		},
	},
	methods: {
		...mapActions({
			setSubmenu: 'settings/setSubmenu',
		}),
		// 二级菜单展开和收起，type：1、展开；2、收起
		handleMenuClick(idx, type) {
			const menus = this.$refs.hoverMenu
			const box = menus[idx].$el.parentNode
			const parent = box.parentNode
			setTimeout(() => {
				const bodyH = document.body.offsetHeight
				const totalH = parent.offsetTop + box.offsetHeight
				if (type === 1 && totalH > bodyH) {
					parent.style.bottom = 0
					parent.style.overflowY = 'auto'
				}
				if (type === 2 && totalH < bodyH) {
					parent.style.bottom = 'inherit'
					parent.style.overflowY = 'inherit'
				}
			}, 300)
		},
		// 鼠标移开后清除悬浮菜单样式
		handleMouseleave(idx) {
			const menu = this.$refs.hoverMenu
			const parent = menu[idx].$el.parentNode.parentNode
			parent.style.bottom = 'inherit'
			parent.style.overflowY = 'inherit'
		},
		toggleMenu(route) {
			if (this.collapse) return
			this.$store.commit('settings/SET_EXTRA', {
				...this.extra,
				first: route.name,
			})
			if (openFirstMenu) {
				this.$router.push(this.handlePartialRoutes[0])
			}
		},
		handleTabClick(handler) {
			if (handler !== true && openFirstMenu) {
				this.$router.push(this.handlePartialRoutes[0])
				this.$store.dispatch('settings/openSideBar')
			}
		},
		handleMouseenter() {
			this.setSubmenu(true)
		},
	},
}
</script>

<style lang="scss">
.box-tooltip.el-tooltip__popper.is-dark {
	padding: 8px 10px;
}
.box-popover.el-popper[x-placement^='right'] {
	margin-left: 1px;
	padding: 0;
	border: none;
	border-radius: 4px;
	overflow: hidden;
}
.box-icon {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	@include flex-center;
	flex-direction: column;
}
.box-hover .el-menu {
	border-right: none;
	.el-menu-item,
	.el-submenu__title {
		background: #2f87fe;
		color: #fff;
		height: auto;
		// height: 44px;
		line-height: 44px;
		// overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		vertical-align: middle;
		&:hover {
			opacity: 0.8;
		}
	}

	.two-level-menu {
		.iconfont,
		.iconfontCis {
			font-size: 18px;
			color: #fff;
			margin-right: 4px;
		}

		.el-submenu__title,
		.el-submenu__title i,
		.three-level-menu,
		.el-submenu__title .el-submenu__icon-arrow {
			color: #fff;
		}

		&.is-active {
			opacity: 0.8;
		}

		.three-level-menu .wrap {
			display: block;
			height: 66px;
			width: 110px;
			line-height: 16px;
			overflow-wrap: break-word;
			white-space: break-spaces;
			padding: 16px 0;
		}
	}
	.three-level-menu.is-active {
		background: #72aefe;
	}
}
.mobile .el-submenu__title {
	color: rgba(255, 255, 255, 0.7);
}
</style>

<style lang="scss" scoped>
@mixin active {
	&:hover {
		color: $base-color-blue;
		background-color: $base-column-submenu-background-active !important;

		i,
		svg {
			color: $base-color-blue;
		}
	}

	&.is-active {
		color: $base-color-blue;
		background-color: $base-column-submenu-background-active !important;
	}
}

.column-bar-container {
	position: fixed;
	top: 0;
	bottom: 0;
	left: 0;
	width: $base-left-menu-width;
	height: 100vh;
	overflow: hidden;
	background: $base-column-submenu-background;
	box-shadow: $base-box-shadow;
	display: flex;
	&.z-index {
		z-index: 999;
	}

	::v-deep {
		* {
			transition: $base-transition;
		}
		.el-menu {
			background: none;
		}
		.el-tabs + .el-menu {
			left: $base-left-menu-width-min;
			width: $base-left-menu-width - $base-left-menu-width-min;
			border: 0;
			background: none;
		}

		.left {
			width: $base-left-menu-width-min;
			height: 100%;
			background: $base-color-blue;

			.box-logo {
				height: 85px;
				@include flex-center;
				position: relative;
				&::after {
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					content: '';
					width: 25px;
					height: 1px;
					background: #7fb5fe;
				}
				.logo {
					height: 40px;
				}
			}

			.box-menu {
				margin-top: 30px;
				.menu-item {
					width: 100%;
					height: 55px;
					cursor: pointer;
					@include flex-center;
					position: relative;
					&:hover,
					&.active {
						background: #1a65c7;
						.menu-icon,
						.menu-font {
							color: #fff;
						}
					}
					.menu-icon {
						font-size: 18px;
						color: rgba(255, 255, 255, 0.7);
						margin-bottom: 4px;
					}
					.menu-font {
						font-size: 12px;
						color: #c5deff;
					}

					.box-hover {
						display: none;
						position: fixed;
						margin-top: 0;
						margin-left: 67px;
						width: $base-left-menu-width - $base-left-menu-width-min;
						background: #2f87fe;
					}
				}
			}
		}

		.right {
			width: $base-left-menu-width - $base-left-menu-width-min;
			height: 100%;
			.title {
				height: 85px;
				font-size: 18px;
				font-weight: 500;
				color: $base-color-blue;
				@include flex-center;
			}
			.group-title {
				text-align: center;
				transform: translateY(-50%);
			}
			.box-submenu {
				padding: 20px 10px;
			}
		}
	}

	::v-deep {
		.el-tabs {
			.el-tabs__item {
				&.is-active {
					background: transparent !important;

					.column-grid {
						background: $base-main-menu-background-active;
						color: #fff;
					}
				}
			}
		}

		.el-tabs + .el-menu {
			left: $base-left-menu-width-min + 10;
			width: $base-left-menu-width - $base-left-menu-width-min - 20;
		}

		.el-submenu .el-submenu__title,
		.el-menu-item {
			min-width: 180px;
			border-radius: 5px;
		}
		.el-submenu.is-active {
			background: $base-column-submenu-background-active;
		}

		.two-level-menu {
			margin-bottom: 10px;

			.iconfont,
			.iconfontCis {
				margin-right: 4px;
				font-size: 18px;
				color: #b2bdcc;
			}

			&.is-active {
				background: $base-column-submenu-background-active-area;
				border-radius: $base-border-radius;

				.el-submenu__title,
				.el-submenu__title i,
				.three-level-menu {
					color: #6691cc !important;
				}

				.el-submenu__title .el-submenu__icon-arrow {
					color: $base-color-blue !important;
				}
			}
		}
	}

	&-horizontal {
		::v-deep {
			.logo-container-column {
				.logo {
					width: $base-left-menu-width-min * 1.3 !important;
				}

				.title {
					margin-left: $base-left-menu-width-min * 1.3 !important;
				}
			}

			.el-tabs + .el-menu {
				left: $base-left-menu-width-min * 1.3;
				width: $base-left-menu-width - $base-left-menu-width-min * 1.3;
				border: 0;
			}
		}
	}

	.column-grid {
		display: flex;
		align-items: center;
		justify-content: center;
		width: $base-left-menu-width-min;
		height: $base-menu-item-height;
		overflow: hidden;
		text-overflow: ellipsis;
		word-break: break-all;
		white-space: nowrap;
		color: rgba(255, 255, 255, 0.7);

		&-vertical,
		&-card {
			justify-content: center;
			height: $base-left-menu-width-min;

			> div {
				svg {
					position: relative;
					top: 8px;
					display: block;
					width: $base-font-size-default + 4;
					height: $base-font-size-default + 4;
				}

				[class*='ri-'] {
					display: block;
					height: 20px;
				}
			}
		}

		&-horizontal {
			justify-content: left;
			width: $base-left-menu-width-min * 1.3;
			height: $base-left-menu-width-min / 1.3;
			padding-left: $base-padding / 2;
		}
	}

	::v-deep {
		.el-scrollbar__wrap {
			overflow-x: hidden;
		}

		.el-tabs {
			position: fixed;

			.el-tabs__header.is-left {
				margin-right: 0 !important;

				.el-tabs__nav-wrap.is-left {
					margin-right: 0 !important;
					background: $base-color-blue;

					.el-tabs__nav-scroll {
						height: 100%;
						overflow-y: auto;

						&::-webkit-scrollbar {
							width: 0px;
							height: 0px;
						}
					}
				}
			}

			.el-tabs__nav {
				height: calc(100vh - #{$base-logo-height});
				background: $base-color-blue;
			}

			.el-tabs__item {
				height: auto;
				padding: 0;
				color: $base-color-white;

				&.is-active {
					background: $base-color-blue;
				}
			}
		}

		.el-tabs__active-bar.is-left,
		.el-tabs--left .el-tabs__nav-wrap.is-left::after {
			display: none;
		}

		.el-menu {
			border: 0;

			.el-divider {
				margin: 0 0 $base-margin 0;
				background-color: #f6f6f6;

				&__text {
					color: $base-color-black;
				}
			}

			.el-menu-item,
			.el-submenu__title {
				height: $base-menu-item-height;
				// overflow: hidden;
				line-height: $base-menu-item-height;
				text-overflow: ellipsis;
				white-space: nowrap;
				vertical-align: middle;

				@include active;
			}
		}
	}

	&.is-collapse {
		::v-deep {
			width: 0;
		}
	}
}
</style>
