<template>
	<div class="user-detail readonly" v-loading.fullscreen.lock="loading">
		<!-- 用户信息 -->
		<UserInfo :userDetail="userDetail" @success="_apiGetUserDatailsById" />
		<!-- 详情tab切换区 -->
		<div class="user-content">
			<GcDetailTab
				ref="detailTabRef"
				:tab-list="tabList"
				:default-active-name.sync="defaultActiveName"
				@controlLoading="controlLoading"
				@refresh="_apiGetUserDatailsById"
				@tab-change="handleTabChange"
			></GcDetailTab>
		</div>
	</div>
</template>

<script>
import { apiQueryEnterpriseInfoById } from '@/api/userManage.api.js'
import UserInfo from './user-info' // 用户信息
import Overview from './overview' // 概览
import { mergeOpenInvoice5 } from '@/api/costManage.api'
export default {
	name: 'UserView',
	components: {
		UserInfo,
	},
	computed: {
		tabList() {
			const arr = [
				{
					name: 'Overview',
					label: '概览',
					component: Overview,
					data: this.userDetail,
				},
			]
			return arr
		},
	},
	data() {
		this.invoiceApi = mergeOpenInvoice5
		return {
			loading: false,
			defaultActiveName: 'Overview',
			controlLoading: false,
			userDetail: {},
			openInvoiceType: '',
			billList: [],
			openInvoiceDialogShow: false,
			onInvoiceOpenDone: null,
		}
	},
	activated() {
		if (this.$route.query.userId) {
			this._apiGetUserDatailsById()
		}
	},
	methods: {
		// 用户信息详情
		async _apiGetUserDatailsById() {
			try {
				const data = await apiQueryEnterpriseInfoById({
					userId: this.$route.query.userId,
				})
				this.userDetail = data
			} catch (error) {
				console.log(error)
			}
		},
		handleTabChange(data) {
			this.$refs.detailTabRef.$refs.componentRef[data.index].handleSearch()
		},
	},
}
</script>
<style lang="scss" scoped>
.user-detail {
	width: 100%;
	height: 100%;
	display: flex;
	.user-content {
		width: calc(100% - 290px);
	}
}
::v-deep {
	.container {
		background-color: #fff;
		padding: 20px;
		display: flex;
		flex-direction: column;
		height: 100%;
		.table-container {
			flex: 1;
			overflow: auto;
		}
	}
}
.readonly {
	&::v-deep {
		.el-tabs__active-bar {
			display: none;
		}
	}
}
</style>
