<template>
	<div class="container">
		<Left @getParams="getParams" @clearTable="clearTable" />
		<Right ref="tableRef" />
	</div>
</template>

<script>
import Left from './left/index.vue'
import Right from './right/index.vue'
export default {
	name: '',
	components: { Left, Right },
	data() {
		return {}
	},
	computed: {},
	created() {},
	methods: {
		getParams(params) {
			this.$refs.tableRef.searchParams = params
			this.$refs.tableRef.pageData.current = 1
			this.$refs.tableRef._apiGetMeterCard()
		},
		clearTable() {
			this.$refs.tableRef.clearTable()
		},
	},
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	height: 100%;
}
</style>
