/**
 * hidden: true                   默认是false，设为true则该项路由不会显示在左侧导航栏
 * meta : {
    title: 'title'                
    icon: 'svg-name'/'el-icon-x' sidebar icon
    affix: true                  默认false，设为true则多标签页中固定显示
    permissions: ['archiveAdd', 'monitor', ...]     接口返回当前用户对应的权限字符串，待登录接口确定统一配置
  }
 */
const routerOrder = require('../consts/routerOrder')
const files = require.context('./modules', true, /\.js$/)
const routeMap = []
// 过滤掉不需要的路由
const filterKeys = ['./archives.js']
files.keys().map(key => {
	if (filterKeys.includes(key)) return
	const array = files(key).default || []
	const file = array[0] || {}
	const configOrder = routerOrder.default || {}
	file['meta']['order'] = configOrder[file['name']]
	routeMap.push(...array)
})
// 路由展示顺序排序
routeMap.sort(function (a, b) {
	return a.meta.order - b.meta.order
})

export const constantRoutes = [
	{
		path: '/login',
		component: () => import('@/views/login'),
		hidden: true,
	},
	{
		path: '/om-login',
		component: () => import('@/views/login/omLogin.vue'),
		hidden: true,
	},
]

export const asyncRoutes = routeMap
