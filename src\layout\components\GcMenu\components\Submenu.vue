<template>
	<el-submenu ref="subMenu" :index="itemOrMenu.fullPath" :popper-append-to-body="false">
		<template #title>
			<gc-icon
				v-if="itemOrMenu.meta && itemOrMenu.meta.icon"
				font-family="iconfontCis"
				:icon="itemOrMenu.meta.icon"
			/>
			<span>{{ itemOrMenu.meta.title }}</span>
		</template>
		<slot />
	</el-submenu>
</template>

<script>
export default {
	name: 'Submenu',
	props: {
		itemOrMenu: {
			type: Object,
			default() {
				return null
			},
		},
	},
}
</script>
