<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-11 19:59:52
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-14 16:17:36
-->
<template>
	<gc-el-dialog :show="isShow" title="编辑表册编号" custom-top="120px" width="800px" @close="handleClose">
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { getAlleyMap, bookNoEditSubmit } from '@/api/meterReading.api.js'
import { bookTypeOptions } from '@/consts/optionList.js'

export default {
	name: '',
	props: {
		// 弹窗显示/隐藏
		show: {
			type: <PERSON><PERSON><PERSON>,
			default: false,
		},
		permissions: {
			type: String,
			default: 'plan-collection_meterReadingBook_updateBook',
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				bookId: '',
				orgCode: '',
				alleyId: '',
				bookNo: '',
				bookType: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						col: 12,
						disabled: true,
					},
				},
				{
					type: 'el-select',
					label: '坊别',
					prop: 'alleyId',
					options: [],
					attrs: {
						col: 12,
						disabled: true,
					},
				},
				{
					type: 'el-input',
					label: '表册编号',
					prop: 'bookNo',
					attrs: {
						col: 12,
						disabled: false,
						placeholder: '请输入表册编号',
					},
				},
				{
					type: 'el-select',
					label: '册本类型',
					prop: 'bookType',
					options: bookTypeOptions,
					attrs: {
						col: 12,
						disabled: true,
					},
				},
			],
			formAttrs: {
				labelWidth: '110px',
				labelPosition: 'right',
				rules: {
					bookNo: [
						{
							required: true,
							message: '请输入表册编号',
							trigger: ['blur', 'change'],
						},
					],
				},
			},
		}
	},
	watch: {
		'formData.orgCode': function (newVal) {
			// 获取坊别数据
			this.getAlleyMapData(newVal)
		},
	},
	methods: {
		// 获取坊别数据
		async getAlleyMapData(orgCode) {
			try {
				if (!orgCode) return

				const res = await getAlleyMap({
					orgCode,
				})

				if (!res.length) {
					this.formItems[1].attrs.noDataText = ''
				}

				this.formItems[1].options = res.map(item => {
					return {
						value: item.id,
						label: item.alleyName,
						alleyCode: item.alleyCode,
					}
				})
			} catch (error) {
				console.error(error)
				this.formItems[1].options = []
			}
		},
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				const result = await bookNoEditSubmit(this.formData).catch(e => {
					console.error(e)
					this.$message.error(e.message || '册本编号更新失败')
				})
				if (result === undefined) return
				this.$message.success(`册本编号更新成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
</style>
