<template>
	<div class="page-layout">
		<div class="page-left">
			<gc-model-header
				class="info-title"
				title="年份列表"
				:icon="require(`@/assets/images/icon/title-list.png`)"
			/>
			<el-button
				v-has="'cpm_balanceSheetDate_addBalanceSheetDate'"
				type="primary"
				size="small"
				style="margin-bottom: 12px"
				@click="handleAdd"
			>
				新增结账年份
			</el-button>
			<div class="list-box">
				<div
					class="list-item"
					v-for="(item, index) in yearList"
					:key="item.id"
					:class="{ active: listActive === index }"
					@click="handleYearClick(index)"
				>
					<div class="label">{{ item.dateYear }}</div>
				</div>
			</div>
		</div>
		<div class="page-right">
			<gc-model-header
				class="info-title"
				title="结账日清单"
				:icon="require(`@/assets/images/icon/title-multi-check.png`)"
			/>
			<GcTable ref="tableRef" :loading="loading" :columns="columns" :table-data="tableData">
				<template v-slot:deal="{ row, $index }">
					<el-button
						v-has="'cpm_balanceSheetDate_updateBalanceSheetDate'"
						type="text"
						size="medium"
						:disabled="!isFutureDate(row.balanceSheetDate)"
						@click="handleAdjust(row, $index)"
					>
						修改
					</el-button>
				</template>
			</GcTable>
		</div>

		<!-- 新增结账年份弹窗 -->
		<AddYearDialog ref="addYearDialogRef" :show.sync="showAddYearDialog" @success="getYearList(listActive)" />
		<!-- 修改结账日弹窗 -->
		<AdjustDateDialog
			ref="adjustDateDialogRef"
			:show.sync="showAdjustDateDialog"
			:prev-date="prevDateComp"
			:next-date="nextDateComp"
			@success="getList"
		/>
	</div>
</template>

<script>
import AddYearDialog from './components/AddYearDialog.vue'
import AdjustDateDialog from './components/AdjustDateDialog.vue'
import { queryBalanceSheetDateYearList, queryBalanceSheetDateList } from '@/api/basicConfig.api'

export default {
	name: 'AccountDayConfig',
	components: { AddYearDialog, AdjustDateDialog },
	data() {
		return {
			// 左侧列表
			listActive: 0,
			yearList: [],
			// 右侧列表
			loading: false,
			columns: [
				{
					key: 'dateYear',
					name: '年份',
					tooltip: true,
				},
				{
					key: 'dateMonth',
					name: '月份',
					tooltip: true,
				},
				{
					key: 'balanceSheetDate',
					name: '结账日',
					tooltip: true,
				},
				{
					hide: !this.$has('cpm_balanceSheetDate_updateBalanceSheetDate'),
					key: 'deal',
					name: '操作',
					fixed: 'right',
					width: 100,
				},
			],
			tableData: [],

			// 新增结账年份弹窗
			showAddYearDialog: false,
			// 修改结账日弹窗
			showAdjustDateDialog: false,
			// 当前修改结账日的行下标
			currentRowIndex: 0,
		}
	},
	computed: {
		prevDateComp() {
			if (!this.currentRowIndex || this.tableData.length === 0) return ''
			return this.tableData[this.currentRowIndex - 1].balanceSheetDate
		},
		nextDateComp() {
			if (this.tableData.length === 0 || this.currentRowIndex === this.tableData.length - 1) return ''
			return this.tableData[this.currentRowIndex + 1].balanceSheetDate
		},
	},
	created() {
		this.getYearList()
	},
	methods: {
		isFutureDate(date) {
			const inputDate = this.dayjs(date)
			const currentDate = this.dayjs()

			return inputDate.isAfter(currentDate)
		},
		handleYearClick(index) {
			if (this.listActive !== index) {
				this.listActive = index
				this.getList()
			}
		},

		// 新增年份
		handleAdd() {
			this.showAddYearDialog = true
		},
		// 获取左侧年份列表
		async getYearList(active = 0) {
			try {
				const res = await queryBalanceSheetDateYearList()
				this.yearList = res || []
				this.listActive = active
				this.getList()
			} catch (error) {
				console.error(error)
				this.yearList = []
				this.listActive = 0
			}
		},

		// 右侧列表
		async getList() {
			this.loading = true
			try {
				const res = await queryBalanceSheetDateList({
					dateYear: this.yearList[this.listActive].dateYear,
				})
				this.tableData = res || []
			} catch (error) {
				console.error(error)
				this.tableData = []
			} finally {
				this.loading = false
			}
		},
		// 修改结账日
		handleAdjust(data, index) {
			this.currentRowIndex = index
			this.showAdjustDateDialog = true
			this.$nextTick(() => {
				this.$refs.adjustDateDialogRef.setFormData(data)
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.page-left {
	overflow: auto;
}
.right-top {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-bottom: 12px;
}
.info-title {
	height: auto;
	padding: 0;
	margin-bottom: 12px;
}
.icon-more {
	transform: rotate(90deg);
}
.list-box {
	flex: 1;
	overflow: auto;
	.list-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 42px;
		padding: 0 12px;
		cursor: pointer;
		.label {
			flex: 1;
			margin-right: 12px;
			@include text-overflow;
		}
		&.active {
			color: #2f87fe;
			background-color: rgba(196, 221, 255, 0.5);
			.el-dropdown {
				display: block;
			}
		}
	}
	.el-dropdown {
		display: none;
	}
}
</style>
