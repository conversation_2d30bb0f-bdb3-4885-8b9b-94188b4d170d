<template>
	<div class="container-wrapper">
		<GcModelHeader
			class="info-title"
			title="册本选择"
			:icon="require('@/assets/images/icon/title-common-parameters.png')"
		></GcModelHeader>
		<div class="container">
			<div class="left-container">
				<GcFormSimple
					ref="leftFormRef"
					v-model="leftForm"
					:formItems="leftFormItems"
					:formAttrs="leftFormAttrs"
				>
					<el-form-item>
						<el-button type="primary" @click="handleChangePage({ page: 1 })">查询</el-button>
					</el-form-item>
				</GcFormSimple>
				<div class="table-container">
					<GcTable
						:columns="columns"
						:table-data="tableData"
						showPage
						:page-size="pageData.size"
						:total="pageData.total"
						:current-page="pageData.current"
						@click="handleRowClick"
						@current-page-change="handleChangePage"
					/>
				</div>
				<div class="button-group">
					<button class="gc-button gc-button-three" type="button" @click="handleReset">重 置</button>
					<button class="gc-button gc-button-two" type="button" @click="changeTab('addressChoose')">
						下一项
					</button>
				</div>
			</div>
			<div class="right-container">
				<GcFormSimple ref="rightFormRef" v-model="rightForm" :formItems="rightFormItems"></GcFormSimple>
			</div>
		</div>
	</div>
</template>

<script>
import { ruleRequired } from '@/utils/rules'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getFormItems } from './formItem'
import { getAlleyMap, getBookListNoAuth } from '@/api/meterReading.api.js'
export default {
	name: '',
	components: {},
	props: {
		detailData: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			leftForm: {
				orgCode: '',
				bookType: 2,
				alleyId: '',
				bookNo: '',
			},
			leftFormItems: getFormItems(this).leftFormItems,
			leftFormAttrs: {
				inline: true,
				labelWidth: '80px',
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
				},
			},
			rightForm: {
				bookNo: '',
				bookTypeDesc: '',
				alleyName: '',
				meterReadingStaffName: '',
				meterReadingStaffPhone: '',
				meterReadingCycleDesc: '',
				archivesNoRange: '',
			},
			rightFormItems: getFormItems(this).rightFormItems,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			currentBookInfo: {},
		}
	},
	mounted() {
		const orgList = this.$store.getters.orgList || []
		const firstOrgItem = orgList[0] || {}
		this.leftForm.orgCode = firstOrgItem.value || ''
	},
	watch: {
		// 修改
		detailData: {
			async handler(v) {
				if (v) {
					this.leftForm.orgCode = this.detailData.orgCode
					this.leftForm.bookNo = this.detailData.bookNo
					this.leftForm.bookType = this.detailData.bookType
					await this._getBookList()
					if (this.tableData.length > 0) {
						this.setCurrentRow(this.detailData)
					} else {
						this.setCurrentRow({})
					}
				}
			},
			deep: true,
		},
	},
	computed: {
		columns() {
			return [
				{
					width: 50,
					key: 'bookId',
					name: '',
					render: (h, row) => {
						return h(
							'el-radio',
							{
								props: {
									value: this.currentBookInfo.bookId,
									label: row.bookId,
								},
								on: {
									input: value => {
										this.currentBookInfo.bookId = value ? row.bookId : ''
									},
								},
							},
							'',
						)
					},
				},
				{
					key: 'bookNo',
					name: '表册编号',
					tooltip: true,
				},
				{
					key: 'archivesNoRange',
					name: '抄表范围',
					tooltip: true,
				},
				{
					key: 'meterReadingStaffName',
					name: '抄表员',
					tooltip: true,
				},
			]
		},
	},
	methods: {
		// 获取坊别
		async _getAlleyMap() {
			const leftAlleyObj = this.leftFormItems.find(item => item.prop === 'alleyId')
			this.leftForm.alleyId = ''
			leftAlleyObj.options = []
			getAlleyMap({
				orgCode: this.leftForm.orgCode,
			}).then(data => {
				if (leftAlleyObj) {
					leftAlleyObj.options = data.map(item => {
						return {
							value: item.id,
							label: item.alleyName,
							...item,
						}
					})
				}
			})
		},
		// 获取册本
		async _getBookList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.leftForm))
				Object.assign(formParams, { current, size })
				const { records, total } = await getBookListNoAuth(formParams)

				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleRowClick({ row, event }) {
			if (event.target.tagName.toLowerCase() === 'input' && event.target.type === 'radio') {
				return
			}
			this.setCurrentRow(row)
		},
		setCurrentRow(obj) {
			const flag = Boolean(obj.bookId)

			flag ? this.assignForm(obj) : this.clearRightForm()
			this.currentBookInfo = obj
			this.$emit('getValid', 'recordChoose', flag)
		},
		assignForm(obj) {
			const keys = Object.keys(this.rightForm)
			keys.forEach(key => {
				if (obj && Object.prototype.hasOwnProperty.call(obj, key)) {
					this.rightForm[key] = obj[key]
				}
			})
		},
		clearRightForm() {
			this.$refs.rightFormRef.resetFields()
		},
		async handleSearch() {
			const valid = await this.$refs.leftFormRef.validate()
			if (!valid) return
			await this._getBookList()
			if (this.tableData.length > 0) {
				this.setCurrentRow(this.tableData[0])
			} else {
				this.setCurrentRow({})
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.handleSearch()
		},
		changeTab(v) {
			if (!this.currentBookInfo.bookId) {
				this.$message.error('未选择册本')
				return
			}
			this.$emit('changeTab', v)
		},
		handleReset() {
			this.$refs.leftFormRef.resetFields()
			this.setCurrentRow({})
			this.tableData = []
			this.pageData.total = 0
		},
	},
}
</script>

<style lang="scss" scoped>
.container-wrapper {
	height: 100%;
}
.container {
	display: flex;
	height: calc(100% - 60px);
	gap: 20px;
	.left-container {
		display: flex;
		flex-direction: column;
		flex: 1;
		width: 0;
		height: 100%;
		.table-container {
			flex: 1;
			overflow: auto;
		}
		.button-group {
			width: 100%;
			margin-top: 20px;
			padding: 0 20px;
			.gc-button {
				margin-right: 8px;
			}
		}
	}
	.right-container {
		padding: 16px;
		width: 224px;
		height: 100%;
		overflow: auto;
		background-color: #f2f9ff;
	}
}
</style>
