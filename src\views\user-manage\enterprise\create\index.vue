<template>
	<div class="enterprise-container" v-loading.fullscreen.lock="loading">
		<GcModelHeader
			title="企业信息"
			:icon="require('@/assets/images/icon/title-common-parameters.png')"
		></GcModelHeader>
		<div class="container">
			<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<template v-slot:enterpriseNumber>
					<el-input
						v-if="isModify"
						v-model="formData.enterpriseNumber"
						placeholder="请输入"
						disabled
					></el-input>
					<el-input
						v-else
						v-model="formData.enterpriseNumber"
						placeholder="请输入"
						@input="debouncedInputHandler"
					>
						<img slot="append" src="@/assets/images/icon/get-num.svg" @click="_apiGetEnterpriseNumber" />
					</el-input>
				</template>
				<template v-slot:collectionAgreementNumber>
					<div class="collection-agreement-number-input">
						<el-input v-model="formData.collectionAgreementNumber" class="account-more" readonly>
							<i slot="append" class="el-icon-more" @click="handleSetCollection"></i>
						</el-input>
						<i class="el-icon-close close-button" @click="handleClearCollection"></i>
					</div>
				</template>
				<template v-slot:userSyn>
					<el-radio-group v-model="formData.userSyn">
						<el-radio :label="1">是</el-radio>
						<el-radio :label="0">否</el-radio>
					</el-radio-group>
				</template>
				<template v-slot:taxpayerIdentity>
					<el-autocomplete
						style="width: 100%"
						v-model="formData.taxpayerIdentity"
						:fetch-suggestions="queryTaxpayerIdentity"
						placeholder="请输入"
						@select="handleTaxpayerIdentitySelect"
						@change="handleTaxpayerIdentityChange"
					>
						<template slot-scope="{ item }">
							<div class="billing-information-item">
								<p class="billing-information-item-row">纳税人识别号：{{ item.taxpayerIdentity }}</p>
								<p class="billing-information-item-row">开票抬头：{{ item.userName }}</p>
							</div>
						</template>
					</el-autocomplete>
				</template>
			</GcFormRow>
		</div>
		<div class="button-group">
			<el-button class="btn-preview" @click="handleCancel">取消</el-button>
			<el-button class="btn-create" type="primary" @click="handleSave">
				{{ isModify ? '确定修改' : '确定创建' }}
			</el-button>
		</div>
		<!-- 弹窗 -->
		<SearchCollectionAccount
			:show.sync="showDialog"
			:collectionAgreementNumber="formData.collectionAgreementNumber"
			:orgCode="formData.orgCode"
			@success="setCollectionSuccess"
		/>
	</div>
</template>

<script>
import _ from 'lodash'
import {
	ruleRequired,
	RULE_PHONE,
	RULE_INCORRECTEMAIL,
	ruleMaxLength,
	RULE_POSTALCODE,
	RULE_INTEGERONLY,
	RULE_INT_ENGLISH,
} from '@/utils/rules'
import { removeNullParams, trimParams } from '@/utils/index.js'
import getFormItems from './userForm.js'
import { apiGetEnterpriseNumber } from '@/api/meterManage.api'
import {
	apiAddEnterprise,
	apiUpdateEnterprise,
	apiQueryEnterpriseInfoByNumber,
	apiVerifyEnterpriseNumber,
	apiQueryInvoiceBuyer,
} from '@/api/userManage.api'
import SearchCollectionAccount from './components/SearchCollectionAccount.vue'

export default {
	name: 'EnterpriseCreate',
	components: {
		SearchCollectionAccount,
	},
	data() {
		return {
			loading: false,
			formData: {
				orgCode: '',
				enterpriseNumber: '',
				enterpriseName: '',
				contactPeople: '',
				enterpriseAddress: '',
				chargingMethod: '',
				collectionAccountId: '',
				collectionAgreementNumber: '',
				userMobile: '',
				contactPhone: '',
				mailingAddress: '',
				zipCode: '',
				email: '',
				invoiceType: '',
				taxpayerIdentity: '',
				openBank: '',
				bankAccount: '',
				userSyn: 1,
				buyerName: '',
			},
			billingInfoDisabled: false,
			showDialog: false,
			collectionChargeConst: 2, // 收费方式-托收常量2
		}
	},
	computed: {
		formItems() {
			let formItems = getFormItems(this)
			if (!this.isModify) {
				formItems = formItems.filter(item => item.prop !== 'userSyn')
			}
			return formItems
		},
		formAttrs() {
			return {
				labelWidth: '135px',
				labelPosition: 'right',
				rules: {
					orgCode: [ruleRequired('必填')],
					enterpriseNumber: [
						ruleRequired('必填'),
						{
							pattern: /^\d{7}$/,
							message: '必须为数字且7位',
							trigger: '',
						},
					],
					enterpriseName: [ruleRequired('必填'), ruleMaxLength(32)],
					contactPeople: [ruleMaxLength(64)],
					enterpriseAddress: [ruleRequired('必填'), ruleMaxLength(128)],
					chargingMethod: [ruleRequired('必填')],
					collectionAccountId: [RULE_INTEGERONLY],
					userMobile: [RULE_PHONE],
					contactPhone: [ruleMaxLength(32)],
					mailingAddress: [ruleMaxLength(64)],
					zipCode: [RULE_POSTALCODE],
					email: [RULE_INCORRECTEMAIL],
					openBank: [ruleMaxLength(32)],
					taxpayerIdentity: [RULE_INT_ENGLISH, ruleMaxLength(20)],
					bankAccount: [RULE_INTEGERONLY, ruleMaxLength(32)],
					buyerName: [ruleMaxLength(32)],
				},
			}
		},
		isModify() {
			return this.$route.path === '/userManage/enterpriseModify'
		},
	},
	async activated() {
		const { enterpriseNumber } = this.$route.query
		if (enterpriseNumber) {
			const data = await apiQueryEnterpriseInfoByNumber({ enterpriseNumber })
			this.assignForm(data)
		}
	},
	created() {
		this.queryTaxpayerIdentity = _.debounce(this.queryTaxpayerIdentity, 250)
	},
	methods: {
		debouncedInputHandler: _.debounce(async function () {
			const valid = await this.$refs.formRef.validateField('enterpriseNumber')
			if (valid !== '') return
			try {
				await apiVerifyEnterpriseNumber({
					enterpriseNumber: this.formData.enterpriseNumber,
				})
			} catch (error) {
				this.$message.error(error.message)
			}
		}, 500),
		// 获取企业编号
		async _apiGetEnterpriseNumber() {
			if (!this.formData.orgCode) {
				this.$message.error('请选择营业分公司')
				return
			}
			try {
				const { enterpriseNumber } = await apiGetEnterpriseNumber({
					orgCode: this.formData.orgCode,
				})
				const message = enterpriseNumber ? '获取企业编号成功' : '未查询到数据'
				this.formData.enterpriseNumber = enterpriseNumber
				this.$message.success(message)
			} catch (error) {
				console.error(error)
			}
		},
		assignForm(obj) {
			this.formData = Object.assign(this.formData, obj)
		},
		handleSetCollection() {
			if (!this.formData.orgCode) {
				this.$message.error('请选择营业分公司')
				return
			}
			if (this.formData.chargingMethod !== this.collectionChargeConst) {
				this.$message.error('收费类型需为银行托收')
				return
			}
			this.showDialog = true
		},
		setCollectionSuccess(data) {
			this.assignForm({
				collectionAccountId: data.collectionAccountId,
				collectionAgreementNumber: data.collectionAgreementNumber,
			})
		},
		handleClearCollection() {
			this.assignForm({
				collectionAccountId: '',
				collectionAgreementNumber: '',
			})
		},
		handleCancel() {
			this.$store.dispatch('tagsView/delView', this.$route).then(tags => {
				const { fullPath } = tags.slice(-1)[0]
				this.$router.push(fullPath || '/')
			})
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			if (
				this.formData.chargingMethod === this.collectionChargeConst &&
				!this.formData.collectionAgreementNumber
			) {
				this.$message.error('托收协议号不能为空')
				return
			}
			const params = trimParams(removeNullParams(this.formData))

			if (this.isModify) {
				params.userSyn = this.formData.userSyn === 1 ? true : false
			} else {
				delete params.userSyn
			}

			try {
				this.loading = true
				this.isModify ? await apiUpdateEnterprise(params) : await apiAddEnterprise(params)
				this.$message.success('操作成功')
				this.$store.dispatch('tagsView/delView', this.$route).then(() => {
					this.$router.push({
						path: '/userManage/enterpriseList',
					})
				})
			} catch (error) {
				this.$message.error(error)
				console.error('操作失败:', error)
			} finally {
				this.loading = false
			}
		},
		async queryTaxpayerIdentity(taxpayerIdentity, callback) {
			taxpayerIdentity = taxpayerIdentity || ''
			try {
				let result = await apiQueryInvoiceBuyer({
					taxpayerIdentity,
				})
				result = result || []
				const list = result.map(item => {
					const { taxpayerIdentity } = item
					return { ...item, value: taxpayerIdentity }
				})

				callback(list)
			} catch (e) {
				console.log(e)
				callback([])
			}
		},
		handleTaxpayerIdentitySelect(item) {
			const { userName, openBank, bankAccount } = item
			this.formData.openBank = openBank
			this.formData.buyerName = userName
			this.formData.bankAccount = bankAccount
			this.billingInfoDisabled = true
		},
		handleTaxpayerIdentityChange() {
			this.billingInfoDisabled = false
		},
	},
}
</script>

<style lang="scss" scoped>
.enterprise-container {
	height: 100%;
	display: flex;
	flex-direction: column;
	width: 100%;
	.model-header {
		background-color: #fff;
	}
	.container {
		flex: 1;
		background-color: #fff;
	}
	.button-group {
		width: 100%;
		height: 40px;
		.btn-create,
		.btn-preview {
			margin-top: 20px;
			border-radius: 4px;
			height: 32px;
		}
		.btn-create {
			width: 216px;
		}
		.btn-preview {
			width: 110px;
			border: 1px solid #2f87fe;
			color: #2f87fe;
		}
	}
	.collection-agreement-number-input {
		display: flex;
		align-items: center;
		.account-more {
			width: calc(100% - 38px);
		}
		.close-button {
			width: 33px;
			height: 30px;
			text-align: center;
			line-height: 30px;
			border-radius: 4px;
			margin-left: 5px;
			background-color: #f5f7fa;
			color: rgba(0, 0, 0, 0.65);
			border: 1px solid #dcdfe6;
			cursor: pointer;
		}
	}
}
::v-deep {
	.el-form {
		padding: 0 20px;
	}

	.el-input-group__append {
		padding: 0;
		img,
		.el-icon-more {
			display: block;
			padding: 0 10px;
			height: 30px;
			line-height: 30px;
			object-fit: none;
			cursor: pointer;
		}
	}
	.account-more .el-input__inner {
		background-color: #f7f7f7;
	}
}
.billing-information-item {
	padding: 8px 0;
}
</style>
