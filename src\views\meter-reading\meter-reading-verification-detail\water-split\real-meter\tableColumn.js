export function getColumn(_) {
	return [
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'distributionModeDesc',
			name: '水量分配方式',
			tooltip: true,
		},
		{
			key: 'splitFlagDesc',
			name: '本次水量是否拆分',
			tooltip: true,
		},
		{
			key: 'lastMeterReading',
			name: '上次指针',
			tooltip: true,
		},
		{
			key: 'curMeterReading',
			name: '本次指针',
			tooltip: true,
		},
		{
			key: 'checkStatusDesc',
			name: '抄表情况',
			width: 160,
		},
		{
			key: 'useAmount',
			name: '本次水量',
			tooltip: true,
		},
		{
			key: 'thisRecordDate',
			name: '抄表日期',
			minWidth: 180,
			tooltip: true,
		},
		{
			hide: !_.$has([
				'plan-collection_meterReadingReview_waterSpilt',
				'plan-collection_meterReadingReview_reviewPass2',
				'plan-collection_meterReadingReview_reviewReject2',
				'plan-collection_meterReadingTask_updateMeterReadingRecord3',
			]),
			key: 'deal',
			name: '操作',
			width: 240,
			fixed: 'right',
		},
	]
}
