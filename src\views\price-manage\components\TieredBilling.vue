<template>
	<!-- 阶梯计费策略 -->
	<div class="model-tiered-bill">
		<div class="title">
			<p>阶梯计费策略</p>
			<div class="title-right">
				<span>阶梯数量</span>
				<el-input-number
					size="mini"
					:min="2"
					:max="5"
					v-model="levelNumber"
					@blur="levelNumber = levelNumber || levelPriceForm.tableData.length"
				></el-input-number>
			</div>
		</div>
		<div class="price-table">
			<el-form :model="levelPriceForm" ref="levelPriceForm">
				<el-table
					:data="levelPriceForm.tableData"
					style="width: 100%"
					:header-cell-style="{
						background: '#DDE7FA',
						color: '#222',
					}"
				>
					<el-table-column label="阶梯" width="60">
						<template slot-scope="scope">{{ enumLevel[scope.$index] }}阶梯</template>
					</el-table-column>
					<el-table-column prop="" :label="`${fieldName.baseName}量（${fieldName.baseUnit}）`" width="300">
						<template slot-scope="scope">
							<div class="form-label">
								<el-form-item
									:rules="
										[
											ruleRequired(`请输入${fieldName.useQuantity}`),
											{
												pattern: /^[\d]{0,8}$/,
												message: '最多输入8位正整数',
											},
											scope.$index > 0
												? {
														pattern: /^[1-9]\d*(\.\d+)?$/i,
														message: `${fieldName.useQuantity}必须大于0`,
												  }
												: undefined,
										].filter(Boolean)
									"
									:prop="`tableData[${scope.$index}].min`"
								>
									<el-input
										@blur="changeLevelNumber(scope.$index, 'min', scope.row.min)"
										:disabled="scope.$index === 0"
										v-model.number="scope.row.min"
									></el-input>
								</el-form-item>
								<span class="_text">＜ {{ enumLevel[scope.$index] + '阶' }} ≤</span>
								<el-form-item
									:rules="[
										ruleRequired(`请输入${fieldName.useQuantity}`),
										{
											pattern: /^[\d]{0,8}$/,
											message: '最多输入8位正整数',
										},
										{
											pattern: /^[1-9]\d*(\.\d+)?$/i,
											message: `${fieldName.useQuantity}必须大于0`,
										},
									]"
									:prop="`tableData[${scope.$index}].max`"
								>
									<el-input
										:disabled="scope.$index === levelNumber - 1"
										@blur="changeLevelNumber(scope.$index, 'max', scope.row.max)"
										v-model.number="scope.row.max"
									></el-input>
								</el-form-item>
							</div>
						</template>
					</el-table-column>
					<el-table-column prop="warnLevelBorder" :label="`报警阈值（${fieldName.baseUnit}）`">
						<template slot-scope="scope">
							<el-form-item
								:rules="levelPriceForm.tableData[scope.$index].warnLevelBorderRules"
								:prop="`tableData[${scope.$index}].warnLevelBorder`"
							>
								<el-input
									v-model.trim="scope.row.warnLevelBorder"
									:disabled="scope.$index === levelNumber - 1"
									@blur="updateWarnLevelBorderRules(scope.$index)"
								></el-input>
							</el-form-item>
						</template>
					</el-table-column>
					<el-table-column prop="price" :label="`价格（元/${fieldName.baseUnit}）`">
						<template slot-scope="scope">
							<el-form-item
								:rules="[
									ruleRequired('请输入价格'),
									metrologicalVerification ? RULE_ZERO_PRICE : RULE_PRICE,
								]"
								:prop="`tableData[${scope.$index}].price`"
							>
								<el-input v-model="scope.row.price"></el-input>
							</el-form-item>
						</template>
					</el-table-column>
					<el-table-column prop="address" label="操作" width="50">
						<template slot-scope="scope">
							<span
								v-if="scope.$index > 1"
								class="el-icon-delete"
								@click="
									levelNumber = levelNumber - 1
									deleteIdx = scope.$index
								"
							></span>
							<span v-else>/</span>
						</template>
					</el-table-column>
				</el-table>
			</el-form>
		</div>
	</div>
</template>

<script>
import { ruleRequired, RULE_PRICE, RULE_ZERO_PRICE } from '@/utils/rules'
import getFieldName from '@/mixin/getFieldName.js'
import { mapState } from 'vuex'
import { accAdd, accSub } from '@/utils/calc'
export default {
	mixins: [getFieldName],
	props: {
		operateType: Number,
		levelBorder: String,
		warnLevelBorder: String,
		levelPrice: String,
	},
	data() {
		return {
			ruleRequired,
			RULE_PRICE,
			RULE_ZERO_PRICE,
			enumLevel: { 0: '一', 1: '二', 2: '三', 3: '四', 4: '五' },
			levelNumber: 5,
			levelPriceForm: {
				tableData: [
					{
						min: 0,
						max: '',
						warnLevelBorder: '',
						price: '',
						warnLevelBorderRules: [
							{
								pattern: /^[\d]{0,8}$/,
								message: '最多输入8位正整数',
							},
							{
								pattern: /^[1-9]\d*(\.\d+)?$/i,
								message: '报警阈值必须大于0',
							},
						],
					},
					{
						min: '',
						max: '',
						warnLevelBorder: '',
						price: '',
						warnLevelBorderRules: [
							{
								pattern: /^[\d]{0,8}$/,
								message: '最多输入8位正整数',
							},
							{
								pattern: /^[1-9]\d*(\.\d+)?$/i,
								message: '报警阈值必须大于0',
							},
						],
					},
					{
						min: '',
						max: '',
						warnLevelBorder: '',
						price: '',
						warnLevelBorderRules: [
							{
								pattern: /^[\d]{0,8}$/,
								message: '最多输入8位正整数',
							},
							{
								pattern: /^[1-9]\d*(\.\d+)?$/i,
								message: '报警阈值必须大于0',
							},
						],
					},
					{
						min: '',
						max: '',
						warnLevelBorder: '',
						price: '',
						warnLevelBorderRules: [
							{
								pattern: /^[\d]{0,8}$/,
								message: '最多输入8位正整数',
							},
							{
								pattern: /^[1-9]\d*(\.\d+)?$/i,
								message: '报警阈值必须大于0',
							},
						],
					},
					{
						min: '',
						max: 99999999,
						warnLevelBorder: 99999999,
						price: '',
						warnLevelBorderRules: [
							{
								pattern: /^[\d]{0,8}$/,
								message: '最多输入8位正整数',
							},
							{
								pattern: /^[1-9]\d*(\.\d+)?$/i,
								message: '报警阈值必须大于0',
							},
						],
					},
				],
			},
			deleteIdx: null,
		}
	},

	created() {
		if (this.levelBorder && this.operateType === 1) {
			this.initBackRenderTable()
		}
	},

	methods: {
		/* 调价初始化更新阶梯计费策略表格数据信息 */
		initBackRenderTable() {
			const levelBorder = this.levelBorder.split('|').map(o => parseInt(o))
			const warnLevelBorder = this.warnLevelBorder ? this.warnLevelBorder.split('|') : []
			const levelPrice = this.levelPrice.split('|')
			this.levelNumber = levelPrice.length
			// 计算数据前几个数据之和得到新的list
			const beforeSumLevel = levelBorder.reduce(
				(prev, cur) => {
					const sum = Math.min(prev[prev.length - 1] + cur, 99999999)
					prev.push(sum)
					return prev
				},
				[0],
			)
			this.levelPriceForm.tableData = []
			for (let i = 0; i < beforeSumLevel.length - 1; i++) {
				this.levelPriceForm.tableData.push({
					min: beforeSumLevel[i],
					max: beforeSumLevel[i + 1],
					warnLevelBorder: warnLevelBorder[i]
						? i + 1 === beforeSumLevel.length - 1
							? warnLevelBorder[i]
							: accAdd(beforeSumLevel[i], warnLevelBorder[i])
						: '',
					price: levelPrice[i],
				})
				this.updateWarnLevelBorderRules(i)
			}
		},

		/**
		 * 更新阶梯值内用量输入值自动更新关联阶梯数据
		 * @param { Number } idx 当前更新阶梯值下标索引
		 * @param { String } type 当前更新阶梯值为起始点还是结束点 min - 起始点， max - 结束点
		 * @param { String } levelNumber 当前阶梯值输入数值
		 */
		changeLevelNumber(idx, type, levelNumber) {
			const data = this.levelPriceForm.tableData
			if (type === 'max') {
				if (data[idx].min && levelNumber && Number(levelNumber) <= Number(data[idx].min)) {
					this.$message.error('同一阶最大值必须大于最小值')
					this.levelPriceForm.tableData[idx][type] = ''
					return
				}
				if (data[idx + 1][type] && Number(levelNumber) >= data[idx + 1][type]) {
					this.$message.error('上一阶的最大值必须小于下一阶的最大值')
					this.levelPriceForm.tableData[idx][type] = ''
					return
				}
				this.levelPriceForm.tableData[idx + 1].min = levelNumber
				this.updateWarnLevelBorderRules(idx + 1)
			} else if (type === 'min') {
				if (data[idx].max && levelNumber && Number(levelNumber) >= Number(data[idx].max)) {
					this.$message.error('同一阶最小值必须小于最大值')
					this.levelPriceForm.tableData[idx][type] = ''
					return
				}
				if (data[idx - 1][type] && Number(levelNumber) <= data[idx - 1][type]) {
					this.$message.error('上一阶的最小值必须小于下一阶的最小值')
					this.levelPriceForm.tableData[idx][type] = ''
					return
				}
				this.levelPriceForm.tableData[idx - 1].max = levelNumber
				this.updateWarnLevelBorderRules(idx - 1)
			}
			this.updateWarnLevelBorderRules(idx)
		},
		/**
		 * 动态更新报警阈值验证规则
		 * @param { Number } idx 当前行的索引
		 */
		updateWarnLevelBorderRules(idx) {
			const { min = '', max = '' } = this.levelPriceForm.tableData[idx]
			let rules = [
				{
					pattern: /^[\d]{0,8}$/,
					message: '最多输入8位正整数',
				},
				{
					pattern: /^[1-9]\d*(\.\d+)?$/i,
					message: '报警阈值必须大于0',
				},
			]
			if (min !== '' && max !== '') {
				rules.push({
					validator: (rule, value, callback) => {
						if (!value) {
							callback()
						} else if (min !== '' && max !== '' && (value <= min || value > max)) {
							callback(new Error(`必须>${min}且≤${max}`))
						} else {
							callback()
						}
					},
					trigger: 'blur',
				})
			}
			this.$set(this.levelPriceForm.tableData[idx], 'warnLevelBorderRules', rules)
			if (min !== '' && max !== '') {
				this.$nextTick(() => {
					this.$refs.levelPriceForm.validateField(`tableData[${idx}].warnLevelBorder`)
				})
			}
		},

		/**
		 * 删除指定层级价格数据信息
		 * @param { Number } idx 需要删除阶梯价格下标索引
		 */
		deleteLevelPrice(idx) {
			this.levelPriceForm.tableData.splice(idx, this.levelPriceForm.tableData.length - this.levelNumber)
			const L = this.levelPriceForm.tableData.length
			if (idx === L) {
				this.levelPriceForm.tableData[L - 1].max = 99999999
				this.levelPriceForm.tableData[L - 1].warnLevelBorder = 99999999
				this.levelPriceForm.tableData[L - 1].warnLevelBorderRules = []
				this.updateWarnLevelBorderRules(idx - 1)
			} else {
				this.levelPriceForm.tableData[idx].min = this.levelPriceForm.tableData[idx - 1].max
				this.levelPriceForm.tableData[L - 1].warnLevelBorderRules = []
				this.updateWarnLevelBorderRules(idx)
			}
			this.deleteIdx = null
		},

		/**
		 * 根据tableData获取levelBorder和levelPrice
		 * @return { Object }
		 */
		packageParams() {
			const data = this.levelPriceForm.tableData
			const levelBorder = data
				.map((o, index) => {
					if (index === data.length - 1) {
						return 99999999
					}
					return o.max - o.min
				})
				.join('|')
			const warnLevelBorder = data
				.map((o, index) => {
					if (index === data.length - 1) {
						return o.warnLevelBorder ? 99999999 : ''
					}
					return o.warnLevelBorder ? accSub(o.warnLevelBorder, o.min) : ''
				})
				.join('|')
			const levelPrice = data.map(o => o.price).join('|')
			return {
				levelBorder,
				warnLevelBorder,
				levelPrice,
				ladderLevel: data.length,
			}
		},
	},

	watch: {
		levelNumber(newVal, oldVal) {
			if (newVal > oldVal) {
				const L = this.levelPriceForm.tableData.length
				const diffL = newVal - L
				this.levelPriceForm.tableData[L - 1].max = ''
				this.levelPriceForm.tableData[L - 1].warnLevelBorder = ''
				for (let i = 1; i <= diffL; i++) {
					this.levelPriceForm.tableData.push({
						min: '',
						max: i === diffL ? 99999999 : '',
						warnLevelBorder: i === diffL ? 99999999 : '',
						price: '',
						warnLevelBorderRules: [
							{
								pattern: /^[\d]{0,8}$/,
								message: '最多输入8位正整数',
							},
							{
								pattern: /^[1-9]\d*(\.\d+)?$/i,
								message: '报警阈值必须大于0',
							},
						],
					})
				}
				this.$nextTick(() => {
					this.$refs.levelPriceForm.clearValidate()
				})
			} else {
				this.deleteLevelPrice(this.deleteIdx || newVal)
			}
		},
	},

	computed: {
		...mapState({
			metrologicalVerification: state => {
				return state.user.tenant?.business_config?.is_metrological_verification || false
			},
		}),
	},
}
</script>

<style lang="scss" scoped>
.model-tiered-bill {
	padding: 20px 25px;
	background: #f5f8ff;
	box-sizing: border-box;
	.title {
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		p {
			color: #222;
		}
		.title-right {
			span {
				margin-right: 8px;
				color: #222;
			}
			::v-deep .el-input-number--mini {
				width: 102px;
			}
		}
	}
	.price-table {
		margin-top: 10px;
		.form-label {
			display: flex;
			align-items: center;
		}
		::v-deep .el-form {
			padding: 0;
			.el-form-item {
				margin-bottom: 0;
			}
			.el-form-item__error {
				position: relative;
			}
		}
		::v-deep .el-input {
			width: 98px;
		}
		._text {
			margin: 0 6px;
		}
		.el-icon-delete {
			color: #ec6b60;
			font-size: 16px;
			cursor: pointer;
		}
	}
}
</style>
