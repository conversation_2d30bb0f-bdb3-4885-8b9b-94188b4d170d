import request from './request'
import qs from 'qs'

export const apiLogin = (requestInfo, loginCertify) => {
	//   requestInfo.password = encodeURIComponent(requestInfo.password);
	return request({
		url: `/auth/aggregation/login?${qs.stringify(requestInfo)}`,
		method: 'post',
		headers: { ...loginCertify },
	})
}
export const apiOmLogin = param => {
	//   param.password = encodeURIComponent(param.password);
	return request({
		url: `auth/aggregation/om/login?${qs.stringify(param)}`,
		method: 'POST',
	})
}

export const apiGetNeedVerify = parameter => {
	return request({
		url: 'v1/tos/tenant/loginverifyenabled?subdomain=' + parameter,
		data: parameter,
	})
}

export const apiGetDataList = data => {
	return request({
		url: '/cpm/sysdata/getsysdatalist?' + qs.stringify(data),
		method: 'get',
		data,
	})
}

//获取验证码
export const apiGetCodeImg = params =>
	request({
		url: `auth/authn/code?${qs.stringify(params)}`,
		method: 'post',
	})

export const apiLogout = () => {
	return request({
		url: 'auth/session/logout',
		method: 'POST',
	})
}

// 租户账户修改自身的密码
export const apiTenantUpdatePass = parameter => {
	return request({
		url: 'auth/aggregation/password/tenant/update',
		method: 'POST',
		params: parameter,
	})
}
// 管理员修改自己的密码
export const apiAdminUpdatePass = parameter => {
	return request({
		url: 'auth/aggregation/password/sys/update',
		method: 'POST',
		params: parameter,
	})
}

//租户业务配置
export const apiGetTenantDetail = parameter => {
	return request({
		url: 'v1/tos/tenant/detail',
		method: 'get',
		params: parameter,
	})
}

//获取租户所在省份
export const apiGetOrgDetail = parameter => {
	return request({
		url: 'v1/tos/organization/detail',
		method: 'get',
		params: parameter,
	})
}
