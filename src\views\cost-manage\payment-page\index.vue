<template>
	<div class="tab-content overview">
		<div class="bg-overflow">
			<div class="layout-overview">
				<div class="left">
					<div class="data-container">
						<gc-model-header
							title="缴费信息"
							:icon="require('@/assets/images/icon/title-cash.png')"
						></gc-model-header>
						<div v-if="tableData.length" class="left-top">
							<div class="left-buttons">
								<el-button
									v-has="'billing_bill-adjust_adjustment3'"
									type="primary"
									:disabled="feeAdjustDisabled()"
									@click="handleClickLeftButton('adjustment')"
								>
									费用调整
								</el-button>
								<el-button
									v-has="'billing_bill-adjust_reduction2'"
									type="primary"
									:disabled="feeAdjustDisabled()"
									@click="handleClickLeftButton('reduction')"
								>
									减免
								</el-button>
								<el-button
									v-has="'billing_bill-adjust_partial-payment2'"
									type="primary"
									:disabled="partialDisabled()"
									@click="handleClickLeftButton('partial')"
								>
									部分缴费
								</el-button>
							</div>
							<div class="select-bill-total">
								<span>选中账单合计：</span>
								<div class="bill-item">
									<el-checkbox :disabled="waterFeeCheckDisabled" v-model="waterFeeChecked">
										<span>水费：</span>
										<span class="amount">￥{{ checkedTotalMoney.waterFee | toThousand }}</span>
									</el-checkbox>
								</div>
								<div class="bill-item">
									<el-checkbox :disabled="billItemAmtCheckDisabled" v-model="billItemAmtChecked">
										<span>污水费：</span>
										<span class="amount">￥{{ checkedTotalMoney.billItemAmt | toThousand }}</span>
									</el-checkbox>
								</div>
								<div class="bill-item">
									<el-checkbox :disabled="billItemAmt2CheckDisabled" v-model="billItemAmt2Checked">
										<span>附加费：</span>
										<span class="amount">￥{{ checkedTotalMoney.billItemAmt2 | toThousand }}</span>
									</el-checkbox>
								</div>
							</div>
						</div>
						<div class="table-container">
							<GcTable
								ref="gcTableRef"
								:columns="columns"
								:table-data="tableData"
								:page-size="pageData.size"
								:total="pageData.total"
								:current-page="pageData.current"
								:current-row-key="currentBillNo"
								isHighlightCurrent
								row-key="billNo"
								showPage
								needType="selection"
								reserve-selection
								@selectChange="selectChange"
								@click="rowClick"
								@current-page-change="changePage"
							>
								<template slot="append" v-if="tableData.length">
									<div class="footer-bill-total">
										<span>当页账单金额小计：</span>
										<div class="bill-item">
											<span class="label">应缴金额：</span>
											<span class="amount">￥{{ footerTotalMoney.total | toThousand }}</span>
										</div>
										<div class="bill-item">
											<span class="label">水费：</span>
											<span class="amount">￥{{ footerTotalMoney.useAmt | toThousand }}</span>
										</div>
										<div class="bill-item">
											<span class="label">污水费：</span>
											<span class="amount">
												￥{{ footerTotalMoney.billItemAmt | toThousand }}
											</span>
										</div>
										<div class="bill-item">
											<span class="label">附加费：</span>
											<span class="amount">
												￥{{ footerTotalMoney.billItemAmt2 | toThousand }}
											</span>
										</div>
									</div>
								</template>
							</GcTable>
						</div>
					</div>
					<!-- 册本信息、价格信息 -->
					<div class="data-container left-bottom">
						<div class="records-info">
							<gc-model-header
								title="默认开票信息"
								:icon="require('@/assets/images/icon/title-multi-check.png')"
							></gc-model-header>
							<gc-group-detail :data="defaultInvoiceData"></gc-group-detail>
						</div>
						<!-- <div class="records-info">
							<gc-model-header
								title="表具信息"
								:icon="require('@/assets/images/icon/title-statistics.png')"
							></gc-model-header>
							<gc-group-detail :data="meterData"></gc-group-detail>
						</div> -->
						<div class="price-info">
							<gc-model-header
								title="价格信息"
								:icon="require('@/assets/images/icon/title-statistics.png')"
							></gc-model-header>
							<gc-group-detail :data="priceData">
								<template #currentPrice>
									<div class="price-show">
										<p v-for="(item, index) in levelPrice" :key="index">
											<span>{{ item.key }}</span>
											<span>{{ item.value }}</span>
										</p>
									</div>
								</template>
								<template #surcharge>
									<div class="price-show">
										<p v-for="(item, index) in surchangePrice" :key="index">
											<span>{{ item.key }}</span>
											<span>{{ item.value }}</span>
										</p>
									</div>
								</template>
							</gc-group-detail>
						</div>
					</div>
					<div class="account-balance">
						<gc-model-header
							title="账户余额"
							:icon="require('@/assets/images/icon/title-account.png')"
						></gc-model-header>
						<div class="amount" :class="{ ashes: !this.tableData.length }">
							<span class="unit">￥</span>
							<span v-if="!isBlank(meterBalanceAmount)">
								{{ meterBalanceAmount | toThousand }}
							</span>
							<span v-else>--</span>
						</div>
					</div>
				</div>
				<!-- 缴费信息 -->
				<!-- 一期先隐藏校验 -->
				<div class="right">
					<div class="search-wrapper">
						<gc-search-fake
							:title="'搜索缴费信息'"
							@handleclick="handleClickFakeSearch"
							@clear="clearFromComponent"
							:valueObj="valueObj"
						></gc-search-fake>
						<!-- 弹窗 -->
						<GcSearchDialogNew
							title="搜索缴费信息"
							width="900px"
							:dialogVisible.sync="showSearchPopou"
							:search-condition="searchCondition"
							:active-condition.sync="activeCondition"
							:valueObj="valueObj"
							:filterConditionVisible="false"
							@dialog-close="closeDialog"
							@public-search="searchTable"
						/>
					</div>
					<div class="pay-wrapper">
						<el-form ref="formRef" :model="totalMoneyForm" :rules="totalMoneyRules">
							<el-form-item prop="receivableAmount" class="pay-container">
								<h5>账单应缴总金额(元)</h5>
								<PayInput v-model="totalMoneyForm.receivableAmount" :disabled="true" />
							</el-form-item>
							<div class="paid-change">
								<el-form-item prop="totalMoney" class="pay-container">
									<h5>实收金额(元)</h5>
									<PayInput
										v-model="totalMoneyForm.totalMoney"
										:disabled="isPayButtonDisabled"
										:show-number-tip="isPayButtonDisabled || showNumberTip"
									/>
								</el-form-item>
								<el-form-item class="pay-container give-change">
									<h5>找零(元)</h5>
									<PayInput :value="changeAmount" :disabled="true" />
									<div class="auto-transfer">
										<span>自动转预存</span>
										<el-switch v-model="autoTransfer" active-color="#13ce66"></el-switch>
									</div>
								</el-form-item>
							</div>
							<el-form-item prop="payMode">
								<h3>付款方式</h3>
								<el-select v-model="totalMoneyForm.payMode" placeholder="请选择" filterable clearable>
									<el-option
										v-for="(item, index) in payModeList"
										:key="index"
										:label="item.label"
										:value="item.value"
									></el-option>
								</el-select>
							</el-form-item>
							<el-form-item prop="userMobile">
								<h3>手机号</h3>
								<el-input v-model="totalMoneyForm.userMobile" placeholder="请输入" clearable />
							</el-form-item>
							<el-form-item prop="email">
								<h3>电子邮箱</h3>
								<el-input v-model="totalMoneyForm.email" placeholder="请输入" clearable />
							</el-form-item>
						</el-form>
						<!-- 充值操作区 -->
						<button class="btn" type="button" :disabled="isPayButtonDisabled" @click="handleSubmit">
							缴 费
						</button>
					</div>
				</div>
			</div>
		</div>
		<SuccessDialog
			:amount="{ value: autoTransfer ? totalMoneyForm.totalMoney : totalMoneyForm.receivableAmount, unit: '元' }"
			:show.sync="showPaySuccessDialog"
			:invoiceOpenDisabled="invoiceOpenDisabled"
			:tipsShow="tipsShow"
			@go-on="reload"
			@print-invoice="handleOpenInvoice"
			@print-fee-detail="handlePrintFeeDetail"
		/>
		<!-- 开票 -->
		<PaymentInvoiceDialog
			ref="openInvoiceDialog"
			:type="openInvoiceType"
			:billList="billList"
			:api="invoiceApi"
			:show.sync="openInvoiceDialogShow"
			:submitExtraParams="invoiceExtraParams"
			@success="handleOpenInvoiceSuccess"
		/>
		<!-- 费用明细打印 -->
		<FeeDetailDialog ref="FeeDetailDialog" :show.sync="openFeeDialogShow" />
	</div>
</template>

<script>
import { apiGetBillArrearsList, apiBillClear, apiGetAccountBalance } from '@/api/costManage.api'
import { apiGetArchivesDetail2 } from '@/api/meterManage.api'
import { getfilterName } from '@/utils'
import { accAdd } from '@/utils/calc.js'
import { getColumn } from './tableColumn.js'
import { isBlank } from '@/utils/validate.js'
import { RULE_INCORRECTEMAIL, RULE_PHONE } from '@/utils/rules.js'
import PayInput from './components/payInput.vue'
import SuccessDialog from './components/SuccessDialog.vue'
import PaymentInvoiceDialog from '@/components/PaymentInvoiceDialog'
import FeeDetailDialog from './components/FeeDetailDialog.vue'
import { mergeOpenInvoice6, batchOpenInvoice2 } from '@/api/costManage.api'
export default {
	name: '',
	components: { PayInput, SuccessDialog, PaymentInvoiceDialog, FeeDetailDialog },
	data() {
		this.invoiceApi = {
			merge: mergeOpenInvoice6,
			batch: batchOpenInvoice2,
		}
		return {
			columns: getColumn(),
			tableData: [],
			pageData: {
				current: 1,
				size: 300,
				total: 0,
			},
			totalMoneyForm: {
				receivableAmount: '', // 账单应缴总金额
				totalMoney: '', // 实收金额
				payMode: '1',
				// isYes: "",一期隐藏
				userMobile: '',
				email: '',
			},
			totalMoneyRules: {
				totalMoney: { validator: this.validator },
				payMode: [{ required: true, message: '请选择付款方式', trigger: 'blur' }],
				userMobile: [RULE_PHONE],
				email: [RULE_INCORRECTEMAIL],
			},
			showNumberTip: false,
			archivesDetail: {},
			billIds: [],
			currentBillNo: '',
			// 搜索
			valueObj: {
				value: null,
			},
			showSearchPopou: false,
			searchCondition: [
				{
					key: 'enterpriseNumber',
					label: '企业编号',
					attrs: {
						placeholder: '请输入企业编号(多个编号请用英文逗号分隔)',
					},
				},
				{
					key: 'archivesIdentity',
					label: '表卡编号',
					attrs: {
						placeholder: '请输入表卡编号(多个编号请用英文逗号分隔)',
					},
				},
				{
					key: 'userName',
					label: '用户名称',
				},
				// {
				// 	key: 'bookNo',
				// 	label: '表册编号',
				// },
				{
					key: 'collectionAgreementNumber',
					label: '托收协议号',
				},
				// {
				// 	key: 'billDate',
				// 	label: '账期',
				// 	type: 'el-date-picker',
				// 	attrs: {
				// 		type: 'month',
				// 	},
				// },
				{
					key: 'billNo',
					label: '账单编号',
				},
				{
					key: 'userMobile',
					label: '手机号',
				},
				{
					key: 'certificateNo',
					label: '证件号码',
				},
			],
			activeCondition: 'enterpriseNumber',
			billIdList: [],
			billNoList: [],
			showPaySuccessDialog: false,
			billList: [],
			openInvoiceType: 'merge',
			openInvoiceDialogShow: false,
			// 账户余额
			meterBalanceAmount: null,
			openFeeDialogShow: false,
			executing: false,
			// 是否自动转预存
			autoTransfer: false,
			waterFeeChecked: true,
			billItemAmtChecked: true,
			billItemAmt2Checked: true,
			tipsShow: false,
		}
	},
	computed: {
		isPayButtonDisabled() {
			const { waterFeeChecked, billItemAmtChecked, billItemAmt2Checked, totalMoneyForm } = this
			const { receivableAmount } = totalMoneyForm || {}
			const isCheckedFee = [waterFeeChecked, billItemAmtChecked, billItemAmt2Checked].some(checked => checked)
			// 金额必须大于0
			const isCanPay = receivableAmount > 0
			return !this.billIds.length || !isCheckedFee || !isCanPay
		}, // 缴费按钮是否禁用
		payModeList() {
			return this.$store.getters.dataList.payMode
				? this.$store.getters.dataList.payMode
						.filter(item => item.sortValue != 3)
						.map(item => {
							return {
								label: item.sortName,
								value: item.sortValue,
							}
						})
				: []
		}, // 付款方式
		meterData() {
			const list = [
				{
					key: '表卡编号',
					value: '--',
					field: 'archivesIdentity',
				},
				{
					key: '用户名称',
					value: '--',
					slot: 'userName',
					field: 'userName',
				},
				{
					key: '联系人',
					value: '--',
					field: 'contactPeople',
				},
				{
					key: '手机号',
					value: '--',
					field: 'userMobile',
				},
				{
					key: '收费方式',
					value: '--',
					field: 'chargingMethod',
				},
				{
					key: '表具地址',
					value: '--',
					field: 'addressFullName',
				},
				{
					key: '水表仓库编号',
					value: '--',
					field: 'meterWarehouseCode',
				},
				{
					key: '表具类型',
					value: '--',
					field: 'meterTypeName',
				},
				{
					key: '指针数',
					value: '--',
					field: 'meterReading',
				},
			]
			const extractedData = Object.assign(
				{},
				this.archivesDetail.address,
				this.archivesDetail.archives,
				this.archivesDetail.meter,
				this.archivesDetail.user,
			)
			list.forEach(item => {
				if (item.field === 'chargingMethod') {
					item.value = this.$store.getters.dataList.chargingMethod
						? getfilterName(
								this.$store.getters.dataList.chargingMethod,
								extractedData[item.field],
								'sortValue',
								'sortName',
						  )
						: '--'
				} else {
					item.value = extractedData[item.field]
				}
			})
			return {
				list,
				row: 3,
			}
		}, // 表具信息
		priceData() {
			const list = [
				{
					key: '价格编号',
					value: '--',
					field: 'priceCode',
				},
				{
					key: '价格名称',
					value: '--',
					field: 'priceName',
				},
				{
					key: '计费类型',
					field: 'billingTypeId',
				},
				{
					key: '当前价格',
					slot: 'currentPrice',
					col: 2,
				},
				{
					key: '附加费',
					slot: 'surcharge',
				},
			]
			const extractedData = Object.assign({}, ...Object.values(this.archivesDetail))
			list.forEach(item => {
				if (item.field === 'billingTypeId') {
					item.value = this.$store.getters.dataList.billingType
						? getfilterName(
								this.$store.getters.dataList.billingType,
								extractedData[item.field],
								'sortValue',
								'sortName',
						  )
						: '--'
				} else {
					item.value = extractedData[item.field]
				}
			})
			return {
				list,
				row: 3,
			}
		},
		levelPrice() {
			const str = this.archivesDetail.price && this.archivesDetail.price.priceDesc
			const arr =
				str &&
				str.split('<br/>').map(item => {
					const [key, value] = item.split(' ')
					return { key, value }
				})
			return arr
		}, // 当前价格
		surchangePrice() {
			const list = this.archivesDetail.price && this.archivesDetail.price.priceBillItemList
			const arr =
				list &&
				list.map(item => {
					return { key: item.itemName, value: item.billItemPrice + '元/吨' }
				})
			return arr
		}, // 附加费
		invoiceOpenDisabled() {
			if (!this.billList.length) return true
			return this.billList.some(({ billType, invoiceStatus }) => {
				// 罚没款生成的账单 不能开票
				// 已销账，且未开票或者已冲红的才能开票
				return billType === 6 || (invoiceStatus !== 0 && invoiceStatus !== 2)
			})
		},
		// 开票额外参数
		invoiceExtraParams() {
			const { userMobile, email } = this.totalMoneyForm
			const params = {}
			if (userMobile) {
				params.userMobile = userMobile
			}
			if (email) {
				params.email = email
			}
			return params
		},
		// 找零
		changeAmount() {
			const amount = this.totalMoneyForm.totalMoney - this.totalMoneyForm.receivableAmount
			return amount < 0 ? 0 : amount
		},
		// 选中合计
		checkedTotalMoney() {
			const FEE_TOTAL_CONFIG = [
				{
					// 水费
					sourceKey: 'useAmt',
					outputKey: 'waterFee',
				},
				{
					// 污水费
					sourceKey: 'billItemAmt',
					outputKey: 'billItemAmt',
				},
				{
					// 附加费
					sourceKey: 'billItemAmt2',
					outputKey: 'billItemAmt2',
				},
			]
			const billList = this.billList

			return FEE_TOTAL_CONFIG.reduce((acc, config) => {
				const { sourceKey, outputKey } = config
				const total = this.calculateTotal(billList, sourceKey)
				acc[outputKey] = total
				return acc
			}, {})
		},
		// 当前页合计
		footerTotalMoney() {
			const FEE_TOTAL_CONFIG = [
				{
					// 水费
					sourceKey: 'useAmt',
					outputKey: 'useAmt',
				},
				{
					// 污水费
					sourceKey: 'billItemAmt',
					outputKey: 'billItemAmt',
				},
				{
					// 附加费
					sourceKey: 'billItemAmt2',
					outputKey: 'billItemAmt2',
				},
			]
			const tabData = this.tableData || []
			return FEE_TOTAL_CONFIG.reduce(
				(acc, config) => {
					const { sourceKey, outputKey } = config
					const total = this.calculateTotal(tabData, sourceKey)
					acc[outputKey] = total
					acc.total = this.accAddMultiple([acc.total, total])
					return acc
				},
				{ total: 0 },
			)
		},
		// 选中的账单应收总金额
		checkedTotalAmount() {
			const { waterFeeChecked, billItemAmtChecked, billItemAmt2Checked, checkedTotalMoney } = this
			// 附加费必缴
			const calculateFeeItemKeys = []

			if (waterFeeChecked) {
				calculateFeeItemKeys.push('waterFee')
			}

			if (billItemAmtChecked) {
				calculateFeeItemKeys.push('billItemAmt')
			}

			if (billItemAmt2Checked) {
				calculateFeeItemKeys.push('billItemAmt2')
			}

			const totalList = calculateFeeItemKeys.map(key => {
				return checkedTotalMoney[key] || 0
			})

			return this.accAddMultiple(totalList)
		},
		// 是否选中罚没款账单
		isHasFineBillItem() {
			let { billList } = this
			billList = billList || []

			for (let i = 0; i < billList.length; i++) {
				const item = billList[i]
				// 账单类型为罚没款 或者 计划用水的账单
				if ([5, 6].includes(item.billType)) {
					return true
				}
			}

			return false
		},
		billItemAmtCheckDisabled() {
			const { isHasFineBillItem } = this
			return isHasFineBillItem
		},
		waterFeeCheckDisabled() {
			const { isHasFineBillItem } = this
			return isHasFineBillItem
		},
		billItemAmt2CheckDisabled() {
			const { isHasFineBillItem } = this
			return isHasFineBillItem
		},
		defaultInvoiceData() {
			const list = [
				{
					key: '开票类型',
					value: '--',
					field: 'invoiceType',
				},
				{
					key: '发票抬头',
					value: '--',
					field: 'userName',
				},
				{
					key: '银行账户',
					value: '--',
					field: 'bankAccount',
				},
				{
					key: '纳税人识别号',
					value: '--',
					field: 'taxpayerIdentity',
				},
				{
					key: '电子邮箱',
					value: '--',
					field: 'email',
				},
				{
					key: '手机号码',
					value: '--',
					field: 'phoneNumber',
				},
			]
			const { invoiceType = [] } = this.$store.getters.dataList || {}

			const { archivesDetail } = this
			const { invoiceBuyer } = archivesDetail || {}
			const extractedData = invoiceBuyer || {}

			list.forEach(item => {
				item.value = extractedData[item.field]
				if (item.field === 'invoiceType') {
					item.value = getfilterName(invoiceType, item.value, 'sortValue', 'sortName')
				}
			})
			return {
				list,
				row: 3,
			}
		},
	},
	watch: {
		isHasFineBillItem: {
			handler(flag) {
				this.waterFeeChecked = flag ? true : this.waterFeeChecked
				this.billItemAmtChecked = flag ? true : this.billItemAmtChecked
				this.billItemAmt2Checked = flag ? true : this.billItemAmt2Checked
			},
			immediate: true,
		},
		checkedTotalAmount: {
			handler(total) {
				total = total || 0
				this.totalMoneyForm.totalMoney = total
				this.totalMoneyForm.receivableAmount = total
			},
			immediate: true,
		},
		// archivesDetail: {
		// 	handler(data) {
		// 		const { invoiceBuyer } = data || {}
		// 		const { email, phoneNumber } = invoiceBuyer || {}
		// 		this.totalMoneyForm.email = email || ''
		// 		this.totalMoneyForm.userMobile = phoneNumber || ''
		// 	},
		// 	immediate: true,
		// },
	},
	activated() {
		const { ids, billNos } = this.$route.query
		if (ids) {
			this.billIdList = ids.split(',')
			this._apiGetBillArrearsList()
		} else if (billNos) {
			this.billNoList = billNos.split(',')
			this._apiGetBillArrearsList()
		}
	},
	filters: {
		// 千分位
		toThousand(num) {
			return new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(num)
		},
	},
	methods: {
		isBlank,
		changePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this._apiGetBillArrearsList()
		},
		// 待缴费列表
		async _apiGetBillArrearsList() {
			const { year } = this.$route.query
			const params = {
				current: this.pageData.current,
				size: this.pageData.size,
			}
			if (year) {
				params.year = year
			}
			if (this.billIdList.length) {
				params.billIdList = this.billIdList
			}
			if (this.billNoList.length) {
				params.billNoList = this.billNoList
			}
			if (this.valueObj.value) {
				params[this.valueObj.key] = this.valueObj.value
			}
			try {
				const { records, total } = await apiGetBillArrearsList(params)
				this.pageData.total = total
				this.tableData = records
				this.currentBillNo = this.tableData[0]?.billNo
				if (this.tableData.length) {
					this._apiGetArchivesDetail(this.tableData[0]?.archivesId)
					this.getAccountBalance(this.tableData[0]?.archivesId)
				} else {
					this.archivesDetail = {}
					this.meterBalanceAmount = null
				}
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 300,
					total: 0,
				}
			}
		},
		// 高亮行表册信息
		async _apiGetArchivesDetail(archivesId) {
			const res = await apiGetArchivesDetail2({ archivesId })
			this.archivesDetail = res
		},
		// 点击行
		rowClick(scope) {
			const archivesId = scope.row.archivesId
			this._apiGetArchivesDetail(archivesId)
			this.getAccountBalance(archivesId)
		},
		// 多选
		selectChange(arr) {
			if (this.executing) return
			if (arr.length > 300) {
				this.executing = true
				this.$message.error('最多只能选择 300 行账单！')
				this.$refs.gcTableRef.clearCheckTableSelection()
				this.billList.forEach(row => {
					this.$refs.gcTableRef.toggleRowSelection(row, true)
				})
				this.executing = false
				return
			}
			this.billList = arr
			this.billIds = arr.map(item => {
				return {
					billId: item.billId,
					year: item.year,
				}
			})
		},
		accAddMultiple(args) {
			return args.reduce((acc, curr) => accAdd(acc, curr), 0)
		},
		calculateTotal(data, key) {
			return this.accAddMultiple(data.map(item => item[key] || 0))
		},
		// 充值输入校验
		validator(rule, value, callback) {
			const val = Number(value)
			this.showNumberTip = false
			if (isNaN(val) || isBlank(value)) {
				callback(new Error('请输入合法的数字'))
			} else if (rule.field === 'totalMoney' && val < this.totalMoneyForm.receivableAmount) {
				callback(new Error('实收金额不能小于账单应缴总金额'))
			} else if (rule.field === 'totalMoney' && val > 9999999999.99) {
				callback(new Error('实收金额超出最大值'))
			} else {
				this.showNumberTip = true
				callback()
			}
		},
		handleSubmit() {
			this.$refs.formRef.validate(valid => {
				if (valid) {
					const extractedData = Object.assign({}, ...Object.values(this.archivesDetail))
					// 罚没款生成的账单不提示
					const fineFlag = this.billList.some(item => item.billType === 6)
					this.tipsShow = fineFlag
					if (!fineFlag && !extractedData.taxpayerIdentity && extractedData.userType === 4) {
						this.$confirm('表卡无纳税人识别编号，请确认是否继续缴费？', '提示').then(() => {
							this._apiBillClear()
						})
					} else {
						this._apiBillClear()
					}
				}
			})
		},
		// 销账接口
		async _apiBillClear() {
			const { totalMoneyForm } = this
			const { totalMoney, receivableAmount } = totalMoneyForm || {}
			// 不转预存 取 应缴金额， 转预存 取 实收金额
			const paidAmount = this.autoTransfer ? totalMoney : receivableAmount

			const feeType = this.getFeeType()
			const params = {
				cleareList: this.billIds,
				paidAmount,
				payMode: this.totalMoneyForm.payMode,
				feeType,
				receivableAmount,
			}
			try {
				await apiBillClear(params)
				this.showPaySuccessDialog = true
			} catch (error) {
				console.log(error)
			}
		},
		reload() {
			this.showPaySuccessDialog = false
			this.changePage({ page: 1 })
			this.$refs.gcTableRef.clearCheckTableSelection()
			this.$refs.formRef.resetFields()
			this.billIds = []
			this.billList = []
			this.autoTransfer = false
		},
		// 搜索
		handleClickFakeSearch() {
			this.showSearchPopou = true
		},
		clearFromComponent() {
			this.valueObj = { value: null }
			this.activeCondition = 'enterpriseNumber'
			this.reload()
		},
		closeDialog(params) {
			this.valueObj = params
		},
		// 校验逗号格试
		validateCommaSeparated(value) {
			const regex = /^[^，,]+(?:,[^，,]+)*(,)?$/
			return regex.test(value)
		},
		searchTable(params) {
			if (params.key === 'billDate') {
				params.value = params.value ? this.dayjs(params.value).format('YYYY-MM') : ''
			}
			if (['archivesIdentity', 'enterpriseNumber'].includes(params.key) && params.value) {
				if (!this.validateCommaSeparated(params.value)) {
					this.$message.error('输入格式不正确，请确保使用英文逗号分隔多个值')
					return
				}
				if (params.value.endsWith(',')) {
					params.value = params.value.slice(0, -1)
				}
			}
			this.valueObj = params
			this.billIdList = []
			this.billNoList = []
			this.reload()
		},
		handleOpenInvoice(type) {
			this.openInvoiceType = type
			this.openInvoiceDialogShow = true
		},
		handleOpenInvoiceSuccess() {
			this.reload()
			this.openInvoiceDialogShow = false
		},
		// 查询账户余额
		async getAccountBalance(archivesId) {
			const data = await apiGetAccountBalance({
				archivesId,
			})
			const { meterBalanceAmount } = data || {}
			this.meterBalanceAmount = meterBalanceAmount
		},
		// 打印费用明细
		handlePrintFeeDetail() {
			this.openFeeDialogShow = true
		},
		// 费用调整、减免按钮禁用状态
		feeAdjustDisabled() {
			if (this.billList.length !== 1) return true
			const row = this.billList[0]
			// 计划用水、罚没款生成的账单，不允许账单的减免、费用调整操作
			const flag =
				[0, 2, 5].includes(row.billStatus) &&
				row.pushFlag !== 1 &&
				row.invoiceStatus === 0 &&
				![5, 6].includes(row.billType) &&
				// 不为历史账单 历史账单标识：0-非历史账单，1-历史账单
				row.historicalBilling !== 1

			return !flag
		},
		// 部分缴费按钮禁用状态
		partialDisabled() {
			if (this.billList.length !== 1) return true
			const row = this.billList[0]
			// 计划用水、罚没款生成的账单，不允许账单的部分缴费 操作
			const flag = [2, 5].includes(row.billStatus) && row.pushFlag !== 1 && ![5, 6].includes(row.billType)
			return !flag
		},
		// 费用调整、减免、部分缴费
		handleClickLeftButton(type) {
			const row = this.billList[0]
			if (type === 'reduction') {
				this.$router.push({
					path: '/costManage/costRelief',
					query: {
						id: row.billId,
						year: row.year,
					},
				})
			} else if (type === 'adjustment') {
				this.$router.push({
					path: '/costManage/feeAdjustment',
					query: {
						id: row.billId,
						year: row.year,
					},
				})
			} else if (type === 'partial') {
				this.$router.push({
					path: '/costManage/partialPayment',
					query: {
						id: row.billId,
						year: row.year,
					},
				})
			}
		},
		getFeeType() {
			const FEE_TYPE_SCORE_MAP = ['waterFeeChecked', 'billItemAmtChecked', 'billItemAmt2Checked']
			const result = FEE_TYPE_SCORE_MAP.map(key => {
				const isChecked = this[key]
				return isChecked ? 1 : 0
			})
			return parseInt(result.reverse().join(''), 2)
		},
	},
}
</script>

<style lang="scss" scoped>
@import './style/common.scss';
.left {
	.table-container {
		padding: 0 20px;
		height: 370px;
	}
	.left-bottom {
		display: flex;
		gap: 20px;
		background: #eceff8;
		.records-info {
			width: 50%;
			background: #fff;
		}
		.price-info {
			width: 50%;
			background: #fff;
			.price-show {
				p {
					display: flex;
					span {
						display: inline-block;
					}
					span:nth-child(1) {
						width: 48%;
						max-width: 200px;
					}
					span:nth-child(2) {
						padding-left: 2px;
					}
				}
				p + p {
					padding-top: 12px;
				}
			}
		}
	}
	.account-balance {
		background: #fff;
		margin-top: 20px;
		.amount {
			padding: 0 20px 20px;
			font-size: 30px;
			span.unit {
				font-size: 22px;
			}
			&.ashes {
				color: #ccc;
			}
		}
	}
	.left-top {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-left: 20px;
		padding-right: 10px;
		margin-bottom: 10px;
	}
	.select-bill-total,
	.footer-bill-total {
		display: flex;
		align-items: center;
		.amount {
			color: $base-color-red;
			font-weight: 600;
			// margin-left: 15px;
			margin-right: 10px;
		}
		::v-deep {
			.el-checkbox__label {
				padding-left: 5px;
				color: #222;
				font-weight: normal;
			}
		}
	}
	.select-bill-total {
		padding-right: 0;
	}
	.footer-bill-total {
		justify-content: flex-end;
		margin-top: 10px;
		.amount {
			margin-left: 0;
		}
	}
	::v-deep {
		.gc-table .is-show-page {
			height: calc(100% - 64px - 24px);
		}
	}
}
.right {
	display: flex;
	flex-direction: column;
	.search-wrapper {
		margin-bottom: 10px;
		.fake-search-container {
			width: 100%;
			border-width: 0;
		}
	}
	.pay-wrapper {
		flex: 1;
		background: linear-gradient(137deg, #7dd1c7 0%, #3dad9f 100%);
		box-shadow: 0px 2px 25px 0px rgba(0, 0, 0, 0.1);
		border-radius: 0 4px 4px;
		padding: 20px;
		.pay-container {
			border-radius: 6px;
			padding: 10px;
			height: 135px;
			background-color: #fff;
			h5 {
				color: black;
				margin-bottom: 20px;
			}
		}

		.paid-change {
			display: flex;
			gap: 16px;
			.pay-container {
				width: 50%;
			}
			.give-change {
				position: relative;
				::v-deep {
					.input-false,
					.box-icon,
					input {
						color: #222;
						cursor: default;
					}
				}
				.auto-transfer {
					position: absolute;
					bottom: -32px;
					right: 10px;
					display: flex;
					align-items: center;
					span {
						color: #666;
						margin-right: 6px;
					}
				}
			}
		}

		.recharge-ops {
			padding-top: $base-padding;
			display: flex;
			.el-button {
				flex: 1;
				border-radius: 17px;
			}
			.recharge {
				background: linear-gradient(180deg, #789fff 0%, #3565df 100%);
				color: #ffffff;
				&:hover {
					background: linear-gradient(180deg, #93b2ff 0%, #5e84e6 100%);
				}
				&:focus {
					background: linear-gradient(180deg, #6c8fe6 0%, #2e59c4 100%);
				}
				&.is-disabled {
					background: linear-gradient(180deg, #aec5ff 0%, #86a3ec 100%);
				}
			}
		}
		.btn {
			margin-top: 40px;
			width: 100%;
			height: 38px;
			line-height: 38px;
			text-align: center;
			color: #fff;
			background: linear-gradient(180deg, #f5da94 0%, #e5a410 100%);
			border-radius: 20px;
			border: none;
			cursor: pointer;
			&:hover {
				background: linear-gradient(180deg, #f7e2a9 0%, #ebb741 100%);
			}
			&:active {
				background: linear-gradient(180deg, #ffd871 0%, #ecb538 100%);
			}
			&[disabled] {
				background: linear-gradient(180deg, #f9e9be 0%, #f0c971 100%);
				cursor: not-allowed;
			}
		}
		::v-deep {
			.el-select {
				width: 100%;
			}
			.el-input--small .el-input__inner,
			.input {
				border: none;
			}
			.el-radio__label {
				color: #fff;
			}
		}
	}
}
</style>
