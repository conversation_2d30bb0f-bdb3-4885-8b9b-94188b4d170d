<template>
	<div class="container">
		<div class="base-info">
			<GcModelHeader title="抄表信息" :icon="require('@/assets/images/icon/title-file.png')" />
			<GcGroupDetail :data="recordData"></GcGroupDetail>
		</div>
		<div class="table-container">
			<GcModelHeader title="水量修改记录" :icon="require('@/assets/images/icon/title-file.png')" />
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handleCurrentChange"
			/>
		</div>
	</div>
</template>

<script>
import { getMeterRecordModfiyLog } from '@/api/meterReading.api'
export default {
	name: '',
	components: {},
	data() {
		return {
			loading: false,
			detailData: {},
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			tableData: [],
			columns: [
				{
					key: 'createTime',
					name: '修改时间',
					tooltip: true,
				},
				{
					key: 'beforeLastMeterReading',
					name: '修改前上次指针',
					tooltip: true,
				},
				{
					key: 'beforeCurMeterReading',
					name: '修改前本次指针',
					tooltip: true,
				},
				{
					key: 'beforeUseAmount',
					name: '修改前本次水量',
					tooltip: true,
				},
				{
					key: 'beforeCheckStatusDesc',
					name: '修改前抄表情况',
					tooltip: true,
				},
				{
					key: 'afterLastMeterReading',
					name: '修改后上次指针',
					tooltip: true,
				},
				{
					key: 'afterCurMeterReading',
					name: '修改后本次指针',
					tooltip: true,
				},
				{
					key: 'afterUseAmount',
					name: '修改后本次水量',
					tooltip: true,
				},
				{
					key: 'afterCheckStatusDesc',
					name: '修改后抄表情况',
					tooltip: true,
				},
				{
					key: 'staffName',
					name: '修改人',
					tooltip: true,
				},
			],
		}
	},
	computed: {
		// 记录信息
		recordData() {
			const list = [
				{
					key: '表册编号',
					value: '--',
					field: 'bookNo',
				},
				{
					key: '表卡编号',
					value: '--',
					field: 'archivesIdentity',
				},
				{
					key: '地址',
					value: '--',
					field: 'addressFullName',
					tooltip: true,
				},
				{
					key: '抄表日期',
					value: '--',
					field: 'thisRecordDate',
				},
				{
					key: '抄表员',
					value: '--',
					field: 'meterReadingStaffName',
				},
				{
					key: '上次指针',
					value: '--',
					field: 'lastMeterReading',
				},
				{
					key: '本次指针',
					value: '--',
					field: 'curMeterReading',
				},
				{
					key: '本次水量',
					value: '--',
					field: 'useAmount',
				},
				{
					key: '抄表情况',
					value: '--',
					field: 'checkStatusDesc',
				},
				{
					key: '抄表状态',
					value: '--',
					field: 'readingStatusDesc',
				},
			]
			list.forEach(item => {
				if (this.detailData) {
					item.value = this.detailData[item.field] || '--'
				}
			})
			return {
				list,
				row: 6,
			}
		},
	},
	mounted() {
		console.log('mounted - route params:', this.$route.params)
		console.log('mounted - route query:', this.$route.query)

		// 获取传递的数据
		if (this.$route.params.rowData) {
			this.detailData = this.$route.params.rowData
		} else if (this.$route.query.rowData) {
			try {
				this.detailData = JSON.parse(this.$route.query.rowData)
			} catch (error) {
				console.error('解析 query.rowData 失败:', error)
				this.detailData = {}
			}
		} else {
			console.warn('未找到传递的数据')
			this.detailData = {}
		}

		console.log('mounted - detailData:', this.detailData)

		// 安全检查并设置 meterReadingRecordId
		if (this.detailData && this.detailData.meterReadingRecordId) {
			this.pageData.meterReadingRecordId = this.detailData.meterReadingRecordId
			this._apiGetMeterRecordModfiyLog()
		} else {
			console.warn('detailData 中没有 meterReadingRecordId')
		}
	},
	methods: {
		handleCurrentChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this._apiGetMeterRecordModfiyLog()
		},
		_apiGetMeterRecordModfiyLog() {
			const meterReadingRecordId = this.detailData.meterReadingRecordId
			if (!meterReadingRecordId) {
				return
			}
			this.loading = true
			getMeterRecordModfiyLog(Object.assign({ meterReadingRecordId }, this.pageData))
				.then(res => {
					this.loading = false
					this.tableData = res.records
					this.pageData.total = res.total
				})
				.catch(err => {
					console.error(err)
					this.loading = false
					this.tableData = []
					this.pageData = {
						current: 1,
						size: 10,
						total: 0,
					}
				})
		},
	},
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100%;
}
.base-info {
	margin-bottom: 10px;
	height: 190px;
	background-color: #fff;
	border: 1px solid #ccc;
	border-radius: 6px;
	padding-bottom: 12px;
}
.table-container {
	flex: 1;
	height: 0;
	background-color: #fff;
	border: 1px solid #ccc;
	border-radius: 6px;
	display: flex;
	flex-direction: column;

	::v-deep .gc-table {
		padding: 0 20px 10px 20px;
	}
}
</style>
