<template>
	<GcElDialog :show="isShow" title="开票信息" width="500px" :showFooter="false" @close="isShow = false">
		<GcGroupDetail :data="userInfo"></GcGroupDetail>
	</GcElDialog>
</template>

<script>
import { getfilterName } from '@/utils'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		invoiceDetail: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		userInfo() {
			// 居民
			const list = [
				{
					key: '开票类型',
					value: '--',
					field: 'invoiceType',
				},
				{
					key: '用户名称',
					value: '--',
					field: 'userName',
				},
				{
					key: '纳税人识别号',
					value: '--',
					field: 'taxpayerIdentity',
				},
				{
					key: '开户银行',
					value: '--',
					field: 'openBank',
				},
				{
					key: '银行账号',
					value: '--',
					field: 'bankAccount',
				},
			]
			const extractedData = Object.assign({}, ...Object.values(this.invoiceDetail))
			list.forEach(item => {
				if (item.field === 'invoiceType') {
					item.value = this.$store.getters.dataList.invoiceType
						? getfilterName(
								this.$store.getters.dataList.invoiceType,
								extractedData[item.field],
								'sortValue',
								'sortName',
						  )
						: '--'
				} else {
					item.value = extractedData[item.field]
				}
			})
			return {
				list,
			}
		},
	},
}
</script>
