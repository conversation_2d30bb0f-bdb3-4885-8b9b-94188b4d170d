<template>
	<GcElDialog
		:show="isShow"
		title="编辑水表"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
	</GcElDialog>
</template>

<script>
import { apiGetMeterType } from '@/api/meterManage.api.js'
import { modifyMeterInfo2, modifyMeterInfo3 } from '@/api/waterMeter.api'
import { getFormItems } from './formItem.js'
import { ruleRequired, ruleMaxLength } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		detailData: {
			type: Object,
			default: () => ({}),
		},
		permissionCode: {
			type: String,
			default: 'cpm_meter_modify-meter2',
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		newDetailData() {
			return Object.assign({}, ...Object.values(this.detailData))
		},
		formItems() {
			return getFormItems(this)
		},
	},
	watch: {
		isShow: {
			handler(val) {
				if (val) {
					this._apiGetMeterType()
				}
			},
		},
		formItems: {
			handler(arr) {
				arr.forEach(item => {
					this.formData[item.prop] = this.newDetailData[item.prop]
				})
			},
			deep: true,
		},
	},
	data() {
		return {
			formData: {
				meterNo: '',
				meterWarehouseCode: '',
				antiTheftCode: '',
				baseMeterNo: '',
				meterTypeId: '',
				manufacturerName: '',
				meterModel: '',
				caliber: '',
				ranges: '',
				useYears: '',
				tableWellLocation: '',
				installPosition: '',
			},
			formAttrs: {
				rules: {
					meterNo: [ruleRequired('必填'), ruleMaxLength(32)],
					meterWarehouseCode: [ruleMaxLength(32)],
					antiTheftCode: [ruleMaxLength(32)],
					baseMeterNo: [ruleMaxLength(32)],
					tableWellLocation: [ruleMaxLength(64)],
					meterTypeId: [ruleRequired('必填')],
					meterModel: [ruleMaxLength(30)],
					caliber: [ruleMaxLength(16)],
					ranges: [ruleRequired('必填')],
				},
			},
			meterTypeOptions: [],
		}
	},
	methods: {
		async _apiGetMeterType() {
			const res = await apiGetMeterType({
				tenantId: this.$store.getters.userInfo.tenantId,
			})

			this.meterTypeOptions = res.map(item => {
				return {
					value: item.meterTypeId,
					label: item.meterTypeName,
					...item,
				}
			})
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			let formObj = trimParams(removeNullParams(this.formData))
			const { archivesId, meterId } = this.newDetailData
			Object.assign(formObj, {
				meterId,
			})
			const apiMethods = {
				'cpm_meter_modify-meter2': modifyMeterInfo2,
				'cpm_meter_modify-meter3': modifyMeterInfo3,
			}
			await apiMethods[this.permissionCode]({
				meterVO: formObj,
				archivesId,
			})
			this.$message.success('修改水表成功')
			this.handleClose()
			this.$emit('refresh')
		},
	},
}
</script>
