<template>
	<div class="page-layout">
		<div class="page-left">
			<el-button v-has="'cpm_planUsage_import'" type="primary" @click="handleImport">
				导入新一年计划用水
			</el-button>
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>

			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:deal="{ row }">
					<!-- isRecovery: 0未追缴 1已追缴-->
					<el-button
						v-has="'cpm_planUsage_recovery'"
						v-if="row.overPlanAmt && row.overPlanAmt !== '0' && row.isRecovery === 0"
						type="text"
						size="medium"
						@click="handleRecovery(row)"
					>
						追缴水量
					</el-button>
					<span v-else>--</span>
				</template>
			</GcTable>
		</div>

		<!-- 导入弹窗 -->
		<ImportDialog ref="importDialogRef" :show.sync="showImport" @success="getList(1)" />

		<!-- 追缴水量弹窗 -->
		<RecoveryWaterDialog ref="recoveryDialogRef" :show.sync="showRecovery" @success="getList(1)" />
	</div>
</template>

<script>
import { getColumn } from './tableColumn'
import RecoveryWaterDialog from './components/RecoveryWaterDialog.vue'
import { queryPlanUsagePage } from '@/api/specialWater.api'
import ImportDialog from './components/ImportDialog.vue'

export default {
	name: 'PlanWaterManage',
	components: { RecoveryWaterDialog, ImportDialog },
	data() {
		return {
			// 左侧查询
			formData: {
				orgCode: '',
				planYear: this.dayjs().format('YYYY'),
				enterpriseNum: '',
				// payStatus: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						col: 24,
						clearable: false,
						placeholder: '请选择营业分公司',
					},
				},
				{
					type: 'el-date-picker',
					label: '年份',
					prop: 'planYear',
					attrs: {
						col: 24,
						type: 'year',
						clearable: false,
						valueFormat: 'yyyy',
					},
				},
				{
					type: 'el-input',
					label: '企业编号',
					prop: 'enterpriseNum',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入企业编号',
					},
				},
				// {
				// 	type: 'el-select',
				// 	label: '缴费状态',
				// 	prop: 'payStatus',
				// 	options:
				// 		this.$store.getters?.dataList?.payRecordStatus?.map(item => {
				// 			return {
				// 				label: item.sortName,
				// 				value: item.sortValue,
				// 			}
				// 		}) || [],
				// 	attrs: {
				// 		col: 24,
				// 		placeholder: '请选择缴费状态',
				// 	},
				// },
			],
			formAttrs: {
				rules: {
					orgCode: {
						required: true,
						message: '请选择营业分公司',
						trigger: 'change',
					},
				},
			},
			// 右侧列表
			loading: false,
			columns: getColumn(this),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 导入弹窗
			showImport: false,
			// 追缴水量弹窗
			showRecovery: false,
		}
	},
	computed: {
		orgOptions() {
			return this.formItems[0].options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.$nextTick(() => {
						this.handleSearch()
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetFormSmooth()
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			this.selectedData = []
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await queryPlanUsagePage({
					size,
					current,
					...this.formData,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 追缴水量
		handleRecovery(data) {
			this.showRecovery = true
			this.$nextTick(() => {
				this.$refs.recoveryDialogRef.setFormData(data)
			})
		},

		// 导入
		handleImport() {
			this.showImport = true
		},
	},
}
</script>

<style lang="scss" scoped></style>
