<template>
	<GcElDialog :show="isShow" title="新增用户列表" width="1200px" :showFooter="false" @close="handleClose">
		<div class="container-search">
			<GcFormSimple v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" @click="getList">
						<i class="iconfontCis icon-small-search"></i>
						筛选
					</el-button>
					<el-button @click="handleFilterReset">
						<i class="iconfontCis icon-reset"></i>
						重置
					</el-button>
				</el-form-item>
			</GcFormSimple>
		</div>
		<div class="container-table">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
			/>
		</div>
	</GcElDialog>
</template>
<script>
import { queryUnionCard } from '@/api/costManage.api'
import { getAlleyMap } from '@/api/meterReading.api.js'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		rowData: {
			type: Object,
			default: () => ({}),
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		const orgCodeList = this.$store.getters.orgList
		return {
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'orgCode',
					name: '营业分公司',
					tooltip: true,
					render: (h, row) => {
						const org = orgCodeList.find(item => item.value === row.orgCode)
						return h('span', {}, org ? org.label : row.orgCode)
					},
				},
				{
					key: 'areaName',
					name: '坊别',
					tooltip: true,
				},
				{
					key: 'cardNo',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'cardAddr',
					name: '地址',
					tooltip: true,
				},
				{
					key: 'cardName',
					name: '用户名称',
					tooltip: true,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			formData: {
				orgCode: '',
				archivesIdentity: '',
				alleyId: '',
				userName: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: orgCodeList,
					events: {
						change: () => {
							this._getAlleyMap()
						},
					},
				},
				{
					type: 'el-select',
					label: '坊别',
					prop: 'alleyId',
					loading: false,
					options: [],
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
					},
				},
				{
					type: 'el-input',
					label: '用户名称',
					prop: 'userName',
					attrs: {
						clearable: true,
					},
				},
			],
			formAttrs: {
				inline: true,
			},
		}
	},
	watch: {
		isShow(val) {
			if (val) {
				try {
					if (val) {
						this.getList()
					}
				} catch (error) {
					console.log(error)
				}
			}
		},
	},
	methods: {
		filterFormData(data) {
			const filteredData = {}
			for (const key in data) {
				if (data[key]) {
					filteredData[key] = data[key]
				}
			}
			return filteredData
		},
		// 获取坊别
		_getAlleyMap() {
			const alleySelect = this.formItems.find(item => item.prop === 'alleyId')
			alleySelect.loading = true
			alleySelect.options = []
			this.formData.alleyId = ''
			getAlleyMap({
				orgCode: this.formData.orgCode,
			})
				.then(res => {
					const arr = res.map(item => {
						return {
							value: item.id,
							label: item.alleyName,
							...item,
						}
					})
					alleySelect.options = arr
				})
				.finally(() => {
					alleySelect.loading = false
				})
		},
		async getList() {
			this.loading = true
			try {
				const params = this.filterFormData({
					batchId: this.rowData.batchId,
					...this.formData,
					current: this.pageData.current,
					size: this.pageData.size,
				})
				const data = await queryUnionCard(params).catch(e => {
					this.tableData = []
					this.pageData = {
						current: 1,
						size: 10,
						total: 0,
					}
					this.$message.error(e.message || '新增用户查询失败！')
				})
				if (data) {
					this.pageData.total = data.total
					this.tableData = data.records
				}
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleClose() {
			this.isShow = false
			this.tableData = []
		},
		handleFilterReset() {
			this.formData = {
				orgCode: '',
				archivesIdentity: '',
				alleyId: '',
				userName: '',
			}
			this.getList()
		},
	},
}
</script>
<style lang="scss" scoped>
.distributionMode {
	margin-bottom: 10px;
}
.container-table {
	height: 500px;
}
.container-search {
	&::v-deep {
		.el-form-item__content {
			width: 150px;
			display: flex;
		}
		.el-form-item__label {
			padding: 0 15px 0 0;
		}
		.el-form-item {
			display: inline-flex;
			margin-bottom: 15px;
		}
	}
}
</style>
