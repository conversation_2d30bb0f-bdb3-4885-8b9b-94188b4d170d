<!-- 注意事项，具有嵌套关系的slot请注意，如果已经把外面的大块取代，则里面的插槽不可再单独使用 -->
<gc-detail-card
    :detail-card-info="detailCardInfo" 卡片的信息 （必传-Object）
    数据格式：
    detailCardInfo: {
        bgUrl: "", //背景图url
        signUrl: require("@/assets/images/pic/meter.png"), //header中的标志的url
        cardName: "档案信息", //卡片名
    },
    :header-num="headerNum" 卡片头部字段（必传-Object）
    数据格式：
    headerNum: {
        key: "档案编号",
        value: "1234567890",
      },
    数据格式：
    headerNum: {
        key: "档案编号",
        value: "1234567890",
    },
    :display-list="displayList"  内容区要显示的字段 （非必传，默认为[]）-如果有涉及到按钮的显示的，可采用slot自行定义该块，类名保持一致即可
    数据格式：
    displayList: [
        {
          key: "档案标识",
          value: "DB1375",
        },
    ],
    :special-flag="specialFlag"  卡片内的特殊样式 （非必传-Boolean，默认为false）-主要见表具详情
    :special-display-list="specialDisplayList"  卡片内特殊样式内的内容展示 （非必传，默认为[]）
    数据格式：
    specialDisplayList: [
        {
          key: "档案编号",
          value: "JK1234567890",
        },
        {
          key: "用户名",
          value: "纪云禾",
        },
        {
          key: "余额",
          value: "200.00",
          company: "元",
        },
      ],
>
  <!-- slot name="tag" 小标识 背景色，字体颜色，i(小圆点)的颜色自行设置-->
  <template #tag>
    <span class="tag"><i></i>开阀</span>
  </template>

  <!-- slot name="header-button" 标题区的操作按钮 按钮背景色自行设置-->
  <template #header-button>
    <el-button type="primary" size="small">查看更多</el-button>
  </template>

  <!-- slot name="card-content" 内容区 -->
  <template #card-content>
    <div class="card-content">
      <div class="content-item">
        <p class="field">档案标识1</p>
        <p class="value">DB1375</p>
      </div>
      <div class="content-item">
        <p class="field">档案标识1</p>
        <p class="value">DB1375</p>
      </div>
    </div>
  </template>

  <!-- slot name="special-bg" 内容区内特殊样式区 -->
  <template #special-bg>
    <div class="special-bg">
      <div class="small-card-header">
        <div class="label">
          <span class="identification"></span>
          <span>所属档案</span>
        </div>
        <div class="status">
          <span>已建档</span>
          <span class="next">&gt;</span>
        </div>
      </div>
      <div class="small-card-content">
        <div class="content-item">
          <p class="field">档案标识1</p>
          <p class="value">DB1375</p>
        </div>
        <div class="content-item">
          <p class="field">余额</p>
          <p class="value"><span class="money">200.00</span>元</p>
        </div>
      </div>
    </div>
  </template>
  
  <!-- slot name="archives-status" 档案状态，仅替换特殊样式区一小块，其他传值，和整体替换二选一使用 -->
  <template #archives-status>
    <div class="status">
      <span>已建档</span>
      <span class="next">&gt;</span>
    </div>
  </template>

  <!--slot name="card-footer" 底部按钮区，样式自行设置 -->
  <template #card-footer>
    <div class="card-footer">
      <span>修改</span>
      <span>停用</span>
      <span>销档</span>
    </div>
  </template>
</gc-detail-card>
