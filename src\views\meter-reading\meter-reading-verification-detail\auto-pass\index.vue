<template>
	<div class="wrapper">
		<div class="container-search">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<template v-slot:rate>
					<div class="custom-box">
						&nbsp;
						<span>大水量区间</span>
						&nbsp;
						<el-form-item prop="useAmountLower" style="margin-bottom: 0">
							<el-input-number
								v-model="formData.useAmountLower"
								:controls="false"
								:step="1"
								step-strictly
								style="width: 100px"
							/>
						</el-form-item>
						<span>至</span>
						&nbsp;
						<el-form-item prop="useAmountUpper" style="margin-bottom: 0">
							<el-input-number
								v-model="formData.useAmountUpper"
								:controls="false"
								:step="1"
								step-strictly
								style="width: 100px"
							/>
						</el-form-item>
					</div>
				</template>
				<el-form-item>
					<el-button type="primary" @click="handleSearch">筛选</el-button>
					<el-button @click="handleReset">重置</el-button>
				</el-form-item>
			</GcFormSimple>
			<div class="btn-group">
				<!-- TODO -->
				<el-button v-has="'plan-collection_report_reviewList_export_excel2'" disabled type="primary">
					导出审核结果
				</el-button>
				<el-button
					v-has="'plan-collection_meterReadingReview_batchReviewPass1'"
					type="primary"
					:disabled="selectedData.length === 0"
					@click="handlePass(true, selectedDataRecordIds)"
				>
					批量复核通过
				</el-button>
				<el-button
					v-has="'plan-collection_meterReadingReview_batchReviewReject1'"
					type="primary"
					:disabled="selectedData.length === 0"
					@click="handleReject(true, selectedDataRecordIds)"
				>
					批量复核驳回
				</el-button>
			</div>
		</div>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				needType="selection"
				@selectChange="handleSelectChange"
				@current-page-change="handlePageChange"
			>
				<!-- 抄表经纬度 -->
				<template v-slot:latitudeLongitude="{ row }">
					<span>
						{{ row.longitude && row.latitude ? `${row.longitude},${row.latitude}` : '--' }}
					</span>
				</template>
				<!-- 本次指针 -->
				<template v-slot:curMeterReading="{ row, $index }">
					<span v-show="!row.isEditing">
						{{ !judgeBlank(row.curMeterReading) ? row.curMeterReading : '--' }}
					</span>
					<el-form
						v-show="row.isEditing"
						:ref="`curMeterReadingFormRef${$index}`"
						class="table-form"
						:model="tableData[$index]"
					>
						<el-form-item
							prop="curMeterReading"
							:rules="{
								validator: validFn('curMeterReading', $index),
								trigger: 'blur',
							}"
						>
							<el-input-number
								v-model="tableData[$index].curMeterReading"
								class="input-number"
								placeholder="请输入本次指针"
								:min="0"
								:max="999999999"
								step-strictly
								:controls="false"
								@change="handleCalcUseAmt($index)"
							/>
						</el-form-item>
					</el-form>
				</template>
				<!-- 本次水量 -->
				<template v-slot:useAmount="{ row, $index }">
					<span v-show="!row.isEditing">{{ !judgeBlank(row.useAmount) ? row.useAmount : '--' }}</span>
					<el-form
						v-show="row.isEditing"
						:ref="`useAmountFormRef${$index}`"
						class="table-form"
						:model="tableData[$index]"
					>
						<el-form-item
							prop="useAmount"
							:rules="{
								validator: validFn('useAmount', $index),
								trigger: 'blur',
							}"
						>
							<el-input-number
								v-model="tableData[$index].useAmount"
								class="input-number"
								placeholder="请输入本次水量"
								:min="-999999999"
								:max="999999999"
								step-strictly
								:controls="false"
							/>
						</el-form-item>
					</el-form>
				</template>
				<!-- 抄表情况 -->
				<template v-slot:checkStatusDesc="{ row, $index }">
					<span v-show="!row.isEditing">{{ row.checkStatusDesc || '--' }}</span>
					<el-form
						v-if="row.isEditing"
						:ref="`checkStatusFormRef${$index}`"
						class="table-form"
						:model="tableData[$index]"
					>
						<el-form-item
							prop="checkStatus"
							:rules="[
								{
									required: true,
									message: '请选择抄表情况',
									trigger: 'change',
								},
								{ validator: checkStatusValidtor($index), trigger: 'change' },
							]"
						>
							<el-select
								v-model="tableData[$index].checkStatus"
								clearable
								filterable
								placeholder="请选择抄表情况"
							>
								<el-option
									v-for="item in checkStatusList"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
					</el-form>
				</template>
				<template v-slot:imageUrl="{ row }">
					<UploadImgSimple v-model="row.imageUrl" />
				</template>
				<template v-slot:deal="{ row, $index }">
					<div v-show="!row.isEditing">
						<el-button
							v-has="'plan-collection_meterReadingReview_reviewPass'"
							type="text"
							size="medium"
							@click="handlePass(false, [row.meterReadingRecordId])"
						>
							复核通过
						</el-button>
						<el-button
							v-has="'plan-collection_meterReadingReview_reviewReject'"
							type="text"
							size="medium"
							@click="handleReject(false, [row.meterReadingRecordId])"
						>
							驳回
						</el-button>
						<el-button
							v-has="'plan-collection_meterReadingTask_updateMeterReadingRecord2'"
							type="text"
							size="medium"
							@click="handleAdjust(row, $index)"
						>
							修改
						</el-button>
					</div>
					<div v-has="'plan-collection_meterReadingTask_updateMeterReadingRecord2'" v-show="row.isEditing">
						<el-button type="text" size="medium" @click="handleAdjustSave(row, $index)">保存</el-button>
						<el-button type="text" size="medium" @click="handleAdjustCancel($index)">取消</el-button>
					</div>
				</template>
			</GcTable>
		</div>
	</div>
</template>

<script>
import UploadImgSimple from '@/components/UploadImgSimple'
import { getColumn } from './tableColumn.js'
import { isBlank } from '@/utils/validate.js'
import { checkStatusOptions, specialCheckStatus, CHECK_STATUS_CALC_METHODS } from '@/consts/optionList.js'
import {
	getReviewDetailList2,
	reviewPass,
	reviewPass1,
	reviewReject,
	reviewReject1,
	updateMeterReadingRecord2,
} from '@/api/meterReading.api.js'

export default {
	name: '',
	components: { UploadImgSimple },
	props: {
		type: Number,
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
				useAmountLower: undefined,
				useAmountUpper: undefined,
			},
			formItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
				{
					type: 'slot',
					prop: 'rate',
					slotName: 'rate',
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '80px',
			},
			loading: false,
			columns: getColumn(this),
			tableData: [
				// {
				//   isEditing: false,
				//   archivesNo: "测试数据",
				//   curMeterReading: undefined,
				//   useAmount: 123,
				//   checkStatus: 13,
				// },
				// {
				//   isEditing: false,
				//   archivesNo: "1",
				//   curMeterReading: undefined,
				//   useAmount: undefined,
				//   checkStatus: 1,
				// },
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			// 抄表情况下拉选择
			checkStatusList: checkStatusOptions,
			// 列表选中数据
			selectedData: [],

			// 编辑的当前行数据（用于取消时恢复）
			currentEditData: {},
		}
	},
	computed: {
		selectedDataRecordIds() {
			return this.selectedData.map(item => item.meterReadingRecordId)
		},
	},
	created() {
		this.getList()
	},
	methods: {
		handleSelectChange(data) {
			this.selectedData = data
		},
		handleSearch() {
			const { useAmountLower, useAmountUpper } = this.formData
			if (!isBlank(useAmountLower) && isBlank(useAmountUpper)) {
				return this.$message.error('请输入水量截至值')
			}
			if (isBlank(useAmountLower) && !isBlank(useAmountUpper)) {
				return this.$message.error('请输入水量起始值')
			}
			if (!isBlank(useAmountLower) && !isBlank(useAmountUpper) && useAmountUpper < useAmountLower) {
				return this.$message.error('水量截至值必须大于水量起始值')
			}
			this.getList(1)
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.formData.useAmountLower = undefined
			this.formData.useAmountUpper = undefined
			this.getList(1)
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		async getList(curPage) {
			this.loading = true

			this.selectedData = []
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { meterReadingTaskId, date } = this.$route.query
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getReviewDetailList2({
					type: this.type,
					current,
					size,
					meterReadingTaskId,
					date,
					...this.formData,
				})
				this.tableData = records.map(item => {
					return {
						...item,
						curMeterReading: !isBlank(item.curMeterReading) ? item.curMeterReading : undefined,
						useAmount: !isBlank(item.useAmount) ? item.useAmount : undefined,
					}
				})
				this.pageData.total = total
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 复核通过
		handlePass(isBatch = false, meterReadingRecordId) {
			this.$confirm('确定复核通过吗?').then(async () => {
				const { meterReadingTaskId, taskYear } = this.$route.query
				if (isBatch) {
					await reviewPass1({
						meterReadingTaskId,
						taskYear,
						meterReadingRecordId,
					})
				} else {
					await reviewPass({
						meterReadingTaskId,
						taskYear,
						meterReadingRecordId,
					})
				}
				this.$message.success('复核通过成功')
				this.getList(1)
			})
		},
		// 复核驳回
		handleReject(isBatch = false, meterReadingRecordId) {
			this.$confirm('确定要驳回吗?').then(async () => {
				const { taskYear } = this.$route.query
				if (isBatch) {
					await reviewReject1({
						taskYear,
						meterReadingRecordId,
					})
				} else {
					await reviewReject({
						taskYear,
						meterReadingRecordId,
					})
				}
				this.$message.success('驳回成功')
				this.getList(1)
			})
		},
		// 修改
		handleAdjust(row, index) {
			this.currentEditData = row
			this.$set(this.tableData[index], 'isEditing', true)
		},
		// 本次指针、本次水量验证函数
		validFn(key, index) {
			return (rule, value, callback) => {
				if (!specialCheckStatus.includes(this.tableData[index].checkStatus) && isBlank(value)) {
					callback(new Error(`请输入本次${key === 'curMeterReading' ? '指针' : '水量'}`))
				} else {
					callback()
				}
			}
		},
		// 抄表情况验证函数
		checkStatusValidtor(index) {
			return async (rule, value, callback) => {
				if (value && specialCheckStatus.includes(value)) {
					this.$refs[`curMeterReadingFormRef${index}`].clearValidate()
					this.$refs[`useAmountFormRef${index}`].clearValidate()
					callback()
				} else {
					const promise1 = new Promise(resolve => {
						this.$refs[`curMeterReadingFormRef${index}`].validate(valid => {
							resolve(valid)
						})
					})
					const promise2 = new Promise(resolve => {
						this.$refs[`useAmountFormRef${index}`].validate(valid => {
							resolve(valid)
						})
					})
					const valids = await Promise.all([promise1, promise2])
					if (!valids.includes(false)) {
						callback()
					}
				}
			}
		},
		// 修改保存
		async handleAdjustSave(row, index) {
			const form1Promise = new Promise(resolve => {
				this.$refs[`curMeterReadingFormRef${index}`].validate(valid => {
					resolve(valid)
				})
			})
			const form2Promise = new Promise(resolve => {
				this.$refs[`useAmountFormRef${index}`].validate(valid => {
					resolve(valid)
				})
			})
			const form3Promise = new Promise(resolve => {
				this.$refs[`checkStatusFormRef${index}`].validate(valid => {
					resolve(valid)
				})
			})

			const valids = await Promise.all([form1Promise, form2Promise, form3Promise])
			if (!valids.includes(false)) {
				const {
					meterReadingRecordId,
					checkStatus,
					curMeterReading,
					useAmount,
					meterReadingStaffId,
					imageUrl,
					thisRecordDate,
					lastMeterReading,
					lastRecordDate,
				} = row
				await updateMeterReadingRecord2({
					meterReadingRecordId,
					checkStatus,
					curMeterReading,
					useAmount,
					meterReadingStaffId,
					imageUrl,
					thisRecordDate,
					taskYear: this.$route.query.taskYear,
					lastMeterReading,
					lastRecordDate,
					addModifyLog: true,
				})
				this.$message.success('修改成功')
				this.getList(1)
			}
		},
		handleAdjustCancel(index) {
			this.tableData.splice(index, 1, {
				...this.currentEditData,
				isEditing: false,
			})
			this.currentEditData = {}
		},
		judgeBlank(val) {
			return isBlank(val)
		},
		// 计算用水量
		// 抄表复核页面在调整下
		// 1) 本次指针输入后， 用量 自动计算 ，用量统一=本次指针 - 上次
		// 2）用量输入框： 允许调整，调整后，不用再次计算
		handleCalcUseAmt(index) {
			const data = this.tableData[index]
			if (data) {
				const { curMeterReading, lastMeterReading } = data
				const methods = CHECK_STATUS_CALC_METHODS.default
				const { useAmount } = methods({
					curMeterReading,
					lastMeterReading,
				})
				data.useAmount = useAmount
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	flex: 1;
	height: 0;
	display: flex;
	flex-direction: column;
	padding: 20px;
}
.container-search {
	display: flex;
	justify-content: space-between;
}
.table-container {
	flex: 1;
	height: 0;
}

.input-number {
	width: 100%;
	::v-deep {
		.el-input__inner {
			text-align: left;
		}
	}
}
.table-form {
	height: 100%;
	::v-deep {
		.el-form-item {
			margin: 12px 0;
		}
		.el-form-item__error {
			padding-top: 0;
		}
	}
}
.custom-box {
	span {
		color: #4e4e4e;
	}
}
</style>
