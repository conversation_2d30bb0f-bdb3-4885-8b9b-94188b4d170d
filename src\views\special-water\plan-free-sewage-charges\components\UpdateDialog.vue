<template>
	<gc-el-dialog
		:show="isShow"
		:title="`${typeText}免污水费表卡`"
		custom-top="120px"
		width="600px"
		@close="handleClose"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { addPlanWaste, getInfoByArchivesNo } from '@/api/specialWater.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		editType: {
			type: String,
			default: 'add',
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},

		typeText() {
			return this.editType === 'add' ? '新增' : '编辑'
		},
	},
	created() {},
	data() {
		return {
			formData: {
				orgCode: '',
				planYear: '',
				archivesNo: '',
				userName: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						col: 24,
						placeholder: '请选择营业分公司',
					},
					events: {
						change: value => {
							if (value) {
								this.formData.archivesNo = ''
								this.formData.userName = ''
								this.formItems[2].options = []
							}
						},
					},
				},
				{
					type: 'el-date-picker',
					label: '计划年份',
					prop: 'planYear',
					attrs: {
						col: 24,
						type: 'year',
						valueFormat: 'yyyy',
						placeholder: '请选择计划年份',
						pickerOptions: {
							disabledDate: time => {
								const timestamp = time.getTime()
								const currentYear = this.dayjs().startOf('year').valueOf()
								return timestamp < currentYear
							},
						},
					},
				},
				{
					type: 'el-select',
					label: '表卡编号',
					prop: 'archivesNo',
					attrs: {
						col: 24,
						filterable: true,
						remote: true,
						loading: false,
						clearable: true,
						placeholder: '请输入表卡编号进行查询',
						remoteMethod: this.getArchivesNoByInput,
					},
					options: [],
					events: {
						change: value => {
							if (value) {
								const target = this.formItems[2].options.find(item => item.value === value)
								if (target) {
									this.setFormData(target)
								}
							} else {
								this.formItems[2].options = []
							}
						},
					},
				},
				{
					type: 'el-input',
					label: '用户名称',
					prop: 'userName',
					attrs: {
						col: 24,
						disabled: true,
						placeholder: '',
					},
				},
			],
			formAttrs: {
				labelPosition: 'right',
				labelWidth: '110px',
				rules: {
					orgCode: [{ required: true, message: '请选择营业分公司', trigger: 'change' }],
					planYear: [{ required: true, message: '请选择计划年份', trigger: 'change' }],
					archivesNo: [
						{
							required: true,
							message: '请选择表卡编号',
							trigger: 'change',
						},
					],
				},
			},
		}
	},
	methods: {
		// 根据输入点表卡编号获取数据
		async getArchivesNoByInput(queryString) {
			if (queryString !== '') {
				this.formItems[2].attrs.loading = true
				try {
					const res = await getInfoByArchivesNo({
						orgCode: this.formData.orgCode,
						archivesNo: queryString,
					})
					const data = res || []
					this.formItems[2].options = data.map(item => {
						const { archivesNo, ...rest } = item
						return {
							label: archivesNo,
							value: archivesNo,
							...rest,
						}
					})
				} catch (error) {
					console.error(error)
					this.formItems[2].options = []
				} finally {
					this.formItems[2].attrs.loading = false
				}
			}
		},

		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				if (this.editType === 'add') {
					await addPlanWaste(this.formData)
				}
				this.$message.success(`${this.typeText}成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.formItems[2].options = []
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
</style>
