<template>
	<el-breadcrumb class="gc-breadcrumb" separator="/">
		<el-breadcrumb-item v-for="(item, index) in levelList" :key="index">
			<span>
				{{
					realm !== 'water'
						? item.meta.title
						: item.meta.title === '用气类报表'
						? fieldName.reportName
						: item.meta.title
				}}
			</span>
		</el-breadcrumb-item>
	</el-breadcrumb>
</template>

<script>
import getFieldName from '@/mixin/getFieldName.js'
export default {
	name: 'GcBreadcrumb',
	mixins: [getFieldName],
	data() {
		return {
			levelList: [],
		}
	},
	watch: {
		$route: {
			handler() {
				this.levelList = this.getBreadcrumb()
			},
			immediate: true,
		},
	},
	methods: {
		getBreadcrumb() {
			return this.$route.matched.filter(item => item.meta && item.meta.title && !item.meta.hiddenCrumb)
		},
	},
}
</script>

<style lang="scss" scoped>
::v-deep {
	.el-breadcrumb__separator {
		margin: 0 4px;
	}
}
.gc-breadcrumb {
	height: $base-nav-height;
	line-height: $base-nav-height;

	::v-deep {
		.el-breadcrumb__item {
			.el-breadcrumb__inner {
				span {
					font-weight: normal;
					font-size: $base-font-size-small;
					color: $base-color-9;
				}
			}

			&:last-child {
				.el-breadcrumb__inner {
					span {
						color: #595959;
					}
				}
			}
		}
	}
}
</style>
