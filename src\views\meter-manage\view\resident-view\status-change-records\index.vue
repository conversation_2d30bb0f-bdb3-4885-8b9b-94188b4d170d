<template>
	<div class="container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch">
					<i class="iconfontCis icon-small-search"></i>
					筛选
				</el-button>
				<el-button @click="handleReset">
					<i class="iconfontCis icon-reset"></i>
					重置
				</el-button>
			</el-form-item>
		</GcFormSimple>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handleChangePage"
			/>
		</div>
	</div>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getfilterName } from '@/utils'
import { apiGetMobileModifyRecords } from '@/api/meterManage.api.js'

export default {
	props: {
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			formData: {
				modifyTime: [
					this.dayjs().startOf('year').format('YYYY-MM-DD'),
					this.dayjs().endOf('year').format('YYYY-MM-DD'),
				],
				operatorType: '',
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '变更日期',
					prop: 'modifyTime',
					attrs: {
						type: 'daterange',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						style: {
							width: '250px',
						},
					},
				},
				{
					type: 'el-select',
					label: '操作类型',
					prop: 'operatorType',
					options: this.$store.getters.dataList.archivesOperatorType
						? this.$store.getters.dataList.archivesOperatorType.map(item => {
								return {
									label: item.sortName,
									value: Number(item.sortValue),
								}
						  })
						: [],
					attrs: {
						style: {
							width: '150px',
						},
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '80px',
			},
			columns: [
				{
					key: 'createTime',
					name: '变更日期',
					tooltip: true,
				},
				{
					key: 'createStaffName',
					name: '操作人员',
					tooltip: true,
				},
				{
					key: 'operatorType',
					name: '操作类型',
					tooltip: true,
					render: (h, row) => {
						const valueStr = this.$store.getters.dataList.archivesOperatorType
							? getfilterName(
									this.$store.getters.dataList.archivesOperatorType,
									row.operatorType,
									'sortValue',
									'sortName',
							  )
							: ''
						return h('span', {}, valueStr)
					},
				},
				{
					key: 'archivesStatus',
					name: '操作后表卡状态',
					tooltip: true,
					render: (h, row) => {
						const valueStr = this.$store.getters.dataList.archiveState
							? getfilterName(
									this.$store.getters.dataList.archiveState,
									row.archivesStatus,
									'sortValue',
									'sortName',
							  )
							: ''
						return h('span', {}, valueStr)
					},
				},
				{
					key: 'meterNo',
					name: '水表编号',
					tooltip: true,
				},
				{
					key: 'manufacturerName',
					name: '水表厂商',
					tooltip: true,
				},
				{
					key: 'meterTypeName',
					name: '水表型号',
					tooltip: true,
				},
				{
					key: 'meterReading',
					name: '指针数',
					tooltip: true,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
		}
	},
	methods: {
		async getList() {
			this.loading = true
			try {
				const extractedData = Object.assign({}, ...Object.values(this.tabData))
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
					archivesId: extractedData?.archivesId,
				})
				if (formParams.modifyTime && formParams.modifyTime.length > 1) {
					const modifyTimeStart = this.dayjs(formParams.modifyTime[0]).format('YYYY-MM-DD')
					const modifyTimeEnd = this.dayjs(formParams.modifyTime[1]).format('YYYY-MM-DD')
					Object.assign(formParams, {
						modifyTimeStart,
						modifyTimeEnd,
					})
					delete formParams.modifyTime
				}
				const { records, total } = await apiGetMobileModifyRecords(formParams)
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleSearch() {
			this.handleChangePage({ page: 1 })
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.handleChangePage({ page: 1 })
		},
	},
}
</script>
