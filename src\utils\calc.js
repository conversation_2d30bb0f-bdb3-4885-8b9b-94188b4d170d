/* eslint-disable no-empty */
/**
 ** 加法函数，用来得到精确的加法结果
 ** 说明：javascript的加法结果会有误差，在两个浮点数相加的时候会比较明显。这个函数返回较为精确的加法结果。
 ** 调用：accAdd(arg1, arg2)
 ** 返回值：arg1加上arg2的精确结果
 **/
export const accAdd = (arg1, arg2) => {
	let r1, r2, m, c
	try {
		r1 = arg1.toString().split('.')[1].length
	} catch (e) {
		r1 = 0
	}
	try {
		r2 = arg2.toString().split('.')[1].length
	} catch (e) {
		r2 = 0
	}
	c = Math.abs(r1 - r2)
	m = Math.pow(10, Math.max(r1, r2))
	if (c > 0) {
		const cm = Math.pow(10, c)
		if (r1 > r2) {
			arg1 = Number(arg1.toString().replace('.', ''))
			arg2 = Number(arg2.toString().replace('.', '')) * cm
		} else {
			arg1 = Number(arg1.toString().replace('.', '')) * cm
			arg2 = Number(arg2.toString().replace('.', ''))
		}
	} else {
		arg1 = Number(arg1.toString().replace('.', ''))
		arg2 = Number(arg2.toString().replace('.', ''))
	}
	return (arg1 + arg2) / m
}

// 减法函数
export const accSub = (arg1, arg2) => {
	return accAdd(arg1, -arg2)
}

// 乘法函数
export const accMul = (arg1, arg2) => {
	let m = 0,
		s1 = arg1.toString(),
		s2 = arg2.toString()
	try {
		m += s1.split('.')[1].length
	} catch (e) {}
	try {
		m += s2.split('.')[1].length
	} catch (e) {}
	return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m)
}

// 除法函数
export const accDiv = (arg1, arg2) => {
	let t1 = 0,
		t2 = 0,
		r1,
		r2
	try {
		t1 = arg1.toString().split('.')[1].length
	} catch (e) {}
	try {
		t2 = arg2.toString().split('.')[1].length
	} catch (e) {}
	r1 = Number(arg1.toString().replace('.', ''))
	r2 = Number(arg2.toString().replace('.', ''))
	return (r1 / r2) * Math.pow(10, t2 - t1)
}
