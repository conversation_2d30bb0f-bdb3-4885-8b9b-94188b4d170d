<template>
	<div class="steps-container">
		<div class="step-item" v-for="(item, index) in stepList" :key="item.step">
			<i class="el-icon-success success-icon" v-if="item.success"></i>
			<span v-else :class="['num', item.step === active && 'active-num']">
				{{ item.step }}
			</span>
			<span :class="['text', item.step <= active ? 'active-text' : '']">{{ item.text }}</span>
			<span v-if="index + 1 !== stepList.length" :class="['line', item.step < active && 'active-line']"></span>
		</div>
	</div>
</template>
<script>
export default {
	name: 'MeterSteps',
	props: {
		list: {
			type: Array,
			default: () => [
				// { step: 1, text: '文字' }
			],
		},
		active: {
			type: Number,
			default: 1,
		},
	},
	data() {
		return {
			stepList: [],
		}
	},
	watch: {
		active: {
			handler() {
				this.stepList = this.list.map(v => ({
					...v,
					success: v.step < this.active,
				}))
			},
			immediate: true,
		},
	},
}
</script>

<style lang="scss" scoped>
.steps-container {
	width: 100%;
	margin: 0 auto;
	display: flex;
	align-items: center;
	justify-content: center;
	.step-item {
		flex: 1;
		display: inline-flex;
		align-items: center;
		flex-direction: row;
	}
	.step-item:last-of-type {
		flex-basis: auto !important;
		flex-shrink: 0;
		flex-grow: 0;
	}

	.num {
		flex-grow: 0;
		flex-shrink: 0;
		width: 32px;
		height: 32px;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-grow: 0;
		flex-shrink: 0;
		margin: 0 10px;
		font-family: Source Han Sans CN;
		font-size: 14px;
		font-weight: 400;
		color: #3f435e;
		background-color: #f4f5fb;
	}
	.active-num {
		color: #fff;
		background-color: #1d70f5;
		border: 2px solid #1d70f5;
	}

	.success-icon {
		color: #b2d2fe;
		font-size: 32px;
		margin: 0 10px;
	}
	.text {
		font-family: PingFang SC, PingFang SC;
		font-size: 14px;
		font-weight: 500;
		color: #282c42;
		word-break: keep-all;
	}

	.line {
		flex-basis: 100%;
		height: 1px;
		background-color: #d5d6e2;
		margin-left: 10px;
	}
	.active-line {
		background-color: #5075ec;
	}
}
</style>
