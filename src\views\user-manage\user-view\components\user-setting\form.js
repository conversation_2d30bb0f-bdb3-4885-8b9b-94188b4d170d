export const getFormItem = _this => {
	return [
		{
			type: 'el-input',
			label: '用户名称',
			prop: 'userName',
			attrs: {
				disabled: true,
				col: 12,
			},
		},
		{
			type: 'el-select',
			label: '用户分类',
			prop: 'userType',
			options:
				_this.$store.getters?.dataList?.userType?.map(item => {
					return {
						label: item.sortName,
						value: item.sortValue,
					}
				}) || [],
			attrs: {
				disabled: true,
				col: 12,
			},
		},
		{
			type: 'el-select',
			label: '用户类型',
			prop: 'userSubType',
			options:
				_this.$store.getters?.dataList?.resident?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '手机',
			prop: 'userMobile',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '身份证号',
			prop: 'certificateNo',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '电话',
			prop: 'contactPhone',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-select',
			label: '收费方式',
			prop: 'chargingMethod',
			options: (_this.$store.getters.dataList.chargingMethod || []).map(item => {
				return {
					label: item.sortName,
					value: item.sortValue,
				}
			}),
			attrs: {
				col: 12,
				disabled: false,
			},
		},
		{
			type: 'el-input',
			label: '曾用名',
			prop: 'nameUsedBefore',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-select',
			label: '其他证件',
			prop: 'certificateType',
			options: _this.$store.getters.dataList.certificateType
				? _this.$store.getters.dataList.certificateType.map(item => {
						return {
							label: item.sortName,
							value: item.sortValue,
						}
				  })
				: [],
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '证件号码',
			prop: 'otherCertificateNo',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '电子邮箱',
			prop: 'email',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '邮寄地址',
			prop: 'mailingAddress',
			attrs: {
				col: 12,
				type: 'textarea',
				maxlength: '64',
				showWordLimit: true,
				autosize: {
					minRows: 3,
					maxRows: 4,
				},
			},
		},

		{
			type: 'slot',
			slotName: 'otherMobile',
			attrs: {
				col: 12,
			},
		},
		{
			type: 'el-input',
			label: '邮编',
			prop: 'zipCode',
			attrs: {
				col: 12,
			},
		},
	]
}
