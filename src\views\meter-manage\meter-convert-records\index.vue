<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />

			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div class="right-top">
				<el-button
					v-has="'cpm_archives_export_change-modifyrecords-excel'"
					:disabled="!tableData.length"
					type="primary"
					@click="handleExport"
				>
					导出
				</el-button>
			</div>
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			/>
		</div>
	</div>
</template>

<script>
import { getColumn } from './tableColumn'
import { getFormItems } from './formItem.js'
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams, exportBlob } from '@/utils/index.js'
import { apiGetChangeModifyrecords, apiExportChangeModifyRecordsExcel } from '@/api/meterManage.api.js'
export default {
	name: 'MeterConvertRecords',
	data() {
		return {
			// 左侧查询
			formData: {
				orgCode: '',
				archivesIdentity: '',
				oldArchivesIdentity: '',
				modifyTime: '',
			},
			columns: getColumn(this),
			formItems: getFormItems(this),
			formAttrs: {
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
				},
			},
			// 右侧列表
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	computed: {
		orgOptions() {
			return this.formItems[0]?.options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.$nextTick(() => {
						this.handleSearch()
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleReset() {
			this.$refs.formRef.clearValidate()
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.handlePageChange({ page: 1 })
		},
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
				})
				if (formParams.modifyTime) {
					formParams.modifyTimeStart = formParams.modifyTime[0]
					formParams.modifyTimeEnd = formParams.modifyTime[1]
					delete formParams.modifyTime
				}
				const { total = 0, records = [] } = await apiGetChangeModifyrecords(formParams)

				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		async handleExport() {
			const maxLength = 300000
			const formParams = trimParams(removeNullParams(this.formData))

			const params = {
				...formParams,
				current: 1,
				size: this.pageData.total,
			}
			if (this.pageData.total > maxLength) {
				this.$message.error('导出数量不能超过30万条')
				return
			}

			await apiExportChangeModifyRecordsExcel(params).then(res => {
				exportBlob(res, '表卡转换记录列表')
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.right-top {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-bottom: 12px;
}
</style>
