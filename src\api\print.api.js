import service from './request'

//票据
export function apiBill(parameter) {
	return service({
		url: 'cpm/charge/charge-print-info',
		method: 'POST',
		data: parameter,
	})
}

//获取发票购方信息详情
export function apiInvoiceBuyerInfo(parameter) {
	return service({
		url: 'cpm/invoice-buyer/query-invoicebuyer-info',
		method: 'GET',
		params: parameter,
	})
}

//获取税率详情
export function apiTaxRateInfo(parameter) {
	return service({
		url: 'cpm/taxRate/taxRateInfo',
		method: 'GET',
		params: parameter,
	})
}

//获取费用列表
export function apiCostRecords(parameter) {
	return service({
		url: 'cpm/charge/cost-records',
		method: 'GET',
		params: parameter,
	})
}

//发票开具
export function apiOpeningInvoice(parameter) {
	return service({
		url: 'cpm/nuonuo-invoice/opening-invoice',
		method: 'POST',
		data: parameter,
	})
}

//发票红冲
export function apiRedhashInvoice(parameter) {
	return service({
		url: 'cpm/nuonuo-invoice/red-invoice',
		method: 'POST',
		data: parameter,
	})
}

// 下载PDF-发票
export function apiInvoicePDF(parameter) {
	return service({
		url: 'cpm/nuonuo-invoice/pdf',
		method: 'GET',
		params: parameter,
	})
}

// 交付发票
export function apiRePush(parameter) {
	return service({
		url: 'cpm/nuonuo-invoice/push',
		method: 'POST',
		data: parameter,
	})
}

// 同步发票
export function apiSynchronization(parameter) {
	return service({
		url: 'cpm/nuonuo-invoice/sync',
		method: 'PUT',
		data: parameter,
	})
}

// 开票重试
export function apiRetry(parameter) {
	return service({
		url: 'cpm/nuonuo-invoice/reInvoice',
		method: 'POST',
		data: parameter,
	})
}

// 开票信息查询
export function queryInvoiceInfoList(data) {
	return service({
		url: `cpm/invoice-buyer/buyerList`,
		method: 'POST',
		data,
	})
}

// 开票信息查询(居民)
export function queryInvoiceInfoList2(data) {
	return service({
		url: `cpm/invoice-buyer/buyerList2`,
		method: 'POST',
		data,
	})
}
// 开票信息查询(企业)
export function queryInvoiceInfoList3(data) {
	return service({
		url: `cpm/invoice-buyer/buyerList3`,
		method: 'POST',
		data,
	})
}

// 开票信息查询
export function setDefaultInvoiceInfo(parameter) {
	return service({
		url: `cpm/archives/defualt/invoiceBuyer`,
		method: 'GET',
		params: parameter,
	})
}

// 更新开票信息
export function apiUpdateInvoiceInfo(data) {
	return service({
		url: '/cpm/invoice-buyer',
		method: 'PUT',
		data,
	})
}
