import Vue from 'vue'

import '@/styles/index.scss'
import '@/styles/common.scss'
import '@/assets/svg-symbols/iconfont.js'
import '@/assets/iconfont-cis/iconfontCis.css'
import 'remixicon/fonts/remixicon.css'

import components from '@/components/common'
Vue.use(components)

import directives from '@/directive'
Vue.use(directives)

import filters from '@/filters'
Vue.use(filters)

import vuescroll from 'vuescroll'
import 'vuescroll/dist/vuescroll.css'
Vue.use(vuescroll)

// 引入时间控件
import dayjs from 'dayjs'
var utc = require('dayjs/plugin/utc')
dayjs.extend(utc)
Vue.prototype.dayjs = dayjs

// 引入复制插件
import VueClipboard from 'vue-clipboard2'
Vue.use(VueClipboard)

import * as option from '@/consts/options'
Vue.prototype.$option = option

// idle vue 交互空闲
import IdleVue from 'idle-vue'
const eventsHub = new Vue()
Vue.use(IdleVue, {
	eventEmitter: eventsHub,
	idleTime: 1000 * 60 * 30,
	startAtIdle: false, // isIdle初始值是否为true
})
// 引入 swipper
import VueAwesomeSwiper from 'vue-awesome-swiper'
Vue.use(VueAwesomeSwiper)

// 批量导入模块
const all = require.context('@/global', false, /\.js$/)
all.keys().map(all)
