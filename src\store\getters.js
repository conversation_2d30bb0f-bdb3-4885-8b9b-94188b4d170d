const getters = {
	token: state => state.user.token, // token
	userInfo: state => state.user.userInfo, // 用户信息
	permissions: state => state.user.permissions, // 权限集合
	routes: state => state.routes.routes, // 权限集合映射的路由表
	tags: state => state.tagsView.tags,
	fieldName: state => state.user.fieldName, // 水气差异字段名
	dataList: state => state.dataList.dataList, // 数据字典
	orgList: state => state.dataList.orgList, // 营业分公司数据
	tenant: state => state.user.tenant, // 租户相关：id-租户id，address-租户所在地址，basic-租户基本信息，business_config-租户业务配置
	billList: state => state.apiCache.billItems || [], // 当前用户对应租户的账项列表
	paramsList: state => state.archives.modifyParamsList, //修改档案
	updateList: state => state.archives.archivesUpdateList, //决定是否进行档案的更新
	cityCode: state => state.user.tenant?.address?.city || '',
	manufacturerList: state => state.sysMeterInfo.manufacturerList, //系统信息设置·表具信息维护·表具厂商地址列表
	isSwitchTenantShow: state => state.routes.isSwitchTenantShow,
	isSwitchOrgShow: state => state.routes.isSwitchOrgShow,
	targetRouterPath: state => state.routes.targetRouterPath,
	// 新增
	billCheckedList: state => state.billCache.billCheckedList, //账单选中列表
}
export default getters
