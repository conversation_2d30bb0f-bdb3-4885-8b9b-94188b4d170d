<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-10 16:12:11
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 14:07:14
-->
<template>
	<div class="right-container">
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			@dblclick="handleRowClick"
			@current-page-change="handlePageChange"
		/>
	</div>
</template>

<script>
import { getReviewList } from '@/api/meterReading.api.js'

export default {
	name: '',
	components: {},
	props: {
		params: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			loading: false,
			columns: [
				{
					key: 'bookNo',
					name: '表册编号',
					tooltip: true,
				},
				{
					key: 'taskDate',
					name: '抄表月',
					tooltip: true,
				},
				{
					key: 'meterCount',
					name: '应抄表卡数',
					tooltip: true,
				},
				{
					key: 'waterAmount',
					name: '总抄表水量',
					tooltip: true,
				},
				{
					key: 'noReadingCount',
					name: '未抄表数',
					tooltip: true,
				},
				{
					key: 'autoNormalCount',
					name: '自动审核通过数',
					tooltip: true,
				},
				{
					key: 'autoAbnormalCount',
					name: '自动审核异常数',
					tooltip: true,
				},
				{
					key: 'manualNormalCount',
					name: '人工审核通过数',
					tooltip: true,
				},
				{
					key: 'manualAbnormalCount',
					name: '人工审核异常数',
					tooltip: true,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	computed: {},
	created() {},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		async getList(curPage) {
			this.loading = true

			if (curPage) {
				this.pageData.current = curPage
			}

			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getReviewList({
					current,
					size,
					...this.params,
				})
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 双击跳转复核详情页面
		handleRowClick({ row }) {
			if (!this.$has('plan-collection_meterReadingReview_getReviewDetailList')) return

			const { meterReadingTaskId, bookNo, taskYear = '', taskMonth = '', taskStatus = '' } = row
			this.$router.push({
				path: '/meterReading/meterReadingVerificationDetail',
				query: {
					meterReadingTaskId,
					bookNo,
					taskYear,
					taskMonth,
					date: taskYear && taskMonth ? `${taskYear}-${taskMonth <= 9 ? `0${taskMonth}` : taskMonth}` : '--',
					taskStatus,
				},
			})
		},
		resetTableData() {
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.right-container {
	width: 0;
	flex: 1;
	padding: 20px;
	background-color: #fff;
	text-align: right;
}
</style>
