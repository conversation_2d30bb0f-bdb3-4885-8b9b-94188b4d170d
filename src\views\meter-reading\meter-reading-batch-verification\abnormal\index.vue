<template>
	<div class="wrapper">
		<div class="container-search">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" @click="getList(1)">筛选</el-button>
					<el-button @click="handleReset">重置</el-button>
				</el-form-item>
			</GcFormSimple>
		</div>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:imageUrl="{ row }">
					<UploadImgSimple v-model="row.imageUrl" />
				</template>
				<template v-slot:longitudeLatitude="{ row }">
					<span>
						{{ row.longitude && row.latitude ? `${row.longitude},${row.latitude}` : '--' }}
					</span>
				</template>
			</GcTable>
		</div>
	</div>
</template>

<script>
import UploadImgSimple from '@/components/UploadImgSimple'
import { getColumn } from './tableColumn.js'
import { getReviewDetailListNewV2 } from '@/api/meterReading.api.js'

export default {
	name: '',
	components: { UploadImgSimple },
	props: {
		type: Number,
		topParams: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '80px',
			},
			loading: false,
			columns: getColumn(),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	computed: {},
	created() {
		this.getList()
	},
	methods: {
		handleReset() {
			this.$refs.formRef.resetForm()
			this.getList(1)
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		async getList(curPage) {
			this.loading = true

			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getReviewDetailListNewV2({
					type: this.type,
					current,
					size,
					...this.formData,
					...this.topParams,
				})
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	flex: 1;
	height: 0;
	display: flex;
	flex-direction: column;
	padding: 20px;
}
.container-search {
	display: flex;
	justify-content: space-between;
}
.table-container {
	flex: 1;
	height: 0;
}
</style>
