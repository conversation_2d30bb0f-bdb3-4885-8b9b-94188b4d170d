<template>
	<GcDetailCard :detail-card-info="detailCardInfo" :header-num="headerNum">
		<template #card-content>
			<div class="card-content">
				<div class="content-item" v-for="(item, index) in displayList" :key="index">
					<p class="field">{{ item.key }}</p>
					<p class="value">
						{{ item.value }}
					</p>
				</div>
			</div>
		</template>
	</GcDetailCard>
</template>

<script>
export default {
	props: {
		reqData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			detailCardInfo: {
				bgUrl: require('@/assets/images/bg/pic-file.png'),
				signUrl: require('@/assets/images/icon/title-file.png'),
				cardName: '', //卡片名
			},
		}
	},
	computed: {
		headerNum() {
			let obj = {
				value: this.reqData.addressAreaName || '--',
			}
			return obj
		},
		//左侧卡片展示字段
		displayList() {
			const list = [
				{
					key: '区/县',
					value: '--',
					field: 'regionName',
				},
				{
					key: '街道/乡镇',
					value: '--',
					field: 'streetName',
				},
				{
					key: '接管状态',
					value: '--',
					field: 'takeOver',
				},
			]
			list.forEach(item => {
				if (item.field === 'takeOver') {
					item.value = this.reqData[item.field] === 1 ? '已接管' : '未接管'
					return
				}
				item.value = this.reqData[item.field] || '--'
			})

			return list
		},
	},
}
</script>

<style lang="scss" scoped>
::v-deep {
	.header-title img {
		display: none;
	}
}
</style>
