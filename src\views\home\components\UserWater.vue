<template>
	<CardContainer :cardDetail="cardDetail">
		<div class="content-box">
			<div class="top">
				<el-cascader
					v-model="selValue"
					:options="natureOptions"
					filterable
					:show-all-levels="false"
					:placeholder="`请选择`"
					:props="{
						label: 'natureName',
						value: 'priceNatureId',
						emitPath: false,
						checkStrictly: true,
					}"
					@visible-change="handleChange"
				/>
				<el-radio-group v-model="tabValue" @input="getList">
					<el-radio-button :label="0">月</el-radio-button>
					<el-radio-button :label="1">年</el-radio-button>
				</el-radio-group>
			</div>
			<div class="bottom">
				<GcBar1
					:seriesData="chartOptions.seriesData"
					:seriesName="chartOptions.seriesName"
					:dataZoom="{
						show: true,
						start: 0,
						end: 30,
						zoomLock: true,
						disabled: true,
					}"
					:xAxis="{
						data: chartOptions.xData,
						axisLabel: {
							interval: 0,
							rotate: 40,
						},
					}"
					:yAxis="{
						name: chartOptions.unit,
					}"
				/>
			</div>
		</div>
	</CardContainer>
</template>

<script>
import CardContainer from './CardContainer.vue'
import { apiGetWaterVolumeChart } from '@/api/home.api'
export default {
	name: 'UserWater',
	components: { CardContainer },
	props: {
		natureOptions: {
			type: Array,
			default: () => [],
		},
		orgCode: {
			type: String,
			default: '',
		},
	},
	watch: {
		natureOptions: {
			handler: function (arr) {
				if (arr && arr.length) {
					this.selValue = Number(arr[0].priceNatureId)
				}
			},
			deep: true,
		},
		orgCode(v) {
			if (v) {
				if (!this.$has('cpm_home_charts_waterVolumeChart')) {
					return
				}
				this.getList()
			}
		},
		'cardDetail.activeTab'(v) {
			const typeMap = this.cardDetail.tabList.reduce((map, item) => {
				map[item.value] = item.name.replace('表卡', '')
				return map
			}, {})
			this.chartOptions.seriesName = typeMap[v] || ''
			this.getList()
		},
	},
	data() {
		return {
			cardDetail: {
				titleList: [
					{
						label: '当月水量',
						value: '--',
						unit: '万m³',
					},
					{
						label: '同期比',
						value: '--',
						unit: '%',
					},
					{
						label: '全年累计',
						value: '--',
						unit: '万m³',
					},
					{
						label: '同期比',
						value: '--',
						unit: '%',
					},
				],
				activeTab: '3',
				tabList: [
					{
						name: '居民表卡',
						value: '3',
					},
					{
						name: '企业表卡',
						value: '4',
					},
				],
				bg: require('@/assets/images/bg/home-bg1.png'),
				name: '用户水量',
			},
			tabValue: 0,
			selValue: '',
			chartOptions: {
				unit: '万m³',
				xData: [],
				seriesData: [],
				seriesName: '居民',
			},
		}
	},
	methods: {
		handleChange(v) {
			if (v === false) {
				this.chartOptions.seriesData = []
				this.getList()
			}
		},
		// 递归查找
		findNatureItem(list, selValue) {
			for (const item of list) {
				if (item.priceNatureId == selValue) {
					return item
				}
				if (Array.isArray(item.children) && item.children.length) {
					const found = this.findNatureItem(item.children, selValue)
					if (found) return found
				}
			}
			return null
		},
		async getList() {
			try {
				const {
					annualTotal,
					currentMonthAmount,
					monthSameRatio,
					yearSameRatio,
					natureAmountList = [],
				} = await apiGetWaterVolumeChart({
					dimension: this.tabValue,
					orgCode: this.orgCode,
					userType: this.cardDetail.activeTab,
				})
				this.cardDetail.titleList[0].value = currentMonthAmount
				this.cardDetail.titleList[1].value = monthSameRatio
				this.cardDetail.titleList[2].value = annualTotal
				this.cardDetail.titleList[3].value = yearSameRatio
				if (Array.isArray(natureAmountList) && natureAmountList.length) {
					const result = this.findNatureItem(natureAmountList, this.selValue)
					if (result && Array.isArray(result.children) && result.children.length) {
						this.chartOptions.xData = result.children.map(item => item.natureName)
						this.chartOptions.seriesData = result.children.map(item => item.useAmount)
					} else {
						this.chartOptions.xData = [result.natureName]
						this.chartOptions.seriesData = [result.useAmount]
					}
				}
			} catch (error) {
				console.error(error)
			}
		},
	},
}
</script>
