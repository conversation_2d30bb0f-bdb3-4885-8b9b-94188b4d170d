<template>
	<div class="wrapper">
		<div class="search">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" @click="handleSearch(false)">查询</el-button>
					<el-button @click="handleReset">重置</el-button>
				</el-form-item>
			</GcFormSimple>
		</div>
		<div class="content">
			<div class="side-box">
				<GcFormSimple
					ref="leftSideFormRef"
					v-model="leftSideFormData"
					:formItems="leftSideFormItems"
					:formAttrs="formAttrs"
				>
					<el-form-item>
						<el-button
							type="primary"
							@click="handleSideSearch('left', leftSideFormData)"
							:disabled="!hasBookList"
						>
							查询
						</el-button>
					</el-form-item>
				</GcFormSimple>
				<GcTable
					:loading="leftLoading"
					:columns="columns"
					:table-data="leftTableData"
					needType="selection"
					@selectChange="handleLeftSelectChange"
				/>
			</div>
			<div class="btns">
				<el-button
					v-has="'plan-collection_meterReadingBook_bookArchivesMove'"
					type="primary"
					:disabled="!leftSelected.length || !leftSideFormData.bookId || !rightSideFormData.bookId"
					@click="handleMove('toRight')"
				>
					右移
					<i class="el-icon-arrow-right"></i>
				</el-button>
				<el-button
					v-has="'plan-collection_meterReadingBook_bookArchivesMove'"
					type="primary"
					:disabled="!rightSelected.length || !rightSideFormData.bookId || !leftSideFormData.bookId"
					@click="handleMove('toLeft')"
				>
					左移
					<i class="el-icon-arrow-left"></i>
				</el-button>
				<el-button
					v-has="'plan-collection_meterReadingBook_bookArchivesDisplace'"
					type="primary"
					:disabled="
						!leftSelected.length ||
						leftSelected.length > 1 ||
						!rightSelected.length ||
						rightSelected.length > 1 ||
						!rightSideFormData.bookId ||
						!leftSideFormData.bookId
					"
					@click="handleDisplace"
				>
					置换
					<i class="el-icon-sort" style="transform: rotate(90deg)"></i>
				</el-button>
			</div>
			<div class="side-box">
				<GcFormSimple
					ref="rightSideFormRef"
					v-model="rightSideFormData"
					:formItems="rightSideFormItems"
					:formAttrs="formAttrs"
				>
					<el-form-item>
						<el-button
							type="primary"
							@click="handleSideSearch('right', rightSideFormData)"
							:disabled="!hasBookList"
						>
							查询
						</el-button>
					</el-form-item>
				</GcFormSimple>
				<GcTable
					:loading="rightLoading"
					:columns="columns"
					:table-data="rightTableData"
					needType="selection"
					@selectChange="handleRightSelectChange"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import { ruleRequired } from '@/utils/rules.js'
import { bookTypeOptions } from '@/consts/optionList.js'
import {
	getAlleyMap,
	getBookMap,
	getBookRecordList2,
	bookArchivesMove,
	bookArchivesDisplace,
} from '@/api/meterReading.api.js'

export default {
	components: {},
	data() {
		return {
			// 顶部查询条件
			formData: {
				orgCode: '',
				bookType: '',
				alleyId: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					attrs: {
						clearable: true,
						placeholder: '请选择营业分公司',
					},
					options: this.$store.getters.orgList,
					events: {
						change: orgCode => {
							this.formData.alleyId = ''
							this.getAlleyMapData(orgCode)
						},
					},
				},
				{
					type: 'el-select',
					label: '册本类型',
					prop: 'bookType',
					attrs: {
						clearable: true,
						placeholder: '请选择册本类型',
					},
					options: bookTypeOptions,
				},
				{
					type: 'el-select',
					label: '坊别',
					prop: 'alleyId',
					attrs: {
						clearable: true,
						placeholder: '请选择坊别',
					},
					options: [],
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '110px',
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
					bookType: [ruleRequired('请选择册本类型')],
					alleyId: [],
					bookId: [ruleRequired('请选择册本编号')],
				},
			},
			// 左右两侧查询条件
			leftLoading: false,
			leftSideFormData: {
				bookId: '',
				archivesIdentity: '',
			},
			leftSideFormItems: [
				{
					type: 'el-select',
					label: '册本编号',
					prop: 'bookId',
					attrs: {
						clearable: true,
						placeholder: '请选择册本编号',
						style: {
							width: '160px',
						},
					},
					options: [],
					events: {
						change: value => {
							const leftOptions = this.leftSideFormItems[0].options
							const rightOptions = this.rightSideFormItems[0].options
							leftOptions.forEach(item => {
								item.disabled = false
							})
							rightOptions.forEach(item => {
								item.disabled = false
							})

							const bookId = this.rightSideFormData.bookId
							if (bookId) {
								const index = leftOptions.findIndex(item => item.value === bookId)
								leftOptions.splice(index, 1, {
									...leftOptions[index],
									disabled: true,
								})
							}

							if (value) {
								const index = rightOptions.findIndex(item => item.value === value)
								if (index !== -1) {
									rightOptions[index].disabled = true
								}
							} else {
								this.leftSideFormData.archivesIdentity = ''
							}
						},
					},
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
						style: {
							width: '160px',
						},
					},
				},
			],
			rightLoading: false,
			rightSideFormData: {
				bookId: '',
				archivesIdentity: '',
			},
			rightSideFormItems: [
				{
					type: 'el-select',
					label: '册本编号',
					prop: 'bookId',
					attrs: {
						clearable: true,
						placeholder: '请选择册本编号',
						style: {
							width: '160px',
						},
					},
					options: [],
					events: {
						change: value => {
							const leftOptions = this.leftSideFormItems[0].options
							const rightOptions = this.rightSideFormItems[0].options
							rightOptions.forEach(item => {
								item.disabled = false
							})
							leftOptions.forEach(item => {
								item.disabled = false
							})

							const bookId = this.leftSideFormData.bookId
							if (bookId) {
								const index = rightOptions.findIndex(item => item.value === bookId)
								rightOptions.splice(index, 1, {
									...rightOptions[index],
									disabled: true,
								})
							}

							if (value) {
								const index = leftOptions.findIndex(item => item.value === value)
								if (index !== -1) {
									leftOptions[index].disabled = true
								}
							} else {
								this.rightSideFormData.archivesIdentity = ''
								// leftOptions.forEach((item) => {
								//   item.disabled = false;
								// });
							}
						},
					},
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
						style: {
							width: '160px',
						},
					},
				},
			],
			// 左右两侧表格
			columns: [
				{
					key: 'addressFullName',
					name: '表卡地址',
					tooltip: true,
				},
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'recordSeq',
					name: '册内序号',
					tooltip: true,
				},
			],
			leftTableData: [],
			leftSelected: [],
			rightTableData: [],
			rightSelected: [],
		}
	},
	created() {},
	computed: {
		orgOptions() {
			return this.formItems[0].options
		},
		hasBookList() {
			return this.leftSideFormItems[0].options.length > 0
		},
	},
	watch: {
		'leftSideFormData.bookId': function (newVal) {
			if (newVal) {
				this.handleSideSearch('left', this.leftSideFormData)
			} else {
				this.leftTableData = []
			}
		},
		'rightSideFormData.bookId': function (newVal) {
			if (newVal) {
				this.handleSideSearch('right', this.rightSideFormData)
			} else {
				this.rightTableData = []
			}
		},
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					const findOrg = newVal.find(item => item.value === this.$store.getters.userInfo?.orgCode)
					if (findOrg) {
						this.formData.orgCode = findOrg.value
					} else {
						this.formData.orgCode = newVal[0].value
					}
					this.getAlleyMapData(this.formData.orgCode)
				}
			},
			deep: true,
			immediate: true,
		},
		'formData.bookType': function (newVal) {
			if (newVal === 2) {
				this.formAttrs.rules.alleyId = [ruleRequired('请选择坊别')]
			} else {
				this.formAttrs.rules.alleyId = []
				this.$refs.formRef.clearValidate('alleyId')
			}
		},
		formData: {
			handler: function () {
				this.resetReadingOrderFilter()
			},
			deep: true,
		},
	},
	methods: {
		resetReadingOrderFilter() {
			this.leftTableData = []
			this.leftSelected = []
			this.rightTableData = []
			this.rightSelected = []
			this.rightSideFormItems[0].options = []
			this.leftSideFormItems[0].options = []
			this.$refs.leftSideFormRef.resetFields()
			this.$refs.rightSideFormRef.resetFields()
		},
		// 顶部查询 获取册本编号下拉数据
		async handleSearch(isInit = false) {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			let flag = false

			if (isInit) {
				flag = true
			} else {
				const { orgCode = '', bookType = '', alleyId = '' } = this.formData
				const selfCondition = !!(orgCode || bookType || alleyId)
				if (selfCondition) {
					flag = true
				} else {
					this.$message.warning('请至少选择一个查询条件进行查询')
					return
				}
			}

			if (flag) {
				this.resetReadingOrderFilter()
				this.getBookMapData()
			}
		},
		// 左右两侧查询
		async handleSideSearch(type = 'left', data) {
			const valid = await this.$refs[type + 'SideFormRef'].validate()
			if (!valid) return

			if (type === 'left') {
				this.leftLoading = true
			} else if (type === 'right') {
				this.rightLoading = true
			}

			try {
				const list = await this.getList(data)
				if (type === 'left') {
					this.leftSelected = []
					this.leftTableData = list || []
				} else if (type === 'right') {
					this.rightSelected = []
					this.rightTableData = list || []
				}
			} catch (error) {
				console.error(error)
				if (type === 'left') {
					this.leftSelected = []
					this.leftTableData = []
				} else if (type === 'right') {
					this.rightSelected = []
					this.rightTableData = []
				}
			} finally {
				if (type === 'left') {
					this.leftLoading = false
				} else if (type === 'right') {
					this.rightLoading = false
				}
			}
		},
		handleReset() {
			this.$refs.formRef.resetForm()
		},
		handleLeftSelectChange(data) {
			this.leftSelected = JSON.parse(JSON.stringify(data))
		},
		handleRightSelectChange(data) {
			this.rightSelected = data
		},

		// 获取坊别数据
		async getAlleyMapData(orgCode = '') {
			try {
				const res = await getAlleyMap({
					orgCode,
				})
				if (res) {
					this.formItems[2].options = res.map(item => {
						return {
							value: item.id,
							label: item.alleyName,
						}
					})
				}
			} catch (error) {
				console.error(error)
				this.formItems[2].options = []
			}
		},
		// 获取册本下拉框数据
		async getBookMapData() {
			try {
				const res = await getBookMap(this.formData)

				const data = res.map(item => {
					return {
						label: item.bookNo,
						value: item.bookId,
						disabled: false,
					}
				})
				this.leftSideFormItems[0].options = [...data]
				this.rightSideFormItems[0].options = [...data]
			} catch (error) {
				console.error(error)
				this.leftSideFormItems[0].options = []
				this.rightSideFormItems[0].options = []
			}
		},
		// 获取册本表卡记录
		async getList({ bookId = '', archivesIdentity = '' }) {
			if (!bookId) {
				this.$message.warning('请先选择册本编号进行查询')
				return Promise.resolve([])
			}

			try {
				const { records = [] } = await getBookRecordList2({
					bookId,
					archivesIdentity,
					size: 9999,
				})
				return Promise.resolve(records || [])
			} catch (error) {
				console.error(error)
				return Promise.reject(error)
			}
		},
		// 左移、右移
		async handleMove(type) {
			let params = {
				bookId: '',
				archivesId: [],
			}
			let text = ''
			if (type === 'toRight') {
				text = '右移'
				params = {
					bookId: this.rightSideFormData.bookId,
					archivesId: this.leftSelected.map(item => item.archivesId),
				}
			} else if (type === 'toLeft') {
				text = '左移'
				params = {
					bookId: this.leftSideFormData.bookId,
					archivesId: this.rightSelected.map(item => item.archivesId),
				}
			}
			try {
				await bookArchivesMove(params)
				this.$message.success(`${text}成功`)
				this.handleSideSearch('left', this.leftSideFormData)
				this.handleSideSearch('right', this.rightSideFormData)
			} catch (error) {
				console.error(error)
			}
		},
		// 置换
		async handleDisplace() {
			if (this.leftSelected.length === 1 && this.rightSelected.length === 1) {
				try {
					await bookArchivesDisplace({
						archivesId1: this.leftSelected[0].archivesId,
						archivesId2: this.rightSelected[0].archivesId,
					})
					this.$message.success('置换成功')
					this.handleSideSearch('left', this.leftSideFormData)
					this.handleSideSearch('right', this.rightSideFormData)
				} catch (error) {
					console.error(error)
				}
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	flex-wrap: wrap;
	height: 100%;
}
.search {
	padding: 20px 20px 0 20px;
	background-color: #fff;
	margin-bottom: 14px;
	::v-deep {
		.el-form {
			.el-form-item {
				margin-bottom: 12px;
			}
		}
	}
}
.content {
	flex: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	overflow: auto;

	.side-box {
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow: auto;
		height: 100%;
		padding: 16px 12px;
		background-color: #fff;
	}
	::v-deep .gc-table {
		flex: 1;
	}
}
.btns {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 20px;
	.el-button {
		margin: 10px 0;
		&.el-button {
			margin-left: 0;
		}
	}
}
</style>
