export const getFormItems = _this => {
	return [
		{
			type: 'el-select',
			label: '市',
			prop: 'cityCode',
			options: [],
			events: {
				change: value => _this.handleChangeAddress(value, 'cityCode'),
			},
		},
		{
			type: 'el-select',
			label: '区/县',
			prop: 'regionCode',
			options: [],
			events: {
				change: value => _this.handleChangeAddress(value, 'regionCode'),
			},
		},
		{
			type: 'el-select',
			label: '街道/乡镇',
			prop: 'streetCode',
			options: [],
		},
		{
			type: 'el-input',
			label: '小区/村庄',
			prop: 'areaName',
		},
		{
			type: 'el-select',
			label: '接管状态',
			prop: 'takeOver',
			options: [
				{
					label: '已接管',
					value: 1,
				},
				{
					label: '未接管',
					value: 0,
				},
			],
		},
	]
}
