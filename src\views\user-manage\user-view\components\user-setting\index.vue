<template>
	<GcElDialog
		:show="isShow"
		title="修改用户信息"
		width="800px"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:otherMobile>
				<el-form-item class="other-mobile" label="其他手机" prop="otherMobile">
					<AddOtherMobile v-model="formData.otherMobile" :mobileList.sync="formData.mobileList" />
				</el-form-item>
			</template>
		</GcFormRow>
	</GcElDialog>
</template>

<script>
import { isBlank } from '@/utils/validate.js'
import {
	ruleRequired,
	ruleMaxLength,
	RULE_PHONE,
	RULE_INCORRECTIDCARD,
	RULE_INCORRECTEMAIL,
	RULE_POSTALCODE,
} from '@/utils/rules'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getFormItem } from './form.js'
import { apiModifyUser } from '@/api/userManage.api'
import AddOtherMobile from '@/views/meter-manage/components/AddOtherMobile'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		userDetail: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	components: { AddOtherMobile },
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	watch: {
		isShow: {
			handler(val) {
				if (val) {
					this.formItems = getFormItem(this)
					this.assignForm(this.userDetail.user)
				}
			},
			immediate: true,
		},
	},
	data() {
		return {
			formData: {
				userName: '',
				userType: '',
				userSubType: -1,
				certificateNo: '',
				otherCertificateNo: '',
				userMobile: '',
				contactPhone: '',
				chargingMethod: '',
				nameUsedBefore: '',
				certificateType: '',
				email: '',
				mailingAddress: '',
				zipCode: '',
				otherMobile: '', // 其他手机号
				mobileList: [], // 其他手机号
			},
			formItems: [],
			formAttrs: {
				labelWidth: '120px',
				rules: {
					userName: [ruleRequired('必填'), ruleMaxLength(32)],
					userMobile: [RULE_PHONE],
					certificateNo: [RULE_INCORRECTIDCARD],
					otherCertificateNo: [ruleMaxLength(32)],
					contactPhone: [ruleMaxLength(32)],
					nameUsedBefore: [ruleMaxLength(32)],
					email: [RULE_INCORRECTEMAIL],
					mailingAddress: [ruleMaxLength(64)],
					zipCode: [RULE_POSTALCODE],
					otherMobile: [RULE_PHONE],
				},
			},
		}
	},
	methods: {
		assignForm(obj) {
			const keys = Object.keys(this.formData)
			keys.forEach(key => {
				if (obj && Object.prototype.hasOwnProperty.call(obj, key)) {
					const value = obj[key]
					this.formData[key] = isBlank(value) || key === 'userSubType' ? value : String(value)
				}
			})
			this.formData.mobileList = obj.otherContactPhone ? obj.otherContactPhone.split(',') : []
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return

			const userVO = trimParams(removeNullParams(this.formData))
			const otherContactPhone = userVO.mobileList ? userVO.mobileList.join(',') : ''
			delete userVO.mobileList
			Object.assign(userVO, {
				userId: this.userDetail?.user?.userId,
			})
			if (otherContactPhone) {
				Object.assign(userVO, {
					otherContactPhone,
				})
			}
			await apiModifyUser({ userVO })
			this.$message.success('修改用户信息成功')
			this.$emit('success')
			this.isShow = false
		},
		handleClose() {
			this.$refs.formRef.resetForm()
			this.$refs.formRef.clearValidate()
			this.isShow = false
		},
	},
}
</script>
<style lang="scss" scoped>
::v-deep {
	.el-dialog__body {
		max-height: 600px !important;
	}
}
.other-mobile {
	::v-deep {
		.el-form-item__error {
			position: absolute;
			top: 36px;
		}
	}
}
</style>
