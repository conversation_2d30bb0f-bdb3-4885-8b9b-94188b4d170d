import { getLodop } from '../lodop/LodopFuncs'
import {
	apiGetArchivesDetail3,
	apiGetArchivesDetail4,
	apiGetArchivesDetail6,
	apiGetArchivesDetailList3,
} from '@/api/meterManage.api'
import { getfilterName } from '@/utils'

// 表卡打印
// type: priview预览打印 print直接打印
export const generateMeterTemplate = (data, type = 'preview') => {
	const LODOP = getLodop()
	// 初始化打印任务，设置打印区域
	// strPrintTaskName：打印任务的名称，字符型。
	// intLeft：打印区域左边距，整数型，表示距离纸张左边的距离。 intTop：打印区域上边距，整数型，表示距离纸张顶端的距离。
	// intWidth：打印区域的宽度，整数型。 intHeight：打印区域的高度，整数型。
	LODOP.PRINT_INITA(0, 0, 800, 600, '用水抄表卡片')
	// 设置打印模式 模式名称 模式名称指定的值
	// "COPIES"：设置打印的份数。 "PRINTER"：设置使用的打印机名称。
	// "ORIENTATION"：设置打印方向，可以是0（横向）或1（纵向）。 "PAPERSIZE"：设置纸张大小，可以指定具体的纸张尺寸。
	// "SCALE"：设置打印缩放比例。 "MIRROR"：设置是否镜像打印。
	// "NEGATIVE"：设置是否打印为负片效果。 "COLOR"：设置是否打印彩色。
	LODOP.SET_PRINT_MODE('PROGRAM_CONTENT_BYVAR', true)
	LODOP.SET_PRINT_STYLE('FontSize', 12)

	LODOP.ADD_PRINT_TEXT(50, 300, 200, 36, '用水抄表卡片')
	LODOP.SET_PRINT_STYLEA(0, 'FontSize', 20)
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.SET_PRINT_STYLEA(0, 'Bold', 1)
	LODOP.SET_PRINT_STYLEA(0, 'Underline', 1)

	LODOP.ADD_PRINT_TEXT(80, 150, 50, 26, '账号')
	LODOP.ADD_PRINT_TEXT(80, 200, 130, 26, data.accountNumber)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

	LODOP.ADD_PRINT_TEXT(80, 500, 50, 26, '本号')
	LODOP.ADD_PRINT_TEXT(80, 540, 60, 26, data.bookNo)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

	LODOP.ADD_PRINT_TEXT(80, 600, 50, 26, '顺号')
	LODOP.ADD_PRINT_TEXT(80, 640, 60, 26, data.recordSeq)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

	LODOP.ADD_PRINT_TEXT(140, 100, 50, 26, '户名')
	LODOP.ADD_PRINT_TEXT(140, 150, 550, 26, data.userName)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

	LODOP.ADD_PRINT_TEXT(166, 100, 50, 26, '地址')
	LODOP.ADD_PRINT_TEXT(166, 150, 550, 52, data.addressFullName)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

	LODOP.ADD_PRINT_TEXT(220, 100, 125, 26, '口径')
	LODOP.ADD_PRINT_TEXT(246, 100, 125, 26, data.caliber)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
	LODOP.ADD_PRINT_TEXT(220, 225, 125, 26, '表位')
	LODOP.ADD_PRINT_TEXT(246, 225, 125, 26, data.installPosition)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
	LODOP.ADD_PRINT_TEXT(220, 350, 205, 26, '表井位置')
	LODOP.ADD_PRINT_TEXT(246, 350, 205, 26, data.tableWellLocation)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
	LODOP.ADD_PRINT_TEXT(220, 555, 145, 26, '自来水号码')
	LODOP.ADD_PRINT_TEXT(246, 555, 145, 26, data.tapWaterNo)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

	LODOP.ADD_PRINT_TEXT(280, 100, 125, 26, '在装水表类型')
	LODOP.ADD_PRINT_TEXT(306, 100, 125, 26, data.meterTypeName)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
	LODOP.ADD_PRINT_TEXT(280, 225, 125, 26, '水表编号')
	LODOP.ADD_PRINT_TEXT(306, 225, 125, 26, data.meterNo)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
	LODOP.ADD_PRINT_TEXT(280, 350, 205, 26, '防盗装置')
	LODOP.ADD_PRINT_TEXT(306, 350, 205, 26, data.antiTheftCode)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
	LODOP.ADD_PRINT_TEXT(280, 555, 145, 26, '安装时间')
	LODOP.ADD_PRINT_TEXT(306, 555, 146, 26, data.installationDate)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

	LODOP.ADD_PRINT_TEXT(340, 100, 125, 26, '联系人')
	LODOP.ADD_PRINT_TEXT(364, 100, 125, 26, data.userName)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
	LODOP.ADD_PRINT_TEXT(340, 225, 125, 26, '联系电话')
	LODOP.ADD_PRINT_TEXT(364, 225, 125, 26, data.userMobile)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
	LODOP.ADD_PRINT_TEXT(340, 350, 205, 26, '用水性质')
	LODOP.ADD_PRINT_TEXT(364, 350, 205, 26, data.natureName)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
	LODOP.ADD_PRINT_TEXT(340, 555, 145, 26, '水价')
	LODOP.ADD_PRINT_HTM(364, 555, 146, 300, `<div style="color: #808080">${data.priceDesc}</div>`)
	LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

	if (type === 'preview') {
		LODOP.PREVIEW()
		// LODOP.PRINT_DESIGN();
	} else {
		LODOP.PRINT()
	}
}

export const meterCardPrint = async (permission, archivesId, t) => {
	try {
		let res = {
			user: {},
			book: {},
			address: {},
			meter: {},
			price: {},
			archives: {},
		}
		if (permission === 'plan-collection_meterReadingBook_meterPrint') {
			// 册本视图 抄表卡打印
			res = await apiGetArchivesDetail3({
				archivesId,
			})
		} else if (permission === 'cpm_archives_print') {
			// 表卡视图 表卡打印
			res = await apiGetArchivesDetail4({
				archivesId,
			})
		} else if (permission === 'cpm_archives_print2') {
			// 企业表卡视图 表卡打印
			res = await apiGetArchivesDetail6({
				archivesId,
			})
		}
		const { user = {}, book = {}, address = {}, meter = {}, price = {}, archives = {} } = res
		const { bookNo = '' } = book
		const { userName = '', userMobile = '', contactPeople = '', contactPhone = '' } = user
		const { addressFullName = '', tapWaterNo = '' } = address
		const {
			caliber = '',
			installPosition = '',
			tableWellLocation = '',
			meterTypeName = '',
			meterNo = '',
			antiTheftCode = '',
			installationDate = '',
		} = meter
		const { accountNumber = '', recordSeq = '' } = archives
		const { natureName = '', priceDesc = '' } = price
		generateMeterTemplate({
			accountNumber,
			bookNo,
			recordSeq,
			userName,
			userMobile,
			addressFullName,
			caliber,
			installPosition: getfilterName(
				t.$store.getters.dataList.installPosition,
				installPosition,
				'sortValue',
				'sortName',
			),
			tableWellLocation,
			tapWaterNo,
			meterTypeName,
			meterNo,
			antiTheftCode,
			installationDate,
			contactPeople,
			contactPhone,
			natureName,
			priceDesc,
		})
	} catch (error) {
		console.error(error)
	}
}

export const meterCardPrintBatch = async (permission, archivesIdList, t) => {
	try {
		let resList = []
		if (permission === 'cpm_archives_detail-list') {
			// 册本视图 抄表卡打印
			resList = await apiGetArchivesDetailList3({
				archivesIdList,
			})
		}
		const templateDataList = resList.map(item => {
			const { user = {}, book = {}, address = {}, meter = {}, price = {}, archives = {} } = item || {}
			const { bookNo = '' } = book || {}
			const { userName = '', userMobile = '', contactPeople = '', contactPhone = '' } = user || {}
			const { addressFullName = '', tapWaterNo = '' } = address || {}
			const {
				caliber = '',
				installPosition = '',
				tableWellLocation = '',
				meterTypeName = '',
				meterNo = '',
				antiTheftCode = '',
				installationDate = '',
			} = meter || {}
			const { accountNumber = '', recordSeq = '' } = archives || {}
			const { natureName = '', priceDesc = '' } = price || {}
			return {
				accountNumber,
				bookNo,
				recordSeq,
				userName,
				userMobile,
				addressFullName,
				caliber,
				installPosition: getfilterName(
					t.$store.getters.dataList.installPosition,
					installPosition,
					'sortValue',
					'sortName',
				),
				tableWellLocation,
				tapWaterNo,
				meterTypeName,
				meterNo,
				antiTheftCode,
				installationDate,
				contactPeople,
				contactPhone,
				natureName,
				priceDesc,
			}
		})
		console.log(templateDataList)
		generateMeterBatchTemplate(templateDataList)
	} catch (error) {
		console.error(error)
	}
}

export const generateMeterBatchTemplate = (dataList, type = 'preview') => {
	const LODOP = getLodop()

	// 初始化打印任务，设置打印区域（只初始化一次）
	// strPrintTaskName：打印任务的名称，字符型。
	// intLeft：打印区域左边距，整数型，表示距离纸张左边的距离。 intTop：打印区域上边距，整数型，表示距离纸张顶端的距离。
	// intWidth：打印区域的宽度，整数型。 intHeight：打印区域的高度，整数型。
	LODOP.PRINT_INITA(0, 0, 800, 600, '用水抄表卡片')
	// 设置打印模式 模式名称 模式名称指定的值
	// "COPIES"：设置打印的份数。 "PRINTER"：设置使用的打印机名称。
	// "ORIENTATION"：设置打印方向，可以是0（横向）或1（纵向）。 "PAPERSIZE"：设置纸张大小，可以指定具体的纸张尺寸。
	// "SCALE"：设置打印缩放比例。 "MIRROR"：设置是否镜像打印。
	// "NEGATIVE"：设置是否打印为负片效果。 "COLOR"：设置是否打印彩色。
	LODOP.SET_PRINT_MODE('PROGRAM_CONTENT_BYVAR', true)
	LODOP.SET_PRINT_STYLE('FontSize', 12)

	dataList.forEach((data, index) => {
		if (index != 0) {
			LODOP.NEWPAGE()
		}

		LODOP.ADD_PRINT_TEXT(50, 300, 200, 36, '用水抄表卡片')
		LODOP.SET_PRINT_STYLEA(0, 'FontSize', 20)
		LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
		LODOP.SET_PRINT_STYLEA(0, 'Bold', 1)
		LODOP.SET_PRINT_STYLEA(0, 'Underline', 1)

		LODOP.ADD_PRINT_TEXT(80, 150, 50, 26, '账号')
		LODOP.ADD_PRINT_TEXT(80, 200, 130, 26, data.accountNumber)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

		LODOP.ADD_PRINT_TEXT(80, 500, 50, 26, '本号')
		LODOP.ADD_PRINT_TEXT(80, 540, 60, 26, data.bookNo)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

		LODOP.ADD_PRINT_TEXT(80, 600, 50, 26, '顺号')
		LODOP.ADD_PRINT_TEXT(80, 640, 60, 26, data.recordSeq)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

		LODOP.ADD_PRINT_TEXT(140, 100, 50, 26, '户名')
		LODOP.ADD_PRINT_TEXT(140, 150, 550, 26, data.userName)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

		LODOP.ADD_PRINT_TEXT(166, 100, 50, 26, '地址')
		LODOP.ADD_PRINT_TEXT(166, 150, 550, 52, data.addressFullName)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

		LODOP.ADD_PRINT_TEXT(220, 100, 125, 26, '口径')
		LODOP.ADD_PRINT_TEXT(246, 100, 125, 26, data.caliber)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
		LODOP.ADD_PRINT_TEXT(220, 225, 125, 26, '表位')
		LODOP.ADD_PRINT_TEXT(246, 225, 125, 26, data.installPosition)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
		LODOP.ADD_PRINT_TEXT(220, 350, 205, 26, '表井位置')
		LODOP.ADD_PRINT_TEXT(246, 350, 205, 26, data.tableWellLocation)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
		LODOP.ADD_PRINT_TEXT(220, 555, 145, 26, '自来水号码')
		LODOP.ADD_PRINT_TEXT(246, 555, 145, 26, data.tapWaterNo)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

		LODOP.ADD_PRINT_TEXT(280, 100, 125, 26, '在装水表类型')
		LODOP.ADD_PRINT_TEXT(306, 100, 125, 26, data.meterTypeName)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
		LODOP.ADD_PRINT_TEXT(280, 225, 125, 26, '水表编号')
		LODOP.ADD_PRINT_TEXT(306, 225, 125, 26, data.meterNo)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
		LODOP.ADD_PRINT_TEXT(280, 350, 205, 26, '防盗装置')
		LODOP.ADD_PRINT_TEXT(306, 350, 205, 26, data.antiTheftCode)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
		LODOP.ADD_PRINT_TEXT(280, 555, 145, 26, '安装时间')
		LODOP.ADD_PRINT_TEXT(306, 555, 146, 26, data.installationDate)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')

		LODOP.ADD_PRINT_TEXT(340, 100, 125, 26, '联系人')
		LODOP.ADD_PRINT_TEXT(364, 100, 125, 26, data.userName)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
		LODOP.ADD_PRINT_TEXT(340, 225, 125, 26, '联系电话')
		LODOP.ADD_PRINT_TEXT(364, 225, 125, 26, data.userMobile)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
		LODOP.ADD_PRINT_TEXT(340, 350, 205, 26, '用水性质')
		LODOP.ADD_PRINT_TEXT(364, 350, 205, 26, data.natureName)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
		LODOP.ADD_PRINT_TEXT(340, 555, 145, 26, '水价')
		LODOP.ADD_PRINT_HTM(364, 555, 146, 300, `<div style="color: #808080">${data.priceDesc}</div>`)
		LODOP.SET_PRINT_STYLEA(0, 'FontColor', '#808080')
	})

	if (type === 'preview') {
		LODOP.PREVIEW()
		// LODOP.PRINT_DESIGN();
	} else {
		LODOP.PRINT()
	}
}
