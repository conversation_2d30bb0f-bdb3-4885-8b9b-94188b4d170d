<template>
	<div class="wrapper">
		<GcTable
			ref="tableRef"
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			@current-page-change="handlePageChange"
		>
			<template #deal="{ row }">
				<el-button type="text" size="medium" @click="handleRemove(row)">删除</el-button>
			</template>
		</GcTable>
		<div class="button-group">
			<button class="gc-button gc-button-three" type="button" @click="handlePrev">上一项</button>
			<button class="gc-button gc-button-two" type="button" @click="handleNext">下一项</button>
		</div>
	</div>
</template>

<script>
import { meterCardPreviewPage, meterCardChoose } from '@/api/meterReading.api'

export default {
	components: {},
	data() {
		return {
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'orgName',
					name: '营业分公司',
					tooltip: true,
				},
				{
					key: 'alleyName',
					name: '坊别',
					tooltip: true,
				},
				{
					key: 'bookNo',
					name: '表册编号',
					tooltip: true,
				},
				{
					key: 'bookSeq',
					name: '册内序号',
					tooltip: true,
				},
				{
					key: 'meterNo',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'userName',
					name: '户名',
					tooltip: true,
				},
				{
					key: 'addressName',
					name: '地址',
					tooltip: true,
				},
				{
					key: 'tapWaterCode',
					name: '自来水号',
					tooltip: true,
				},
				{
					key: 'simpleAddressName',
					name: '简写地址',
					tooltip: true,
				},
				{
					key: 'households',
					name: '户数',
					tooltip: true,
				},
				{
					key: 'resiPopulation',
					name: '人口',
					tooltip: true,
				},
				{
					key: 'priceCode',
					name: '价格编号',
					tooltip: true,
				},
				{
					key: 'deal',
					name: '操作',
					fixed: 'right',
					width: 80,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await meterCardPreviewPage({
					size,
					current,
					handOver: 2,
					...this.formData,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},

		// 删除
		handleRemove(row) {
			this.$confirm(`正在对【表卡编号：${row.meterNo ?? '--'}】进行删除，请确认是否删除？`).then(async () => {
				await meterCardChoose({
					archivesIdList: [row.archivesId],
					handOver: 0,
				})
				this.$message.success('删除成功')
				this.$emit('delete', this.tableData.length > 1)
				this.getList(1)
			})
		},
		// 上一步
		handlePrev() {
			this.$emit('next', 'cardChoose')
		},
		// 下一步
		handleNext() {
			this.$emit('next', 'transferCheck')
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
}
.button-group {
	width: 100%;
	.gc-button {
		margin-right: 8px;
	}
}

::v-deep {
	.el-form-item {
		margin-bottom: 12px;
	}
}
.right-top {
	display: flex;
	justify-content: flex-end;
	margin-bottom: 12px;
}
</style>
