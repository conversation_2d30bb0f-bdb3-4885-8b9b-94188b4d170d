import service from './request'

// 查询电子发票管理详细信息
export function apiInvoiceSaler(params) {
	return service({
		url: `/cpm/invoiceSaler/details`,
		method: 'get',
		params: params,
	})
}

// 电子发票管理详细信息编辑保存
export function apiInvoiceSalerSave(data) {
	return service({
		url: `/cpm/invoiceSaler/save`,
		method: 'post',
		data,
	})
}

// 查询租户获取已授予的表具类型列表
export function apiMeterType(params) {
	return service({
		url: `/cpm/device-type/tenant/meter-type`,
		method: 'get',
		params: params,
	})
}

// 获取可配置的开户指令集列表
export function apiCommandlist(params) {
	return service({
		url: `/cpm/code/archives/commandlist`,
		method: 'get',
		params,
	})
}

// 查询税率列表
export function apiTaxRateList() {
	return service({
		url: `/cpm/taxRate/taxRateList`,
		method: 'get',
	})
}

// 新增税率
export function apiAddTaxRate(data) {
	return service({
		url: `/cpm/taxRate/taxRateAdd`,
		method: 'post',
		data,
	})
}

// 删除税率
export function apiRemoveRate(taxRateId) {
	return service({
		url: `/cpm/taxRate?taxRateId=${taxRateId}`,
		method: 'delete',
	})
}

// 更新税率
export function apiModifyRate(data) {
	return service({
		url: `/cpm/taxRate/taxRateModify`,
		method: 'post',
		data,
	})
}

// 查询系统参数配置表单列表
export function apiSysparalist(params) {
	return service({
		url: `/cpm/sysdata/get-sysparalist`,
		method: 'get',
		params,
	})
}

// 编辑保存系统参数配置
export function apiSysparaCreate(data) {
	return service({
		url: `/cpm/sysdata/create-syspara`,
		method: 'post',
		data,
	})
}

// 设备类型下拉表类型获取
export function apiSysdatalist(params) {
	return service({
		url: `/cpm/sysdata/getsysdatalist`,
		method: 'get',
		params,
	})
}

export function apiSetChargeLimit(data) {
	return service({
		url: `/cpm/charge-limit/save`,
		method: 'post',
		data,
	})
}

export function apiGetChargeLimitList(params) {
	return service({
		url: `/cpm/charge-limit/list`,
		method: 'get',
		params,
	})
}

// 计量数据采集器列表获取
export function apiDtuType(params) {
	return service({
		url: `/cpm/device-type/tenant/dtu-type`,
		method: 'get',
		params,
	})
}

// 查询报警参数配置表列表和采集器配置列表
export function apiAlarmList(params) {
	return service({
		url: `/cpm/alarm/list`,
		method: 'get',
		params,
	})
}

// 微信短信业务列表查询
export function apiBusinessList(data) {
	return service({
		url: `/sns/message/tenant/notify-biz-list`,
		method: 'post',
		data,
	})
}

// 修改短信和微信业务配置
export function apiBusinessSetting(data) {
	return service({
		url: `/sns/message/tenant/notify-biz-setting`,
		method: 'post',
		data,
	})
}

// 获取员工列表
export function apiStaffList(params) {
	return service({
		url: `/v1/tos/staff/list`,
		method: 'get',
		params,
	})
}

// 获取部门树列表
export function apiOrganizationTree() {
	return service({
		url: `/v1/tos/organization/tree`,
		method: 'get',
	})
}

// 开户指令集配置保存
export function apiArchiveSave(data) {
	return service({
		url: `/cpm/code/archives/save`,
		method: 'post',
		data,
	})
}

// 报警参数配置保存
export function apiAlarmSave(data) {
	return service({
		url: `/cpm/alarm/save-alarm`,
		method: 'post',
		data,
	})
}
// 获得系统账项列表
export function apiGetAccountItems(data) {
	return service({
		url: `/cpm/bill-item/query`,
		method: 'post',
		data,
	})
}
// 新增系统账项
export function apiAddAccountItem(data) {
	return service({
		url: `/cpm/bill-item/add`,
		method: 'post',
		data,
	})
}
// 修改系统账项
export function apiModifyAccountItem(data) {
	return service({
		url: `/cpm/bill-item/modify`,
		method: 'post',
		data,
	})
}
// 删除系统账项
export function apiDelAccountItem(data) {
	return service({
		url: `/cpm/bill-item/remove`,
		method: 'post',
		data,
	})
}

// 开票设置管理列表
export function apiGetInvoiceOpenSetList(data) {
	return service({
		url: '/cpm/invoice/invoice-open-set/list',
		method: 'post',
		data,
	})
}

// 开票设置保存
export function apiSaveInvoiceOpenSet(data) {
	return service({
		url: '/cpm/invoice/invoice-open-set',
		method: 'put',
		data,
	})
}
