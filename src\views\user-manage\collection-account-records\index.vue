<template>
	<div class="wrapper">
		<div class="title">托收账户变更记录</div>
		<div class="subTitle">托收协议号：{{ $route.query.num || '--' }}</div>
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			showPage
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			@current-page-change="handlePageChange"
		>
			<template v-slot:after="{ row, column }">
				<div v-if="column.property === 'after'">
					<div v-if="row.after">
						<div v-for="(value, key) in JSON.parse(row.after)" :key="key">{{ key }}: {{ value }}</div>
					</div>
					<div v-else>--</div>
				</div>
			</template>
		</GcTable>
	</div>
</template>

<script>
import { apiGetCollectionAccountModifyRecord } from '@/api/userManage.api'
export default {
	name: 'CollectionAccountRecords',
	data() {
		return {
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'createTime',
					name: '变更时间',
					tooltip: true,
				},
				{
					key: 'createStaffName',
					name: '操作人',
					tooltip: true,
				},
				{
					key: 'after',
					name: '变更内容',
					tooltip: true,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	activated() {
		if (this.$route.query.recordId) {
			this.handlePageChange({ page: 1 })
		}
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		async getList() {
			this.loading = true
			try {
				const { recordId } = this.$route.query
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await apiGetCollectionAccountModifyRecord({
					size,
					current,
					recordId,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	padding: 20px;
}

.title,
.subTitle {
	margin-bottom: 10px;
	color: black;
	font-weight: 900;
	font-size: 15px;
}
.subTitle {
	font-weight: 700;
	font-size: 14px;
}
</style>
