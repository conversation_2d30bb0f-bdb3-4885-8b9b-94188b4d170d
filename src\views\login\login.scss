.login-container {
	position: relative;
	height: 100%;
	width: 100%;
	min-width: 640px;
	overflow: hidden;
	background: url('./loginBg.png') rgb(5, 53, 149) no-repeat;
	background-size: cover;
	background-position: center;
	.login-bg {
		width: 100%;
		height: 100%;
		min-width: 640px;
		img {
			display: block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
	@media screen and (max-width: 800px) {
		.login-content {
			transform: translate(11%, -50%) !important;
		}
	}
	.login-content {
		position: absolute;
		width: 320px;
		top: 48%;
		left: 50%;
		transform: translate(160px, -50%);
		.logo {
			margin: 0 auto;
			margin-left: -45px;
			width: 410px;
			height: 91px;
			background: url(./logo.svg) no-repeat center bottom;
			background-size: 100%;
			color: red;
		}

		.login-form.el-form--label-top {
			margin: 50px auto 0;
			::v-deep {
				.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before,
				.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before {
					display: none;
				}
				.el-form-item {
					margin-bottom: 24px;
					.el-form-item__error {
						color: red;
						padding: 4px 0 8px 20px;
					}
					.el-form-item__label {
						padding: 0;
						margin: 0 0 10px 20px;
						font-size: 12px;
						color: #cccccc;
						line-height: 12px;
						height: 12px;
					}
					input:-webkit-autofill,
					input:-webkit-autofill:hover,
					input:-webkit-autofill:focus,
					input:-webkit-autofill:active {
						-webkit-transition-delay: 99999s;
						-webkit-transition: color 99999s ease-out, background-color 99999s ease-out;
					}
					.el-input__inner {
						padding: 0 20px;
						width: 320px;
						height: 40px;
						background: rgba(255, 255, 255, 0.16);
						// background: red;
						border: none;
						font-size: 16px;
						color: #ffffff;
						line-height: 24px;
						border-radius: 20px;
						overflow: hidden;

						&::placeholder {
							color: #647eb3;
						}
					}
				}

				@mixin el-input-style($inputWidth, $appendWidth, $appendBg: rgba(255, 255, 255, 0.16)) {
					display: flex;
					.el-input__inner {
						flex: 1;
						border-top-right-radius: 0;
						border-bottom-right-radius: 0;
					}

					.el-input-group__append {
						padding: 0;
						flex: 0 0 $appendWidth;
						width: $appendWidth;

						border-radius: 0;
						border-top-right-radius: 40px;
						border-bottom-right-radius: 40px;
						border: none;
						overflow: hidden;
						box-sizing: border-box;
						background-color: $appendBg;
						font-size: 0 !important;
					}
				}

				.el-input.code-input {
					@include el-input-style(220px, 100px, rgb(79, 112, 178));
					.code-img {
						margin-top: 0;
						display: inline-block;
						width: 100px;
						height: 40px;
						object-fit: contain !important;
						overflow: hidden;
					}
				}
				.el-input.password-input {
					@include el-input-style(280px, 40px, rgba(255, 255, 255, 0.16));
					.show-pwd {
						padding-right: 10px;
						line-height: 40px;
						color: white;
						font-size: 12px;
						text-align: center;
						// border: 1px solid red;
						&.icon-biyan {
							&:before {
								transform: scale(0.64);
								transform-origin: left center;
							}
						}
						&::before {
							display: inline-block;
						}
					}
				}
			}
			.el-button.login-btn {
				margin-top: 18px;
				width: 320px;
				height: 40px;
				background: linear-gradient(360deg, #148cc7 0%, #78e4ff 100%);
				border-radius: 20px;
				border: none;
				font-size: 14px;
				color: #ffffff;
				&.om-login-btn {
					background: linear-gradient(to right, #23ccff, #6882fc, #9468f5);
				}
			}
		}
	}
	.remarks {
		position: absolute;
		width: 100%;
		bottom: 18px;
		line-height: 20px;
		color: rgba($color: #fff, $alpha: 0.8);
		text-align: center;
	}
	.code-input-message {
		cursor: pointer;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #cccccc;
		height: 100%;
		font-size: 12px !important;
	}
}
