<template>
	<GcElDialog
		:show="isShow"
		:title="(editType === 'add' ? '新增' : '修改') + title"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
	</GcElDialog>
</template>

<script>
import { ruleRequired, ruleMaxLength } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getFormItems } from './formItem'
import {
	apiGetRegion,
	apiGetAddressAreaMap,
	apiAddNeighbourhood,
	apiUpdateNeighbourhood,
	apiAddStreet,
	apiUpdateStreet,
	apiAddCounty,
	apiUpdateCounty,
} from '@/api/addressManage.api.js'

export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		tab: {
			type: String,
			default: '',
		},
		editType: {
			type: String,
			default: '',
		},
		data: {
			type: Object,
			default: () => {},
		},
	},
	watch: {
		show(val) {
			if (val) {
				this.formItems = getFormItems(this)
				this.setFormRuls()
				this.$nextTick(() => {
					this.$refs.formRef.$refs.formRef.clearValidate()
				})
				this._getCityOriRegionData(21, 'cityCode')
				if (this.editType === 'edit') {
					const formData =
						this.tab === 'region'
							? Object.assign(this.data, {
									cityCode: this.data.parentCode,
							  })
							: this.data
					this.assignForm(formData)
					this.data.cityCode && this._getCityOriRegionData(this.data.cityCode, 'regionCode')
					this._getStreetData()
				}
			}
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		title() {
			if (this.tab === 'community') {
				return `小区`
			} else if (this.tab === 'street') {
				return `街道/乡镇`
			} else if (this.tab === 'region') {
				return `区县`
			}
			return ``
		},
	},
	data() {
		return {
			formData: {},
			formItems: [],
			formAttrs: {
				labelPosition: 'top',
				rules: {},
			},
		}
	},
	methods: {
		assignForm(obj) {
			const keys = Object.keys(this.formData)
			keys.forEach(key => {
				if (obj && Object.prototype.hasOwnProperty.call(obj, key)) {
					this.formData[key] = obj[key] + ''
				}
			})
		},
		// 获取市、区县数据  key:cityCode、regionCode
		async _getCityOriRegionData(value, key) {
			const { records } = await apiGetRegion({ regionCode: value })
			const obj = this.formItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = records.map(item => {
				return {
					value: item.regionCode,
					label: item.regionName,
				}
			})
		},
		// 获取街道数据
		async _getStreetData() {
			const data = await apiGetAddressAreaMap({
				parentCode: this.formData.regionCode,
			})
			const streetObj = this.formItems.find(item => item.prop === 'streetCode')
			if (!streetObj) return
			streetObj.options = data.map(item => {
				return {
					value: item.addressAreaCode,
					label: item.addressAreaName,
				}
			})
		},
		// 设置表单校验规则
		setFormRuls() {
			const commonRules = {
				cityCode: [ruleRequired('必填')],
				regionCode: [ruleRequired('必填')],
			}
			if (this.tab === 'community') {
				this.formAttrs.rules = {
					...commonRules,
					streetCode: [ruleRequired('必填')],
					addressAreaName: [ruleRequired('必填', 'blur'), ruleMaxLength(32)],
				}
			} else if (this.tab === 'street') {
				this.formAttrs.rules = {
					...commonRules,
					addressAreaName: [ruleRequired('必填', 'blur'), ruleMaxLength(32)],
				}
			} else if (this.tab === 'region') {
				this.formAttrs.rules = {
					cityCode: [ruleRequired('必填')],
					regionName: [ruleRequired('必填', 'blur'), ruleMaxLength(16)],
				}
			}
		},
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				try {
					const formParams = trimParams(removeNullParams(this.formData))

					let successMsg = `${this.title}${this.editType === 'add' ? '新增' : '修改'}成功`

					if (this.tab === 'community') {
						formParams['neighbourhoodName'] = formParams.addressAreaName
						delete formParams.cityCode
						delete formParams.addressAreaName
						const apiMethod = this.editType === 'add' ? apiAddNeighbourhood : apiUpdateNeighbourhood
						const params =
							this.editType === 'add'
								? formParams
								: Object.assign(formParams, {
										addressAreaId: this.data.addressAreaId,
								  })

						await apiMethod(params)
					} else if (this.tab === 'street') {
						formParams['streetName'] = formParams.addressAreaName
						delete formParams.cityCode
						delete formParams.addressAreaName
						const apiMethod = this.editType === 'add' ? apiAddStreet : apiUpdateStreet
						const params =
							this.editType === 'add'
								? formParams
								: Object.assign(formParams, {
										addressAreaId: this.data.addressAreaId,
								  })

						await apiMethod(params)
					} else if (this.tab === 'region') {
						const apiMethod = this.editType === 'add' ? apiAddCounty : apiUpdateCounty
						const commonParams = {
							parentCode: formParams.cityCode,
							regionName: formParams.regionName,
						}
						const params =
							this.editType === 'add'
								? commonParams
								: Object.assign(commonParams, {
										regionId: this.data.regionId,
								  })

						await apiMethod(params)
					}
					this.$message.success(successMsg)
					this.$emit('success')
					this.isShow = false
				} catch (error) {
					console.log(error)
				}
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
	},
}
</script>
<style lang="scss" scoped>
.el-tag {
	margin-right: 10px;
}
</style>
