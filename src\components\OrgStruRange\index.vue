<template>
	<div class="orgStru-range">
		<el-select class="community1" v-model="orgCode" filterable placeholder="全部">
			<el-option
				v-for="item in orgCodeArr"
				:key="item.org_code"
				:label="item.name"
				:value="item.org_code"
			></el-option>
		</el-select>
	</div>
</template>

<script>
import { apiOrganizeTree, apiGetTenantOrgList } from '@/api/organize.api'
import identity from '@/mixin/identity.js'
export default {
	name: '',
	mixins: [identity],
	components: {},
	props: {
		value: {
			type: String,
		},
		isAllInclude: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			orgCodeArr: [],
			farr: [],
		}
	},
	computed: {
		orgCode: {
			get: function () {
				return this.value
			},
			set: function (val) {
				this.$emit('update:value', val)
				let filtedObj = this.orgCodeArr.find(it => it.org_code == val) || {}
				this.$emit('orgChange', { val: val, name: filtedObj.name })
			},
		},
	},
	watch: {
		orgCodeArr(nval) {
			if (nval && nval.length) {
				this.orgCode = nval[0].org_code
			}
		},
		tenantId(nval, oval) {
			if (nval !== oval) {
				this.init()
			}
		},
		userInfo: {
			immediate: true,
			deep: true,
			handler() {
				this.init()
			},
		},
	},
	created() {},
	mounted() {
		this.init()
	},
	methods: {
		init() {
			let orgsTemplateTree = this.$ls.get('orgsTemplateTree')
			if (this.userLevel != 0) {
				//  租户级
				let uf = this.userInfo
				// 租户管理员
				if (uf.isTenantAdmin) {
					this.orgCodeArr = this.getorgCodeArr(orgsTemplateTree)
					return
				}
				/*
        跨域运维 - FEAT 3.7.0取消跨域运维角色
        */
				if (uf.isCrossDomain) {
					if (uf.orgCode.split('-').length < 4) {
						//分公司以上层级
						orgsTemplateTree.map(item => {
							if (item.tenant_organization.org_code == uf.orgCode) {
								this.orgCodeArr = this.getorgCodeArr(item.children)
							}
						})
						return
					}
				}
				// 成员公司级别-当前成员公司下的所有分公司
				if (uf.orgStruName === '成员公司') {
					this.gettenantOrgList(apiOrganizeTree)
					return
				}
				this.orgCode = uf.branchCompanyOrgCode
				this.orgCodeArr = [{ name: uf.branchCompanyOrgName, org_code: uf.branchCompanyOrgCode }]
				return
			}
			if (this.userLevel == 0 && this.tenantId) {
				//系统级
				this.gettenantOrgList(apiGetTenantOrgList)
			}
		},
		//所属机构发生变化的时候
		orgChange(item) {
			this.$emit('orgChange', item)
		},
		getQuitCondition(orgcode) {
			let arr = orgcode.split('-')
			return arr.length == 4 ? true : false
		},
		// 获取分公司列表
		getSubComList(arr) {
			//过滤出分公司的数据
			if (arr.length > 0) {
				arr.map(item => {
					if (this.getQuitCondition(item.tenant_organization.org_code)) {
						this.farr.push({
							name: item.tenant_organization.name,
							org_code: item.tenant_organization.org_code,
						})
					} else {
						if (item.children) {
							this.getSubComList(item.children)
						}
					}
				})
				return this.farr
			}
		},
		// 分公司级别
		getorgCodeArr(arr) {
			this.farr = []
			let result = this.getSubComList(arr)
			if (this.isAllInclude) {
				result.unshift({
					name: '全部',
					org_code: null,
				})
			}
			return result
		},
		gettenantOrgList(api) {
			api({ tid: this.tenantId }).then(res => {
				this.orgCodeArr = this.getorgCodeArr(res)
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.orgStru-range {
	.el-select,
	.el-cascader {
		width: 100%;
	}
}
</style>
