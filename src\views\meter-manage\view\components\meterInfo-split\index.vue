<template>
	<GcElDialog
		:show="isShow"
		title="拆表登记"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
	</GcElDialog>
</template>

<script>
import { apiArchivesRemoveMeter, apiArchivesRemoveMeter3 } from '@/api/meterManage.api.js'
import { getFormItems } from './formItem.js'
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		detailData: {
			type: Object,
			default: () => ({}),
		},
		permissionCode: {
			type: String,
			default: 'cpm_archives_remove-meter',
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	watch: {
		isShow: {
			handler(val) {
				if (val) {
					this.formData.archivesIdentity = this.detailData?.archivesIdentity || ''
					this.formData.meterNo = this.detailData?.meterNo || ''
				}
			},
		},
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
				meterNo: '',
				removeDate: '',
				removePerson: this.$store.getters.userInfo.staffName,
				removeReason: '',
			},
			formItems: getFormItems(),
			formAttrs: {
				rules: {
					removeDate: [ruleRequired('必填')],
					removeReason: [ruleRequired('必填')],
				},
			},
		}
	},
	methods: {
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			let formObj = trimParams(removeNullParams(this.formData))
			const { archivesId } = this.detailData
			delete formObj.meterNo
			delete formObj.archivesIdentity
			const enableDateStr = this.dayjs(formObj.removeDate).format('YYYY-MM-DD')
			Object.assign(formObj, { removeDate: enableDateStr, archivesId })

			const apiMethods = {
				'cpm_archives_remove-meter': apiArchivesRemoveMeter,
				'cpm_archives_remove-meter3': apiArchivesRemoveMeter3,
			}
			await apiMethods[this.permissionCode](formObj)
			this.$message.success('拆表登记成功')
			this.handleClose()
			this.$emit('refresh')
		},
	},
}
</script>
