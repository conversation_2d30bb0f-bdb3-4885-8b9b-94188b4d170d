import service from './request'
import { CPM } from '@/consts/moduleNames'

//天气查询
export function getWeatherFromIp(ip) {
	return service({
		url: '/cpm/chart/ip-to-weather?ip=' + ip,
		cancelUnitErrToast: true,
	})
}

// 用户水量
export function apiGetWaterVolumeChart(data) {
	return service({
		url: `${CPM}/home/<USER>/waterVolumeChart`,
		method: 'post',
		data,
	})
}

// 销售收入
export function apiGetWaterCostChart(data) {
	return service({
		url: `${CPM}/home/<USER>/waterCostChart`,
		method: 'post',
		data,
	})
}

// 水费回收率
export function apiGetWaterPayCostChart(data) {
	return service({
		url: `${CPM}/home/<USER>/waterPayCostChart`,
		method: 'post',
		data,
	})
}

// 用户欠费
export function apiGetWaterFeeArrearsChart(data) {
	return service({
		url: `${CPM}/home/<USER>/waterFeeArrearsChart`,
		method: 'post',
		data,
	})
}

// 抄表率
export function apiGetMeterReadingRateChart(data) {
	return service({
		url: `${CPM}/home/<USER>/meterReadingRateChart`,
		method: 'post',
		data,
	})
}

// 用户缴费
export function apiGetObtainUserPayChart(data) {
	return service({
		url: `${CPM}/home/<USER>/obtainUserPayChart`,
		method: 'post',
		data,
	})
}
// 大用户用水量提醒
export function apiGetObtainUserBigAmountChart(data) {
	return service({
		url: `${CPM}/home/<USER>/obtainUserBigAmountChart`,
		method: 'post',
		data,
	})
}

// 阶梯用水量预警提醒
export function apiGetObtainUserLadderLevelChart(data) {
	return service({
		url: `${CPM}/home/<USER>/obtainUserLadderLevelChart`,
		method: 'post',
		data,
	})
}
