<template>
	<gc-el-dialog :show="isShow" title="催缴登记" custom-top="120px" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { urgeRegister, urgeRegister2, urgeRegister3 } from '@/api/arrearageManage.api'
import { queryStaffByType } from '@/api/meterReading.api.js'
export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		data: {
			require: true,
			type: Array,
		},
		permissionCode: {
			type: String,
			default: 'cpm_urge_payment_urgeRegister',
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	watch: {
		isShow(val) {
			if (val) {
				this.getStaffMapData()
			}
		},
	},
	data() {
		return {
			formData: {
				callPaymentTime: '',
				urgeRemark: '',
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '催缴时间',
					prop: 'callPaymentTime',
					attrs: {
						col: 24,
						type: 'datetime',
						format: 'yyyy-MM-dd HH:mm',
						valueFormat: 'yyyy-MM-dd HH:mm:ss',
						placeholder: '请选择催缴时间',
					},
				},
				{
					type: 'el-select',
					label: '催缴人',
					prop: 'callUserId',
					options: [],
					attrs: {
						clearable: true,
						placeholder: '请选择催缴人',
					},
				},
				{
					type: 'el-input',
					label: '备注',
					prop: 'urgeRemark',
					attrs: {
						col: 24,
						type: 'textarea',
						placeholder: '请输入备注',
						maxlength: '300',
						showWordLimit: true,
						autosize: {
							minRows: 4,
							maxRows: 8,
						},
					},
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					callPaymentTime: [{ required: true, message: '请选择催缴时间', trigger: 'change' }],
					callUserId: [{ required: true, message: '请选择催缴人', trigger: 'change' }],
				},
			},
		}
	},
	methods: {
		// 获取催缴人下拉框数据
		async getStaffMapData() {
			const orgCode = this.data && this.data.length ? this.data[0].orgCode : ''
			try {
				const res = await queryStaffByType({
					staffType: 2,
					status: 0,
					orgCode,
				})
				this.formItems[1].options = res.map(item => {
					const { staffId, staffName } = item
					return {
						value: staffId,
						label: staffName,
					}
				})
			} catch (error) {
				console.error(error)
				this.formItems[1].options = []
			}
		},
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				const callUserName = this.formItems[1].options.find(
					item => item.value === this.formData.callUserId,
				).label
				const params = {
					billItemList: this.data.map(item => {
						const {
							archivesId,
							archivesNo,
							billId,
							billNo,
							billYear,
							userId: billUserId,
							arrearsAmount: receivableAmount,
							billDate,
							waterAmount,
						} = item
						return {
							archivesId,
							archivesNo,
							billId,
							billNo,
							billYear,
							billUserId,
							receivableAmount,
							billDate,
							waterAmount,
						}
					}),
					...this.formData,
					callUserName,
				}

				const apiMethods = {
					cpm_urge_payment_urgeRegister: urgeRegister,
					cpm_urge_payment_urgeRegister2: urgeRegister2,
					cpm_urge_payment_urgeRegister3: urgeRegister3,
				}
				await apiMethods[this.permissionCode](params)
				this.$message.success('催缴登记成功')
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
</style>
