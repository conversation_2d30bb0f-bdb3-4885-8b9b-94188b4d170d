<template>
	<div class="left-container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
		<div class="btn-group">
			<el-button style="width: 50%" round @click="handleReset">重置</el-button>
			<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
		</div>
	</div>
</template>

<script>
import { bookTypeOptions } from '@/consts/optionList.js'
import { getAlleyMap, getStaffMap } from '@/api/meterReading.api.js'

export default {
	name: '',
	components: {},
	data() {
		return {
			formData: {
				orgCode: '',
				bookType: '',
				bookNo: '',
				meterReadingStaffId: '',
				alleyId: '',
				handOverFlag: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						col: 24,
						clearable: false,
						placeholder: '请选择营业分公司',
					},
					events: {
						change: () => {
							this.formData.alleyId = ''
							this.formData.meterReadingStaffId = ''
							this.getAlleyMapData()
							this.getStaffMapData()
						},
					},
				},
				{
					type: 'el-select',
					label: '册本类型',
					prop: 'bookType',
					options: bookTypeOptions,
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择册本类型',
					},
				},
				{
					type: 'el-input',
					label: '表册编号',
					prop: 'bookNo',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入表册编号',
					},
				},
				{
					type: 'el-select',
					label: '抄表员',
					prop: 'meterReadingStaffId',
					options: [],
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择抄表员',
					},
				},
				{
					type: 'el-select',
					label: '坊别',
					prop: 'alleyId',
					options: [],
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择坊别',
					},
				},
				{
					type: 'el-select',
					label: '是否移交',
					prop: 'handOverFlag',
					options: [
						{ label: '是', value: true },
						{ label: '否', value: false },
					],
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择是否移交',
					},
				},
			],
			formAttrs: {
				rules: {
					orgCode: {
						required: true,
						message: '请选择营业分公司',
						trigger: 'change',
					},
				},
			},
		}
	},
	computed: {
		orgOptions() {
			return this.formItems[0].options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.getAlleyMapData()
					this.getStaffMapData()
					this.$emit('search', this.formData)
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		handleReset() {
			this.$refs.formRef.resetFormSmooth()
			this.$emit('reset')
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.$emit('search', this.formData)
			}
		},
		// 获取坊别数据
		async getAlleyMapData() {
			try {
				const res = await getAlleyMap({
					orgCode: this.formData.orgCode,
				})
				if (res) {
					this.formItems[4].options = res.map(item => {
						return {
							value: item.id,
							label: item.alleyName,
						}
					})
				}
			} catch (error) {
				console.error(error)
				this.formItems[4].options = []
			}
		},
		// 获取抄表员工数据
		async getStaffMapData() {
			try {
				const res = await getStaffMap({
					orgCode: this.formData.orgCode,
				})
				if (res) {
					this.formItems[3].options = res.map(item => {
						const { staffId, staffName } = item
						return {
							value: staffId,
							label: staffName,
						}
					})
				}
			} catch (error) {
				console.error(error)
				this.formItems[3].options = []
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.left-container {
	display: flex;
	flex-direction: column;
	flex: 0 0 270px;
	margin-right: 20px;
	padding: 20px;
	background-color: #fff;
}
.el-form {
	flex: 1;
	padding: 0 10px;
	overflow: auto;
}
.btn-group {
	flex: 0 0 52px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	.el-button {
		height: 30px;
	}
}
</style>
