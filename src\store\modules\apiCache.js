import {
	apiEffectivePrice,
	apiMeterTypeList,
	apiGetRegion,
	apiGetArea,
	apiDtuTypeList,
	apiAllPrices,
} from '@/api/archives.api.js'
import { apiGetAccountItems } from '@/api/systemSetting.api.js'
import { apiGetMeterTypeList } from '@/api/meterMonitor.api.js'
import { isBlank } from '@/utils/validate.js'

export default {
	namespaced: true,
	state: {
		effectivePriceList: null,
		meterTypeList: null,
		regionCode: null,
		regionData: null,
		dtuTypeList: null,
		allPriceList: null, //获取全部价格（包括已禁用）
		billItems: null, // 附加费列表
		costType: [], // 其他费用类型
		meterTypeListAll: null, //获取已授予和已撤销的表类型
	},
	getters: {
		billItems: state => state.billItems,
		costType: state => state.costType,
	},
	mutations: {
		SET_PRICE_LIST(state, payload) {
			state.effectivePriceList = payload
		},
		SET_METER_TYPE_LIST(state, payload) {
			state.meterTypeList = payload
		},
		SET_REGION_CODE(state, payload) {
			state.regionCode = payload
		},
		SET_REGION_DATA(state, payload) {
			state.regionData = payload
		},
		SET_DTU_TYPE_LIST(state, payload) {
			state.dtuTypeList = payload
		},
		SET_ALL_PRICE_LIST(state, payload) {
			state.allPriceList = payload
		},
		SET_BILL_ITEMS(state, payload) {
			state.billItems = payload
		},
		SET_COST_TYPE(state, payload) {
			state.costType = payload
		},
		SET_METER_TYPE_LIST_ALL(state, payload) {
			state.meterTypeListAll = payload
		},
	},
	actions: {
		// 获取有效价格列表
		getEffectivePrice({ state, commit }) {
			return new Promise(resolve => {
				if (state.effectivePriceList) {
					resolve(state.effectivePriceList)
				} else {
					apiEffectivePrice().then((res = []) => {
						commit('SET_PRICE_LIST', res)
						resolve(res)
					})
				}
			})
		},
		// 获取全部价格列表
		getAllPrice({ state, commit }) {
			return new Promise(resolve => {
				if (state.allPriceList) {
					resolve(state.allPriceList)
				} else {
					apiAllPrices().then((res = []) => {
						commit('SET_ALL_PRICE_LIST', res)
						resolve(res)
					})
				}
			})
		},
		// 获取表类型列表
		getMeterTypeList({ state, commit, rootGetters }) {
			return new Promise(resolve => {
				if (state.meterTypeList) {
					resolve(state.meterTypeList)
				} else {
					apiMeterTypeList({
						tenantId: rootGetters?.tenant?.id,
					}).then((res = []) => {
						commit('SET_METER_TYPE_LIST', res)
						resolve(res)
					})
				}
			})
		},
		// 获取DTU类型列表
		getDtuTypeList({ state, commit, rootGetters }) {
			return new Promise(resolve => {
				if (state.dtuTypeList) {
					resolve(state.dtuTypeList)
				} else {
					apiDtuTypeList({
						tenantId: rootGetters.tenant.id,
					}).then((res = []) => {
						commit('SET_DTU_TYPE_LIST', res)
						resolve(res)
					})
				}
			})
		},
		// 获取行政区域级联数据（区县）
		getRegionData({ state, commit, rootGetters }) {
			const regionCode = rootGetters?.tenant?.address?.city || ''
			return new Promise(resolve => {
				if (state.regionCode === regionCode && state.regionData) {
					resolve(state.regionData?.records || [])
				} else {
					apiGetRegion({ regionCode }).then((res = []) => {
						commit('SET_REGION_CODE', regionCode)
						commit('SET_REGION_DATA', res)
						resolve(res?.records || [])
					})
				}
			})
		},
		/**
		 * 获取街道或小区信息
		 * @param {integer} level 区域等级，4-街道，6-小区  required
		 * @param {string} regionCode 区县编码
		 * @param {string} streetCode 街道编码
		 * @param {string} addressAreaName 小区名称
		 * @param {string} orgCode 组织机构编码
		 * @param {string} tenantId 租户id
		 * @param {boolean} isJoin 自定义参数-是否需要拼接街道和小区
		 * @returns Array
		 */
		getAreaData({ rootGetters }, params) {
			const tenantId = rootGetters?.tenant?.id
			const { userLevel, orgCode } = rootGetters?.userInfo || {}
			return new Promise(resolve => {
				params = {
					level: 6,
					size: 9999,
					status: 1, //只筛选启用状态的小区
					...params,
				}
				const { isJoin } = params
				delete params.isJoin
				if (userLevel == 0) params.orgCode = orgCode
				if (!isBlank(tenantId)) params.tenantId = tenantId
				apiGetArea(params).then(({ records = [] }) => {
					if (isJoin) {
						records = records.map(item => {
							const { streetName = '', addressAreaName = '' } = item
							const streetArea = streetName ? streetName + addressAreaName : addressAreaName
							return {
								...item,
								streetArea,
							}
						})
					}
					resolve(records)
				})
			})
		},
		/**
		 * 查询账项
		 * @param {integer} billItemId 费用类型值,id
		 * @param {integer} generateType 费用生成方式（1-按量生成，2-按次生成）
		 * @param {integer} isSurcharge 是否附加费（1-是，0-否）
		 * @param {string} itemName 账项名称
		 * @param {integer} itemStatus 状态（1-在用，0-作废）
		 * @param {integer} itemType 账项类别（1-水费，2-营业费）
		 * @returns Array
		 */
		getBillItems({ state, commit, rootGetters }, args) {
			return new Promise(resolve => {
				if (!args && state.billItems?.length) {
					resolve(state.billItems)
				} else {
					let obj = {
						current: 1,
						size: 1000,
						tenantId: rootGetters.tenant.id, //租户id
					}
					const { branchCompanyOrgCode, userLevel } = rootGetters.userInfo
					if (userLevel) {
						obj['branchCompanyOrgCode'] = branchCompanyOrgCode //分公司编码-租户级别传值
					}
					// 保证自定义传参可覆盖默认参数
					let params = Object.assign(obj, {
						...args,
					})
					apiGetAccountItems(params).then(({ records = [] }) => {
						if (!args) {
							commit('SET_BILL_ITEMS', records)
						}
						resolve(records)
					})
				}
			})
		},
		// 获取其他费用
		getCostType({ commit, dispatch, rootGetters }) {
			return new Promise(resolve => {
				const realm = rootGetters?.tenant?.realm || 'gas'
				if (realm === 'gas') {
					const costType = rootGetters?.dataList?.costType || []
					commit('SET_COST_TYPE', costType)
					resolve(costType)
				} else {
					dispatch('getBillItems').then(data => {
						const result = data.map(item => {
							return {
								sortValue: String(item.billItemId),
								sortName: item.itemName,
								...item,
							}
						})
						commit('SET_COST_TYPE', result)
						resolve(result)
					})
				}
			})
		},
		// 获取已授予和已撤销的设备类型列表
		getMeterTypeListAll({ state, commit }, args) {
			return new Promise(resolve => {
				if (state.meterTypeListAll) {
					resolve(state.meterTypeListAll)
				} else {
					apiGetMeterTypeList({
						type: 1,
						...args,
					}).then((res = {}) => {
						commit('SET_METER_TYPE_LIST_ALL', res.records || [])
						resolve(res.records || [])
					})
				}
			})
		},
	},
}
