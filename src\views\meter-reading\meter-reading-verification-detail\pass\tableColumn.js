export function getColumn() {
	return [
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'virtualMeterTypeDesc',
			name: '表卡类型',
			tooltip: true,
		},
		{
			key: 'lastMeterReading',
			name: '上次指针',
			tooltip: true,
		},
		{
			key: 'curMeterReading',
			name: '本次指针',
			tooltip: true,
		},
		{
			key: 'useAmount',
			name: '本次水量',
			tooltip: true,
		},
		{
			key: 'checkStatusDesc',
			name: '抄表情况',
			tooltip: true,
		},
		{
			key: 'thisRecordDate',
			name: '抄表日期',
			minWidth: 180,
			tooltip: true,
		},
		{
			key: 'imageUrl',
			name: '抄表照片',
		},
		{
			key: 'longitudeLatitude',
			name: '抄表经纬度',
		},
		{
			key: 'meterReadingStaffName',
			name: '抄表员',
			tooltip: true,
		},
		{
			key: 'readingStatusDesc',
			name: '抄表状态',
			tooltip: true,
		},
		{
			key: 'billTime',
			name: '开账时间',
			minWidth: 180,
			tooltip: true,
		},
		{
			key: 'deal',
			name: '操作',
			fixed: 'right',
			width: 120,
		},
	]
}
