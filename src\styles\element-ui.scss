/* element-ui 样式修改 */

/* el-message */
.el-message {
	height: 52px;
	box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.2);
	border: none !important;
	border-radius: 3px !important;
	justify-content: center;
	.el-message__icon,
	.iconfontCis,
	.el-message__content {
		color: #fff !important;
	}
	.el-message__icon,
	.iconfontCis {
		font-size: 20px;
		margin-right: 8px;
	}
	&.el-message--success {
		background: $base-color-green;
	}
	&.el-message--info {
		background: $base-color-blue;
	}
	&.el-message--warning {
		background: $base-color-yellow;
	}
	&.el-message--error {
		background: $base-color-red;
	}
}

/* $confirm --> el-message-box--center */
.gc-message-box.el-message-box--center {
	position: absolute;
	top: 120px;
	left: 50%;
	transform: translateX(-50%);
	padding-bottom: 20px;
	.el-message-box__header {
		padding: 20px 24px;
	}
	.el-message-box__status {
		padding-right: 6px;
		transform: translateY(0);
	}
	.el-message-box__title {
		justify-content: flex-start;
	}
	.el-message-box__headerbtn {
		top: 20px;
		right: 24px;
	}
	.el-message-box__content {
		text-align: left;
		padding: 0 24px;
	}
	.el-message-box__message p {
		color: #666;
		line-height: 21px;
	}
	.el-message-box__btns {
		text-align: right;
		padding: 31px 24px 0;
		.el-button {
			padding: 0 24px;
			height: 32px;
			border-radius: 16px;
			border: 1px solid #d8d8d8;
			box-sizing: border-box;
		}
		button:nth-child(2) {
			border: none;
			background: $base-color-blue;
		}
	}
}

.el-dialog__footer {
	.el-button--small.is-round {
		width: 80px;
	}
}

.el-table__body-wrapper {
	&::-webkit-scrollbar {
		width: 6px;
		background-color: #fff;
		height: 6px;
	}
	&::-webkit-scrollbar-track {
		width: 6px;
	}
	&::-webkit-scrollbar-thumb {
		width: 6px;
		background-color: rgba(0, 0, 0, 0.1);
		border-radius: 3px;
		cursor: pointer;
		transition: 0.3s;
	}
	&::-webkit-scrollbar-thumb:hover {
		cursor: pointer;
		background-color: rgba(0, 0, 0, 0.2);
	}
}
.el-table__fixed::before,
.el-table__fixed-right::before {
	height: 0 !important;
}
.el-table {
	tr.current-row {
		position: relative;
		td:first-of-type::before {
			content: '';
			width: 4px;
			height: 100%;
			position: absolute;
			top: 0;
			left: 0;
			z-index: 9;
		}
	}
	tr.current-row td:first-of-type::before {
		background: #2e7fff;
	}
}

.dialog-form-s {
	.el-select--small {
		width: 100%;
	}
	.el-row {
		margin: 0 !important;
	}
	.el-form-item__label {
		line-height: normal !important;
		padding-bottom: 5px !important;
		height: auto !important;
		font-weight: 600 !important;
	}
	.el-form-item {
		margin-bottom: 15px;
	}
	.el-form-item__error {
		padding-top: 0;
	}
}

.el-table--border {
	border: none;
	&::after {
		width: 0 !important;
	}
}
.el-table::before {
	left: 0;
	bottom: 0;
	width: 100%;
	height: 0 !important;
}

// 表单全局样式-UI设计规范
.el-form {
	.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label,
	.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label {
		position: relative;
		&:before {
			position: absolute;
			left: -10px;
			top: 50%;
			transform: translateY(-50%);
		}
	}
	&.el-initial {
		.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label,
		.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label {
			position: initial;
			&:before {
				position: initial;
			}
		}
	}
	.el-form-item__label {
		padding-right: 20px;
		// width: 104px;
		height: 36px;
		font-size: 14px;
		font-family: SourceHanSansCN-Regular, SourceHanSansCN;
		font-weight: 400;
		color: #4e4e4e;
		line-height: 21px;
		word-break: break-all;
		word-wrap: break-word;
		box-sizing: border-box;
		&:before {
			margin-right: 7px;
		}
	}
	.el-input.is-disabled .el-input__inner,
	.el-textarea.is-disabled .el-textarea__inner {
		background: #f7f7f7;
		border: 1px solid #f7f7f7;
	}
	.el-input__inner,
	.el-textarea__inner {
		color: #4e4e4e;
		border: 1px solid #d9d9d9;
	}
}
// 首页公告弹框
.el-message-box__wrapper {
	.home-notice {
		position: relative;
		box-sizing: border-box;
		width: 640px;
		height: 210px;
		background: url('~@/assets/images/bg/notice-bg.png') no-repeat;
		padding: 0 98px 0 216px;
		border: 0;
		.el-message-box__header,
		.el-message-box__content,
		.el-message-box__btns {
			padding: 0;
		}
		.el-message-box__content {
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 20px 0;
		}
		.el-message-box__container {
			height: 100%;
		}
		.el-message-box__message {
			display: flex;
			font-size: 16px;
			line-height: 24px;
			color: #fff;
			height: 100%;
			p {
				height: 100%;
				display: flex;
				align-content: center;
				justify-content: center;
				flex-direction: column;
				padding-right: 5px;
				overflow-y: auto;
			}
			div {
				height: 100%;
				padding-right: 5px;
				overflow-y: auto;
			}
		}
		.el-message-box__close {
			position: absolute;
			top: 5px;
			right: -92px;
			display: inline-block;
			font-size: 16px;
			background: rgba(255, 255, 255, 0.6);
			border-radius: 50%;
		}
		.noticer {
			text-align: right;
		}
	}
	.longtxt {
		.el-message-box__message {
			p {
				justify-content: flex-start;
			}
		}
	}
	.cust-dialog {
		width: 900px;
		height: 524px;
		border: none;
		.el-message-box__title {
			color: #333;
			font-size: 22px;
		}
		.el-message-box__header {
			padding-top: 164px;
			padding-bottom: 20px;
		}
		.el-message-box__headerbtn {
			top: 34px;
			right: -40px;
			background: #ccc;
			border-radius: 50%;
			width: 18px;
			.el-message-box__close {
				color: #f5f9ff;
			}
		}
		.el-message-box__content {
			font-size: 16px;
			color: #666;
		}
	}
	.relax {
		padding: 0 430px 70px 60px;
		background: url('~@/assets/images/bg/relax.png') no-repeat;
		background-size: contain;
		.el-message-box__headerbtn {
			right: -410px;
		}
	}
	.life-tips {
		padding: 0 60px 70px 430px;
		background: url('~@/assets/images/bg/life-tips.png') no-repeat;
		background-size: contain;
	}
}

// 下拉菜单
.el-dropdown-menu__item {
	font-size: 14px !important;
	min-width: 116px;
}

button {
	&.el-button--primary,
	&.el-button--danger {
		&.is-plain {
			background: transparent;
		}
	}

	&.el-button.is-disabled,
	&.el-button.is-disabled:hover,
	&.el-button.is-disabled:focus {
		background: #f7f7f7;
		border: 1px solid #f7f7f7;
		color: #d1d1d1;
	}
}

.base-bold {
	@include base-bold;
}

.el-textarea {
	.el-input__count {
		background: transparent !important;
		bottom: -25px !important;
	}
}
.el-button {
	height: 32px;
	.iconfont {
		font-size: 10px;
		padding-right: 5px;
	}
}

.el-image {
	.el-image__placeholder {
		width: 32px;
		height: 32px;
		background: url('~@/assets/images/loading.gif') no-repeat 50% 50% !important;
		background-size: 50% !important;
	}
}
