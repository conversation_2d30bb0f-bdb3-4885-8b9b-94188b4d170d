// 多标签状态管理
const state = () => ({
	visitedRoutes: [],
})
const getters = {
	visitedRoutes: state => state.visitedRoutes,
}
const mutations = {
	// 添加标签页
	ADD_VISITED_ROUTE(state, route) {
		const target = state.visitedRoutes.find(item => item.path === route.path)
		if (target) Object.assign(target, route)
		else state.visitedRoutes.push(Object.assign({}, route))
	},
	// 删除当前标签页
	DEL_VISITED_ROUTE(state, path) {
		state.visitedRoutes.splice(
			state.visitedRoutes.findIndex(_ => _.path === path),
			1,
		)
	},
	// 删除全部标签页
	DEL_ALL_VISITED_ROUTES(state) {
		state.visitedRoutes = state.visitedRoutes.filter(item => item.meta.noClosable)
	},
}
const actions = {
	// 添加标签页
	addVisitedRoute({ commit }, route) {
		commit('ADD_VISITED_ROUTE', route)
	},
	// 删除当前标签页
	delVisitedRoute({ commit }, path) {
		commit('DEL_VISITED_ROUTE', path)
	},
	// 删除全部标签页
	delAllVisitedRoutes({ commit }) {
		commit('DEL_ALL_VISITED_ROUTES')
	},
}
export default { state, getters, mutations, actions }
