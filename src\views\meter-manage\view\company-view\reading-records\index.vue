<template>
	<div class="container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch">
					<i class="iconfontCis icon-small-search"></i>
					筛选
				</el-button>
				<el-button @click="handleReset">
					<i class="iconfontCis icon-reset"></i>
					重置
				</el-button>
			</el-form-item>
		</GcFormSimple>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handleChangePage"
			/>
		</div>
	</div>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getStaffMap } from '@/api/meterReading.api.js'
import { apiGetMeterReadingList3 } from '@/api/meterManage.api.js'
export default {
	props: {
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		const _t = this
		const pickerOptions = {
			shortcuts: [
				{
					text: '最近一年',
					onClick(picker) {
						const end = new Date(_t.dayjs().endOf('year').format('YYYY-MM-DD'))
						const start = new Date(_t.dayjs().startOf('year').format('YYYY-MM-DD'))
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近三年',
					onClick(picker) {
						const end = new Date(_t.dayjs().endOf('year').format('YYYY-MM-DD'))
						const start = new Date(_t.dayjs().subtract(2, 'year').startOf('year').format('YYYY-MM-DD'))
						picker.$emit('pick', [start, end])
					},
				},
			],
		}
		return {
			formData: {
				modifyTime: [
					this.dayjs().subtract(2, 'year').startOf('year').format('YYYY-MM-DD'),
					this.dayjs().endOf('year').format('YYYY-MM-DD'),
				],
				meterReadingStaffId: '',
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '抄表日期',
					prop: 'modifyTime',
					attrs: {
						type: 'daterange',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						style: {
							width: '250px',
						},
						pickerOptions,
					},
				},
				{
					type: 'el-select',
					label: '抄表员',
					prop: 'meterReadingStaffId',
					options: [],
					attrs: {
						style: {
							width: '150px',
						},
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '90px',
				rules: {
					modifyTime: [{ required: true, message: '请选择抄表日期', trigger: 'change' }],
				},
			},
			columns: [
				{
					key: 'thisRecordDate',
					name: '操作时间',
					tooltip: true,
					minWidth: 190,
				},
				{
					key: 'meterReadingStaffName',
					name: '抄表员',
					tooltip: true,
				},
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'addressFullName',
					name: '地址',
					tooltip: true,
				},
				{
					key: 'lastMeterReading',
					name: '上次指针数',
					tooltip: true,
				},
				{
					key: 'curMeterReading',
					name: '本次指针数',
					tooltip: true,
				},
				{
					key: 'useAmount',
					name: '用水量',
					tooltip: true,
				},
				{
					key: 'checkStatusDesc',
					name: '抄表情况',
					tooltip: true,
				},
				{
					key: 'billTime',
					name: '生成账单时间',
					tooltip: true,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
		}
	},
	watch: {
		tabData: {
			handler() {
				this.getStaffMapData()
			},
			deep: true,
		},
	},
	methods: {
		async getStaffMapData() {
			const orgCode = (this.tabData.archives && this.tabData.archives.orgCode) || ''
			try {
				const res = await getStaffMap({
					orgCode,
				})
				this.formItems[1].options = res.map(item => {
					const { staffId, staffName, staffPhone } = item
					return {
						value: staffId,
						label: staffName,
						staffPhone,
					}
				})
			} catch (error) {
				console.error(error)
				this.formItems[1].options = []
			}
		},
		async getList() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) {
				return
			}
			this.loading = true
			try {
				const extractedData = Object.assign({}, ...Object.values(this.tabData))
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
					archivesId: extractedData?.archivesId,
				})
				if (formParams.modifyTime && formParams.modifyTime.length > 1) {
					formParams.readDateBegin = this.dayjs(formParams.modifyTime[0]).format('YYYY-MM-DD')
					formParams.readDateEnd = this.dayjs(formParams.modifyTime[1]).format('YYYY-MM-DD')
					delete formParams.modifyTime
				}

				const { records, total } = await apiGetMeterReadingList3(formParams)
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleSearch() {
			this.handleChangePage({ page: 1 })
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.handleSearch()
		},
	},
}
</script>
