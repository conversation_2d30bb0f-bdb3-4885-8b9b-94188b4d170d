<template>
	<div class="page-layout">
		<template v-if="queryMode">
			<div class="page-left">
				<gc-model-header
					class="info-title"
					title="查询模板"
					:icon="require(`@/assets/images/icon/title-multi-check.png`)"
				/>
				<div class="list-box" v-loading="treeLoading">
					<div
						v-show="queryTemplateList.length > 0"
						class="list-item"
						v-for="(item, index) in queryTemplateList"
						:key="item.id"
						:class="{ active: listActive === index }"
						@click="handleTemplateClick(index)"
					>
						<div class="label">{{ item.templateName }}</div>
						<div class="template-actions">
							<el-button
								type="text"
								icon="el-icon-edit"
								:disabled="loading"
								v-has="'cpm_report_unionTemplate_updateName'"
								@click.native="handleRename(item)"
							></el-button>
							<el-button
								type="text"
								icon="el-icon-delete"
								:disabled="loading"
								v-has="'cpm_report_unionTemplate_delete'"
								@click.native="handleDeleteTemplate(item)"
							></el-button>
						</div>
					</div>
					<gc-empty v-show="queryTemplateList.length === 0" />
				</div>
				<div class="list-actions">
					<el-button
						type="primary"
						size="small"
						icon="el-icon-plus"
						v-has="'cpm_report_unionTemplate_add'"
						@click="handleAddTemplate"
					>
						新增查询模板
					</el-button>
				</div>
			</div>
			<div v-show="queryMode" class="page-right">
				<div v-has="'cpm_report_unionTemplate_add'" class="right-top">
					<el-button
						v-has="'cpm_report_unionTemplate_export_union-data-record-excel'"
						type="primary"
						:disabled="!tableData.length"
						@click="handleExport"
					>
						导出
					</el-button>
					<el-button
						v-has="'cpm_report_unionTemplate_update'"
						:disabled="!queryTemplateList[listActive]"
						@click="updateTemplateConfig"
					>
						<i class="el-icon-setting el-icon--right"></i>
						更改查询设置
					</el-button>
				</div>
				<GcTable
					ref="tableRef"
					:loading="loading"
					:columns="columns"
					:table-data="tableData"
					:page-size="pageData.size"
					:total="pageData.total"
					:current-page="pageData.current"
					showPage
					@current-page-change="handlePageChange"
				/>
			</div>
		</template>
		<template v-else>
			<TemplateConfig
				:updateMode="updateMode"
				:insertMode="insertMode"
				:templateId="currentTemplateId"
				:data="currentTemplateData"
				@close="handleTemplateConfigClose"
				@refresh="handleRefresh"
			></TemplateConfig>
		</template>
	</div>
</template>

<script>
import axios from 'axios'
import { isBlank } from '@/utils/validate.js'
import {
	queryQueryTemplateList,
	executeTemplateQuery,
	exportTemplateQueryData,
	updateQueryTemplateName,
	deleteQueryTemplate,
} from '@/api/statisticsManage.api'

import TemplateConfig from './TemplateConfig.vue'
import { exportBlob } from '@/utils'

export default {
	name: 'ReportConfig',
	components: { TemplateConfig },
	data() {
		return {
			// 左侧列表
			treeLoading: false,
			updateMode: false,
			insertMode: false,
			queryMode: true,
			listActive: null,
			queryTemplateList: [],
			// 右侧列表
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			// 新增查询模板弹窗
			editTemplate: 'add',
			showUpdate: false,
			templateData: {},
		}
	},
	computed: {
		currentTemplateId() {
			if (this.listActive === null) return null
			const tl = this.queryTemplateList
			if (!tl.length) return null
			return tl[this.listActive] ? tl[this.listActive].id : null
		},
		currentTemplateName() {
			if (this.listActive === null) return null
			const tl = this.queryTemplateList
			if (!tl.length) return null
			return tl[this.listActive] ? tl[this.listActive].templateName : null
		},
		currentTemplateData() {
			let { listActive, queryTemplateList } = this
			queryTemplateList = queryTemplateList || []
			const item = queryTemplateList[listActive]
			return item
		},
		columns() {
			const { currentTemplateData } = this
			if (currentTemplateData) {
				const { templateDisplay } = currentTemplateData
				try {
					return JSON.parse(templateDisplay || '[]')
				} catch (e) {
					console.log(e)
					return []
				}
			}
			return []
		},
	},
	watch: {
		columns() {
			this.$nextTick(() => {
				if (this.$refs.tableRef) {
					this.$refs.tableRef.doLayout()
				}
			})
		},
	},
	beforeRouteEnter(to, from, next) {
		next(vm => {
			vm.$nextTick(() => {
				vm.getTemplateList()
			})
		})
	},
	methods: {
		handleTemplateClick(index) {
			this.insertMode = false
			this.queryMode = true
			this.updateMode = false
			if (this.listActive !== index) {
				this.listActive = index
				this.tableData = []
				this.getList(1)
			}
		},
		typeInputValidator(value) {
			if (isBlank(value)) return '请输入查询模板名称'
			if (value.length > 16) return '模板名称不能超过16位字符'
		},
		handleTemplateConfigClose() {
			this.insertMode = false
			this.queryMode = true
			this.updateMode = false
		},
		handleRefresh() {
			if (this.insertMode) {
				this.listActive = null
			}
			this.handleTemplateConfigClose()

			this.tableData = []
			this.getTemplateList()
			this.getList(1)
		},

		// 新增查询模板
		handleAddTemplate() {
			this.insertMode = true
			this.queryMode = false
			this.updateMode = false
		},
		// 重命名
		handleRename(data) {
			this.$prompt('查询模板名称', '重命名', {
				customClass: 'type-message-box',
				inputValue: data.categoryName,
				closeOnClickModal: false,
				inputPlaceholder: '请输入查询模板名称',
				inputValidator: this.typeInputValidator,
			}).then(async ({ value }) => {
				await updateQueryTemplateName({
					id: this.currentTemplateId,
					templateName: value,
				})
				this.$message.success('重命名成功')
				this.getTemplateList(this.listActive)
			})
		},
		// 删除查询模板
		handleDeleteTemplate(data) {
			this.$confirm('确定要删除该查询模板吗？').then(async () => {
				await deleteQueryTemplate({ id: data.id, deleted: 1 })
				this.$message.success('删除成功')
				this.getTemplateList()
			})
		},
		// 获取左侧查询模板列表
		async getTemplateList(active) {
			this.treeLoading = true
			try {
				const data = await queryQueryTemplateList()
				this.queryTemplateList = data || []

				if (active !== undefined) {
					this.listActive = active
				}
				if (this.queryTemplateList.length > 0 && active !== undefined) {
					this.getList(1)
				}
			} catch (error) {
				console.error(error)
				this.queryTemplateList = []
				// this.listActive = 9999
			} finally {
				this.treeLoading = false
			}
		},
		// 右侧列表
		async getList(curPage) {
			if (curPage) {
				this.pageData.current = curPage
			}
			if (this.currentTemplateId === null) {
				return
			}
			try {
				const { current, size } = this.pageData
				this.loading = true
				const { total = 0, records = [] } = await executeTemplateQuery({
					size,
					current,
					templateId: this.currentTemplateId,
				})
				this.pageData.total = total
				this.tableData = records
				this.loading = false
			} catch (error) {
				if (axios.isCancel(error)) {
					console.error(error)
				} else {
					this.tableData = []
					this.pageData = {
						current: 1,
						size: 10,
						total: 0,
					}
					this.loading = false
				}
			}
		},
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		// 编辑
		handleEdit(data) {
			this.editTemplate = 'edit'
			this.showUpdate = true
			this.$nextTick(() => {
				this.$refs.updateDialogRef.setFormData(data)
			})
		},
		// 导出
		async handleExport() {
			const maxLength = 600000
			const params = {
				templateId: this.currentTemplateId,
				current: this.pageData.current,
				size: this.pageData.total,
			}
			if (this.pageData.total > maxLength) {
				this.$message.error('导出数量不能超过60万条')
				return
			}
			this.loading = true
			await exportTemplateQueryData(params).then(res => {
				// 从响应头中获取文件名
				let fileName = this.currentTemplateName // 默认文件名

				// 现在res是完整的响应对象，可以访问headers
				if (res.headers && res.headers['content-disposition']) {
					const contentDisposition = res.headers['content-disposition']
					const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
					if (fileNameMatch && fileNameMatch[1]) {
						fileName = fileNameMatch[1].replace(/['"]/g, '')
						console.log('fileName source=', fileName)
						// 处理URL编码的文件名
						try {
							fileName = decodeURIComponent(fileName)
						} catch (e) {
							// 如果解码失败，使用原始文件名
							console.warn('Failed to decode filename:', fileName)
						}
					}
				}

				// 使用响应数据
				exportBlob(res.data, fileName)
				this.loading = false
			})
		},
		updateTemplateConfig() {
			this.insertMode = false
			this.queryMode = false
			this.updateMode = true
		},
	},
}
</script>

<style lang="scss">
.type-message-box {
	position: absolute;
	top: 120px;
	left: 50%;
	transform: translateX(-50%);
	padding-bottom: 20px;

	.el-message-box__message {
		display: flex;
		align-items: center;

		&::before {
			content: '*';
			color: #ec6b60;
			margin-right: 4px;
		}
	}

	.el-message-box__input {
		padding-top: 6px;
	}
}
</style>
<style lang="scss" scoped>
.page-left {
	overflow: auto;
}

.right-top {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-bottom: 12px;
}

.info-title {
	height: auto;
	padding: 5px 0;
	margin-bottom: 12px;
}

.list-actions {
	margin-top: 12px;

	::v-deep .el-button {
		width: 100%;
	}
}

.icon-more {
	transform: rotate(90deg);
}

.list-box {
	flex: 1;
	overflow: auto;

	.list-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 42px;
		padding: 0 12px;
		cursor: pointer;

		.label {
			flex: 1;
			margin-right: 12px;
			@include text-overflow;
		}

		&.active {
			color: #2f87fe;
			background-color: rgba(196, 221, 255, 0.5);

			.el-dropdown {
				display: block;
			}
		}
	}

	.el-dropdown {
		display: none;
	}
}

::v-deep {
	.el-dropdown-menu__item {
		min-width: auto;
	}
}

.template-actions {
	opacity: 0;
	transition: opacity 0.1s;
	pointer-events: none;

	::v-deep {
		.el-button {
			font-size: 17px;
			padding: 5px 0;
		}
	}
}

.list-item:hover .template-actions {
	opacity: 1;
	pointer-events: auto;
}
</style>
