import service from './request'
import { CPM } from '@/consts/moduleNames'

//行政区域级联查询
export function apiGetRegion(parameter) {
	return service({
		url: CPM + '/region/children',
		method: 'GET',
		params: parameter,
	})
}
// 获取小区列表
export function apiGetArea(data) {
	return service({
		url: CPM + '/address-areas/list',
		method: 'POST',
		data: data,
	})
}
// 楼栋维护分页列表查询
export function apiGetBuildingList(data) {
	return service({
		url: CPM + '/area/queryBuilding',
		method: 'POST',
		data: data,
	})
}

// 批量删除楼栋
export function apiBatchDeleteBuilding(data) {
	return service({
		url: CPM + '/area/batchDeleteBuilding',
		method: 'POST',
		data: data,
	})
}
// 删除楼栋
export function apiDeleteBuilding(data) {
	return service({
		url: CPM + '/area/deleteBuilding',
		method: 'POST',
		data: data,
	})
}

// 新增单个楼栋
export function apiAddBuilding(data) {
	return service({
		url: CPM + '/area/addBuilding',
		method: 'POST',
		data: data,
	})
}
// 批量新增楼栋
export function apiBatchAddBuilding(data) {
	return service({
		url: CPM + '/area/batchAddBuilding',
		method: 'POST',
		data: data,
	})
}
// 修改楼栋
export function apiUpdateBuilding(data) {
	return service({
		url: CPM + '/area/updateBuilding',
		method: 'POST',
		data: data,
	})
}

// 启用禁用街道
export function apiUpdateStreetStatus(data) {
	return service({
		url: CPM + '/area/updateStreetStatus',
		method: 'POST',
		data: data,
	})
}

//小区管理分页列表查询
export function apiQueryNeighbourhood(data) {
	return service({
		url: CPM + '/area/queryNeighbourhood',
		method: 'POST',
		data: data,
	})
}

// 街道管理分页列表查询
export function apiQueryStreetPage(data) {
	return service({
		url: CPM + '/area/queryStreetPage',
		method: 'POST',
		data: data,
	})
}
// 区县管理列表查询
export function apiQueryCounty(data) {
	return service({
		url: CPM + '/region/countyList',
		method: 'POST',
		data: data,
	})
}

// 操作人
export function apiQueryAreaStaffList(data) {
	return service({
		url: CPM + '/area/queryStaffList',
		method: 'POST',
		data: data,
	})
}
// 新增小区
export function apiAddNeighbourhood(data) {
	return service({
		url: CPM + '/area/addNeighbourhood',
		method: 'POST',
		data: data,
	})
}
// 修改小区
export function apiUpdateNeighbourhood(data) {
	return service({
		url: CPM + '/area/updateNeighbourhood',
		method: 'POST',
		data: data,
	})
}

// 新增街道
export function apiAddStreet(data) {
	return service({
		url: CPM + '/area/addStreet',
		method: 'POST',
		data: data,
	})
}
// 修改街道
export function apiUpdateStreet(data) {
	return service({
		url: CPM + '/area/updateStreet',
		method: 'POST',
		data: data,
	})
}

// 新增区县
export function apiAddCounty(data) {
	return service({
		url: CPM + '/region/addCounty',
		method: 'POST',
		data: data,
	})
}
// 修改区县
export function apiUpdateCounty(data) {
	return service({
		url: CPM + '/region/updateCounty',
		method: 'POST',
		data: data,
	})
}
//  小区操作记录列表查询:
export function apiQueryRecordPage(data) {
	return service({
		url: CPM + '/area/queryRecordPage',
		method: 'POST',
		data,
	})
}
// 街道 操作记录列表查询:
export function apiQueryStreetRecordPage(data) {
	return service({
		url: CPM + '/area/queryStreetRecordPage',
		method: 'POST',
		data,
	})
}
// 地址操作记录列表查询
export function apiQueryAddressRecordPage(data) {
	return service({
		url: CPM + '/newAddress/queryAddressRecordPage',
		method: 'POST',
		data: data,
	})
}
// 区县操作记录列表查询
export function apiQueryRegionRecordPage(data) {
	return service({
		url: CPM + '/region/queryRecordPage',
		method: 'POST',
		data: data,
	})
}

// 地址列表
export function apiQueryAddressPage(data) {
	return service({
		url: CPM + '/newAddress/queryAddressPage',
		method: 'POST',
		data: data,
	})
}

// 修改地址
export function apiUpdateAddress(data) {
	return service({
		url: CPM + '/newAddress/updateAddress',
		method: 'POST',
		data: data,
	})
}
// 地址批量创建-地址选择列表查询
export function apiQueryAreaPage(data) {
	return service({
		url: CPM + '/newAddress/queryAreaPage',
		method: 'POST',
		data: data,
	})
}
// 地址批量创建
export function apiBatchCreateAddress(data) {
	return service({
		url: CPM + '/newAddress/batchCreateAddress',
		method: 'POST',
		data: data,
	})
}

//街道、小区、楼栋下拉框
export function apiGetAddressAreaMap(data) {
	return service({
		url: CPM + '/area/queryAddressAreaMap',
		method: 'POST',
		data: data,
	})
}
