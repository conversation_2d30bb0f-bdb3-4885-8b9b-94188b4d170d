const path = require('path')
const resolve = dir => path.join(__dirname, dir)
const IS_PROD = ['production', 'prod'].includes(process.env.NODE_ENV)
const TerserPlugin = require('terser-webpack-plugin')
const { GitRevisionPlugin } = require('git-revision-webpack-plugin')
const gitRevisionPlugin = new GitRevisionPlugin()
const dayjs = require('dayjs')

const target = {
	auth: process.env.VUE_APP_PROXY_TARGET,
	cpm: process.env.VUE_APP_PROXY_TARGET,
	billing: process.env.VUE_APP_PROXY_TARGET,
	payment: process.env.VUE_APP_PROXY_TARGET,
	planCollection: process.env.VUE_APP_PROXY_TARGET,
	dlsw: 'http://121.196.214.189:8088',
}

module.exports = {
	publicPath: IS_PROD ? process.env.VUE_APP_API_BASE_URL : './', // 默认'/'，部署应用包时的基本 URL
	outputDir: process.env.outputDir || 'dist', // 'dist', 生产环境构建文件的目录
	assetsDir: '', // 相对于outputDir的静态资源(js、css、img、fonts)目录
	lintOnSave: !IS_PROD,
	runtimeCompiler: true, // 是否使用包含运行时编译器的 Vue 构建版本
	productionSourceMap: !IS_PROD, // 生产环境的 source map

	devServer: {
		open: true,
		compress: true,
		proxy: {
			'api/billing': {
				target: target.billing,
				changeOrigin: true,
				ws: false,
				pathRewrite: {
					'^/api': '',
				},
				secure: false,
			},
			'api/cpm': {
				target: target.cpm,
				changeOrigin: true,
				ws: false,
				pathRewrite: {
					'^/api': '',
				},
				secure: false,
			},
			'api/payment': {
				target: target.payment,
				changeOrigin: true,
				ws: false,
				pathRewrite: {
					'^/api': '',
				},
				secure: false,
			},
			'api/plan-collection': {
				target: target.planCollection,
				changeOrigin: true,
				ws: false,
				pathRewrite: {
					'^/api': '',
				},
				secure: false,
			},
			'api/v1': {
				target: target.cpm,
				changeOrigin: true,
				ws: false,
				pathRewrite: {
					'^/api': '',
				},
				secure: false,
			},
			'api/auth': {
				target: target.auth,
				changeOrigin: true,
				ws: false,
				pathRewrite: {
					'^/api': '',
				},
				secure: false,
			},
			'/dlsw': {
				target: target.dlsw,
				changeOrigin: true,
				ws: false,
				secure: false,
			},
		},
	},
	css: {
		requireModuleExtension: true,
		sourceMap: true,
		loaderOptions: {
			scss: {
				additionalData(content, loaderContext) {
					const { resourcePath, rootContext } = loaderContext
					const relativePath = path.relative(rootContext, resourcePath)
					if (relativePath.replace(/\\/g, '/') !== 'src/styles/variables/variables.scss')
						return '@import "~@/styles/variables/variables.scss";' + content
					return content
				},
			},
		},
	},
	chainWebpack: config => {
		// 若热更新失效，修复HMR
		config.resolve.symlinks(true)
		config.resolve.alias.set('@', resolve('src')).set('moment', resolve('dayjs'))
		// html注入变量信息
		config.plugin('html').tap(args => {
			args[0].minify = {
				removeComments: false,
			}
			args[0].banner = {
				date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
				branch: gitRevisionPlugin.branch(),
				commitHash: gitRevisionPlugin.commithash(),
				lastCommitDateTime: dayjs(gitRevisionPlugin.lastcommitdatetime()).format('YYYY-MM-DD HH:mm:ss'),
			}
			return args
		})
	},
	configureWebpack: config => {
		// webpack 配置
		// https://webpack.docschina.org/configuration/devtool/#production
		config.devtool = process.env.NODE_ENV == 'production' ? 'none' : 'eval'

		// 公共代码抽离
		config.optimization = {
			minimizer: [
				new TerserPlugin({
					terserOptions: {
						compress: {
							// drop_console: IS_PROD,
							drop_console: false,
							drop_debugger: IS_PROD,
						},
					},
				}),
			],
		}
	},
}
