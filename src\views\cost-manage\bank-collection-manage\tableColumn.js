export function getColumn() {
	return [
		{
			key: 'orgName',
			name: '营业分公司',
			tooltip: true,
		},
		{
			key: 'fileMonth',
			name: '送盘生成月',
			tooltip: true,
		},
		{
			key: 'channelDesc',
			name: '托收渠道',
			tooltip: true,
		},
		{
			key: 'sendFileNo',
			name: '文件编号',
			tooltip: true,
		},
		{
			key: 'billNum',
			name: '账单数',
			tooltip: true,
			align: 'right',
		},
		{
			key: 'billAmount',
			name: '总金额(元)',
			tooltip: true,
			align: 'right',
			render: function (h, row) {
				const amount = row.billAmount - 0
				return h('span', {}, amount.toFixed(2))
			},
		},
		{
			key: 'statusDesc',
			name: '送盘文件状态',
			tooltip: true,
		},
		{
			key: 'returnFileStatusDesc',
			name: '回盘文件状态',
			tooltip: true,
		},
		{
			key: 'returnAmount',
			name: '回盘总金额(元)',
			tooltip: true,
			align: 'right',
			render: function (h, row) {
				const amount = row.returnAmount - 0
				return h('span', {}, amount.toFixed(2))
			},
		},
		{
			key: 'returnDate',
			name: '回盘日期',
			tooltip: true,
		},
		{
			key: 'successNum',
			name: '已销账账单数',
			tooltip: true,
			align: 'right',
		},
		{
			key: 'successAmount',
			name: '已销账总金额(元)',
			tooltip: true,
			align: 'right',
			render: function (h, row) {
				const amount = row.successAmount - 0
				return h('span', {}, amount.toFixed(2))
			},
		},
		{
			key: 'operate',
			name: '操作',
			minWidth: 350,
			fixed: 'right',
		},
	]
}
