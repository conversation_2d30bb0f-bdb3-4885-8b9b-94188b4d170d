import service from './request'
import { PAYMENT } from '@/consts/moduleNames'

// 开票信息 购方信息列表
export function getInvoiceBuyerList(data) {
	return service({
		url: `${PAYMENT}/invoice-buyer/list`,
		method: 'post',
		data,
	})
}
// 开票记录 发票记录列表
export function getInvoiceRecordList(data) {
	return service({
		url: `${PAYMENT}/invoice/record-list`,
		method: 'post',
		data,
	})
}
// 获取发票PDF
export function getInvoicePdf(params) {
	return service({
		url: `${PAYMENT}/invoice/pdf`,
		method: 'get',
		params,
	})
}
// 获取发票信息详情
export function getInvoiceRecordDetail(params) {
	return service({
		url: `${PAYMENT}/invoice/details`,
		method: 'get',
		params,
	})
}
