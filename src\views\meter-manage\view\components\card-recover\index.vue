<template>
	<GcElDialog
		:show="isShow"
		title="表卡恢复登记"
		width="800px"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:newMeterInfo>
				<Search v-if="isShow" style="float: right" :active-tab="{ id: 3 }" @use="handleSearchMeter" />
			</template>
		</GcFormRow>
	</GcElDialog>
</template>

<script>
import { isBlank } from '@/utils/validate.js'
import { getFormItems } from './form.js'
import { ruleRequired, ruleMaxLength, RULE_STARTMETER_READING } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiGetMeterType, apiResumeArchives, apiResumeArchives3, apiResumeArchives4 } from '@/api/meterManage.api.js'
import Search from '../search/index.vue'

// 表具操作 装表
const OPERATOR_METER_TYPE_INSTALL = '2'
// 表具状态 恢复用水
const OPERATOR_METER_TYPE_RESTOR = '5'

export default {
	components: { Search },
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		data: {
			type: Object,
			default: () => ({}),
		},
		permissionCode: {
			type: String,
			default: 'cpm_archives_resume-archives',
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	watch: {
		isShow: {
			handler(val) {
				if (val) {
					this.initFormItem()
					this.formData.archivesIdentity = this.data.archivesIdentity
					this.formData.currentMeterNo = this.data.meterNo
					this.formData.resumePerson = this.$store.getters.userInfo.staffName
					this.formData.resumeDate = this.dayjs().format('YYYY-MM-DD')
					// 虚表不选择操作类型
					if (this.data?.virtualMeterType !== 1) {
						// 如果存在表号 则 认为是恢复用水
						if (this.data.meterNo) {
							this.formData.meterOperate = OPERATOR_METER_TYPE_RESTOR
						} else {
							this.formData.meterOperate = OPERATOR_METER_TYPE_INSTALL
						}
					}
				}
			},
		},
		'formData.meterOperate': {
			handler(value) {
				const { baseFormItems, newWaterMeterFormItems } = this
				if (value == 2) {
					this.formItems = baseFormItems.concat(newWaterMeterFormItems)
				} else {
					this.formItems = baseFormItems
				}

				this.formItems
					.filter(
						item =>
							![
								'archivesIdentity',
								'currentMeterNo',
								'resumeDate',
								'resumePerson',
								'meterOperate',
								'resumeReason',
							].includes(item.prop) && item.prop,
					)
					.forEach(item => {
						this.$set(this.formData, item.prop, '')
					})
			},
		},
	},
	data() {
		return {
			formData: {
				currentMeterNo: '',
			},
			formItems: [],
			formAttrs: {
				labelWidth: '110px',
				labelPosition: 'right',
				rules: {
					resumeDate: [ruleRequired('必填')],
					meterOperate: [ruleRequired('必填')],
					resumeReason: [ruleRequired('必填'), ruleMaxLength(64)],
					meterNo: [ruleRequired('必填'), ruleMaxLength(32)],
					meterTypeId: [ruleRequired('必填')],
					installationDate: [ruleRequired('必填')],
					meterWarehouseCode: [ruleMaxLength(32)],
					antiTheftCode: [ruleMaxLength(32)],
					meterModel: [ruleMaxLength(30)],
					caliber: [ruleMaxLength(16)],
					startMeterReading: [RULE_STARTMETER_READING],
					baseMeterNo: [ruleMaxLength(32)],
				},
			},
			meterTypeOptions: [],
			meterId: '',
		}
	},
	methods: {
		async _apiGetMeterType() {
			const res = await apiGetMeterType({
				tenantId: this.$store.getters.userInfo.tenantId,
			})

			this.meterTypeOptions = res.map(item => {
				return {
					value: item.meterTypeId,
					label: item.meterTypeName,
					...item,
				}
			})
		},
		initFormItem() {
			const { baseFormItems, newWaterMeterFormItems } = getFormItems(this)
			this.formItems = baseFormItems
			this.baseFormItems = baseFormItems
			this.newWaterMeterFormItems = newWaterMeterFormItems
			this.data?.virtualMeterType === 1 && this.formItems.splice(-2, 1)
			this.$nextTick(() => {
				this.$refs.formRef.clearValidate()
			})
		},
		handleSearchMeter(obj) {
			Object.keys(this.formData).forEach(key => {
				if (Object.prototype.hasOwnProperty.call(obj, key)) {
					if (key === 'startMeterReading' && !isBlank(obj.meterReading)) {
						this.formData[key] = obj.meterReading
					} else if (key !== 'resumePerson') {
						this.formData[key] = obj[key]
					}
				}
			})
			this.meterId = obj.meterId
		},
		handleClose() {
			this.handleReset()
			this.isShow = false
			this.meterId = ''
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			try {
				const formParams = trimParams(removeNullParams(this.formData))
				if (formParams.meterOperate == 2) {
					this.meterId && (formParams.meterId = this.meterId)
				} else if (formParams.currentMeterNo) {
					formParams.meterNo = formParams.currentMeterNo.trim()
					formParams.meterId = this.data.meterId
					delete formParams.currentMeterNo
				}
				formParams['meter'] = {}
				Object.keys(formParams).forEach(key => {
					if (
						![
							'archivesIdentity',
							'resumeDate',
							'resumePerson',
							'meterOperate',
							'resumeReason',
							'meter',
						].includes(key)
					) {
						formParams['meter'][key] = formParams[key]
						delete formParams[key]
					}
				})
				Object.assign(formParams, {
					archivesId: this.data?.archivesId,
				})
				const apiMethods = {
					'cpm_archives_resume-archives': apiResumeArchives,
					'cpm_archives_resume-archives3': apiResumeArchives3,
					'cpm_archives_resume-archives4': apiResumeArchives4,
				}
				await apiMethods[this.permissionCode](formParams)

				const successMessage =
					this.data.virtualMeterType === 1 ? '虚表表卡恢复正常，请至虚表管理页重新分配水量' : '恢复表卡成功'
				this.$message.success(successMessage)
				this.$emit('refresh')
				this.handleClose()
			} catch (error) {
				console.log(error)
			}
		},
		handleReset() {
			this.$refs.formRef.clearValidate()
			this.$refs.formRef.resetForm()
		},
	},
	mounted() {
		this._apiGetMeterType()
	},
}
</script>
