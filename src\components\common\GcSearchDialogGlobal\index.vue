<template>
	<el-dialog
		custom-class="gc-global-search"
		top="0"
		title=""
		:visible.sync="innerVisible"
		:width="dialogWidth"
		class="search-dialog"
		:class="{ 'show-list': newTableData.length }"
		:append-to-body="true"
		:close-on-click-modal="true"
		:show-close="false"
		@close="handleClose"
	>
		<div slot="title" class="box-tab">
			<div
				class="tab-item"
				v-for="(item, index) in searchCondition"
				:key="index"
				:class="[item.key == activeTab ? 'active' : '']"
				@click="changeTab(item.key)"
			>
				{{ item.label }}
			</div>
		</div>
		<div class="content" v-loading="innerloading">
			<el-form ref="searchInputForm" :model="searchInputForm" @submit.native.prevent>
				<div class="content-operation">
					<el-form-item prop="input" :rules="formRules">
						<el-input
							v-model="searchInputForm.input"
							:placeholder="activePlaceholder"
							@keyup.enter.native="handleSearch"
						>
							<i slot="suffix" @click="handleSearch">搜索</i>
							<i slot="prefix" class="el-input__icon el-icon-search"></i>
						</el-input>
					</el-form-item>
				</div>
			</el-form>
			<div class="archives-menu" v-show="newTableData.length">
				<div class="menu-list">
					<div class="menu-item" v-for="(item, index) in newTableData" :key="index">
						<detail-item :detailData="item" @close="handleClose"></detail-item>
					</div>
				</div>
				<div class="page-turn">
					<gc-pagination
						:total="pageData.total"
						:page-size="pageData.size"
						:current-page.sync="pageData.current"
						:show-page-sizes="true"
						@current-page-change="pageChange"
						isRightAlign
					></gc-pagination>
				</div>
			</div>
			<div class="archives-menu" v-show="newTableData.length == 0">
				<div class="menu-empty">
					<span>暂无数据</span>
				</div>
			</div>
		</div>
	</el-dialog>
</template>

<script>
import DetailItem from './DetailItem'
import { isBlank } from '@/utils/validate.js'
import { apiGetMeterCard } from '@/api/meterManage.api'
export default {
	name: 'GcSearchDialogGlobal',
	components: { DetailItem },
	props: {
		// 弹窗的显示/关闭
		dialogVisible: {
			type: Boolean,
			required: true,
			default: false,
		},
		// 弹窗内的搜索条件
		searchCondition: {
			type: Array,
			required: true,
			default: () => [],
		},
		// 弹窗当前所处的tab
		activeCondition: {
			type: String,
			required: true,
			default: () => '',
		},
		// 弹框宽度
		dialogWidth: {
			type: String,
			default: '407px',
		},
		loading: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			searchInputForm: {
				input: '', //输入框内容
			},
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			tableData: [],
			innerloading: false,
		}
	},
	computed: {
		formRules() {
			const current = this.searchCondition.find(item => item.key === this.activeCondition)
			return current?.rule || {}
		},
		innerVisible: {
			get: function () {
				return this.dialogVisible
			},
			set: function (val) {
				this.$emit('update:dialogVisible', val)
			},
		},
		activePlaceholder() {
			let str = ''
			if (this.searchCondition) {
				this.searchCondition.map(item => {
					if (item.key == this.activeTab) {
						str += '请输入' + item.label
					}
				})
			}
			return str
		},
		activeTab: {
			get: function () {
				return this.activeCondition
			},
			set: function (val) {
				this.$emit('update:active-condition', val)
			},
		},
		newTableData() {
			const defaultKey = 'certificateNo'
			const activeKey = this.keyMap[this.activeTab] || defaultKey

			return this.tableData.map(obj => {
				const item = Object.assign({}, ...Object.values(obj))
				const { address } = obj || {}
				const { addressFullName } = address || {}
				return {
					userType: item.userType,
					name: item[activeKey],
					list: [
						{
							key: 'archivesIdentity',
							label: '表卡编号',
							value: item.archivesIdentity,
						},
						{
							key: 'userName',
							label: '用户名称',
							value: item.userName,
						},
						{
							key: 'bookNo',
							label: '表册编号',
							value: item.bookNo,
						},
						{
							key: 'userMobile',
							label: '手机号',
							value: item.userMobile,
						},
						// {
						// 	key: 'certificateNo',
						// 	label: '证件号码',
						// 	value: item.certificateNo,
						// },
						{
							key: 'addressFullName',
							label: '地址',
							value: addressFullName,
						},
					].filter(field => field.key !== activeKey),
					archivesId: item.archivesId,
				}
			})
		},
		keyMap() {
			return this.searchCondition.reduce((map, condition) => {
				map[condition.key] = condition.key
				return map
			}, {})
		},
	},
	watch: {
		activeCondition(newVal) {
			this.activeTab = newVal
		},
	},
	methods: {
		async getList() {
			// 2025/07/10 14:21 jiew 需求:全局查询-与居民表卡管理权限/企业表卡管理权限关联 都没不展示 有一种权限只查对应权限的 全有不限制用户类型
			let permissions = this.$store.getters.permissions
			if (!permissions) {
				return
			}
			let hasResident = permissions.includes('cpm_archives_list')
			let hasCompany = permissions.includes('cpm_archives_list2')
			if (!hasResident && !hasCompany) {
				return
			}
			let flag = false
			let tip = ''
			this.innerloading = true
			const params = {
				current: this.pageData.current,
				size: this.pageData.size,
			}
			params[this.activeTab] = this.searchInputForm.input
			if (!hasResident || !hasCompany) {
				if (hasResident) {
					params.userType = 3
				} else if (hasCompany) {
					params.userType = 4
				}
			}
			if (
				!flag &&
				this.activeTab === 'userName' &&
				this.searchInputForm.input.length < 2 &&
				!isBlank(this.searchInputForm.input)
			) {
				flag = true
				tip = '用户名称搜索关键字请至少输入两位'
				this.innerloading = false
			}
			if (flag) return this.$message.error(tip)
			try {
				const { records, total } = await apiGetMeterCard(params)
				if (!records.length) {
					this.$message.info('查询结果为空')
					return
				}
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.innerloading = false
			}
		},
		changeTab(val) {
			this.activeTab = val
			this.handleReset()
		},
		pageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		handleSearch() {
			this.pageChange({ page: 1 })
		},
		handleClose() {
			this.innerVisible = false
			this.handleReset()
		},
		handleReset() {
			this.searchInputForm.input = ''
			this.tableData = []
		},
	},
}
</script>
<style lang="scss" scoped>
@import './index.scss';
.gc-global-search {
	.menu-item:not(:first-child) {
		padding-top: 8px !important;
	}
}

.search-dialog {
	/deep/.el-dialog {
		max-height: 90%;
		display: flex;
		flex-direction: column;
		.el-dialog__body {
			flex: 1;
			display: flex;
			flex-direction: column;
			overflow: hidden;
			.content {
				display: flex;
				flex-direction: column;
				overflow: hidden;
			}
			.archives-menu {
				flex: 1;
				display: flex;
				flex-direction: column;
				overflow: hidden;
			}
			.menu-list {
				flex: 1;
				overflow: auto;
			}
		}
	}
}
</style>
