<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:operate="{ row }">
					<!-- 表卡状态已停用展示 -->
					<el-button
						v-has="'cpm_archives_enable2'"
						v-show="row.archivesStatus === 2"
						type="text"
						@click="handleEnableArchives(row)"
					>
						恢复用水
					</el-button>
				</template>
			</GcTable>
		</div>
		<!-- 恢复用水弹窗 -->
		<MeterInfoWaterRecover :show.sync="showDialog" :detailData="currentRow" @refresh="handleSearch" />
	</div>
</template>

<script>
import { getColumn } from './tableColumn'
import { getFormItems } from './formItem.js'
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiGetRegion, apiGetAddressAreaMap } from '@/api/addressManage.api.js'
import { apiGetStopUsingList } from '@/api/meterManage.api.js'
import MeterInfoWaterRecover from '@/views/meter-manage/view/components/meterInfo-water-recover/index.vue'
export default {
	name: 'CutOffManage',
	components: { MeterInfoWaterRecover },
	data() {
		return {
			// 左侧表单
			formData: {
				orgCode: '',
				regionCode: '',
				streetCode: '',
				areaCode: '',
				buildingCode: '',
				archivesIdentity: '',
				userType: '',
			},
			columns: getColumn(this),
			formItems: [],
			formAttrs: {
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
				},
			},
			// 右侧列表
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			showDialog: false,
			currentRow: {},
		}
	},
	computed: {
		orgOptions() {
			return this.formItems[0]?.options
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.$nextTick(() => {
						this.handleSearch()
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		// 区县
		async _getRegionData() {
			const { records } = await apiGetRegion({
				regionCode: 2102,
			})
			const obj = this.formItems.find(item => item.prop === 'regionCode')
			if (!obj) return
			obj.options = records.map(item => {
				return {
					value: item.regionCode,
					label: item.regionName,
				}
			})
		},
		// 街道、小区、楼栋
		async _getAddressAreaMap(value, key) {
			const data = await apiGetAddressAreaMap({
				parentCode: value,
			})
			const obj = this.formItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = data.map(item => {
				let label = item.addressAreaName
				if (key === 'buildingCode') {
					label = label + item.unit
				}
				return {
					value: item.addressAreaCode,
					label,
				}
			})
		},
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, { current, size })
				const { total = 0, records = [] } = await apiGetStopUsingList(formParams)

				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 改变地址
		async handleChangeAddress(value, type) {
			const streetIndex = this.formItems.findIndex(item => item.prop === 'streetCode')
			const communityIndex = this.formItems.findIndex(item => item.prop === 'areaCode')
			const buildingIndex = this.formItems.findIndex(item => item.prop === 'buildingCode')

			if (type === 'regionCode') {
				this.formData.streetCode = ''
				this.formData.areaCode = ''
				this.formData.buildingCode = ''
				this.formItems[streetIndex].options = []
				this.formItems[communityIndex].options = []
				this.formItems[buildingIndex].options = []
				if (value) {
					this._getAddressAreaMap(value, 'streetCode')
				}
			} else if (type === 'streetCode') {
				this.formItems[communityIndex].options = []
				this.formItems[buildingIndex].options = []
				this.formData.areaCode = ''
				this.formData.buildingCode = ''
				if (value) {
					this._getAddressAreaMap(value, 'areaCode')
				}
			} else if (type === 'areaCode') {
				this.formItems[buildingIndex].options = []
				this.formData.buildingCode = ''
				if (value) {
					this._getAddressAreaMap(value, 'buildingCode')
				}
			}
		},
		initFormItem() {
			this._getRegionData()
			this.formItems = getFormItems(this)
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleReset() {
			this.initFormItem()
			this.$refs.formRef.clearValidate()
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.currentRow = {}
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.handlePageChange({ page: 1 })
		},
		handleEnableArchives(row) {
			this.currentRow = row
			this.showDialog = true
		},
	},
	mounted() {
		this.initFormItem()
	},
}
</script>
