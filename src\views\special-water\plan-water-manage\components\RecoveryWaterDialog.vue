<template>
	<gc-el-dialog
		:show="isShow"
		title="追缴水量"
		custom-top="120px"
		width="960px"
		@open="handleOpen"
		@close="handleClose"
	>
		<div class="data-box">
			<div class="data-item">
				<div class="label">企业编号：</div>
				<div class="value">{{ formData.enterpriseNumber || '--' }}</div>
			</div>
			<div class="data-item">
				<div class="label">企业名称：</div>
				<div class="value">{{ formData.enterpriseName || '--' }}</div>
			</div>
			<div class="data-item">
				<div class="label">计划年：</div>
				<div class="value">{{ formData.planYear || '--' }}</div>
			</div>
			<div class="data-item">
				<div class="label">计划用水量：</div>
				<div class="value">{{ formData.planUsageAmt || '--' }}</div>
			</div>
			<div class="data-item">
				<div class="label">实际已用水量：</div>
				<div class="value">{{ formData.yearUsageAmt || '--' }}</div>
			</div>
		</div>
		<GcFormSimple
			ref="formRef"
			v-model="formData"
			:formItems="formItems"
			:formAttrs="formAttrs"
			style="padding-top: 12px"
		>
			<template #placeholder>
				<div style="width: 300px"></div>
			</template>
		</GcFormSimple>
		<GcTable ref="tableRef" :loading="loading" :columns="columns" :table-data="tableData"></GcTable>

		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button :loading="saveLoading" @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { accAdd } from '@/utils/calc'
import { apiEffectivePrice } from '@/api/meterManage.api.js'
import { queryArchives, planUsageRecovery } from '@/api/specialWater.api'
import { isBlank } from '@/utils/validate'

/**
 * 计算可配置阶梯水费
 * @param {number} actualUsage - 实际用水量
 * @param {number} planUsage - 计划用水量
 * @param {number} basePrice - 基础水价（元/吨）
 * @param {Array<{range: [number, number], multiplier: number}>} tiers - 阶梯配置
 * @returns {{
 *   details: Array<{range: string, water: number, cost: number}>,
 *   total: number
 * }}
 */
function calculateWaterBill(actualUsage, planUsage, basePrice, tiers) {
	const excessWater = actualUsage - planUsage
	const result = {
		details: [],
		total: 0,
	}

	if (excessWater <= 0) {
		// 未超量时返回空阶梯
		tiers.forEach(tier => {
			result.details.push({
				range: `${tier.range[0]}%-${tier.range[1]}%`,
				water: 0,
				cost: 0,
			})
		})
		return result
	}

	let remainingWater = excessWater
	let previousLimit = 0
	tiers.forEach(tier => {
		const [minPercent, maxPercent] = tier.range
		// 计算当前阶梯的水量范围（吨）
		const minWater = planUsage * (minPercent / 100)
		const maxWater = planUsage * (maxPercent / 100)
		const tierWaterLimit = maxWater - minWater

		// 当前阶梯实际用水量
		const tierWater = Math.min(remainingWater, tierWaterLimit)
		const tierCost = tierWater * basePrice * tier.multiplier

		result.details.push({
			range: `${minPercent}%-${maxPercent}%`,
			water: parseFloat(tierWater.toFixed(2)),
			cost: parseFloat(tierCost.toFixed(2)),
			multiplier: tier.multiplier,
		})

		remainingWater -= tierWater
		result.total += tierCost
		previousLimit = maxWater
	})

	// 处理超过最高阶梯的情况
	if (remainingWater > 0) {
		const lastTier = tiers[tiers.length - 1]
		const extraCost = remainingWater * basePrice * lastTier.multiplier

		result.details.push({
			range: `>${lastTier.range[1]}%`,
			water: parseFloat(remainingWater.toFixed(2)),
			cost: parseFloat(extraCost.toFixed(2)),
			multiplier: lastTier.multiplier,
		})
		result.total += extraCost
	}

	result.total = parseFloat(result.total.toFixed(2))
	return result
}

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},

		// 当前选择的追收抄表表卡编号
		currentArchivesNoComp() {
			const target = this.formItems[0].options.find(item => item.value === this.formData.archivesId)
			return target ? target.label : ''
		},
		// 当前选择的价格对象数据
		currentPriceDataComp() {
			const target = this.formItems[2].options.find(item => {
				return item.priceId === this.formData.priceId
			})
			return target
		},
		tableData() {
			const DEFAULT_TIERS = [
				{ range: [0, 20], multiplier: 1 }, // 0-20% 部分
				{ range: [20, 30], multiplier: 2 }, // 20-30% 部分
				{ range: [30, Infinity], multiplier: 3 }, // 30%+ 部分
			]
			const { currentArchivesNoComp, currentPriceDataComp, formData } = this
			const { yearUsageAmt, planUsageAmt } = formData

			if (currentArchivesNoComp && currentPriceDataComp && !isBlank(yearUsageAmt) && !isBlank(planUsageAmt)) {
				const { singlePrice } = currentPriceDataComp
				const result = calculateWaterBill(yearUsageAmt, planUsageAmt, singlePrice, DEFAULT_TIERS)
				const { details } = result
				return details.reduce((acc, item) => {
					const { water, cost, multiplier } = item

					if (water) {
						acc.push({
							archivesNo: currentArchivesNoComp,
							useAmount: water,
							usePrice: singlePrice,
							multiple: multiplier,
							usAmt: cost,
						})
					}

					return acc
				}, [])
			}
			return []
		},
	},
	data() {
		return {
			formData: {
				enterpriseName: '',
				enterpriseNumber: '',
				planYear: '',
				planUsageAmt: '',
				yearUsageAmt: '',
				archivesId: '',
				priceId: '',
				onlyRecoveryWaterFees: 1,
			},
			formItems: [
				{
					type: 'el-select',
					label: '追收表卡：',
					prop: 'archivesId',
					options: [],
					attrs: {
						filterable: true,
						clearable: false,
						placeholder: '请选择追收表卡',
					},
				},
				{
					type: 'slot',
					slotName: 'placeholder',
				},
				{
					type: 'el-select',
					label: '价格：',
					prop: 'priceId',
					options: [],
					attrs: {
						clearable: false,
						placeholder: '请选择价格',
					},
				},
			],
			formAttrs: {
				inline: true,
				labelPosition: 'left',
				labelWidth: '110px',
				rules: {
					archivesId: [{ required: true, message: '请选择追收表卡', trigger: 'change' }],
					priceId: [{ required: true, message: '请选择价格', trigger: 'change' }],
				},
			},
			loading: false,
			columns: [
				{
					key: 'archivesNo',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'useAmount',
					name: '水量',
					tooltip: true,
				},
				{
					key: 'usePrice',
					name: '价格',
				},
				{
					key: 'multiple',
					name: '倍数',
					tooltip: true,
				},
				{
					key: 'usAmt',
					name: '水费',
					tooltip: true,
				},
			],

			saveLoading: false,
		}
	},
	created() {},
	methods: {
		// 获取追收表卡下拉数据
		async getArchives() {
			try {
				const res = await queryArchives({
					enterpriseNum: this.formData.enterpriseNumber,
				})
				const data = res || []
				this.formItems[0].options = data.map(item => {
					return {
						label: item.archivesNo,
						value: item.archivesId,
					}
				})
			} catch (error) {
				console.error(error)
				this.formItems[0].options = []
			}
		},
		// 获取价格下拉数据
		async getPriceList() {
			try {
				const res = await apiEffectivePrice()
				const data = res || []
				this.formItems[2].options = data.reduce((acc, item) => {
					const { priceCode, priceName, priceId, billingTypeId } = item

					// 判断是否是单一价格
					if (billingTypeId === 1) {
						acc.push({
							label: `(${priceCode}) ${priceName}`,
							value: priceId,
							...item,
						})
					}

					return acc
				}, [])
			} catch (error) {
				console.error(error)
				this.formItems[2].options = []
			}
		},
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.saveLoading = true
				try {
					const {
						archivesId,
						priceId,
						recordId,
						overPlanAmt: useAmount,
						planUsageAmt,
						yearUsageAmt,
					} = this.formData
					debugger
					await planUsageRecovery({
						archivesId,
						onlyRecoveryWaterFees: 1,
						priceId,
						priceVersion: this.currentPriceDataComp.priceVersion,
						recordId,
						useAmount,
						plannedWater: planUsageAmt,
						actualWater: yearUsageAmt,
					})

					this.$message.success('追缴水量成功')
					this.$emit('success')
					this.isShow = false
				} catch (error) {
					console.error(error)
				} finally {
					this.saveLoading = false
				}
			}
		},
		handleOpen() {
			this.getPriceList()
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.formData = {
				enterpriseName: '',
				enterpriseNumber: '',
				planYear: '',
				planUsageAmt: '',
				yearUsageAmt: '',
				archivesId: '',
				priceId: '',
				onlyRecoveryWaterFees: 1,
			}
			this.tableData = []
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
			this.getArchives()
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.el-dialog__body {
		height: 560px;
	}
	.el-select {
		width: 300px !important;
	}
	.gc-table {
		height: 240px;
	}
}

.data-box {
	.data-item {
		display: flex;
		align-items: center;
		margin-bottom: 12px;
		.label {
			width: 100px;
			margin-right: 10px;
			line-height: 1.5;
		}
		.value {
			flex: 1;
		}
	}
}
.el-tag {
	margin: 2px 0;
	& + .el-tag {
		margin-left: 4px;
	}
}
</style>
