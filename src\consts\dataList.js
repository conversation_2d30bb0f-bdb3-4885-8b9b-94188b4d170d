// 数据字典，前端写死一份，登录后静默请求最新的
// 0811 更新valvaState

export default {
	tenantStatus: [
		{ sortValue: '0', sortName: '禁用' },
		{ sortValue: '1', sortName: '可用' },
	],
	meterLogCategory: [
		{ sortValue: '1', sortName: '异常事件日志' },
		{ sortValue: '2', sortName: '运行事件日志' },
		{ sortValue: '3', sortName: '通信日志' },
		{ sortValue: '4', sortName: '系统异常日志' },
		{ sortValue: '5', sortName: '通信网络情况事件日志' },
		{ sortValue: '6', sortName: '关阀事件日志' },
	],
	source: [
		{ sortValue: 'open_archives', sortName: '开户' },
		{ sortValue: 'recharge_opreation', sortName: '充值' },
		{ sortValue: 'adjust_price', sortName: '批量调价' },
		{ sortValue: 'change_price', sortName: '单表换价' },
		{ sortValue: 'valve_on', sortName: '开阀' },
		{ sortValue: 'valve_off', sortName: '关阀' },
		{ sortValue: 'stop_device', sortName: '停表' },
		{ sortValue: 'start_device', sortName: '启用表' },
		{ sortValue: 'destroy_device', sortName: '销档' },
		{ sortValue: 'web_send', sortName: '参数设置' },
		{ sortValue: 'change_device', sortName: '换表' },
		{ sortValue: 'change_device_type', sortName: '更换表类型' },
		{ sortValue: 'alarm_config_set', sortName: '告警配置' },
		{ sortValue: 'meter_collection', sortName: '表具采集' },
		{ sortValue: 'update_archives', sortName: '修改档案' },
		{ sortValue: 'adjust_price_assist', sortName: '调价-卡控' },
	],
	timeUnitCode: [
		{ sortValue: '0', sortName: '时间单位为小时,循环周期为自然天' },
		{ sortValue: '1', sortName: '时间单位为天,循环周期为自然月' },
		{ sortValue: '2', sortName: '时间单位为月,循环周期为顺延年' },
		{ sortValue: '3', sortName: '时间单位为天,循环周期为顺延年' },
	],
	archivesOperatorType: [
		{ sortValue: '0', sortName: '建档' },
		{ sortValue: '1', sortName: '点火' },
		{ sortValue: '2', sortName: '过户' },
		{ sortValue: '3', sortName: '销档' },
		{ sortValue: '4', sortName: '停用' },
		{ sortValue: '5', sortName: '启用' },
		{ sortValue: '6', sortName: '换表' },
		{ sortValue: '7', sortName: '换价' },
		{ sortValue: '8', sortName: '更换模组' },
		{ sortValue: '9', sortName: '修改档案' },
		{ sortValue: '10', sortName: '调价' },
		{ sortValue: '11', sortName: '完善档案' },
		{ sortValue: '12', sortName: '更换dtu' },
		{ sortValue: '13', sortName: '开阀' },
		{ sortValue: '14', sortName: '关阀' },
	],
	paymentType: [
		{ sortValue: '0', sortName: '预付费' },
		{ sortValue: '1', sortName: '后付费' },
	],
	calculateModel: [
		{ sortValue: '0', sortName: '标况' },
		{ sortValue: '1', sortName: '工况' },
	],
	billingType: [
		{ sortValue: '1', sortName: '单一计费' },
		{ sortValue: '2', sortName: '阶梯计费' },
		{ sortValue: '3', sortName: '分时计费' },
	],
	alarmSort: [
		{ sortValue: '1', sortName: '表端' },
		{ sortValue: '2', sortName: '系统端' },
		{ sortValue: '3', sortName: '程序端' },
	],
	gasDirection: [
		{ sortValue: '1', sortName: '左进气' },
		{ sortValue: '2', sortName: '右进气' },
		{ sortValue: '3', sortName: '其他' },
	],
	costType: [
		{ sortValue: '11', sortName: '气费' },
		{ sortValue: '21', sortName: '清水费' },
		{ sortValue: '22', sortName: '污水费' },
		{ sortValue: '91', sortName: '垃圾费' },
		{ sortValue: '92', sortName: '开户费' },
		{ sortValue: '93', sortName: '过户费' },
		{ sortValue: '94', sortName: 'IC卡费' },
		{ sortValue: '95', sortName: '安装费' },
		{ sortValue: '96', sortName: '改装费' },
		{ sortValue: '97', sortName: '维修费' },
		{ sortValue: '98', sortName: '其他费' },
		{ sortValue: '99', sortName: '补差价' },
	],
	archiveState: [
		{ sortValue: '0', sortName: '已建档' },
		{ sortValue: '1', sortName: '在用' },
		{ sortValue: '2', sortName: '已停用' },
		{ sortValue: '3', sortName: '已销档' },
		{ sortValue: '4', sortName: '待完善' },
	],
	meterFlow: [
		{ sortValue: '1', sortName: 'G1.6' },
		{ sortValue: '2', sortName: 'G2.5' },
		{ sortValue: '3', sortName: 'G4' },
		{ sortValue: '4', sortName: 'G6' },
		{ sortValue: '5', sortName: 'G10' },
		{ sortValue: '6', sortName: 'G16' },
		{ sortValue: '7', sortName: 'G25' },
		{ sortValue: '8', sortName: 'G40' },
		{ sortValue: '9', sortName: 'G65' },
		{ sortValue: '10', sortName: 'G100' },
		{ sortValue: '11', sortName: 'DN25' },
		{ sortValue: '12', sortName: 'DN32' },
		{ sortValue: '13', sortName: 'DN40' },
		{ sortValue: '14', sortName: 'DN50' },
		{ sortValue: '15', sortName: 'DN65' },
		{ sortValue: '16', sortName: 'DN80' },
		{ sortValue: '17', sortName: 'DN100' },
		{ sortValue: '18', sortName: 'DN150' },
		{ sortValue: '19', sortName: 'DN200' },
	],
	action: [
		{ sortValue: '1', sortName: 'POST' },
		{ sortValue: '2', sortName: 'GET' },
		{ sortValue: '3', sortName: 'PUT' },
		{ sortValue: '4', sortName: 'DELETE' },
	],
	alarmLevel: [
		{ sortValue: '1', sortName: '低' },
		{ sortValue: '2', sortName: '中' },
		{ sortValue: '3', sortName: '高' },
	],
	rechargeResult: [
		{ sortValue: '0', sortName: '待通讯' },
		{ sortValue: '1', sortName: '成功' },
		{ sortValue: '2', sortName: '已取消' },
		{ sortValue: '3', sortName: '失败' },
	],
	rechargeOptType: [
		{ sortValue: '1', sortName: '收费' },
		{ sortValue: '2', sortName: '取消收费' },
		{ sortValue: '3', sortName: '补气' },
		{ sortValue: '4', sortName: '取消补气' },
	],
	authType: [
		{ sortValue: '0', sortName: '无' },
		{ sortValue: '1', sortName: 'HMAC' },
	],
	resident: [
		{ sortValue: '7', sortName: '居民' },
		{ sortValue: '8', sortName: '壁挂炉' },
		{ sortValue: '9', sortName: '低保' },
		{ sortValue: '10', sortName: '煤改气' },
	],
	business: [
		{ sortValue: '1', sortName: '商业' },
		{ sortValue: '2', sortName: '工业' },
		{ sortValue: '3', sortName: '供热' },
		{ sortValue: '4', sortName: '公福' },
		{ sortValue: '5', sortName: '车用' },
		{ sortValue: '6', sortName: '自用' },
	],
	notifyEvent: [
		{ sortValue: '1', sortName: '燃气泄漏' },
		{ sortValue: '2', sortName: '电量不足' },
		{ sortValue: '3', sortName: '余量不足一级报警' },
		{ sortValue: '4', sortName: '余量不足二级报警' },
		{ sortValue: '5', sortName: '储值' },
		{ sortValue: '6', sortName: '欠费报警' },
		{ sortValue: '7', sortName: '储值取消' },
		{ sortValue: '8', sortName: '阀门直通' },
		{ sortValue: '9', sortName: '防拆报警' },
		{ sortValue: '10', sortName: '温度超上限' },
		{ sortValue: '11', sortName: '温度超下限' },
		{ sortValue: '12', sortName: '压力超上限' },
		{ sortValue: '13', sortName: '压力超下限' },
	],
	payMode: [
		{ sortValue: '1', sortName: '现金' },
		{ sortValue: '2', sortName: '划卡' },
		{ sortValue: '3', sortName: '账户扣款' },
		{ sortValue: '4', sortName: '支票' },
		{ sortValue: '5', sortName: '微信' },
		{ sortValue: '6', sortName: '支付宝' },
		{ sortValue: '7', sortName: '账单' },
		{ sortValue: '9', sortName: '银行转账' },
		{ sortValue: '10', sortName: '扫码' },
		{ sortValue: '21', sortName: '其他' },
		{ sortValue: '11', sortName: '微信-公众号' },
		{ sortValue: '12', sortName: '微信-生活缴费' },
	],
	valveState: [
		{ sortValue: '0', sortName: '关' },
		{ sortValue: '1', sortName: '开' },
		{ sortValue: '5', sortName: '无阀门' },
		{ sortValue: '2', sortName: '正在关' },
		{ sortValue: '3', sortName: '正在开' },
		{ sortValue: '4', sortName: '故障' },
		{ sortValue: '6', sortName: '普关' },
	],
	billType: [
		{ sortValue: '0', sortName: '表端结算' },
		{ sortValue: '1', sortName: '系统结算' },
		{ sortValue: '2', sortName: '不结算' },
	],
	disableType: [
		{ sortValue: '1', sortName: '欠费停用' },
		{ sortValue: '2', sortName: '自报停用' },
		{ sortValue: '3', sortName: '拆迁停用' },
		{ sortValue: '4', sortName: '表破损停用' },
	],
	compareRule: [
		{ sortValue: '0', sortName: '等于' },
		{ sortValue: '1', sortName: '大于' },
		{ sortValue: '2', sortName: '小于' },
		{ sortValue: '3', sortName: '大于等于' },
		{ sortValue: '4', sortName: '小于等于' },
	],
	commandSort: [
		{ sortValue: '0', sortName: '阀控类' },
		{ sortValue: '1', sortName: '读取类' },
		{ sortValue: '2', sortName: '设置类' },
	],
	costOperationType: [
		{ sortValue: '1', sortName: '充值' },
		{ sortValue: '2', sortName: '取消充值' },
		{ sortValue: '3', sortName: '补气' },
		{ sortValue: '4', sortName: '取消补气' },
		{ sortValue: '5', sortName: '其他费用' },
		{ sortValue: '6', sortName: '取消其他费用' },
	],
	installPosition: [
		{ sortValue: '1', sortName: '厨房' },
		{ sortValue: '2', sortName: '过道' },
		{ sortValue: '3', sortName: '阳台' },
		{ sortValue: '4', sortName: '户外' },
		{ sortValue: '5', sortName: '其他' },
	],
	alarmStatus: [
		{ sortValue: '0', sortName: '告警中' },
		{ sortValue: '1', sortName: '已恢复' },
	],
	deviceCategory: [
		{ sortValue: '0', sortName: '远传表' },
		{ sortValue: '1', sortName: '计量数据采集器' },
	],
	meterLogCategoryTenant: [{ sortValue: '6', sortName: '关阀事件日志' }],
	commandStatus: [
		{ sortValue: '0', sortName: '待发送' },
		{ sortValue: '1', sortName: '成功' },
		{ sortValue: '2', sortName: '失败' },
		{ sortValue: '3', sortName: '已取消' },
		{ sortValue: '5', sortName: '待执行' },
	],
	icReissueOptType: [
		{ sortValue: '1', sortName: '丢气补气' },
		{ sortValue: '2', sortName: '换表补气' },
		{ sortValue: '3', sortName: '赠送补气' },
	],
	meterCategory: [
		{ sortValue: '0', sortName: '普表' },
		{ sortValue: '1', sortName: 'IC卡表' },
		{ sortValue: '2', sortName: '远传表' },
		{ sortValue: '3', sortName: 'IC卡远传表' },
	],
	sendType: [
		{ sortValue: '1', sortName: '验证码' },
		{ sortValue: '2', sortName: '短信通知' },
		{ sortValue: '3', sortName: '推广短信' },
		{ sortValue: '4', sortName: '群发助手' },
	],
	costRecordStatus: [
		{ sortValue: '0', sortName: '待收费' },
		{ sortValue: '1', sortName: '已收费' },
		{ sortValue: '2', sortName: '取消收费' },
	],
	payRecordStatus: [
		{ sortValue: '1', sortName: '已收费' },
		{ sortValue: '2', sortName: '取消收费' },
	],
	payChannel: [
		{ sortValue: '1', sortName: '柜台收费' },
		{ sortValue: '2', sortName: '外部充值' },
	],
	userType: [
		{ sortValue: '1', sortName: '工商' },
		{ sortValue: '2', sortName: '民用' },
	],
	operatorType: [
		{ sortValue: '1', sortName: '批量建档' },
		{ sortValue: '2', sortName: '批量导入用户和地址' },
		{ sortValue: '3', sortName: '批量导入表具和地址' },
		{ sortValue: '4', sortName: '批量充值' },
		{ sortValue: '5', sortName: '批量补气' },
		{ sortValue: '6', sortName: '批量登记其他费用' },
	],
	certificateType: [
		{ sortValue: '1', sortName: '军官证' },
		{ sortValue: '2', sortName: '护照' },
		{ sortValue: '3', sortName: '房产证号' },
		{ sortValue: '9', sortName: '其他' },
	],
	invoiceState: [
		{ sortValue: '0', sortName: '未开票' },
		{ sortValue: '1', sortName: '已开票' },
		{ sortValue: '2', sortName: '已红冲' },
		{ sortValue: '3', sortName: '开票中' },
		{ sortValue: '9', sortName: '开票失败' },
	],
	readWay: [
		{ sortValue: '0', sortName: '入户' },
		{ sortValue: '1', sortName: '远传' },
	],
}
