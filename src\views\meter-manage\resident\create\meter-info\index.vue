<template>
	<div class="container-wrapper">
		<GcModelHeader
			class="info-title"
			title="表具信息"
			:icon="require('@/assets/images/icon/title-common-parameters.png')"
		>
			<template slot="right">
				<Search style="float: right" :active-tab="{ id: 3 }" @use="handleSearchMeter" />
			</template>
		</GcModelHeader>
		<div class="container">
			<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<template v-slot:otherInfo>
					<h5 class="gap-title">其它信息</h5>
				</template>
				<template v-slot:recordSeq>
					<el-input v-model="formData.recordSeq" placeholder="请输入">
						<img slot="append" src="@/assets/images/icon/get-num.svg" @click="_apiGetRecordSeq" />
					</el-input>
				</template>
			</GcFormRow>
			<div class="button-group">
				<button class="gc-button gc-button-three" type="button" @click="handleReset">重 置</button>
				<button class="gc-button gc-button-two" type="button" @click="changeTab('userInfo')">上一项</button>
				<button class="gc-button gc-button-two" type="button" @click="changeTab('recordsInfo')">下一项</button>
			</div>
		</div>
	</div>
</template>
<script>
import { isBlank } from '@/utils/validate.js'
import { RULE_STARTMETER_READING, ruleMaxLength } from '@/utils/rules'
import { apiGetMeterType, apiGetRecordSeq } from '@/api/meterManage.api.js'
import { getFormItems } from './meterForm.js'
import Search from '@/views/meter-manage/view/components/search'
export default {
	name: '',
	props: {
		activeTab: {
			type: String,
			default: '',
		},
		detailData: {
			type: Object,
			default: () => {},
		},
	},
	components: { Search },
	data() {
		return {
			formData: {
				meterNo: '',
				meterTypeId: '',
				installationDate: '',
				meterWarehouseCode: '',
				manufacturerName: '',
				useYears: '',
				antiTheftCode: '',
				ranges: '',
				meterModel: '',
				caliber: '',
				startMeterReading: '',
				installPosition: '',
				tableWellLocation: '',
				baseMeterNo: '',
				bookNo: '',
				bookId: '',
				orgCode: '',
				recordSeq: '',
				remark: '',
				meterId: '',
			},
			formItems: getFormItems(this),
			formAttrs: {
				labelWidth: '110px',
				labelPosition: 'right',
				rules: {
					meterNo: [{ required: true, message: '必填', trigger: 'blur' }, ruleMaxLength(32)],
					meterTypeId: [{ required: true, message: '必填', trigger: 'blur' }],
					installationDate: [{ required: true, message: '必填', trigger: 'blur' }],
					meterWarehouseCode: [ruleMaxLength(32)],
					antiTheftCode: [ruleMaxLength(32)],
					meterModel: [ruleMaxLength(30)],
					caliber: [ruleMaxLength(10)],
					startMeterReading: [RULE_STARTMETER_READING],
					tableWellLocation: [ruleMaxLength(64)],
					baseMeterNo: [ruleMaxLength(32)],
					recordSeq: [
						{
							pattern: /^(?:[0-9]{1,7}|9999999)$/,
							message: '请输入0-9999999的整数',
							trigger: '',
						},
					],
					remark: [ruleMaxLength(64)],
				},
			},
			meterTypeList: [],
		}
	},
	watch: {
		formData: {
			handler: 'validateForm',
			deep: true,
		},
		detailData: {
			handler(v) {
				if (v) {
					this.assignForm(v)
				}
			},
			deep: true,
		},
	},
	methods: {
		async _apiGetRecordSeq() {
			try {
				const data = await apiGetRecordSeq({
					bookId: this.formData.bookId,
				})
				if (data) {
					this.formData.recordSeq = data
				} else {
					this.$message.error('获取册内序号失败')
				}
			} catch (error) {
				console.log(error)
			}
		},
		_apiGetMeterType() {
			apiGetMeterType({
				tenantId: this.$store.getters.userInfo.tenantId,
			})
				.then(res => {
					this.formItems[1].options = res.map(item => {
						return {
							value: item.meterTypeId,
							label: item.meterTypeName,
							...item,
						}
					})
				})
				.catch(() => {})
		},
		assignForm(obj) {
			Object.keys(this.formData).forEach(key => {
				if (Object.prototype.hasOwnProperty.call(obj, key)) {
					this.formData[key] = obj[key]
				}
			})
		},
		async validateForm() {
			const valid = await this.$refs.formRef.validate()
			this.$emit('getValid', 'meterInfo', valid)
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.validateForm()
		},
		changeTab(v) {
			this.$emit('changeTab', v)
		},
		handleSearchMeter(obj) {
			Object.keys(this.formData).forEach(key => {
				if (Object.prototype.hasOwnProperty.call(obj, key)) {
					if (key === 'startMeterReading' && !isBlank(obj.meterReading)) {
						this.formData[key] = obj.meterReading
					} else {
						this.formData[key] = obj[key]
					}
				}
			})
		},
	},
	mounted() {
		this.validateForm()
	},
}
</script>

<style lang="scss" scoped>
.container-wrapper {
	height: 100%;
}
.container {
	padding-right: 20px;
	height: calc(100% - 60px);
	overflow-y: auto;
	overflow-x: hidden;
}
.gap-title {
	padding: 0 20px;
	color: #222222;
	font-size: 14px;
	font-weight: bold;
}
.button-group {
	margin-top: 20px;
	padding: 0 20px;
	.gc-button {
		margin-right: 8px;
	}
}
</style>
