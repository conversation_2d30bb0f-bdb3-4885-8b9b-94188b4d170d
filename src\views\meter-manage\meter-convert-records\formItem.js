export function getFormItems(_this) {
	return [
		{
			type: 'el-select',
			label: '营业分公司',
			prop: 'orgCode',
			options: _this.$store.getters.orgList,
		},
		{
			type: 'el-input',
			label: '表卡编号（新）',
			prop: 'archivesIdentity',
		},
		{
			type: 'el-input',
			label: '表卡编号（旧）',
			prop: 'oldArchivesIdentity',
		},
		{
			type: 'el-select',
			label: '表卡档案类型（新）',
			prop: 'newUserType',
			options:
				_this.$store.getters?.dataList?.userType?.map(item => {
					return {
						label: item.sortName,
						value: item.sortValue,
					}
				}) || [],
		},
		{
			type: 'el-select',
			label: '表卡档案类型（旧）',
			prop: 'oldUserType',
			options:
				_this.$store.getters?.dataList?.userType?.map(item => {
					return {
						label: item.sortName,
						value: item.sortValue,
					}
				}) || [],
		},
		{
			type: 'el-date-picker',
			label: '操作时间',
			prop: 'modifyTime',
			attrs: {
				type: 'daterange',
				startPlaceholder: '开始日期',
				endPlaceholder: '结束日期',
				valueFormat: 'yyyy-MM-dd',
			},
		},
	]
}
