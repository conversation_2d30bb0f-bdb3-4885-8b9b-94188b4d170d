<template>
	<!-- 同一卡号、多条IC卡档案 -->
	<gc-el-dialog :show="innerVisible" title="IC卡所属档案" width="920px" @close="handleClose">
		<div class="box-table">
			<gc-table ref="table" :columns="columns" :table-data="tableData" @click="handleRowClick" />
		</div>
		<template #footer>
			<button class="gc-button gc-button-three" @click="handleClose">关 闭</button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { joinAddress } from '@/utils'
import { isBlank } from '@/utils/validate'
import { apiIcCardList } from '@/api/icManage'

export default {
	name: 'IcMultiDialog',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		icInfo: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			columns: [
				{ key: 'icCardNo', name: 'IC卡号' },
				{ key: 'userName', name: '用户名称' },
				{ key: 'meterNo', name: '表具编号' },
				{ key: 'meterTypeName', name: '表类型' },
				{
					key: 'addressName',
					name: '表具地址',
					render: (h, row) => {
						const addressFull = joinAddress(row)
						return h('span', {}, addressFull)
					},
				},
				{
					key: 'archivesStatus',
					name: '档案状态',
					minWidth: 90,
					render: (h, row) => {
						const status = row.archivesStatus
						return h(
							'span',
							{ class: this.getDotClass(status) },
							this.$options.filters.filterSortName(status, this.archiveState),
						)
					},
				},
			],
			tableData: [],
		}
	},
	computed: {
		innerVisible: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		archiveState() {
			return this.$store.getters.dataList.archiveState
		},
	},
	mounted() {
		this.getCardList()
	},
	methods: {
		// 获取ic卡列表
		getCardList() {
			const { cardNo, companyCode } = this.icInfo
			apiIcCardList({
				makeCardFlag: '1',
				size: 100,
				icCardNo: cardNo,
				companyCode,
			})
				.then(({ records = [] }) => {
					this.$listeners.controlLoading(false)
					if (!records.length) {
						this.$message.info('查询结果为空')
						return
					}
					this.tableData = records
				})
				.catch(() => {
					this.$listeners.controlLoading(false)
					this.$message.error('查询失败')
				})
		},
		handleClose() {
			this.innerVisible = false
		},
		getDotClass(status) {
			if (isBlank(status)) return 'dot'
			const classArr = ['dot blue', 'dot green', 'dot', 'dot', 'red']
			return classArr[+status]
		},
		handleRowClick({ row }) {
			this.$emit('row-click', row)
		},
	},
}
</script>
<style lang="scss" scoped>
::v-deep .gc-dialog-custom .el-dialog__body {
	min-height: 268px;
	max-height: calc(100vh - 352px);
	position: relative;

	.box-table {
		padding: 15px 24px;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
	}

	.el-table__row {
		cursor: pointer;
	}
}
</style>
