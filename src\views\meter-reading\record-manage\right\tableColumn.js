export function getColumn(_) {
	return [
		{
			key: 'bookNo',
			name: '表册编号',
			tooltip: true,
		},
		{
			key: 'bookTypeDesc',
			name: '册本类型',
			tooltip: true,
		},
		{
			key: 'alleyName',
			name: '坊别',
			tooltip: true,
		},
		{
			key: 'meterReadingStaffName',
			name: '抄表员',
			tooltip: true,
		},
		{
			key: 'meterReadingStaffPhone',
			name: '抄表员电话',
			tooltip: true,
		},
		{
			key: 'meterReadingCycleDesc',
			name: '抄表周期',
			tooltip: true,
		},
		{
			key: 'archivesNoRange',
			name: '抄表范围',
			tooltip: true,
		},
		{
			key: 'meterNum',
			name: '实表表卡',
			tooltip: true,
			align: 'right',
		},
		{
			key: 'virtualMeterNum',
			name: '虚表表卡',
			tooltip: true,
			align: 'right',
		},
		{
			key: 'effectiveNum',
			name: '可抄表数',
			tooltip: true,
			align: 'right',
		},
		{
			key: 'noOverNum',
			name: '未移交表卡数',
			tooltip: true,
			align: 'right',
		},
		{
			hide: !_.$has([
				'plan-collection_meterReadingBook_updateBook',
				'plan-collection_meterReadingBook_deleteBook',
				'plan-collection_report_bookArchivesList_export_excel',
				'plan-collection_meterReadingTask_createFirstTask',
			]),
			key: 'deal',
			name: '操作',
			width: 270,
			fixed: 'right',
		},
	]
}
