<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-11 15:00:52
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 16:27:53
-->
<template>
	<div class="virtual-meter">
		<div class="title">
			<span>虚表抄表记录</span>
			<span>实表表卡编号：{{ (data && data.archivesIdentity) || '--' }}</span>
		</div>
		<div class="table-container">
			<GcTable ref="gcTableRef" :loading="loading" :columns="columns" :table-data="tableData">
				<!-- 本次水量 -->
				<template v-slot:useAmount="{ row, $index }">
					<span v-show="!row.isEditing">{{ !judgeBlank(row.useAmount) ? row.useAmount : '--' }}</span>
					<el-form
						v-show="row.isEditing"
						:ref="`useAmountFormRef${$index}`"
						class="table-form"
						:model="tableData[$index]"
					>
						<el-form-item prop="useAmount">
							<el-input-number
								v-model="tableData[$index].useAmount"
								class="input-number"
								placeholder="请输入本次水量"
								:min="-999999999"
								:max="999999999"
								step-strictly
								:controls="false"
							/>
						</el-form-item>
					</el-form>
				</template>
				<template v-slot:deal="{ row, $index }">
					<el-button
						v-has="'plan-collection_meterReadingReview_updateWaterSpilt2'"
						v-show="!row.isEditing"
						type="text"
						size="medium"
						@click="handleAdjust(row, $index)"
					>
						修改本次水量
					</el-button>
					<div v-has="'plan-collection_meterReadingReview_updateWaterSpilt2'" v-show="row.isEditing">
						<el-button type="text" size="medium" @click="handleAdjustSave(row, $index)">保存</el-button>
						<el-button type="text" size="medium" @click="handleAdjustCancel($index)">取消</el-button>
					</div>
				</template>
			</GcTable>
		</div>
	</div>
</template>

<script>
import { isBlank } from '@/utils/validate.js'
import { getColumn } from './tableColumn.js'
import { getWaterSpiltList, updateWaterSpilt } from '@/api/meterReading.api.js'

export default {
	name: '',
	props: {
		data: Object,
		topParams: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			loading: false,
			columns: getColumn(this),
			tableData: [
				// {
				//   archivesNo: "测试数据",
				// },
			],
			// 编辑的当前行数据（用于取消时恢复）
			currentEditData: {},
		}
	},
	created() {},
	watch: {
		data: {
			handler(newVal) {
				if (newVal) {
					this.getList()
				} else {
					this.tableData = []
				}
			},
			deep: true,
		},
	},
	methods: {
		async getList() {
			this.loading = true

			try {
				const res = await getWaterSpiltList({
					meterReadingRecordId: this.data.meterReadingRecordId,
					taskYear: this.topParams.taskYear,
				})
				this.tableData = res || []
			} catch (error) {
				console.error(error)
				this.tableData = []
			} finally {
				this.loading = false
			}
		},
		// 修改本次水量
		handleAdjust(row, index) {
			this.currentEditData = row
			this.$refs.gcTableRef.scrollLeft(500)
			this.$set(this.tableData[index], 'isEditing', true)
		},
		handleAdjustSave(row, index) {
			this.$refs[`useAmountFormRef${index}`].validate(async valid => {
				if (valid) {
					const { archivesId, meterId, recordSeq, waterId, taskYear, useAmount } = row
					const { meterReadingRecordId } = this.data
					await updateWaterSpilt({
						archivesId,
						meterId,
						meterReadingRecordId,
						recordSeq,
						waterId,
						taskYear,
						useAmount,
					})
					this.$message.success('修改本次成功')
					this.getList(1)
				}
			})
		},
		handleAdjustCancel(index) {
			this.tableData.splice(index, 1, {
				...this.currentEditData,
				isEditing: false,
			})
			this.currentEditData = {}
			this.$refs[`useAmountFormRef${index}`].clearValidate()
		},

		judgeBlank(val) {
			return isBlank(val)
		},
	},
}
</script>

<style lang="scss" scoped>
.virtual-meter {
	height: 100%;
	display: flex;
	flex-direction: column;
	padding: 0 20px 0 10px;
	.title {
		display: flex;
		justify-content: space-between;
		padding: 20px;
		position: relative;
		&::before {
			position: absolute;
			content: '';
			display: block;
			width: 2px;
			height: 10px;
			left: 12px;
			top: 23px;
			background-color: blue;
		}
	}
}
.table-container {
	flex: 1;
	height: 0;
	padding-bottom: 20px;
}
.input-number {
	width: 100%;
	::v-deep {
		.el-input__inner {
			text-align: left;
		}
	}
}
.table-form {
	::v-deep {
		.el-form-item {
			margin: 12px 0;
		}
		.el-form-item__error {
			padding-top: 0;
		}
	}
}
</style>
