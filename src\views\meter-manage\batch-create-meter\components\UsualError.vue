<template>
	<gc-el-dialog :show.sync="innerVisible" :width="`800px`" title="常见错误解决方法">
		<div class="content">
			<div class="error-item">
				<div class="title">数据校验未通过？</div>
				<p class="answer">
					答：模板支持数据格式校验，校验不通过的内容将展示在选择文件按钮下方，请根据实际情况进行检查修改。
				</p>
				<div class="example">
					<div class="step">
						<img src="@/assets/images/usualError/format-validate-failed.png" alt="" />
						<div class="decs">
							<span class="decs-num">1. XXXX未填写</span>
							<span class="decs-txt">说明XXXX列是必填项，请按照提示的行数检查。</span>
							<br />
							<span class="decs-num">2. XXXX输入重复</span>
							<span class="decs-txt">
								说明XXXX列输入内容必须是唯一不可重复的，请检查提示的内容是否重复。
							</span>
							<br />
							<span class="decs-num">3. XXXX格式错误</span>
							<span class="decs-txt">说明XXXX列输入内容不符合格式要求，请按照提示的行数检查。</span>
							<br />
							<span class="decs-num">4. XXXX超出长度要求</span>
							<span class="decs-txt">说明XXXX列输入内容过长或过短，请按照提示的行数检查。</span>
							<br />
							<span class="decs-num">5. XXXX超出数值范围</span>
							<span class="decs-txt">说明XXXX列输入数值超出范围，请按照提示的行数检查。</span>
						</div>
					</div>
				</div>
			</div>
			<div class="error-item">
				<div class="title">用户重复？</div>
				<p class="answer">
					答：多个地址共用用户名和手机号码的情况暂不支持批量建档，须单个创建，创建档案时复用用户信息
				</p>
				<div class="example">
					<div class="step">
						<img src="@/assets/images/usualError/error-1.png" alt="" />
						<div class="decs">
							<span class="decs-num">第一步：</span>
							<span class="decs-txt">如示例，错误记录中显示第2行用户重复</span>
						</div>
					</div>
					<div class="step">
						<img v-if="realm !== 'water'" src="@/assets/images/usualError/error-2.png" alt="" />
						<img v-else src="@/assets/images/usualError/error-2-water.png" alt="" />
						<div class="decs">
							<span class="decs-num">第二步：</span>
							<span class="decs-txt">在上传模板中找到第2行中的信息，进行单个建档</span>
						</div>
					</div>
					<div class="step">
						<img v-if="realm !== 'water'" src="@/assets/images/usualError/error-3.png" alt="" />
						<img v-else src="@/assets/images/usualError/error-3-water.png" alt="" />
						<div class="decs">
							<span class="decs-num">第三步：</span>
							<span class="decs-txt">
								建档时通过用户名或手机号查询到该用户信息，点击复用用户信息即可完成建档。
							</span>
						</div>
					</div>
				</div>
			</div>
			<div class="error-item">
				<div class="title">表具编号已存在？</div>
				<p class="answer">答：该表具已存在，无法批量建档，需单独创建档案</p>
				<div class="example">
					<div class="step">
						<img src="@/assets/images/usualError/error-4.png" alt="" />
						<div class="decs">
							<span class="decs-txt">建档时表具信息通过搜索选择复用表具信息</span>
						</div>
					</div>
				</div>
			</div>
			<div class="error-item">
				<div class="title">档案标识重复？</div>
				<p class="answer no-example">答：修改上传模板里的档案标识字段</p>
			</div>
			<div class="error-item">
				<div class="title">地址名称重复？</div>
				<p class="answer">答：须单个创建，创建档案时选择复用此地址</p>
				<div class="example">
					<div class="step">
						<img src="@/assets/images/usualError/error-5.png" alt="" />
						<div class="decs">
							<span class="decs-txt">如图所示复用地址</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<template #footer>
			<span></span>
		</template>
	</gc-el-dialog>
</template>

<script>
import getFieldName from '@/mixin/getFieldName.js'
export default {
	name: 'UsualError',
	mixins: [getFieldName],
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		innerVisible: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
}
</script>
<style lang="scss" scoped>
.error-item {
	padding: 20px 0 24px 0;
	border-top: 1px dashed #cccccc;
	.title {
		font-size: 20px;
		color: $base-color-3;
	}
	.answer {
		padding: 10px 0 24px 0;
		font-size: 14px;
		line-height: 20px;
		color: $base-color-6;
	}
	.answer.no-example {
		padding-bottom: 0;
	}
	.example {
		.step {
			display: flex;
			align-items: center;
			.decs {
				padding-left: 12px;
				display: flex;
				flex-direction: column;
				.decs-num,
				.decs-txt {
					font-size: $base-font-size-small;
					line-height: 16px;
				}
				.decs-num {
					color: $base-color-3;
				}
				.decs-txt {
					color: $base-color-6;
				}
			}
		}
		.step:not(:first-child) {
			padding-top: 20px;
		}
	}
	img {
		width: 376px;
	}
}
.error-item:first-child {
	border-top: none;
	padding-top: 0;
}
</style>
