<template>
	<el-menu-item :index="itemOrMenu.fullPath" @click="handleLink">
		<gc-icon
			v-if="itemOrMenu.meta && itemOrMenu.meta.icon"
			font-family="iconfontCis"
			:icon="itemOrMenu.meta.icon"
		/>
		<span :class="{ wrap: _.get(itemOrMenu, 'meta.title', '').length > 7 }">{{ itemOrMenu.meta.title }}</span>
	</el-menu-item>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'

export default {
	name: 'MenuItem',
	props: {
		itemOrMenu: {
			type: Object,
			default() {
				return null
			},
		},
	},
	computed: {
		...mapGetters({
			device: 'settings/device',
			collapse: 'settings/collapse',
		}),
	},
	methods: {
		...mapActions({
			foldSideBar: 'settings/foldSideBar',
			setSubmenu: 'settings/setSubmenu',
		}),
		handleLink() {
			const routePath = this.itemOrMenu.path
			if (this.$route.fullPath !== routePath) {
				if (this.device === 'mobile') this.foldSideBar()
				this.$router.push(this.itemOrMenu.fullPath)
				if (this.collapse) this.setSubmenu(false)
			}
		},
	},
}
</script>
