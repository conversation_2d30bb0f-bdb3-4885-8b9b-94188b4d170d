<template>
	<gc-icon :icon="isFullscreen ? 'icon-suoxiao' : 'icon-fullscreen'" @click="click" />
</template>

<script>
import screenfull from 'screenfull'

export default {
	name: 'GcFullScreen',
	data() {
		return {
			isFullscreen: false,
		}
	},
	mounted() {
		this.init()
	},
	methods: {
		click() {
			if (!screenfull.isEnabled) {
				this.$notify.error({
					title: 'error',
					message: '开启全屏失败',
				})
			}
			screenfull.toggle()
			this.$emit('refresh')
		},
		change() {
			this.isFullscreen = screenfull.isFullscreen
		},
		init() {
			if (screenfull.isEnabled) screenfull.on('change', this.change)
		},
	},
	beforeDestroy() {
		if (screenfull.isEnabled) screenfull.off('change', this.change)
	},
}
</script>
<style scoped>
.icon-suoxiao,
.icon-fullscreen {
	margin-left: 26px;
	cursor: pointer;
}
</style>
