<template>
	<div class="wrapper">
		<div class="container-search">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" @click="getList(1)">筛选</el-button>
					<el-button @click="handleReset">重置</el-button>
				</el-form-item>
			</GcFormSimple>
			<div class="btn-group">
				<!-- TODO -->
				<el-checkbox
					v-model="checkedAllPage"
					label="选中所有页"
					border
					:disabled="tableData.length === 0"
					class="select-all"
					@change="handleCheckedAllPage"
				></el-checkbox>
				<el-button
					v-has="'plan-collection_meterReadingReview_asyncBatchReviewPassAndCreateBill5'"
					type="primary"
					:disabled="!(selectedData.length || checkedAllPage && this.tableData.length)"
					@click="handleCreateBill(true)"
				>
					账单开账
				</el-button>
			</div>
		</div>
		<div class="table-container">
			<GcTable
				ref="gcTableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				row-key="meterReadingRecordId"
				showPage
				needType="selection"
				:selectable="handleSelectable"
				@selectChange="handleSelectChange"
				@current-page-change="handlePageChange"
			>
				<!-- 抄表经纬度 -->
				<template v-slot:latitudeLongitude="{ row }">
					<span>
						{{ row.longitude && row.latitude ? `${row.longitude},${row.latitude}` : '--' }}
					</span>
				</template>
				<template v-slot:imageUrl="{ row }">
					<UploadImgSimple v-model="row.imageUrl" />
				</template>
				<template v-slot:deal="{ row }">
					<el-button
						v-if="row.recordState === 1 && row.useAmount && row.archivesMeterType === 1"
						type="text"
						size="medium"
						@click="handleCreateBill(false, row)"
					>
						账单开账
					</el-button>
				</template>
			</GcTable>
		</div>
		<!-- 批量账单开账进度显示 -->
		<BatchProcessDialog
			:show.sync="processDialogShow"
			:can-closed="canClosedDialog"
			:data="processResult"
			title="批量账单开账进度"
		/>
	</div>
</template>

<script>
import UploadImgSimple from '@/components/UploadImgSimple'
import BatchProcessDialog from '../components/BatchProcessDialog'
import { getColumn } from './tableColumn.js'
import {
	getReviewDetailListNewV2,
	asyncBatchReviewPassAndCreateBill,
	createBill,
	getBatchReviewResult,
} from '@/api/meterReading.api.js'

export default {
	name: '',
	components: { UploadImgSimple, BatchProcessDialog },
	props: {
		type: Number,
		topParams: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '80px',
			},
			loading: false,
			columns: getColumn(),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			// 列表选中数据
			selectedData: [],
			// 选中所有页
			checkedAllPage: false,
			progressInterval: null,
			processDialogShow: false,
			// 批量账单开账结果
			processResult: {},
			canClosedDialog: false,
		}
	},
	computed: {},
	created() {
		this.getList()
	},
	methods: {
		handleSelectable(row) {
			const { useAmount, recordState, archivesMeterType } = row
			// useAmount 本次水量
			// recordState 账单状态：0-未生成账单 1-待生成账单 2-已生成账单
			// archivesMeterType 结算类型: 0-非贸易结算表 1-贸易结算表
			return recordState === 1 && useAmount && archivesMeterType === 1
		},
		handleSelectChange(data) {
			this.selectedData = data

			this.$nextTick(() => {
				const tableData = this.tableData.filter(item => {
					return this.handleSelectable(item)
				})
				if (this.checkedAllPage && this.selectedData.length !== tableData.length) {
					this.checkedAllPage = false
				}
			})
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.getList(1)
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		async getList(curPage) {
			this.loading = true

			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getReviewDetailListNewV2({
					type: this.type,
					current,
					size,
					...this.formData,
					...this.topParams,
				})
				this.tableData = records
				this.pageData.total = total

				// 勾选 选中所有页
				if (this.checkedAllPage && this.tableData.length) {
					this.$nextTick(() => {
						this.tableData.forEach(item => {
							this.$refs.gcTableRef.toggleRowSelectionUseInnerData(copyTableData => {
								return copyTableData.find(row => row.meterReadingRecordId === item.meterReadingRecordId)
							}, this.handleSelectable(item))
						})
					})
				} else {
					this.checkedAllPage = false
				}
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},

		// 生成账单
		async handleCreateBill(isBatch = false, row) {
			const taskIdsAndRecordIdsMap = this.getTaskIdsAndRecordIdsMap(isBatch, row)
			this.$confirm('确定要账单开账吗?').then(async () => {
				if (isBatch) {
					const data = await asyncBatchReviewPassAndCreateBill({
						type: this.type,
						createBill: true,
						reviewPass: false,
						...this.formData,
						...this.topParams,
						taskIdsAndRecordIdsMap,
					})
					this.handleBatchReviewResult(data)
					this.processResult = {}
					this.processDialogShow = true
					this.canClosedDialog = false
				} else {
					await createBill({
						meterReadingRecordIdList: [row.meterReadingRecordId],
						year: this.topParams.taskYear,
					})
					this.checkedAllPage = false
					this.$message.success('账单开账成功')
					this.getList(1)
				}
			})
		},
		// 选中所有页
		handleCheckedAllPage(value) {
			if (value) {
				this.tableData.forEach(item => {
					this.$refs.gcTableRef.toggleRowSelectionUseInnerData(copyTableData => {
						return copyTableData.find(row => row.meterReadingRecordId === item.meterReadingRecordId)
					}, this.handleSelectable(item))
				})
			} else {
				this.$refs.gcTableRef.clearCheckTableSelection()
			}
		},
		// 获取选中记录数据
		getTaskIdsAndRecordIdsMap(isBatch, row) {
			let taskIdsAndRecordIdsMap = {}
			if (isBatch) {
				if (!this.checkedAllPage) {
					this.selectedData.forEach(item => {
						if (taskIdsAndRecordIdsMap[item.meterReadingTaskId]) {
							taskIdsAndRecordIdsMap[item.meterReadingTaskId].push(item.meterReadingRecordId)
						} else {
							taskIdsAndRecordIdsMap[item.meterReadingTaskId] = [item.meterReadingRecordId]
						}
					})
				}
			} else {
				taskIdsAndRecordIdsMap[row.meterReadingTaskId] = [row.meterReadingRecordId]
			}
			return taskIdsAndRecordIdsMap
		},
		// 查询审批进度
		async handleBatchReviewResult(syncId) {
			try {
				const data = await getBatchReviewResult({ syncId })
				this.processResult = data || {}
				if (data.processRate == 100) {
					clearInterval(this.progressInterval)
					this.canClosedDialog = true
					if (!data.failMsg || !data.failMsg.length) {
						this.checkedAllPage = false
						this.$message.success('账单开账成功')
						this.getList(1)
					} else {
						this.$message.error('账单开账失败')
					}
				} else {
					this.progressInterval = setTimeout(this.handleBatchReviewResult(syncId), 3000)
				}
			} catch (error) {
				this.canClosedDialog = true
				console.error('查询账单开账进度失败', error)
				clearInterval(this.progressInterval)
			}
		},
	},
	beforeDestroy() {
		this.progressInterval && clearInterval(this.progressInterval)
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	flex: 1;
	height: 0;
	display: flex;
	flex-direction: column;
	padding: 20px;
}
.container-search {
	display: flex;
	justify-content: space-between;
}
.table-container {
	flex: 1;
	height: 0;
}
.select-all {
	margin-right: 10px;
}
</style>
