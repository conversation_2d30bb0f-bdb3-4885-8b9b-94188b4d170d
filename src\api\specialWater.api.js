import service from './request'
import { CPM } from '@/consts/moduleNames'

// 罚没款
// 罚没款记录列表分页查询
export function queryFinesPage(data) {
	return service({
		url: `${CPM}/fines/queryPage`,
		method: 'post',
		data,
	})
}
// 罚没款新增
export function addFine(data) {
	return service({
		url: `${CPM}/fines/add`,
		method: 'post',
		data,
	})
}
// 罚没款修改
export function editFine(data) {
	return service({
		url: `${CPM}/fines/update`,
		method: 'post',
		data,
	})
}
// 罚没款删除
export function deleteFine(params) {
	return service({
		url: `${CPM}/fines/delete`,
		method: 'get',
		params,
	})
}
// 罚没款缴费
export function paymentFine(data) {
	return service({
		url: `${CPM}/fines/payment`,
		method: 'post',
		data,
	})
}
// 根据表卡编号获取id和用户信息
export function getInfoByArchivesNo(params) {
	return service({
		url: `${CPM}/fines/queryArchivesInfo`,
		method: 'get',
		params,
	})
}
// 计算追缴金额
export function calculatePrice(data) {
	return service({
		url: `${CPM}/fines/calculatePrice`,
		method: 'post',
		data,
	})
}

// 缴费 创建账单
export function createBill(data) {
	return service({
		url: `${CPM}/fines/createBill`,
		method: 'post',
		data,
	})
}

// 计划免污水费用管理
// 免污水表卡分页查询
export function queryPlanWastePage(data) {
	return service({
		url: `${CPM}/planWaste/queryPage`,
		method: 'post',
		data,
	})
}
// 导入免污水费用表卡
export function importPlanWaste(data) {
	return service({
		url: `${CPM}/planWaste/import`,
		method: 'post',
		data,
		headers: {
			'Content-Type': 'multipart/form-data',
		},
		responseType: 'blob',
	})
}
// 免污水费用表卡新增
export function addPlanWaste(data) {
	return service({
		url: `${CPM}/planWaste/add`,
		method: 'post',
		data,
	})
}
// 免污水表卡移除
export function deletePlanWaste(params) {
	return service({
		url: `${CPM}/planWaste/remove`,
		method: 'get',
		params,
	})
}
// 下载模版
export function downloadPlanWasteTemplate(params) {
	return service({
		url: `${CPM}/planWaste/downloadExcel`,
		method: 'get',
		params,
		responseType: 'blob',
	})
}

// 计划用水管理
// 计划用水管理分页查询
export function queryPlanUsagePage(data) {
	return service({
		url: `${CPM}/planUsage/queryPage`,
		method: 'post',
		data,
	})
}
// 追缴水量
export function planUsageRecovery(data) {
	return service({
		url: `${CPM}/planUsage/recovery`,
		method: 'post',
		data,
	})
}
// 用户表卡下拉框
export function queryArchives(params) {
	return service({
		url: `${CPM}/planUsage/queryArchives`,
		method: 'get',
		params,
	})
}
// 计算追缴水量信息
export function queryRecoveryInfo(data) {
	return service({
		url: `${CPM}/planUsage/queryRecoveryInfo`,
		method: 'post',
		data,
	})
}

// 计划用水Excel模板下载
export function downloadPlanUsageTemplate(params) {
	return service({
		url: `${CPM}/planUsage/downloadExcel`,
		method: 'get',
		params,
		responseType: 'blob',
	})
}
// 新一年计划用水导入
export function importPlanUsage(data) {
	return service({
		url: `${CPM}/planUsage/import`,
		method: 'post',
		data,
		headers: {
			'Content-Type': 'multipart/form-data',
		},
		responseType: 'blob',
	})
}
