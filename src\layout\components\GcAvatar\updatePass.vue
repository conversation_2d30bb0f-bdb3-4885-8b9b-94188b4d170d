<template>
	<!-- 修改密码的弹窗 -->
	<gc-el-dialog
		:show="show"
		width="480px"
		class="update-password"
		@ok="submitForm"
		@close="handleClose"
		@cancel="handleClose"
		title="修改密码"
		icon="icon-popup-general-icon"
		okText="确定"
	>
		<div class="warning-tip" v-if="showWarningTip">
			<span class="iconfontCis icon-lingdang" style="font-size: 13px; padding-right: 8px"></span>
			密码强度较低，请先提升密码强度后再继续后续操作
		</div>
		<el-form
			:model="ruleForm"
			:rules="rules"
			ref="ruleForm"
			label-width="110px"
			class="ruleForm-wrap"
			label-position="top"
		>
			<!-- 阻止浏览器自动填充密码 -->
			<input type="text" name="username" value="" style="position: absolute; left: -10000px" />
			<input type="password" name="password" value="" style="position: absolute; left: -10000px" />
			<el-form-item label="原始密码：" prop="oriPass">
				<input-com kname="oriPass" :value.sync="ruleForm.oriPass" placeholder="请填写原始密码" />
			</el-form-item>
			<el-form-item label="新密码：" prop="newPass">
				<input-com kname="newPass" :value.sync="ruleForm.newPass" placeholder="请填写新密码" />
			</el-form-item>
			<el-form-item label="确认新密码：" prop="confirmPass">
				<input-com kname="confirmPass" :value.sync="ruleForm.confirmPass" placeholder="请再次填写新密码" />
			</el-form-item>
		</el-form>
	</gc-el-dialog>
</template>
<script>
import { apiTenantUpdatePass } from '@/api/organizationManage.api'
import { ruleRequired, ruleMaxLength, ruleMinLength, ruleComplexPassValidate } from '@/utils/rules.js'
import identity from '@/mixin/identity.js'
import InputCom from './InputCom'
import { encrypt } from '@/utils'

export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		showWarningTip: {
			type: Boolean,
			default: false,
		},
	},
	mixins: [identity],
	components: { InputCom },
	data() {
		const equalPassValidate = (rule, value, callback) => {
			if (value !== this.ruleForm.newPass) {
				callback(new Error('两次输入密码不一致!'))
			} else {
				callback()
			}
		}
		return {
			ruleForm: {
				oriPass: '', //原始密码
				newPass: '', //新密码
				confirmPass: '', //确认密码
			},
			iconStatus: {
				oriPass: 'nosee',
				newPass: 'nosee',
				confirmPass: 'nosee',
			},
			rules: {
				oriPass: [ruleRequired('请输入原始密码')],
				newPass: [ruleRequired('请输入新密码'), ruleMinLength(6), ruleMaxLength(18), ruleComplexPassValidate()],
				confirmPass: [ruleRequired('请确认新密码'), { validator: equalPassValidate, trigger: '' }],
			},
		}
	},
	watch: {
		'ruleForm.oriPass'() {
			this.ruleForm.oriPass = this.ruleForm.oriPass.replace(/[\u4E00-\u9FA5]/g, '')
		},
		'ruleForm.newPass'() {
			this.ruleForm.newPass = this.ruleForm.newPass.replace(/[\u4E00-\u9FA5]/g, '')
		},
		'ruleForm.confirmPass'() {
			this.ruleForm.confirmPass = this.ruleForm.confirmPass.replace(/[\u4E00-\u9FA5]/g, '')
		},
	},
	methods: {
		// 租户修改自身的密码
		editTenantUpdatePass(obj) {
			apiTenantUpdatePass(obj)
				.then(() => {
					this.$message({
						message: '密码修改成功',
						type: 'success',
						duration: 1000,
						onClose: () => {
							this.$emit('handle-dialog-close')
							this.$emit('updateSuc', obj.newPwd)
							this.$store.dispatch('user/resetUserLS').then(() => location.reload())
						},
					})
				})
				.catch(err => {
					this.$message.error('修改密码失败:' + err.message)
				})
		},

		submitForm() {
			if (this.ruleForm.newPass.includes(' ') || this.ruleForm.oriPass.includes(' ')) {
				this.$message.error('密码中不能包含空格')
				return
			}
			this.$refs['ruleForm'].validate(valid => {
				if (!valid) return false
				let params = {
					newPwd: encrypt(this.ruleForm.newPass),
					oldPwd: encrypt(this.ruleForm.oriPass),
				}
				this.editTenantUpdatePass(params)
			})
		},
		handleClose() {
			this.$refs['ruleForm'].resetFields()
			this.$emit('handle-dialog-close')
		},
		iconClick(key, val) {
			this.iconStatus[key] = val
		},
	},
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
	margin-top: 10vh !important;
	.el-dialog__header {
		div {
			height: 100%;
			line-height: 55px;
			img {
				width: 24px;
				vertical-align: middle;
				margin-right: 8px;
			}
			span {
				display: inline-block;
				color: #222222;
				font-weight: 500;
				line-height: 24px;
			}
		}
	}
	.el-dialog__body {
		padding: 24px;
		.warning-tip {
			padding: 0 17px;
			margin: 0 auto 8px;
			width: 432px;
			height: 30px;
			background: #ffebe9;
			border-radius: 16px;
			font-size: 10px;
			color: #ec6b60;
			line-height: 30px;
			box-sizing: border-box;
		}
		.tips {
			color: #ec6b60;
			margin: -2px 0 20px 0;
			width: 560px;
			height: 30px;
			line-height: 30px;
			background: #ffebe9;
			border-radius: 16px;
			padding-left: 17px;
			box-sizing: border-box;
			i {
				margin-right: 8px;
			}
		}
		.el-form {
			width: 100%;
			height: 100%;
			.el-form-item:last-child {
				margin-bottom: 0;
				.el-form-item__content {
					margin-left: 0 !important;
				}
				.el-button {
					padding: 0;
					height: 32px;
				}
				.el-button:nth-child(1) {
					width: 80px;
				}
				.el-button:nth-child(2) {
					width: 80px;
					margin-left: 16px;
				}
			}
			.el-form-item__label {
				text-align: left;
				color: #222;
				font-weight: 600;
			}
			.el-form-item__content {
				line-height: 32px;
				height: 32px;
				.el-input {
					.el-input__inner {
						height: 32px;
						line-height: 32px;
					}
					.icon-zhengyan,
					.icon-biyan {
						transform: scale(0.5);
						display: inline-block;
					}
				}
				.password-visi {
					display: inline-block;
					width: 16px;
					// height: 32px;
					// line-height: 32px;
					position: absolute;
					left: 185px;
					top: 8px;
				}
				.input-text {
					margin-left: 12px;
					font-size: 12px;
					color: #9bb0cc;
					position: absolute;
					right: 10px;
					width: 245px;
					line-height: 16px;
				}
			}
		}
	}
}
</style>
