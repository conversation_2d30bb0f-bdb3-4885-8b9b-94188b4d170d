<template>
	<div class="gc-tabs">
		<el-tabs
			v-model="tabActive"
			class="gc-tabs-content"
			type="card"
			@tab-click="handleTabClick"
			@tab-remove="handleTabRemove"
		>
			<el-tab-pane
				v-for="item in visitedRoutes"
				:key="item.path"
				:closable="!isNoClosable(item)"
				:name="item.path"
			>
				<span slot="label" style="display: inline-block" @contextmenu.prevent="openMenu($event, item)">
					<span>{{ item.meta.title }}</span>
				</span>
			</el-tab-pane>
		</el-tabs>

		<ul
			v-if="visible"
			class="contextmenu el-dropdown-menu el-dropdown-menu--small"
			:style="{ left: left + 'px', top: top + 'px' }"
		>
			<li class="el-dropdown-menu__item" @click="closeAllTabs">
				<gc-icon icon="icon-close" size="8" />
				<span>关闭所有标签</span>
			</li>
		</ul>
	</div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { handleActivePath } from '@/utils/routes'

export default {
	name: 'GcTabs',
	data() {
		return {
			tabActive: '',
			active: false,
			visible: false,
			top: 0,
			left: 0,
			hoverRoute: null,
		}
	},
	computed: {
		...mapGetters({
			visitedRoutes: 'tabs/visitedRoutes',
			routes: 'routes/routes',
			theme: 'settings/theme',
		}),
	},
	watch: {
		$route: {
			handler(route) {
				this.$nextTick(() => {
					this.addTabs(route)
				})
			},
			immediate: true,
		},
		visible(value) {
			if (value) document.body.addEventListener('click', this.closeMenu)
			else document.body.removeEventListener('click', this.closeMenu)
		},
	},
	created() {
		this.initNoClosableTabs(this.routes)
	},
	methods: {
		...mapActions({
			addVisitedRoute: 'tabs/addVisitedRoute',
			delVisitedRoute: 'tabs/delVisitedRoute',
			delAllVisitedRoutes: 'tabs/delAllVisitedRoutes',
		}),
		handleTabClick(tab) {
			if (!this.isActive(tab.name)) this.$router.push(this.visitedRoutes[tab.index])
		},
		initNoClosableTabs(routes) {
			routes.forEach(route => {
				if (route.meta && route.meta.noClosable) this.addTabs(route, true)
				if (route.children) this.initNoClosableTabs(route.children)
			})
		},
		/**
		 * 添加标签页
		 * @param tag route
		 * @param init 是否是从router获取路由
		 */
		async addTabs(tag, init = false) {
			let parentIcon = null
			if (tag.matched)
				for (let i = tag.matched.length - 2; i >= 0; i--)
					if (!parentIcon && tag.matched[i].meta.icon) parentIcon = tag.matched[i].meta.icon
			if (!parentIcon) parentIcon = 'menu-line'
			if (tag.name && tag.meta && tag.meta.tabHidden !== true) {
				const path = handleActivePath(tag, true)
				await this.addVisitedRoute({
					path: path,
					query: tag.query,
					params: tag.params,
					name: tag.name,
					matched: init ? [tag.name] : tag.matched.map(_ => _.components.default.name),
					parentIcon,
					meta: { ...tag.meta },
				})
				this.tabActive = path
			}
		},
		/**
		 * 删除指定标签页
		 */
		async handleTabRemove(rawPath) {
			await this.delVisitedRoute(rawPath)
			if (this.isActive(rawPath)) this.toLastTab()
		},
		/**
		 * 删除所有标签页
		 */
		async closeAllTabs() {
			await this.delAllVisitedRoutes()
			this.toLastTab()
			await this.closeMenu()
		},
		/**
		 * 跳转最后一个标签页
		 */
		toLastTab() {
			const latestView = this.visitedRoutes.slice(-1)[0]
			if (latestView) this.$router.push(latestView)
			else this.$router.push('/')
		},
		isActive(path) {
			return path === handleActivePath(this.$route, true)
		},
		isNoClosable(tag) {
			return tag.meta && tag.meta.noClosable
		},
		openMenu(e, item) {
			const offsetLeft = this.$el.getBoundingClientRect().left
			this.left = e.clientX - offsetLeft - 45
			this.top = 30
			this.hoverRoute = item
			this.hoverRoute.fullPath = item.path
			this.visible = true
		},
		closeMenu() {
			this.visible = false
			this.hoverRoute = null
		},
	},
}
</script>

<style lang="scss" scoped>
.gc-tabs {
	position: relative;
	box-sizing: border-box;
	display: flex;
	align-content: center;
	align-items: flex-end;
	justify-content: space-between;
	min-height: $base-tabs-height;
	padding-right: $base-padding;
	padding-left: $base-padding;
	user-select: none;
	background: $base-color-white;

	&-content {
		width: 100%;
		height: $base-tag-item-height;

		::v-deep {
			.el-tabs__nav-next,
			.el-tabs__nav-prev {
				height: $base-tag-item-height;
				line-height: $base-tag-item-height;
			}

			.el-tabs__header {
				border-bottom: 0;

				.el-tabs__nav {
					border: 0;
				}

				.el-tabs__item {
					font-weight: normal;
					font-size: 13px;
					box-sizing: border-box;
					height: $base-tag-item-height;
					line-height: $base-tag-item-height;
					border: none;
					border-radius: $base-border-radius;
					transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) !important;

					&.is-active {
						color: $base-color-blue;
						background: mix($base-color-white, $base-color-blue, 90%);
						border: none;
						outline: none;
					}
				}
			}
		}
	}

	.contextmenu {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 10;
	}

	&-more {
		position: relative;

		&-active,
		&:hover {
			&:after {
				position: absolute;
				bottom: -1px;
				left: 0;
				height: 0;
				content: '';
			}

			.gc-tabs-more-icon {
				transform: rotate(90deg);

				.box-t {
					&:before {
						transform: rotate(45deg);
					}
				}

				.box:before,
				.box:after {
					background: $base-color-blue;
				}
			}
		}

		&-icon {
			display: inline-block;
			color: #9a9a9a;
			cursor: pointer;
			transition: transform 0.3s ease-out;

			.box {
				position: relative;
				display: block;
				width: 14px;
				height: 8px;

				&:before {
					position: absolute;
					top: 0;
					left: 0px;
					width: 6px;
					height: 6px;
					content: '';
					background: #9a9a9a;
				}

				&:after {
					position: absolute;
					top: 0;
					left: 8px;
					width: 6px;
					height: 6px;
					content: '';
					background: #9a9a9a;
				}
			}

			.box-t {
				&:before {
					transition: transform 0.3s ease-out 0.3s;
				}
			}
		}
	}
}
</style>
