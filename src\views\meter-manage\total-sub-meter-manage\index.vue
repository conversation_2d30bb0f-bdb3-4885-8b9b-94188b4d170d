<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />

			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handlePageChange({ page: 1 })">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handlePageChange"
			>
				<template v-slot:operate="{ row }">
					<el-button
						v-has="'cpm_dma-archives_list-sub-archives-record2'"
						type="text"
						@click="handleClickRow(row, 'view')"
					>
						查看分表
					</el-button>
					<el-button type="text" @click="handleClickRow(row)">编辑分表</el-button>
				</template>
			</GcTable>
		</div>
		<SubMeterView :show.sync="showDialog" :dmaArchivesId="currentRow.dmaArchivesId" />
	</div>
</template>

<script>
import { getColumn } from './tableColumn'
import { getFormItems } from './formItem.js'
import { apiGetListDmaArchives } from '@/api/meterManage.api.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import SubMeterView from './components/SubMeterView.vue'
export default {
	name: 'TotalSubMeterManage',
	components: { SubMeterView },
	data() {
		return {
			// 左侧查询
			formData: {
				dmaArchivesIdentity: '',
				dmaArchivesType: '',
			},
			columns: getColumn(this),
			formItems: getFormItems(this),
			formAttrs: {},
			// 右侧列表
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			currentRow: {},
			showDialog: false,
		}
	},
	methods: {
		handleClickRow(row, type) {
			this.currentRow = row
			if (type === 'view') {
				this.showDialog = true
			} else {
				this.$router.push({
					path: '/meterManage/totalSubMeterSettingModify',
					query: {
						dmaArchivesIdentity: row.dmaArchivesIdentity,
					},
				})
			}
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.handlePageChange({ page: 1 })
			this.currentRow = {}
		},
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
				})
				const { total = 0, records = [] } = await apiGetListDmaArchives(formParams)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
	},
	mounted() {
		this.handlePageChange({ page: 1 })
	},
}
</script>
