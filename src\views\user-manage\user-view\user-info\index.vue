<template>
	<GcDetailCard :detail-card-info="detailCardInfo" :header-num="headerNum">
		<template #card-content>
			<div class="card-content">
				<div class="content-item" v-for="(item, index) in displayList" :key="index">
					<p class="field">{{ item.key }}</p>
					<p class="value">
						{{ item.value }}
					</p>
				</div>
			</div>
		</template>
		<template #card-footer>
			<div class="card-footer">
				<el-button v-has="'cpm_user_modify-user'" type="text" class="blue" @click="goModify">
					<i class="iconfontCis icon-modify"></i>
					修改
				</el-button>
			</div>
			<!-- 弹窗 -->
			<!-- 用户表卡用户视图 -->
			<UserSetting
				v-if="showUserSetting"
				:show.sync="showUserSetting"
				:userDetail="userDetail"
				@success="$emit('success')"
			></UserSetting>
			<!-- 企业表卡用户视图 -->
			<CompanyUserSetting
				v-if="showCompanyUserSetting"
				:show.sync="showCompanyUserSetting"
				:userDetail="userDetail"
				@success="$emit('success')"
			></CompanyUserSetting>
		</template>
	</GcDetailCard>
</template>

<script>
import { getfilterName } from '@/utils'
import UserSetting from '../components/user-setting/index.vue'
import CompanyUserSetting from '../components/company-user-setting/index.vue'
export default {
	components: { UserSetting, CompanyUserSetting },
	props: {
		userDetail: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			detailCardInfo: {
				bgUrl: require('@/assets/images/bg/pic-file.png'),
				signUrl: require('@/assets/images/icon/title-user.png'),
				cardName: '用户信息',
			},
			showUserSetting: false,
			showCompanyUserSetting: false,
		}
	},
	computed: {
		headerNum() {
			let obj = {
				key: '用户名称',
				value: this.userDetail?.user?.userName || '--',
				field: 'userName',
			}
			return obj
		},
		//左侧卡片展示字段
		displayList() {
			const list = [
				{
					key: '用户分类',
					value: '--',
					field: 'userType',
				},
				{
					key: '身份证号',
					value: '--',
					field: 'certificateNo',
				},
				{
					key: '手机',
					value: '--',
					field: 'userMobile',
				},
				{
					key: '电话',
					value: '--',
					field: 'contactPhone',
				},
				{
					key: '收费方式',
					value: '--',
					field: 'chargingMethod',
				},
			]

			const extractedData = Object.assign({}, this.userDetail.user)
			list.forEach(item => {
				const { userType = [], chargingMethod = [] } = this.$store.getters.dataList || {}
				if (item.field === 'userType') {
					item.value = getfilterName(userType, extractedData[item.field], 'sortValue', 'sortName')
				} else if (item.field === 'chargingMethod') {
					item.value = getfilterName(chargingMethod, extractedData[item.field], 'sortValue', 'sortName')
				} else {
					item.value = extractedData[item.field] || '--'
				}
			})

			return list
		},
	},
	methods: {
		goModify() {
			if (this.userDetail.user.userType === 3) {
				this.showUserSetting = true
			} else {
				this.showCompanyUserSetting = true
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.detail-card {
	.card-footer {
		padding: 0 0 0 12px;
		text-align: left;
		::v-deep .el-button {
			font-size: 14px;
			color: $base-color-yellow;
			padding-bottom: 0;
			i {
				padding-right: 3px;
				font-size: 16px;
			}
			span {
				display: flex;
				align-items: center;
			}
		}
		.el-button + .el-button {
			margin-left: 2px;
			margin-right: 2px;
		}
		.blue {
			color: $base-color-blue;
		}
	}
}
</style>
