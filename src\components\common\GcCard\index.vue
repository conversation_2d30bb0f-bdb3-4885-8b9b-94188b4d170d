<template>
	<div class="container">
		<GcModelHeader :title="title" :icon="headIcon">
			<div slot="right"><slot name="rightHead"></slot></div>
		</GcModelHeader>
		<main>
			<slot name="main"></slot>
		</main>
	</div>
</template>

<script>
export default {
	name: 'Gc<PERSON><PERSON>',
	components: {},
	props: {
		title: String,
		headIcon: String,
	},
	data() {
		return {}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	methods: {},
}
</script>
<style lang="scss" scoped>
.container {
	background: #ffffff;
	border-radius: 4px;
	padding-bottom: 0;
	height: 100%;
	display: flex;
	flex-direction: column;
	.model_header {
		flex: 0 0 60px;
	}
	main {
		flex-grow: 1;
		height: 0;
		position: relative;
		overflow: auto;
	}
}
</style>
