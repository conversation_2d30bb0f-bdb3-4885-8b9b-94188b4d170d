<template>
	<GcElDialog
		:show="isShow"
		title="表卡注销登记"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:otherInfo>
				<h5 class="gap-title">其它信息</h5>
			</template>
		</GcFormSimple>
	</GcElDialog>
</template>

<script>
import { getFormItems } from './form.js'
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiArchivesClose, apiArchivesClose1, apiArchivesClose2 } from '@/api/meterManage.api.js'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		detailData: {
			type: Object,
			default: () => ({}),
		},
		permissionCode: {
			type: String,
			default: 'cpm_archives_close',
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	watch: {
		isShow: {
			handler(val) {
				if (val) {
					this.formData.archivesIdentity = this.detailData.archivesIdentity || ''
					const hasMeterOperate = this.formItems.find(item => item.prop === 'meterOperate')
					this.formData.closeDate = this.dayjs().format('YYYY-MM-DD')
					this.detailData?.virtualMeterType === 1 && hasMeterOperate && this.formItems.pop()
				}
			},
		},
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
				closeDate: '',
				closePerson: this.$store.getters.userInfo.staffName,
				closeReason: '',
				meterOperate: '',
			},
			formItems: getFormItems(this),
			formAttrs: {
				rules: {
					closeDate: [ruleRequired('必填')],
					closeReason: [ruleRequired('必填')],
					meterOperate: [ruleRequired('必填')],
				},
			},
		}
	},
	methods: {
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			let formObj = trimParams(removeNullParams(this.formData))
			const { archivesId } = this.detailData
			delete formObj.archivesIdentity
			const dateStr = this.dayjs(formObj.closeDate).format('YYYY-MM-DD')
			Object.assign(formObj, { closeDate: dateStr, archivesId })

			if (formObj.meterOperate === 'wucaozuo') {
				delete formObj.meterOperate
			}
			const apiMethods = {
				cpm_archives_close: apiArchivesClose,
				cpm_archives_close1: apiArchivesClose1,
				cpm_archives_close2: apiArchivesClose2,
			}

			await apiMethods[this.permissionCode](formObj)
			const successMessage =
				this.detailData.virtualMeterType === 1
					? '虚表表卡销卡正常，请至虚表管理页对该虚表的实卡进行重新分配水量'
					: '表卡注销登记成功'
			this.$message.success(successMessage)
			this.handleClose()
			this.$emit('refresh')
		},
	},
}
</script>
