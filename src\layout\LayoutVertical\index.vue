<template>
	<!-- 纵向布局 -->
	<div class="gc-layout-vertical fixed">
		<gc-side-bar />
		<div v-if="device === 'mobile' && !collapse" class="v-modal" @click="handleFoldSideBar" />
		<div
			class="gc-main"
			:class="{
				'is-collapse-main': collapse,
			}"
		>
			<div class="gc-layout-header fixed-header">
				<gc-nav />
				<gc-tags-view />
			</div>
			<gc-app-main />
		</div>
	</div>
</template>

<script>
import { mapActions } from 'vuex'

export default {
	name: 'LayoutVertical',
	props: {
		collapse: {
			type: Boolean,
			default() {
				return false
			},
		},
		device: {
			type: String,
			default() {
				return 'desktop'
			},
		},
	},
	methods: {
		...mapActions({
			handleFoldSideBar: 'settings/foldSideBar',
		}),
	},
}
</script>

<style lang="scss" scoped>
.gc-layout-vertical {
	.fixed-header {
		left: $base-left-menu-width;
		width: $base-right-content-width;
	}
}
</style>
