<template>
	<GcElDialog
		:show="isShow"
		title="二维码"
		custom-top="60px"
		width="480px"
		height="800px"
		:showFooter="false"
		@close="isShow = false"
	>
		<div class="code-container">
			<el-image :src="url" v-if="url"></el-image>
			<GcEmpty v-else></GcEmpty>
		</div>
	</GcElDialog>
</template>

<script>
import { apiGetQrcode } from '@/api/login.api'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		accountName: {
			type: String,
			default: '',
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	watch: {
		isShow: {
			handler(val) {
				if (val) {
					this._apiGetQrcode()
				}
			},
		},
	},
	data() {
		return {
			url: '',
		}
	},
	methods: {
		async _apiGetQrcode() {
			try {
				const res = await apiGetQrcode({
					account: this.accountName,
				})
				this.url = res
			} catch (error) {
				console.log(error)
			}
		},
	},
}
</script>

<style scoped>
.qr-code-container {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 20px;
}

.qr-code-image {
	max-width: 100%;
	height: auto;
}
.code-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400px;
}
</style>
