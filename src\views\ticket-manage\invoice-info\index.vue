<template>
	<div class="wrapper">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch(false)">查询</el-button>
				<el-button @click="handleReset">重置</el-button>
			</el-form-item>
		</GcFormSimple>
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			@current-page-change="handlePageChange"
		>
			<template #userType="{ row }">
				<span>{{ getfilterNameFn($store.getters.dataList.userType, row.userType) }}</span>
			</template>
			<template #invoiceType="{ row }">
				<span>{{ getfilterNameFn($store.getters.dataList.invoiceType, row.invoiceType) }}</span>
			</template>
		</GcTable>
	</div>
</template>

<script>
import { getfilterName } from '@/utils'
import { getInvoiceBuyerList } from '@/api/ticketManage.api'

export default {
	name: 'InvoiceInfo',
	components: {},
	data() {
		return {
			formData: {
				userName: '',
				invoiceType: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '所属者名称',
					prop: 'userName',
					attrs: {
						clearable: true,
						placeholder: '请输入所属者名称',
					},
				},
				{
					type: 'el-select',
					label: '开票类型',
					prop: 'invoiceType',
					options:
						this.$store.getters?.dataList?.invoiceType?.map(item => {
							return {
								label: item.sortName,
								value: item.sortValue,
							}
						}) || [],
					attrs: {
						clearable: true,
						placeholder: '请选择开票类型',
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '100px',
			},

			loading: false,
			tableData: [],
			columns: [
				{
					key: 'userName',
					name: '所属者名称',
					tooltip: true,
				},
				{
					key: 'email',
					name: '电子邮箱',
					tooltip: true,
				},
				{
					key: 'userMobile',
					name: '手机号码',
					tooltip: true,
				},
				{
					key: 'invoiceType',
					name: '开票类型',
					tooltip: true,
				},
				{
					key: 'taxpayerIdentity',
					name: '纳税人识别编号',
					tooltip: true,
				},
				{
					key: 'openBank',
					name: '开户银行',
					tooltip: true,
				},
				{
					key: 'bankAccount',
					name: '银行账户',
					tooltip: true,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},

	mounted() {},
	methods: {
		getfilterNameFn(options = [], val, key = 'sortValue', label = 'sortName') {
			return getfilterName(options, val, key, label)
		},

		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getInvoiceBuyerList({
					size,
					current,
					...this.formData,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	padding: 20px;
}
</style>
