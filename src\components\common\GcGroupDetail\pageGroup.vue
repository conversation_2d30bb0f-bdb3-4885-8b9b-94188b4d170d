<template>
	<div class="z-page-group">
		<div class="z-group-title" v-if="groupTitle">
			<div class="_title">{{ groupTitle }}</div>
			<slot name="head"></slot>
		</div>
		<div class="z-group-details" v-if="$slots.default">
			<slot></slot>
		</div>
	</div>
</template>

<script>
export default {
	name: 'PageGroup',
	componentName: 'PageGroup',
	props: {
		groupTitle: String,
	},
	data() {
		return {}
	},
}
</script>

<style lang="scss" scoped>
.z-page-group {
	.z-group-title {
		line-height: 42px;
		display: flex;
		align-items: center;
		._title {
			font-size: 14px;
			padding: 0 6px 0 32px;
			position: relative;
			color: #666;
			letter-spacing: 1px;
			&:after {
				content: '';
				width: 2px;
				height: 13px;
				background: #5ca1ff;
				position: absolute;
				border-radius: 1px;
				left: 25px;
				top: 15px;
			}
		}
	}
	.z-group-details {
		display: flex;
		flex-wrap: wrap;
		padding: 6px 12px;
	}
}
</style>
