<template>
	<!-- tab分页组件 -->
	<div class="gc-detail-tab">
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<template v-for="item in filteredList">
				<el-tab-pane :key="item.name" :label="item.label" :name="item.name" :disabled="item.disabled">
					<keep-alive>
						<!-- 'tab-data':tab页下面的组件入参 -->
						<!-- 在使用时需要在自己声明的组件里接收一下 -->
						<component
							v-if="item.component"
							ref="componentRef"
							:tab-data="item.data || {}"
							:cur-tab-name="curTabName"
							v-bind:is="item.component"
							v-on="$listeners"
							v-bind="$attrs"
						></component>
					</keep-alive>
					<slot />
				</el-tab-pane>
			</template>
		</el-tabs>
	</div>
</template>

<script>
export default {
	name: 'GcDetailTab',
	components: {},
	props: {
		//  tab渲染数据
		/**
		 * tabList[number]:{
		 *    label tab的标签
		 *    name tab标签的name值，用于动态引入需要渲染的组件
		 *    data tab的标签专属组件的入参
		 *    noComp 当前tab是否没有专属组件
		 *    component 当前tab渲染的组件
		 * }
		 * */
		tabList: {
			type: Array,
			default: () => [],
		},
		defaultActiveName: String,
	},
	data() {
		return {
			isNoComp: false, // 当前tab的是否展示自定义组件内容
			curTabIndex: '', // 当前tab的索引
			curTabName: '', // 当前tab的索引
		}
	},
	computed: {
		filteredList() {
			return this.tabList.filter(item => this.checkPermission(item.keyWord))
		},
		activeName: {
			get: function () {
				return this.defaultActiveName || this.filteredList[0].name
			},
			set: function (val) {
				this.$emit('update:defaultActiveName', val)
			},
		},
	},
	methods: {
		handleClick(tab) {
			this.curTabIndex = tab.index
			this.curTabName = tab.name
			this.isNoComp = this.tabList[this.curTabIndex].noComp ? true : false
			this.$emit('tab-change', tab)
		},
		checkPermission(val) {
			return this.$store.getters?.permissions?.includes(val) || !val
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-detail-tab {
	background-color: #eceff8;
	width: 100%;
	height: 100%;
	::v-deep .el-tabs__nav-scroll {
		font-size: 14px;
		overflow: hidden;
		border-radius: 20px;
		border: 1px solid #fff;
		background-color: #fff;
		border-bottom: none;
		.el-tabs__item {
			color: #8795a9;
			&.is-top:last-child {
				padding-right: 40px;
			}
			&.is-top:nth-child(2) {
				padding-left: 40px;
			}
		}
		.is-active {
			color: #2f87fe;
		}
	}
	::v-deep .el-tabs__nav-wrap:after {
		content: '';
		position: absolute;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 2px;
		background-color: transparent;
		z-index: 1;
	}
	::v-deep .el-tabs__content {
		height: calc(100% - 60px);
		.el-tab-pane {
			height: 100%;
		}
	}
	.el-tabs.el-tabs--top {
		height: 100%;
	}
	// 隐掉tab的边框
	::v-deep .el-tabs__item:focus.is-active.is-focus:not(:active) {
		-webkit-box-shadow: none;
		box-shadow: none;
	}
	::v-deep .el-tabs__header {
		margin-bottom: 20px;
	}
}
</style>
