<template>
	<div class="container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch">筛选</el-button>
				<el-button @click="handleReset">重置</el-button>
			</el-form-item>
		</GcFormSimple>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="handleChangePage"
			/>
		</div>
	</div>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getfilterName } from '@/utils'
import { apiGetPayrecords } from '@/api/meterManage.api'
export default {
	props: {
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			formData: {
				modifyTime: [
					this.dayjs().startOf('year').format('YYYY-MM-DD'),
					this.dayjs().endOf('year').format('YYYY-MM-DD'),
				],
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '日期',
					prop: 'modifyTime',
					attrs: {
						type: 'daterange',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						style: {
							width: '250px',
						},
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '80px',
			},
			columns: [
				{
					key: 'createTime',
					name: '操作时间',
					tooltip: true,
				},
				{
					key: 'costOperationType',
					name: '操作类型',
					tooltip: true,
					render: (h, row) => {
						const valueStr = this.$store.getters.dataList.costOperationType
							? getfilterName(
									this.$store.getters.dataList.costOperationType,
									row.costOperationType,
									'sortValue',
									'sortName',
							  )
							: ''
						return h('span', {}, valueStr)
					},
				},
				{
					key: '',
					name: '金额',
					tooltip: true,
				},
				{
					key: '',
					name: '渠道',
					tooltip: true,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
		}
	},
	methods: {
		async getList() {
			const extractedData = Object.assign({}, ...Object.values(this.tabData))
			const formParams = trimParams(removeNullParams(this.formData))
			Object.assign(formParams, {
				archivesId: extractedData?.archivesId,
				current: this.pageData.current,
				size: this.pageData.size,
			})
			if (formParams.modifyTime && formParams.modifyTime.length > 1) {
				Object.assign(formParams, {
					payTimeStart: this.dayjs(formParams.modifyTime[0]).format('YYYY-MM-DD'),
					payTimeEnd: this.dayjs(formParams.modifyTime[1]).format('YYYY-MM-DD'),
				})
				delete formParams.modifyTime
			}
			await apiGetPayrecords(formParams)
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleSearch() {
			this.handleChangePage({ page: 1 })
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.handleChangePage({ page: 1 })
		},
	},
}
</script>

<style lang="scss" scoped>
.container {
	background-color: #fff;
	padding: 20px;
	display: flex;
	flex-direction: column;
	height: 100%;
	.table-container {
		flex: 1;
		overflow: auto;
	}
}
</style>
