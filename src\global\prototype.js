import Vue from 'vue'
import _ from 'lodash'
import { Notification } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

import confirm from './src/confirm'
import message from './src/message'
import toast from './src/toast'
import toFixed from './src/toFixed'
import { has } from '@/utils/index.js'

Vue.prototype._ = _

const duration = 3000

Vue.prototype.$confirm = confirm
Vue.prototype.$message = message
Vue.prototype.$toast = toast
Vue.prototype.$error = (message = '未知错误', title = '错误提示') => {
	Vue.prototype.$alert(message, title, {
		confirmButtonText: '确定',
		type: 'error',
	})
}

// 全局 notify
Vue.prototype.$notify = ({ message, title, type = 'success', position = 'top-right', ...restArgs }) => {
	Notification({
		title,
		message,
		position,
		type,
		duration,
		...restArgs,
	})
}

// 全局事件总线
Vue.prototype.$baseEventBus = new Vue()

// 全局progress bar
NProgress.configure({
	easing: 'ease',
	speed: 500,
	trickleSpeed: 200,
	showSpinner: false,
})
Vue.prototype.$progress = NProgress

Vue.prototype.$has = has

Number.prototype.toFixed = toFixed
String.prototype.toFixed = toFixed
