import service from './request'
import { CPM } from '@/consts/moduleNames'

// 待催缴账单列表分页查询
export function queryBillPage(data) {
	return service({
		url: `${CPM}/urge/payment/queryBillPage`,
		method: 'post',
		data,
	})
}
// 催缴记录分页查询
export function queryUrgeRecordPage(data) {
	return service({
		url: `${CPM}/urge/payment/queryUrgeRecordPage`,
		method: 'post',
		data,
	})
}
// 催缴记录分页查询
export function queryUrgeRecordPage3(data) {
	return service({
		url: `${CPM}/urge/payment/queryUrgeRecordPage3`,
		method: 'post',
		data,
	})
}
// 催缴登记
export function urgeRegister(data) {
	return service({
		url: `${CPM}/urge/payment/urgeRegister`,
		method: 'post',
		data,
	})
}
// 居民表卡视图概览催缴登记
export function urgeRegister2(data) {
	return service({
		url: `${CPM}/urge/payment/urgeRegister2`,
		method: 'post',
		data,
	})
}
//企业表卡视图概览催缴登记
export function urgeRegister3(data) {
	return service({
		url: `${CPM}/urge/payment/urgeRegister3`,
		method: 'post',
		data,
	})
}
// 欠费账单统计
export function billStatistics(data) {
	return service({
		url: `${CPM}/urge/payment/billStatistics`,
		method: 'post',
		data,
	})
}
// 批量查询水费通知单(旅顺)
export function geLsRecall(data) {
	return service({
		url: `billing/recall/ls`,
		method: 'post',
		data,
	})
}
