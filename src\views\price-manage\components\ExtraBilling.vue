<template>
	<!-- 附加费模块 -->
	<div class="model-tiered-bill">
		<div class="title">
			<p>附加费</p>
			<div class="title-right" @click="handleAdd">
				<i class="el-icon-circle-plus-outline"></i>
				<span>增加一行</span>
			</div>
		</div>
		<div class="price-table">
			<el-form :model="extraBillingForm" ref="extraBillingForm">
				<el-table
					:data="extraBillingForm.tableData"
					style="width: 100%"
					:header-cell-style="{
						background: '#DDE7FA',
						color: '#222',
					}"
				>
					<el-table-column label="附加费用">
						<template slot-scope="scope">
							<el-form-item
								:rules="[ruleRequired('请选择附加费用')]"
								:prop="`tableData[${scope.$index}].billItemId`"
							>
								<el-select
									size="small"
									filterable
									v-model="scope.row.billItemId"
									placeholder="请选择附加费用"
									@change="id => handleBillItemIdChange(id, scope.$index)"
								>
									<el-option
										v-for="(item, index) in extraBillings"
										:key="index"
										:label="item.itemName"
										:value="item.billItemId"
										:disabled="item.disabled"
									></el-option>
								</el-select>
							</el-form-item>
						</template>
					</el-table-column>
					<el-table-column prop="price" :label="`价格（元/${fieldName.baseUnit}）`">
						<template slot-scope="scope">
							<el-form-item
								:rules="[ruleRequired('请输入价格'), RULE_PRICE]"
								:prop="`tableData[${scope.$index}].billItemPrice`"
							>
								<el-input style="width: 100%" v-model="scope.row.billItemPrice"></el-input>
							</el-form-item>
						</template>
					</el-table-column>
					<el-table-column prop="option" label="操作" width="60">
						<template slot-scope="scope">
							<span class="opt-delete" @click="handleDelete(scope.$index)">删除</span>
						</template>
					</el-table-column>
				</el-table>
			</el-form>
		</div>
	</div>
</template>

<script>
import { ruleRequired, RULE_PRICE } from '@/utils/rules'
import getFieldName from '@/mixin/getFieldName.js'
import { mapActions } from 'vuex'

export default {
	name: 'ExtraBilling',
	mixins: [getFieldName],
	props: {
		priceBillItemList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			ruleRequired,
			RULE_PRICE,
			extraBillingData: [],
			extraBillingForm: {
				tableData: [],
			},
		}
	},
	computed: {
		extraBillings() {
			const result = this.extraBillingData.map(item => {
				// 下拉列表中禁用当前已填写的附加费
				const target = this.extraBillingForm.tableData.find(b => b.billItemId == item.billItemId)
				return {
					...item,
					disabled: !!target,
				}
			})
			return result
		},
	},
	created() {
		this.getExtraBillings().then(res => {
			const billItemIds = this.priceBillItemList.map(item => item.billItemId)
			// 过滤在用附加费或者已登记过的附加费
			this.extraBillingData = res.filter(
				item =>
					(item.generateType === 1 &&
						item.isSurcharge === 1 &&
						item.itemStatus === 1 &&
						item.itemType === 1) ||
					billItemIds.includes(item.billItemId),
			)
			this.extraBillingForm.tableData = [...this.priceBillItemList]
		})
	},
	methods: {
		...mapActions({
			getExtraBillings: 'apiCache/getBillItems',
		}),

		// 附加费下拉改变
		handleBillItemIdChange(id, index) {
			const result = this.extraBillingData.find(item => item.billItemId === id)
			if (result) {
				this.extraBillingForm.tableData[index].billItemPrice = result.itemAmount
			}
		},
		// 新增附加费
		handleAdd() {
			if (this.extraBillingForm.tableData.length >= 10) {
				this.$message.error('附加费最多只能添加10项')
				return
			}
			this.extraBillingForm.tableData.push({
				billItemId: '',
				billItemPrice: '',
			})
		},
		// 删除附加费
		handleDelete(idx) {
			this.extraBillingForm.tableData.splice(idx, 1)
		},
	},
}
</script>

<style lang="scss" scoped>
.model-tiered-bill {
	padding: 20px 25px;
	background: #f5f8ff;
	box-sizing: border-box;
	.title {
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		p {
			color: #222;
		}
		.title-right {
			cursor: pointer;
			color: $base-color-blue;
			font-weight: 500;
			span {
				margin-left: 4px;
			}
		}
	}
	.price-table {
		margin-top: 10px;
		.form-label {
			display: flex;
			align-items: center;
		}
		::v-deep .el-form {
			padding: 0;
			.el-form-item {
				margin-bottom: 0;
				.el-select,
				.el-select .el-input {
					width: 100%;
				}
			}
			.el-form-item__error {
				position: relative;
			}
		}
		::v-deep .el-input {
			width: 98px;
		}
		._text {
			margin: 0 6px;
		}
		.opt-delete {
			color: #ec6b60;
			cursor: pointer;
		}
	}
}
</style>
