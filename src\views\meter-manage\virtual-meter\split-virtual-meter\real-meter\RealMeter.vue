<template>
	<div class="container-wrapper">
		<gc-model-header
			class="info-title"
			title="实表选择"
			:icon="require('@/assets/images/icon/title-common-parameters.png')"
		></gc-model-header>
		<div class="container">
			<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<template v-slot:button>
					<el-button type="primary" v-show="!$route.query.archivesId" @click="_apiGetArchivesDetail">
						搜索
					</el-button>
				</template>
			</GcFormRow>
			<div class="button-group">
				<button class="gc-button gc-button-three" type="button" @click="handleReset" v-show="!isEdit">
					重 置
				</button>
				<button class="gc-button gc-button-two" type="button" @click="changeTab('virtual')">下一项</button>
			</div>
		</div>
	</div>
</template>
<script>
import { apiGetArchivesDetail2 } from '@/api/meterManage.api'
import { getFormItems } from './realForm.js'
import { ruleMaxLength, ruleRequired, RULE_INTEGERONLY } from '@/utils/rules.js'
export default {
	data() {
		return {
			formData: {
				archivesIdentity: '',
				alleyCode: '',
				bookNo: '',
				regionName: '',
				addressFullName: '',
				meterWarehouseCode: '',
				userName: '',
				contactPeople: '',
				userMobile: '',
				priceId: '',
				priceName: '',
				natureName: '',
			},
			formItems: getFormItems(this),
			formAttrs: {
				labelWidth: '110px',
				labelPosition: 'right',
				rules: {
					archivesIdentity: [ruleRequired('请输入'), ruleMaxLength(11), RULE_INTEGERONLY],
				},
			},
			archivesDetail: null,
		}
	},
	computed: {
		isEdit() {
			return this.$route.query.archivesId
		},
	},
	created() {},
	methods: {
		async _apiGetArchivesDetail() {
			try {
				const res = await apiGetArchivesDetail2({
					archivesIdentity: this.formData.archivesIdentity,
					tenantId: this.$store.getters.userInfo?.tenantId,
				})
				const extractedData = Object.assign({}, ...Object.values(res))
				this.formData = Object.assign(this.formData, extractedData)
				this.archivesDetail = extractedData
				this.validateForm()
			} catch (error) {
				this.handleReset()
			}
		},
		async validateForm() {
			const valid = await this.$refs.formRef.validate()
			const flag = valid && this.archivesDetail
			this.$emit('getValid', 'real', flag, this.archivesDetail)
			return flag
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.validateForm()
			this.archivesDetail = null
		},
		async changeTab(v) {
			const flag = await this.validateForm()
			if (flag) {
				this.$emit('changeTab', v)
			} else {
				this.$message.error(`实表信息待完善`)
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.container-wrapper {
	height: 100%;
}
.container {
	height: calc(100% - 60px);
	overflow-y: auto;
	overflow-x: hidden;
}
.button-group {
	width: 100%;
	margin-top: 20px;
	padding: 0 20px;
	.gc-button {
		margin-right: 8px;
	}
}
</style>
