<template>
	<gc-el-dialog
		:show="isShow"
		title="拆表登记"
		custom-top="120px"
		width="600px"
		@open="handleOpen"
		@close="handleClose"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { ruleMaxLength } from '@/utils/rules'
import { removeMeter } from '@/api/waterMeter.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		data: {
			type: Object,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
				meterNo: '',

				removeDate: '',
				removePerson: '',
				removeReason: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						col: 24,
						disabled: true,
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
				{
					type: 'el-input',
					label: '水表编号',
					prop: 'meterNo',
					attrs: {
						col: 24,
						disabled: true,
						placeholder: '请输入水表编号',
					},
				},
				{
					type: 'el-date-picker',
					label: '拆表时间',
					prop: 'removeDate',
					attrs: {
						col: 24,
						valueFormat: 'yyyy-MM-dd',
						placeholder: '请选择拆表时间',
					},
				},
				{
					type: 'el-input',
					label: '拆表操作人员',
					prop: 'removePerson',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入拆表操作人员',
					},
				},
				{
					type: 'el-input',
					label: '拆除原因',
					prop: 'removeReason',
					attrs: {
						col: 24,
						type: 'textarea',
						clearable: true,
						placeholder: '请输入拆除原因',
						maxlength: '64',
						showWordLimit: true,
						autosize: {
							minRows: 4,
							maxRows: 8,
						},
					},
				},
			],
			formAttrs: {
				labelWidth: '100px',
				labelPosition: 'top',
				rules: {
					removeDate: [{ required: true, message: '请选择装表时间', trigger: 'blur' }],
					removePerson: [
						{ required: true, message: '请输入拆表操作人员', trigger: 'blur' },
						ruleMaxLength(30, '拆表操作人员'),
					],
					removeReason: [{ required: true, message: '请输入拆除原因', trigger: 'blur' }],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				await removeMeter({
					archivesId: this.data.archivesId,
					...this.formData,
				})
				this.$message.success(`拆表成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleOpen() {
			this.formData.archivesIdentity = this.data?.archivesIdentity ?? ''
			this.formData.meterNo = this.data?.meterNo ?? ''
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.el-form-item--small.el-form-item {
		margin-bottom: 12px;
	}
}
</style>
