<template>
	<div class="page-layout">
		<div class="page-left">
			<el-tabs v-model="activeTab" type="border-card" @tab-click="handleTabChange">
				<el-tab-pane v-for="(tab, index) in tabs" :key="index" :label="tab.label" :name="tab.name">
					<GcFormSimple
						:ref="'formRef' + index"
						v-model="formData"
						:formItems="formItems"
						:formAttrs="formAttrs"
					/>
				</el-tab-pane>
			</el-tabs>

			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div class="right-top">
				<el-button
					v-if="activeTab === 'rename'"
					v-has="'cpm_archives_export_modify-user-name-record2-excel'"
					:disabled="!tableData.length"
					type="primary"
					@click="handleExport"
				>
					导出
				</el-button>
				<el-button
					v-if="activeTab === 'transfer'"
					v-has="'cpm_archives_export_change-user-record2-excel'"
					:disabled="!tableData.length"
					type="primary"
					@click="handleExport"
				>
					导出
				</el-button>
				<el-button
					v-if="activeTab === 'modify'"
					v-has="'cpm_enterprise_export_modify-record-excel'"
					:disabled="!tableData.length"
					type="primary"
					@click="handleExport"
				>
					导出
				</el-button>
			</div>
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			></GcTable>
		</div>
	</div>
</template>

<script>
import { removeNullParams, trimParams, exportBlob } from '@/utils/index.js'
import { ruleRequired } from '@/utils/rules.js'
import { getColumn } from './tableColumn'
import { getFormItems } from './formItem.js'
import {
	apiModifyUserNameRecord2,
	apiChangeUserRecord,
	apiModifyEnterpriseRecord,
	apiExportModifyRecordExcel,
	apiExportRenameRecordExcel,
	apiExportTransferRecordExcel,
} from '@/api/userManage.api'

const API_MAP = {
	rename: apiModifyUserNameRecord2,
	transfer: apiChangeUserRecord,
	modify: apiModifyEnterpriseRecord,
}

const EXPORT_API_MAP = {
	rename: {
		api: apiExportRenameRecordExcel,
		fileName: '更名记录列表',
	},
	transfer: {
		api: apiExportTransferRecordExcel,
		fileName: '过户记录列表',
	},
	modify: {
		api: apiExportModifyRecordExcel,
		fileName: '企业修改记录列表',
	},
}

export default {
	name: 'RenameTransferManage',
	data() {
		return {
			activeTab: 'rename',
			// 左侧查询
			formData: {},
			formItems: [],
			formAttrs: {
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
				},
			},
			// 右侧列表
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	computed: {
		columns() {
			return getColumn(this)
		},
		tabs() {
			const arr = []
			if (this.$has('cpm_archives_modify-user-name-record2')) {
				arr.push({ label: '更名', name: 'rename' })
			}
			if (this.$has('cpm_archives_change-user-record2')) {
				arr.push({ label: '过户', name: 'transfer' })
			}
			if (this.$has('cpm_enterprise_modify-record-excel')) {
				arr.push({
					label: '企业修改',
					name: 'modify',
				})
			}

			return arr
		},
		refName() {
			return `formRef${this.tabs.findIndex(item => item.name === this.activeTab)}`
		},
	},
	watch: {
		tabs: {
			handler() {
				this.activeTab = this.tabs[0]?.name
			},
			immediate: true,
		},
	},
	methods: {
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
				})

				if (formParams.optTime && formParams.optTime.length > 1) {
					const startTime = this.dayjs(formParams.optTime[0]).format('YYYY-MM-DD')
					const endTime = this.dayjs(formParams.optTime[1]).format('YYYY-MM-DD')

					if (this.activeTab === 'modify') {
						formParams.opStartDate = startTime
						formParams.opEndDate = endTime
					} else {
						formParams.modifyTimeStart = startTime
						formParams.modifyTimeEnd = endTime
					}
				}
				delete formParams.optTime
				const apiMethod = API_MAP[this.activeTab]

				if (!apiMethod) {
					return
				}

				const { total = 0, records = [] } = await apiMethod(formParams)
				this.pageData.total = total
				this.tableData = records.map(item => {
					const logDetail = JSON.parse(item.logDetail || '{}')
					return {
						...item,
						...logDetail,
					}
				})
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleTabChange() {
			this.handleReset()
			this.formItems = getFormItems(this)
			this.handleSearch()
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleReset() {
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.$refs[this.refName][0].resetFields()
		},
		handleSearch() {
			this.$nextTick(async () => {
				const valid = await this.$refs[this.refName][0].validate()
				if (!valid) return
				this.handlePageChange({ page: 1 })
			})
		},
		async handleExport() {
			const maxLength = 300000
			const formParams = trimParams(removeNullParams(this.formData))

			const params = {
				...formParams,
				current: 1,
				size: this.pageData.total,
			}
			if (this.pageData.total > maxLength) {
				this.$message.error('导出数量不能超过30万条')
				return
			}

			const config = EXPORT_API_MAP[this.activeTab]

			if (!config) {
				return
			}

			const apiMethod = config.api
			const fileName = config.fileName

			await apiMethod(params).then(res => {
				exportBlob(res, fileName)
			})
		},
	},
	mounted() {
		this.handleTabChange()
	},
}
</script>

<style lang="scss" scoped>
.page-left {
	padding-top: 0;
	padding-left: 0;
	padding-right: 0;
	flex: 0 0 280px;

	.btn-group {
		padding: 0 20px;
	}

	// 隐掉tab的边框
	::v-deep {
		.el-tabs {
			width: 100%;
			height: 100%;
			box-shadow: none;
			border: none;
			border-radius: 4px 4px 0 0;
			overflow: hidden;

			.el-tabs__content {
				padding: 0;
				height: calc(100% - 38px);

				.el-tab-pane {
					height: 100%;
				}
			}
		}

		.el-tabs--border-card > .el-tabs__header {
			border: none;
			height: 38px;
			margin-bottom: 10px;

			.el-tabs__nav {
				display: flex;
				align-items: center;
				width: 100%;
				border: none;
				height: 38px;

				.el-tabs__item {
					margin: 0;
					background: #e1ebfa;
					border: none;
					font-size: 14px;
					color: #6d7480;
					flex: 1;
					padding: 0;
					text-align: center;
					height: 38px;

					&.is-active {
						background: #ffffff;
						font-weight: 500;
						color: #2f87fe;
					}
				}
			}
		}

		.el-tabs__item:focus.is-active.is-focus:not(:active) {
			-webkit-box-shadow: none;
			box-shadow: none;
		}

		.el-form {
			padding: 0 20px;
		}
	}

	::v-deep {
		.el-range-editor.el-input__inner {
			height: auto !important;
			flex-wrap: wrap;
			width: 100%;

			.el-range-input {
				float: left;
				width: 192px;
				line-height: 32px;
				height: 32px;
				text-align: left;
				box-sizing: border-box;

				&:first-child {
					flex: 0 0 190px;
				}
			}

			.el-range-separator {
				margin-left: -5px;
				width: 25px;
				font-size: 14px;
				color: #c0c4cc;
				box-sizing: border-box;
			}

			.el-range__close-icon {
				margin-left: 90%;
				margin-top: -60px;
				float: left;
				box-sizing: border-box;
			}
		}
	}
}

.right-top {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-bottom: 12px;
}
</style>
