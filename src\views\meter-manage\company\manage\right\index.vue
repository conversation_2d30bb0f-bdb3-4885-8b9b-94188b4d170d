<template>
	<div class="page-right">
		<div class="btn-container">
			<el-button
				v-has="'cpm_archives_export_archive-list-excel2'"
				type="primary"
				:disabled="!tableData.length"
				@click="handleExport"
			>
				导出列表
			</el-button>
			<div>
				<el-button
					type="primary"
					class="update-button"
					v-has="'cpm_archives_modify_addressInfo2'"
					:disabled="!selectedData.length"
					@click="isShow = true"
				>
					批量修改地址
				</el-button>
				<el-button
					type="primary"
					class="update-button"
					v-has="'cpm_archives_withholdAccount2'"
					:disabled="!selectedData.length || selectedData.length > 1"
					@click="handleWithhold"
				>
					设置邮储代扣信息
				</el-button>
				<el-button
					type="primary"
					class="update-button"
					v-has="'cpm_archives_modify_addressInfo2'"
					:disabled="!selectedData.length"
					@click="isShow = true"
				>
					修改地址相关信息
				</el-button>
			</div>
		</div>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="changePage"
				showPage
				needType="selection"
				@dblclick="rowDbclick"
				@select="handleSelect"
				@select-all="handleSelectAll"
			/>
		</div>
		<UpdateAddress :show.sync="isShow" :data="selectedData" @refresh="changePage({ page: 1 })" />
		<AccountWithhold
			v-if="isShowWithhold"
			:show.sync="isShowWithhold"
			:archivesId="selectedData[0].archives.archivesId"
			:archivesIdentity="selectedData[0].archives.archivesIdentity"
			permissionCode="cpm_archives_withholdAccount"
		/>
	</div>
</template>

<script>
import { apiGetMeterCard2, apiExport2ArchivesList } from '@/api/meterManage.api'
import { getColumn } from './tableColumn'
import { exportBlob } from '@/utils/index.js'
import UpdateAddress from './update-address/index.vue'
import AccountWithhold from '../../../view/components/account-withhold/index.vue'
export default {
	name: '',
	components: { UpdateAddress, AccountWithhold },
	data() {
		return {
			columns: getColumn(this),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			searchParams: {},
			loading: false,
			isShow: false,
			selectedData: [],
			isShowWithhold: false,
		}
	},
	computed: {},
	methods: {
		handleWithhold() {
			this.isShowWithhold = true
		},
		changePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this._apiGetMeterCard()
		},
		async _apiGetMeterCard() {
			this.loading = true
			this.selectedData = []
			const { current, size } = this.pageData
			const params = {
				...this.searchParams,
				current,
				size,
				userType: 4,
			}
			try {
				const { records, total } = await apiGetMeterCard2(params)
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		rowDbclick(obj) {
			if (!this.$has('cpm_archives_detail5')) {
				this.$notify({
					message: '暂无权限访问，请联系管理员！',
					title: '提示',
					offset: 60,
					type: 'warning',
				})
				return
			}
			const archivesId = obj.row.archives.archivesId
			this.$router.push({
				path: '/meterManage/companyMeterView',
				query: {
					archivesId,
				},
			})
		},
		clearTable() {
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
		async handleExport() {
			const maxLength = 300000
			const params = {
				...this.searchParams,
				orgCode: this.searchParams.orgCode,
				current: this.pageData.current,
				size: this.pageData.total,
				userType: 4,
			}
			if (this.pageData.total > maxLength) {
				this.$message.error('导出数量不能超过30万条')
				return
			}

			await apiExport2ArchivesList(params).then(res => {
				exportBlob(res, '企业表卡列表')
			})
		},
		handleSelect(selection) {
			this.selectedData = selection
		},
		handleSelectAll(selection) {
			this.selectedData = selection
		},
	},
}
</script>

<style lang="scss" scoped>
.btn-container {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20px;
	.update-button {
		margin-left: 20px;
	}
}
.table-container {
	flex: 1;
	height: 0;
}
</style>
