<template>
	<GcDetailCard :detail-card-info="detailCardInfo" :header-num="headerNum">
		<template #card-content>
			<div class="card-content">
				<div class="content-item" v-for="(item, index) in displayList" :key="index">
					<p class="field">{{ item.key }}</p>
					<p class="value">
						{{ item.value }}
					</p>
				</div>
			</div>
		</template>
		<template #card-footer>
			<div class="card-footer">
				<el-button v-has="'cpm_enterprise_update'" type="text" class="blue" @click="goModify">
					<i class="iconfontCis icon-modify"></i>
					修改
				</el-button>
			</div>
		</template>
	</GcDetailCard>
</template>

<script>
import { getfilterName } from '@/utils'
export default {
	components: {},
	props: {
		userDetail: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			detailCardInfo: {
				bgUrl: require('@/assets/images/bg/pic-file.png'),
				signUrl: require('@/assets/images/icon/title-user.png'),
				cardName: '企业信息',
			},
		}
	},
	computed: {
		headerNum() {
			let obj = {
				key: '企业名称',
				value: this.userDetail?.enterpriseName || '--',
				field: 'enterpriseName',
			}
			return obj
		},
		//左侧卡片展示字段
		displayList() {
			const list = [
				{
					key: '企业编号',
					value: '--',
					field: 'enterpriseNumber',
				},
				{
					key: '企业地址',
					value: '--',
					field: 'enterpriseAddress',
				},
				{
					key: '联系人',
					value: '--',
					field: 'contactPeople',
				},
				{
					key: '手机',
					value: '--',
					field: 'userMobile',
				},
				{
					key: '电话',
					value: '--',
					field: 'contactPhone',
				},
				{
					key: '收费方式',
					value: '--',
					field: 'chargingMethod',
				},
				{
					key: '托收协议号',
					value: '--',
					field: 'collectionAgreementNumber',
				},
			]

			list.forEach(item => {
				const { chargingMethod = [] } = this.$store.getters.dataList || {}
				if (item.field === 'chargingMethod') {
					item.value = getfilterName(chargingMethod, this.userDetail[item.field], 'sortValue', 'sortName')
				} else {
					item.value = this.userDetail[item.field] || '--'
				}
			})

			return list
		},
	},
	methods: {
		goModify() {
			const { enterpriseNumber = '' } = this.userDetail
			this.$router.push({
				path: '/userManage/enterpriseModify',
				query: {
					enterpriseNumber,
				},
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.detail-card {
	.card-footer {
		padding: 0 0 0 12px;
		text-align: left;
		::v-deep .el-button {
			font-size: 14px;
			color: $base-color-yellow;
			padding-bottom: 0;
			i {
				padding-right: 3px;
				font-size: 16px;
			}
			span {
				display: flex;
				align-items: center;
			}
		}
		.el-button + .el-button {
			margin-left: 2px;
			margin-right: 2px;
		}
		.blue {
			color: $base-color-blue;
		}
	}
}
</style>
