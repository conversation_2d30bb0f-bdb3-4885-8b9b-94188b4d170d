<template>
	<gc-el-dialog
		:show="isShow"
		:title="typeText"
		custom-top="120px"
		width="600px"
		@open="handleOpen"
		@close="handleClose"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { ruleMaxLength } from '@/utils/rules'
import { batchScrapMeter, scrapMeter } from '@/api/waterMeter.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		isBatch: {
			type: Boolean,
			default: false,
		},
		// 批量：meterIds集合
		// 非批量：当前水表信息对象
		data: {
			type: [Array, Object],
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		typeText() {
			return this.isBatch ? '批量报废' : '报废登记'
		},
	},
	data() {
		return {
			formData: {
				meterNo: '',
				operatorDate: '',
				operatorPerson: '',
				reason: '',
			},
			formItems: [
				{
					hide: this.isBatch,
					type: 'el-input',
					label: '水表编号',
					prop: 'meterNo',
					attrs: {
						col: 24,
						disabled: true,
					},
				},
				{
					type: 'el-date-picker',
					label: '报废时间',
					prop: 'operatorDate',
					attrs: {
						col: 24,
						valueFormat: 'yyyy-MM-dd',
						placeholder: '请选择报废时间',
					},
				},
				{
					type: 'el-input',
					label: '操作人员',
					prop: 'operatorPerson',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入操作人员',
					},
				},
				{
					type: 'el-input',
					label: '报废原因',
					prop: 'reason',
					attrs: {
						col: 24,
						type: 'textarea',
						clearable: true,
						placeholder: '请输入报废原因',
						maxlength: '64',
						showWordLimit: true,
						autosize: {
							minRows: 4,
							maxRows: 8,
						},
					},
				},
			],
			formAttrs: {
				labelWidth: '110px',
				labelPosition: 'top',
				rules: {
					operatorDate: [{ required: true, message: '请选择报废时间', trigger: 'change' }],
					operatorPerson: [ruleMaxLength(30, '操作人员')],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				if (this.isBatch) {
					await batchScrapMeter({
						meterIds: this.data,
						...this.formData,
					})
				} else {
					await scrapMeter({
						meterId: this.data.meterId,
						...this.formData,
					})
				}
				this.$message.success(`${this.typeText}成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleOpen() {
			if (this.isBatch) return
			this.formData.meterNo = this.data?.meterNo ?? ''
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.el-form-item--small.el-form-item {
		margin-bottom: 12px;
	}
}
</style>
