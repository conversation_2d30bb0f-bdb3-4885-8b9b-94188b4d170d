<template>
	<div>
		<el-dropdown @command="handleCommand" @visible-change="handleVisibleChange">
			<span class="avatar-dropdown">
				<el-avatar class="user-avatar" :src="avatar" />
				<div class="user-name">
					<div class="main-name">
						<span class="hidden-xs-only login-name">
							{{ _.get(userInfo, 'staffName', '') }}
						</span>
						<i class="gc-dropdown el-icon-arrow-down" :class="{ 'gc-dropdown-active': active }"></i>
					</div>
					<div class="vice-name" v-if="userLevel == 0">{{ tenantName }}</div>
				</div>
			</span>
			<template #dropdown>
				<el-dropdown-menu>
					<el-dropdown-item command="edit">修改密码</el-dropdown-item>
					<el-dropdown-item command="logout">退出登录</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown>
		<update-pass :show="showPass" v-if="showPass" @handle-dialog-close="showPass = false" />
	</div>
</template>

<script>
import updatePass from './updatePass.vue'
import identity from '@/mixin/identity.js'
export default {
	name: 'GcAvatar',
	components: { updatePass },
	mixins: [identity],
	data() {
		return {
			active: false,
			avatar: require('@/assets/images/avatar.png'),
			showPass: false,
		}
	},
	computed: {
		isSwitchTenantShow() {
			return this.$store.getters.isSwitchTenantShow
		},
		isSwitchOrgShow() {
			return this.$store.getters.isSwitchOrgShow
		},
	},
	watch: {
		// 监听租户的切换
		tenantId: {
			handler(newVal, oldVal) {
				if (oldVal && newVal !== oldVal) {
					this.reInitTagsView()
				}
			},
		},
		// 监听组织机构的切换
		userInfo: {
			handler(newVal, oldVal) {
				console.log(1111, newVal, oldVal)
				if (oldVal.orgId && newVal.orgId !== oldVal.orgId) {
					this.reInitTagsView()
				}
			},
		},
		isSwitchTenantShow(val) {
			this.showSwitchTenant = val
		},
		isSwitchOrgShow(val) {
			this.showSwitchOrg = val
		},
	},
	methods: {
		handleCommand(command) {
			switch (command) {
				case 'logout':
					this.$confirm('你确定要退出系统吗？退出后需要重新登录系统。', '提示').then(() => {
						this.logout()
					})
					break
				case 'edit':
					this.showPass = true
					break
			}
		},
		handleVisibleChange(val) {
			this.active = val
		},
		logout() {
			//    fix:SAAS5-3972
			//   this._logout().then(() => {
			this.$store.dispatch('user/resetUserLS').then(() => location.reload())
			//   });
		},
		// 关闭出affix外所有标签
		reInitTagsView() {
			this.$store.dispatch('tagsView/delAllViews').then(tagList => {
				const lastView = tagList.slice(-1)[0]
				if (lastView) {
					this.$router.push(lastView.fullPath)
				} else {
					this.$router.push('/')
				}
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.avatar-dropdown {
	display: flex;
	align-content: center;
	align-items: center;
	justify-content: center;
	justify-items: center;

	.user-avatar {
		width: 32px;
		height: 32px;
		margin-left: 26px;
		cursor: pointer;
		border-radius: 50%;
	}

	.user-name {
		position: relative;
		display: flex;
		flex-direction: column;
		align-content: center;
		align-items: flex-start;
		max-width: 120px;
		margin-left: 6px;
		cursor: pointer;

		[class*='ri-'] {
			margin-left: 0 !important;
		}
		.main-name {
			height: 50%;
			.login-name {
				max-width: 100px;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				display: inline-block;
				vertical-align: middle;
				line-height: 18px;
			}
			.gc-dropdown {
				padding-left: 5px;
				vertical-align: middle;
			}
		}
		.vice-name {
			width: 100%;
			text-overflow: ellipsis;
			overflow: hidden;
			white-space: nowrap;
			font-size: 12px;
			line-height: 15px;
		}
	}
}
</style>
