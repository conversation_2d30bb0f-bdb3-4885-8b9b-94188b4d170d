<template>
	<div class="tab-content">
		<div class="bg-overflow meter-info-box">
			<gc-model-header title="表具信息" :icon="require('@/assets/images/icon/title-user.png')" />
			<gc-group-detail :data="meterData" />
			<div class="footer">
				<el-button
					v-has="'cpm_meter_modify-meter-reading'"
					v-if="editable"
					type="text"
					size="medium"
					@click="handleModifyPointer"
				>
					<i class="iconfontCis icon-modify"></i>
					修改指针数
				</el-button>
			</div>
		</div>
		<div class="bottom-box">
			<div class="bg-overflow card-info">
				<gc-model-header title="表卡信息" :icon="require('@/assets/images/icon/title-file.png')" />
				<div class="content-box">
					<gc-group-detail :data="cardData" />
					<div class="footer">
						<div>
							<!-- 有表卡id才能跳转表卡视图 -->
							<el-button
								v-if="editable && data.archives && data.archives.archivesId"
								type="text"
								size="medium"
								@click="handleGoCardView"
							>
								<i class="iconfontCis icon-read"></i>
								查看表卡视图
							</el-button>
						</div>
						<div v-if="editable" class="right-btns">
							<!-- 无表卡编号 且 状态为待装表 -->
							<el-button
								v-has="'cpm_meter_install-meter'"
								v-if="!data.archives && data.meter.meterStatus === 0"
								type="text"
								size="medium"
								@click="handleInstall"
							>
								<i class="el-icon-circle-plus"></i>
								装表
							</el-button>
							<!-- 存在表卡id 就可以拆表 -->
							<el-button
								v-has="'cpm_archives_remove-meter2'"
								v-if="data.archives && data.archives.archivesId"
								type="text"
								size="medium"
								@click="handleUninstall"
							>
								<i class="el-icon-remove"></i>
								拆表
							</el-button>
							<!-- 状态为在用 才可以停水 -->
							<el-button
								v-has="'cpm_archives_disable3'"
								v-if="data.meter.meterStatus === 1"
								type="text"
								size="medium"
								@click="handleCut"
							>
								<i class="el-icon-video-pause"></i>
								停水
							</el-button>
							<!-- 状态为停水 才可以恢复用水 -->
							<el-button
								v-has="'cpm_archives_enable3'"
								v-if="data.meter.meterStatus === 5"
								type="text"
								size="medium"
								@click="handleRestore"
							>
								<i class="el-icon-refresh-left"></i>
								恢复用水
							</el-button>
						</div>
					</div>
				</div>
			</div>
			<div class="bg-overflow summary-info">
				<gc-model-header title="总表信息" :icon="require('@/assets/images/icon/title-file.png')" />
				<div class="content-box">
					<gc-group-detail :data="summaryData" />
				</div>
			</div>
		</div>

		<!-- 修改指针弹窗 -->
		<ModifyPointerDialog ref="pointerDialogRef" :show.sync="showPointerDialog" @success="handleRefresh" />
		<!-- 装表弹窗 -->
		<InstallMeterDialog
			ref="installDialogRef"
			:show.sync="showInstallDialog"
			:data="dialogData"
			@success="handleRefresh"
		/>
		<!-- 拆表弹窗 -->
		<UninstallMeterDialog
			ref="uninstallDialogRef"
			:show.sync="showUninstallDialog"
			:data="dialogData"
			@success="handleRefresh"
		/>
		<!-- 停水弹窗 -->
		<CutMeterDialog ref="cutDialogRef" :show.sync="showCutDialog" :data="dialogData" @success="handleRefresh" />
		<!-- 恢复用水弹窗 -->
		<RestoreMeterDialog
			ref="restoreDialogRef"
			:show.sync="showRestoreDialog"
			:data="dialogData"
			@success="handleRefresh"
		/>
	</div>
</template>

<script>
import ModifyPointerDialog from '../../components/modify-pointer-dialog.vue'
import InstallMeterDialog from '../../components/install-meter-dialog.vue'
import UninstallMeterDialog from '../../components/uninstall-meter-dialog.vue'
import CutMeterDialog from '../../components/cut-meter-dialog.vue'
import RestoreMeterDialog from '../../components/restore-meter-dialog.vue'
import { getfilterName } from '@/utils'

export default {
	components: {
		ModifyPointerDialog,
		InstallMeterDialog,
		UninstallMeterDialog,
		CutMeterDialog,
		RestoreMeterDialog,
	},
	props: {
		data: {
			type: Object,
			default: () => {
				return {
					address: {},
					archives: {},
					book: {},
					price: {},
					user: {},
				}
			},
		},
		editable: Boolean,
	},
	data() {
		return {
			// 修改指针弹窗
			showPointerDialog: false,
			// 装表弹窗
			showInstallDialog: false,
			// 拆表弹窗
			showUninstallDialog: false,
			// 停水弹窗
			showCutDialog: false,
			// 恢复用水
			showRestoreDialog: false,
		}
	},
	computed: {
		// 表具信息
		meterData() {
			const { meter = {} } = this.data
			const {
				baseMeterNo = '--',
				antiTheftCode = '--',
				meterReading = undefined,
				installPosition = '--',
				useTime = '--',
				tableWellLocation = '--',
			} = meter
			return {
				list: [
					{
						key: '水表标号',
						value: baseMeterNo,
						field: 'baseMeterNo',
					},
					{
						key: '防盗编号',
						value: antiTheftCode,
						field: 'antiTheftCode',
					},
					{
						key: '指针数',
						value: meterReading,
						field: 'meterReading',
					},
					{
						key: '装表位置',
						// value: installPosition,
						value:
							getfilterName(
								this.$store.getters?.dataList?.installPosition || [],
								installPosition,
								'sortValue',
								'sortName',
							) || '--',
						field: 'installPosition',
					},
					{
						key: '装表时间',
						value: useTime,
						field: 'useTime',
					},
					{
						key: '表井位置',
						value: tableWellLocation,
						field: 'tableWellLocation',
					},
				],
				row: 6,
			}
		},
		// 表卡信息
		cardData() {
			const { address = {}, archives = {}, book = {}, price = {}, user = {}, meter = {} } = this.data
			const { meterReadingDate = '--' } = meter
			const { addressFullName = '--' } = address || {}
			const { archivesIdentity = '--', recordSeq = '--' } = archives || {}
			const { bookNo = '--', bookTypeDesc = '--', alleyName = '--', meterReadingCycleDesc = '--' } = book || {}
			const { userName = '--', contactPeople = '--', userMobile = '--', chargingMethod = '--' } = user || {}
			const { priceCode = '--', priceName = '--', billingTypeId = '' } = price || {}
			return {
				list: [
					{
						key: '表卡编号',
						value: archivesIdentity,
						field: 'archivesIdentity',
					},
					{
						key: '用户名称',
						value: userName,
						field: 'userName',
					},
					{
						key: '联系人',
						value: contactPeople,
						field: 'contactPeople',
					},
					{
						key: '手机号',
						value: userMobile,
						field: 'userMobile',
					},
					{
						key: '表具地址',
						value: addressFullName,
						field: 'addressFullName',
						col: 4,
					},
					// {
					//   key: "总分表",
					//   value: summaryArchives,
					//   field: "summaryArchives",
					// },
					{
						key: '表册编号',
						value: bookNo,
						field: 'bookNo',
					},
					{
						key: '册本类型',
						value: bookTypeDesc,
						field: 'bookTypeDesc',
					},
					{
						key: '坊别',
						value: alleyName,
						field: 'alleyName',
					},
					{
						key: '上次抄表时间',
						value: meterReadingDate,
						field: 'meterReadingDate',
					},
					{
						key: '册内序号',
						value: recordSeq,
						field: 'recordSeq',
					},
					{
						key: '抄表周期',
						value: meterReadingCycleDesc,
						field: 'meterReadingCycleDesc',
						col: 3,
					},
					{
						key: '价格编号',
						value: priceCode,
						field: 'priceCode',
					},
					{
						key: '价格名称',
						value: priceName,
						field: 'priceName',
					},
					{
						key: '计费类型',
						value:
							getfilterName(
								this.$store.getters?.dataList?.billingType || [],
								billingTypeId,
								'sortValue',
								'sortName',
							) || '--',
						field: 'billingTypeId',
					},
					{
						key: '收费方式',
						value:
							getfilterName(
								this.$store.getters?.dataList?.chargingMethod || [],
								chargingMethod,
								'sortValue',
								'sortName',
							) || '--',
						field: 'chargingMethod',
					},
				],
				row: 4,
			}
		},
		// 总表信息
		summaryData() {
			const { dmaArchives = {} } = this.data
			const {
				dmaArchivesIdentity = '--',
				dmaArchivesTypeDesc = '--',
				caliber = '--',
				dmaStationNo = '--',
				dmaMonitorBuildingNum = '--',
				dmaMonitoringRange = '--',
				dmaAddress = '--',
			} = dmaArchives || {}
			return {
				list: [
					{
						key: '表卡编号',
						value: dmaArchivesIdentity,
						field: 'dmaArchivesIdentity',
					},
					{
						key: 'DMA类型',
						value: dmaArchivesTypeDesc,
						field: 'dmaArchivesTypeDesc',
					},
					{
						key: '流量计口径',
						value: caliber,
						field: 'caliber',
					},
					{
						key: '站号',
						value: dmaStationNo,
						field: 'dmaStationNo',
					},
					{
						key: '监控栋数',
						value: dmaMonitorBuildingNum,
						field: 'dmaMonitorBuildingNum',
					},
					{
						key: '监控范围',
						value: dmaMonitoringRange,
						field: 'dmaMonitoringRange',
					},
					{
						key: '地址',
						value: dmaAddress,
						field: 'dmaAddress',
						col: 2,
					},
				],
				row: 2,
			}
		},
		// 操作弹窗需要用到的初始化数据 archivesId 或 meterId
		dialogData() {
			const { meter = {}, archives = {} } = this.data
			const { meterId = '', meterNo = '' } = meter || {}
			const { archivesIdentity = '', archivesId = '' } = archives || {}
			return {
				archivesId,
				meterId,
				meterNo,
				archivesIdentity,
			}
		},
	},
	methods: {
		// 跳转表卡视图
		handleGoCardView() {
			const userType = this.data?.user?.userType
			const archivesId = this.data?.archives?.archivesId

			if (!this.$has('cpm_archives_detail') && userType === 3) {
				this.$notify({
					message: '暂无权限访问，请联系管理员！',
					title: '提示',
					offset: 60,
					type: 'warning',
				})
				return
			}
			if (!this.$has('cpm_archives_detail5') && userType === 4) {
				this.$notify({
					message: '暂无权限访问，请联系管理员！',
					title: '提示',
					offset: 60,
					type: 'warning',
				})
				return
			}

			const path = userType === 3 ? '/meterManage/residentMeterView' : '/meterManage/companyMeterView'
			this.$router.push({
				path,
				query: {
					archivesId,
				},
			})
		},
		// 打开修改指针弹窗
		handleModifyPointer() {
			this.showPointerDialog = true
			this.$nextTick(() => {
				this.$refs.pointerDialogRef.setFormData(this.data?.meter || {})
			})
		},
		// 打开装表弹窗
		handleInstall() {
			this.showInstallDialog = true
		},
		// 打开拆表弹窗
		handleUninstall() {
			this.showUninstallDialog = true
		},
		// 打开停水弹窗
		handleCut() {
			this.showCutDialog = true
		},
		// 打开恢复用水弹窗
		handleRestore() {
			this.showRestoreDialog = true
		},
		// 抛出事件 刷新详情数据
		handleRefresh() {
			this.$emit('refresh')
		},
	},
}
</script>

<style lang="scss" scoped>
.tab-content {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 100%;
	padding-right: 3px;
	overflow: auto;
}
.bg-overflow {
	width: 100%;
	height: 100%;
	background: #fff;
	overflow: auto;
}

.meter-info-box {
	height: 180px;
}
.bottom-box {
	flex: 1;
	display: flex;
	align-items: center;
	margin-top: 12px;
	overflow: auto;
}
.card-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}
.summary-info {
	display: flex;
	flex-direction: column;
	width: 400px;
	margin-left: 12px;
}
.footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 24px;
}
.content-box {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	padding: 12px 0;
}
.el-button {
	::v-deep {
		span {
			display: flex;
			align-items: center;
		}
		i {
			padding-right: 3px;
		}
	}
}
.right-btns {
	::v-deep .el-button {
		color: $base-color-yellow;
	}
}
</style>
