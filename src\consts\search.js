import { ruleMaxLength } from '@/utils/rules.js'

export const searchKeys = [
	{ key: 'userName', label: '用户名称' },
	{ key: 'userMobile', label: '手机号码' },
	{ key: 'addressName', label: '表具地址' },
	{ key: 'meterNo', label: '表具编号' },
	{ key: 'archivesNo', label: '档案编号' },
	{ key: 'archivesIdentity', label: '档案标识', isHide: true },
	{ key: 'certificateNo', label: '身份证号', isHide: true },
]

export const icCardSearchKeys = [
	{ key: 'userName', label: '用户名称', rule: [ruleMaxLength(32)] },
	{ key: 'meterNo', label: '表具编号', rule: [ruleMaxLength(32)] },
	{ key: 'addressName', label: '表具地址', rule: [ruleMaxLength(64)] },
]

export const meterMonitorSearchKeys = [
	{
		key: 'userName',
		label: '用户名称',
	},
	{
		key: 'meterNo',
		label: '表具编号',
	},
	{
		key: 'deviceNo',
		label: '通讯编号',
	},
]

export const meterAlarmSearchKeys = [
	{
		key: 'deviceNo',
		label: '设备编号',
	},
]

// 缴费页面搜索选项
export const rechargeSearchKeys = [
	{ key: 'archivesNo', label: '档案编号' },
	{ key: 'userName', label: '用户名称' },
	{ key: 'addressName', label: '表具地址' },
	{ key: 'meterNo', label: '表具编号' },
	{ key: 'userMobile', label: '手机号' },
	{ key: 'archivesIdentity', label: '档案标识' },
	{ key: 'certificateNo', label: '身份证号' },
]
