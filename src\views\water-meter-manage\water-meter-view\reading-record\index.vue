<template>
	<div class="wrapper">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch(false)">查询</el-button>
				<el-button @click="handleReset">重置</el-button>
			</el-form-item>
		</GcFormSimple>
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			@current-page-change="handlePageChange"
		>
			<template v-slot:imageUrl="{ row }">
				<UploadImgSimple v-model="row.imageUrl" />
			</template>
		</GcTable>
	</div>
</template>

<script>
import UploadImgSimple from '@/components/UploadImgSimple'
import { getReadingMeterRecord } from '@/api/waterMeter.api'

export default {
	name: 'ReadingReocrd',
	components: { UploadImgSimple },
	data() {
		const _t = this
		const pickerOptions = {
			shortcuts: [
				{
					text: '最近一年',
					onClick(picker) {
						const end = new Date(_t.dayjs().endOf('year').format('YYYY-MM-DD'))
						const start = new Date(_t.dayjs().startOf('year').format('YYYY-MM-DD'))
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近三年',
					onClick(picker) {
						const end = new Date(_t.dayjs().endOf('year').format('YYYY-MM-DD'))
						const start = new Date(_t.dayjs().subtract(2, 'year').startOf('year').format('YYYY-MM-DD'))
						picker.$emit('pick', [start, end])
					},
				},
			],
		}
		return {
			formData: {
				archivesIdentity: '',
				dateRange: [
					this.dayjs().subtract(2, 'year').startOf('year').format('YYYY-MM-DD'),
					this.dayjs().endOf('year').format('YYYY-MM-DD'),
				],
			},
			formItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
				{
					type: 'el-date-picker',
					label: '抄表日期',
					prop: 'dateRange',
					attrs: {
						type: 'daterange',
						clearable: false,
						defaultTime: ['00:00:00', '23:59:59'],
						valueFormat: 'yyyy-MM-dd',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						pickerOptions,
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '90px',
				rules: {
					dateRange: [{ required: true, message: '请选择抄表日期', trigger: 'change' }],
				},
			},
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'thisRecordDate',
					name: '抄表时间',
					tooltip: true,
				},
				{
					key: 'meterReadingStaffName',
					name: '抄表员',
					tooltip: true,
				},
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'lastMeterReading',
					name: '上次指针数',
					tooltip: true,
				},
				{
					key: 'curMeterReading',
					name: '本次指针数',
					tooltip: true,
				},
				{
					key: 'useAmount',
					name: '水量',
					tooltip: true,
				},
				{
					key: 'imageUrl',
					name: '抄表照片',
					tooltip: true,
				},
				{
					key: 'readingStatusDesc',
					name: '抄表状态',
					tooltip: true,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},

	created() {
		// this.getList();
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.getList(1)
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			// if (!this.formData.dateRange || this.formData.dateRange?.length === 0) {
			//   this.$message.error("请选择抄表日期");
			//   return;
			// }
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { dateRange, ...rest } = this.formData
				const { total = 0, records = [] } = await getReadingMeterRecord({
					meterId: this.$route.query.meterId,
					size,
					current,
					...rest,
					readDateBegin: dateRange && dateRange.length > 0 ? dateRange[0] : '',
					readDateEnd: dateRange && dateRange.length > 0 ? dateRange[1] : '',
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	padding: 20px;
}
</style>
