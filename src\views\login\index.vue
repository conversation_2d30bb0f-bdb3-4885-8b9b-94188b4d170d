<template>
	<div class="login-container">
		<div class="login-content">
			<div class="logo"></div>
			<el-form class="login-form" ref="loginForm" :model="loginForm" label-position="top" :rules="loginRules">
				<el-form-item prop="username" label="用户名">
					<el-input
						ref="username"
						v-model="loginForm.username"
						placeholder="请输入用户名"
						name="username"
						type="text"
					/>
				</el-form-item>
				<el-form-item prop="password" label="密码">
					<el-input
						:key="passwordType"
						ref="password"
						class="password-input"
						v-model="loginForm.password"
						:type="passwordType"
						placeholder="请输入密码"
						name="password"
						@input="handlePwdChange"
						@change="handlePwdChange"
						@blur="handlePwdChange"
					>
						<span
							class="show-pwd iconfontCis"
							:class="passwordType === 'password' ? 'icon-biyan' : 'icon-zhengyan'"
							@click="showPwd"
							slot="append"
						></span>
					</el-input>
				</el-form-item>
				<el-form-item prop="dynamicCode" label="登录令牌">
					<el-input
						placeholder="请输入登录令牌"
						v-model="loginForm.dynamicCode"
						@keyup.enter.native.stop="handleLoginClick"
					></el-input>
				</el-form-item>
				<el-button
					:disabled="loginDisabled"
					:loading="loading"
					@click.native.prevent="handleLoginClick"
					class="login-btn"
					:class="{ 'om-login-btn': isOmLogin }"
				>
					{{ btnText }}
				</el-button>
			</el-form>
		</div>
		<!-- <div class="remarks">
      <div class="certifyInfo" v-show="needCertify">
        登录机构：<span id="output">{{ certifyOrgName }}</span>
      </div>
      Copyright © {{ new Date().getFullYear() }} 金卡智能 All Rights Reserved ｜
      浙ICP备05008091号-10
    </div> -->

		<update-pwd
			:show="showPwdDialog"
			:show-warning-tip="true"
			v-if="showPwdDialog"
			@handle-dialog-close="handleDialogClose"
			@updateSuc="handlePwdUpdated"
		></update-pwd>
	</div>
</template>
<script>
import Login from './login.js'

export default {
	name: 'Login',
	mixins: [Login],
	data: function () {
		return {}
	},
	mounted() {},
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
	.login-container .el-input input {
		color: $cursor;
	}
}
</style>

<style lang="scss" scoped>
@import './login.scss';
</style>
