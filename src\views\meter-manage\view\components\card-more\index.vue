<template>
	<GcElDialog :show="isShow" title="表卡信息" :showFooter="false" @close="isShow = false">
		<GcGroupDetail :data="cardInfo"></GcGroupDetail>
	</GcElDialog>
</template>

<script>
import { getfilterName } from '@/utils'
import { userTypeOptions } from '@/consts/optionList'
import { yesOrNoEnum } from '@/consts/enums.js'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		cardInfo() {
			const list = [
				{
					key: '表卡编号',
					value: '--',
					field: 'archivesIdentity',
				},
				{
					key: '表卡状态',
					value: '--',
					field: 'archivesStatus',
				},
				{
					key: '表卡类型',
					value: '--',
					field: 'virtualMeterType',
				},
				{
					key: '是否小区总表',
					value: '--',
					field: 'summaryArchives',
				},
				{
					key: '是否虚分',
					value: '--',
					field: 'isVirtual',
				},
				{
					key: '档案类型',
					value: '--',
					field: 'userType',
				},
				{
					key: '建档时间',
					value: '--',
					field: 'archivesTime',
				},
				{
					key: '创建者',
					value: '--',
					field: 'createStaffName',
				},
				{
					key: '账号',
					value: '--',
					field: 'accountNumber',
				},
				{
					key: '自来水编号',
					value: '--',
					field: 'tapWaterNo',
				},
				{
					key: '房屋建设年代',
					value: '--',
					field: 'houseYear',
				},
				{
					key: '层数',
					value: '--',
					field: 'floorNum',
				},
				{
					key: '压力区',
					value: '--',
					field: 'pressureZone',
				},
				{
					key: 'GIS编号',
					value: '--',
					field: 'gisCode',
				},
				{
					key: '管网编号',
					value: '--',
					field: 'pipeNetworkCode',
				},
			]
			const extractedData = Object.assign({}, ...Object.values(this.tabData))
			const getValue = (field, value) => {
				const { archiveState = [], virtualMeterType = [] } = this.$store.getters.dataList || {}

				switch (field) {
					case 'archivesStatus':
						return getfilterName(archiveState, value, 'sortValue', 'sortName')
					case 'virtualMeterType':
						return getfilterName(virtualMeterType, value, 'sortValue', 'sortName')
					case 'userType':
						return getfilterName(userTypeOptions, value)
					case 'summaryArchives':
					case 'isVirtual':
						return yesOrNoEnum[value]
					default:
						return value
				}
			}
			list.forEach(item => {
				const obj = [
					'tapWaterNo',
					'houseYear',
					'floorNum',
					'pressureZone',
					'gisCode',
					'pipeNetworkCode',
				].includes(item.field)
					? this.tabData.address
					: extractedData
				item.value = obj ? getValue(item.field, obj[item.field]) : ''
			})

			return {
				list,
			}
		},
	},
}
</script>
