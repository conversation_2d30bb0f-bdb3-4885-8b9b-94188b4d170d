<template>
	<gc-el-dialog :show="isShow" :title="`${typeText}`" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button v-show="isEdit" btn-type="three" @click.native="handleCancel">取消</gc-button>
			<gc-button
				v-has="'cpm_businessHall_updateBusinessHallBaseInfo'"
				v-show="!isEdit"
				@click.native="handleEdit"
			>
				编辑
			</gc-button>
			<gc-button v-show="isEdit" @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { ruleMaxLength, RULE_POSITIVEINTEGERONLY_STARTOFZERO } from '@/utils/rules.js'
import { queryCollectionBankList, queryConsignBankCodeList, updateBusinessHallBaseInfo } from '@/api/basicConfig.api'

const mobileReg = /^1[3456789]\d{9}$/
const telReg = /^(\d{3,4}-)?\d{7,8}$/

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},

		typeText() {
			return !this.isEdit ? '查看营业分公司' : '编辑营业分公司'
		},
	},
	data() {
		return {
			// 是否为编辑状态
			isEdit: false,

			formData: {
				orgName: '',
				businessAdress: '',
				// 联系电话
				phoneNumber: '',
				// 开户银行
				bankName: '',
				// 支行行号
				bankNo: '',

				bankAcctName: '',
				bankAddres: '',
				bankAcctCode: '',
			},
			originFormData: {},
			formItems: [
				{
					type: 'el-input',
					label: '营业分公司名称',
					prop: 'orgName',
					attrs: {
						col: 24,
						disabled: true,
						placeholder: '请输入营业分公司名称',
					},
				},
				{
					type: 'el-input',
					label: '营业分公司地址',
					prop: 'businessAdress',
					attrs: {
						col: 24,
						disabled: true,
						clearable: true,
						placeholder: '请输入营业分公司地址',
					},
				},
				{
					type: 'el-input',
					label: '联系电话',
					prop: 'phoneNumber',
					attrs: {
						col: 24,
						disabled: true,
						clearable: true,
						placeholder: '请输入联系电话（如：0000-1234567）',
					},
				},
				{
					type: 'el-select',
					label: '开户银行',
					prop: 'bankName',
					options: [],
					attrs: {
						col: 24,
						disabled: true,
						filterable: true,
						placeholder: '请选择开户银行',
					},
					events: {
						change: () => {
							this.formItems[4].options = []
							this.formData.bankNo = ''
							this.getSubBrankCode()
						},
					},
				},
				{
					type: 'el-select',
					label: '支行行号',
					prop: 'bankNo',
					options: [],
					attrs: {
						col: 24,
						filterable: true,
						disabled: true,
						placeholder: '请选择支行行号',
					},
				},
				{
					type: 'el-input',
					label: '开户户名',
					prop: 'bankAcctName',
					attrs: {
						col: 24,
						disabled: true,
						clearable: true,
						placeholder: '请输入开户户名',
					},
				},
				{
					type: 'el-input',
					label: '开户地址',
					prop: 'bankAddres',
					attrs: {
						col: 24,
						disabled: true,
						clearable: true,
						placeholder: '请输入开户地址',
					},
				},
				{
					type: 'el-input',
					label: '开户银行账户',
					prop: 'bankAcctCode',
					attrs: {
						col: 24,
						disabled: true,
						clearable: true,
						placeholder: '请输入开户银行账户',
					},
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					orgName: [
						{
							required: true,
							message: '请输入营业分公司名称',
							trigger: 'blur',
						},
					],
					businessAdress: [
						{
							required: true,
							message: '请输入营业分公司地址',
							trigger: 'blur',
						},
						ruleMaxLength(250, '营业分公司地址'),
					],
					phoneNumber: [
						{ required: true, message: '请输入联系电话', trigger: 'blur' },
						{ validator: this.isMobileOrTel, trigger: 'blur' },
					],
					bankName: [{ required: true, message: '请选择开户银行', trigger: 'blur' }],
					bankNo: [{ required: true, message: '请选择支行行号', trigger: 'blur' }],
					bankAcctName: [
						{ required: true, message: '请输入开户户名', trigger: 'blur' },
						ruleMaxLength(100, '开户户名'),
					],
					bankAddres: [
						{ required: true, message: '请输入开户地址', trigger: 'blur' },
						ruleMaxLength(250, '开户地址'),
					],
					bankAcctCode: [
						{ required: true, message: '请输入开户银行账户', trigger: 'blur' },
						ruleMaxLength(40, '开户银行账户'),
						RULE_POSITIVEINTEGERONLY_STARTOFZERO,
					],
				},
			},
		}
	},
	methods: {
		// 获取开户银行下拉数据
		async getBankList() {
			try {
				const res = await queryCollectionBankList()
				this.formItems[3].options = res?.map(item => {
					return {
						label: item.bankName,
						value: item.bankCode,
					}
				})
			} catch (error) {
				this.formItems[3].options = []
				console.error(error)
			}
		},
		// 获取支行行号下拉数据
		async getSubBrankCode() {
			try {
				const res = await queryConsignBankCodeList({
					bankType: this.formData.bankName,
				})
				this.formItems[4].options = res?.map(item => {
					return {
						label: item.bankName,
						value: item.bankCode,
					}
				})
			} catch (error) {
				this.formItems[4].options = []
				console.error(error)
			}
		},

		// 联系电话验证
		isMobileOrTel(rule, value, callback) {
			const isMobile = mobileReg.test(value)
			const isTel = telReg.test(value)
			if (!isMobile && !isTel) {
				callback(new Error('格式错误，请输入有效的手机号或座机号'))
			} else {
				callback()
			}
		},

		// 编辑
		handleEdit() {
			this.isEdit = true
			this.formAttrs.disabled = false
			this.formItems.forEach((item, index) => {
				item.attrs.disabled = index === 0
			})
		},
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				await updateBusinessHallBaseInfo(this.formData)
				this.$message.success(`${this.typeText}成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.handleReset()
			this.formData = {
				orgName: '',
				businessAdress: '',
				// 联系电话
				phoneNumber: '',
				// 开户银行
				bankName: '',
				// 支行行号
				bankNo: '',

				bankAcctName: '',
				bankAddres: '',
				bankAcctCode: '',
			}
			this.originFormData = {}
			this.isShow = false
		},
		handleCancel() {
			this.handleReset()
			this.formData = JSON.parse(JSON.stringify(this.originFormData))
			this.getSubBrankCode()
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.formItems.forEach(item => {
				item.attrs.disabled = true
			})
			this.isEdit = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
			this.originFormData = JSON.parse(JSON.stringify(this.formData))
			this.getBankList()
			this.getSubBrankCode()
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.el-dialog__body {
		height: 560px;
	}
}
</style>
