<template>
	<gc-el-dialog :show="isShow" :title="`${typeText}报表`" custom-top="120px" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { ruleMaxLength } from '@/utils/rules.js'
import { addReport, editReport } from '@/api/statisticsManage.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		editType: {
			type: String,
			default: 'add',
		},
		// 报表的所属的分类id
		reportCategoryId: [String, Number],
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},

		typeText() {
			return this.editType === 'add' ? '新增' : '编辑'
		},
	},
	data() {
		return {
			formData: {
				reportName: '',
				reportUrl: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '报表名称',
					prop: 'reportName',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入报表名称',
					},
				},
				{
					type: 'el-input',
					label: '报表路径',
					prop: 'reportUrl',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入报表路径',
					},
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					reportName: [
						{ required: true, message: '请输入报表名称', trigger: 'blur' },
						ruleMaxLength(16, '报表名称'),
					],
					reportUrl: [{ required: true, message: '请输入报表路径', trigger: 'blur' }],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				if (this.editType === 'add') {
					await addReport({
						...this.formData,
						categoryId: this.reportCategoryId,
					})
				} else {
					await editReport({
						...this.formData,
						categoryId: this.reportCategoryId,
					})
				}
				this.$message.success(`${this.typeText}成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.el-dialog__body {
		height: 240px;
	}
}
</style>
