import service from './request'
import { CPM, PAYMENT } from '@/consts/moduleNames'

// 用户管理列表
export const apiGetUserList = data => {
	return service({
		url: `${CPM}/user/list`,
		method: 'POST',
		data,
	})
}
// 用户视图详情
export const apiGetUserDatailsById = params => {
	return service({
		url: `${CPM}/user/getUserDatailsById`,
		method: 'GET',
		params,
	})
}
// 用户视图：更名
export const apiModifyUserName = data => {
	return service({
		url: `${CPM}/user/modify-user-name`,
		method: 'POST',
		data,
	})
}
// 表卡视图：更名
export const apiModifyUserName2 = data => {
	return service({
		url: `${CPM}/user/modify-user-name2`,
		method: 'POST',
		data,
	})
}
// 企业表卡视图：更名
export const apiModifyUserName3 = data => {
	return service({
		url: `${CPM}/user/modify-user-name3`,
		method: 'POST',
		data,
	})
}
// 更名记录
export const apiModifyUserNameRecord = data => {
	return service({
		url: `${CPM}/archives/modify-user-name-record`,
		method: 'POST',
		data,
	})
}
export const apiModifyUserNameRecord2 = data => {
	return service({
		url: `${CPM}/archives/modify-user-name-record2`,
		method: 'POST',
		data,
	})
}
// 企业修改记录
export const apiModifyEnterpriseRecord = data => {
	return service({
		url: `${CPM}/enterprise/modify-record`,
		method: 'POST',
		data,
	})
}

// 过户记录
export const apiChangeUserRecord = data => {
	return service({
		url: `${CPM}/archives/change-user-record2`,
		method: 'POST',
		data,
	})
}
// 过户记录（用户视图）使用
export const apiChangeUserRecordUserView = data => {
	return service({
		url: `${CPM}/archives/change-user-record`,
		method: 'POST',
		data,
	})
}

// 变更记录-导出
export function apiExportModifyRecordExcel(data) {
	return service({
		url: `${CPM}/enterprise/export/modify-record-excel`,
		method: 'POST',
		data,
		responseType: 'blob',
	})
}
// 更名记录 - 导出
export function apiExportRenameRecordExcel(data) {
	return service({
		url: `${CPM}/archives/export/modify-user-name-record2-excel`,
		method: 'POST',
		data,
		responseType: 'blob',
	})
}
// 过户记录 - 导出
export function apiExportTransferRecordExcel(data) {
	return service({
		url: `${CPM}/archives/export/change-user-record2-excel`,
		method: 'POST',
		data,
		responseType: 'blob',
	})
}

// 开票记录
export const apiInvoiceRecordList2 = data => {
	return service({
		url: `${PAYMENT}/invoice/record-list2 `,
		method: 'POST',
		data,
	})
}
// 新增托收账号
export const apiCreateCollectionAccount = data => {
	return service({
		url: `${CPM}/collectionAccount`,
		method: 'POST',
		data,
	})
}
// 获取托收协议号
export const apiGetAgreementNum = params => {
	return service({
		url: `${CPM}/collectionAccount/obtainAgreementNum`,
		method: 'GET',
		params,
	})
}
// 修改托收账号信息
export const apiSetCollectionAccount = data => {
	return service({
		url: `${CPM}/collectionAccount`,
		method: 'PUT',
		data,
	})
}
// 查询托收账号列表
export const apiGetCollectionAccountList = data => {
	return service({
		url: `${CPM}/collectionAccount/list`,
		method: 'POST',
		data,
	})
}

//查询托收账号所有用户
export const apiGetCollectionAccountUserList = params => {
	return service({
		url: `${CPM}/collectionAccount/user-list`,
		method: 'GET',
		params,
	})
}
// 查询托收账号变更记录
export const apiGetCollectionAccountModifyRecord = params => {
	return service({
		url: `${CPM}/collectionAccount/modify-record`,
		method: 'GET',
		params,
	})
}
// 修改用户信息
export const apiModifyUser = data => {
	return service({
		url: `${CPM}/user/modify-user`,
		method: 'POST',
		data,
	})
}
// 修改用户信息2
export const apiModifyUser2 = data => {
	return service({
		url: `${CPM}/user/modify-user2`,
		method: 'POST',
		data,
	})
}
// 修改用户信息2
export const apiModifyUser3 = data => {
	return service({
		url: `${CPM}/user/modify-user3`,
		method: 'POST',
		data,
	})
}
// 修改用户开票信息
export const apiModifyUserInvoiceInfo = data => {
	return service({
		url: `${CPM}/user/modify-user-invoice-info`,
		method: 'POST',
		data,
	})
}
// 银行下拉框
export const apiQueryCollectionBankList = params => {
	return service({
		url: `${CPM}/businessHall/queryCollectionBankList`,
		method: 'GET',
		params,
	})
}
// 支行行号下拉框
export const apiQueryConsignBankCodeList = params => {
	return service({
		url: `${CPM}/businessHall/queryConsignBankCodeList`,
		method: 'GET',
		params,
	})
}
// 新增企业信息
export const apiAddEnterprise = data => {
	return service({
		url: `${CPM}/enterprise/add`,
		method: 'POST',
		data,
	})
}
// 修改企业信息
export const apiUpdateEnterprise = data => {
	return service({
		url: `${CPM}/enterprise/update`,
		method: 'POST',
		data,
	})
}
// 企业管理分页信息查询
export const queryEnterprisePage = data => {
	return service({
		url: `${CPM}/enterprise/queryEnterprisePage`,
		method: 'POST',
		data,
	})
}

// 查询托收账号详情
export const apiGetCollectionAccountSingleDetail = data => {
	return service({
		url: `${CPM}/collectionAccount/singleDetail`,
		method: 'POST',
		data,
	})
}

// 根据企业编号获取企业信息
export const apiQueryEnterpriseInfoByNumber = params => {
	return service({
		url: `${CPM}/enterprise/queryEnterpriseInfoByNumber`,
		method: 'GET',
		params,
	})
}

//  查询企业信息
export const apiQueryEnterpriseInfoById = params => {
	return service({
		url: `${CPM}/enterprise/queryEnterpriseInfoById`,
		method: 'GET',
		params,
	})
}
// 查询企业表卡信息
export const apiArchivesByEnterpriseNumber = params => {
	return service({
		url: `${CPM}/enterprise/queryArchivesByEnterpriseNumber`,
		method: 'GET',
		params,
	})
}

// 企业编号校验
export function apiVerifyEnterpriseNumber(params) {
	return service({
		url: `${CPM}/enterprise/verify/enterpriseNumber`,
		method: 'GET',
		params,
	})
}
// 查询购方信息
export function apiQueryInvoiceBuyer(params) {
	return service({
		url: `${CPM}/invoice-buyer/by-taxpayerIdentity`,
		method: 'GET',
		params,
	})
}
