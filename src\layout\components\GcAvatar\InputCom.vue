<template>
	<el-input
		:value="ivalue"
		@input="onInput"
		:type="iconStatus[kname] == 'see' ? 'text' : 'password'"
		:placeholder="placeholder"
	>
		<i
			class="iconfontCis icon-biyan"
			slot="suffix"
			@click="iconClick(kname, 'see')"
			v-if="iconStatus[kname] == 'nosee'"
		></i>
		<i class="iconfontCis icon-zhengyan" slot="suffix" v-else @click="iconClick(kname, 'nosee')"></i>
	</el-input>
</template>

<script>
export default {
	name: 'InputCom',
	components: {},
	props: {
		kname: String,
		value: String,
		placeholder: String,
	},
	data() {
		return {
			iconStatus: {
				oriPass: 'nosee',
				newPass: 'nosee',
				confirmPass: 'nosee',
			},
		}
	},
	computed: {
		ivalue: {
			get: function () {
				return this.value
			},
		},
	},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		iconClick(key, val) {
			this.iconStatus[key] = val
		},
		onInput(value) {
			this.$emit('update:value', value)
		},
	},
}
</script>
<style lang="scss" scoped></style>
