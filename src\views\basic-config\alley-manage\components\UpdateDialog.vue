<template>
	<gc-el-dialog :show="isShow" :title="`${typeText}坊别`" width="600px" @open="handleOpen" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { ruleDigit, ruleMaxLength, RULE_POSITIVEINTEGERONLY_STARTOFZERO } from '@/utils/rules.js'
import { obtainCode, checkCodeIsRepeat, addAlley, updateAlley } from '@/api/basicConfig.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		editType: {
			type: String,
			default: 'add',
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},

		typeText() {
			return this.editType === 'add' ? '新增' : '编辑'
		},
	},
	data() {
		const validateCode = async (rule, value, callback) => {
			if (value && this.editType === 'add') {
				const res = await checkCodeIsRepeat({
					alleyCode: this.formData.alleyCode,
				})
				if (res) {
					callback(new Error('编号已存在，请重新输入'))
				}
			}
			callback()
		}
		return {
			formData: {
				// 坊别编号，系统自动创建 取当前已创建的坊别编号 进行+1
				alleyCode: '',
				alleyName: '',
				orgCodeList: [],
			},
			formItems: [
				{
					type: 'el-input',
					label: '坊别编号',
					prop: 'alleyCode',
					attrs: {
						col: 24,
						clearable: true,
						disabled: false,
						placeholder: '请输入坊别编号',
					},
				},
				{
					type: 'el-input',
					label: '坊别名称',
					prop: 'alleyName',
					attrs: {
						col: 24,
						clearable: true,
						disabled: false,
						placeholder: '请输入坊别名称',
					},
				},
				{
					type: 'el-select',
					label: '所属营业分公司',
					prop: 'orgCodeList',
					options: this.$store.getters.orgList,
					attrs: {
						col: 24,
						clearable: true,
						multiple: true,
						placeholder: '请选择营业分公司',
					},
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					alleyCode: [
						{
							required: true,
							message: '请输入坊别编号',
							trigger: ['blur', 'change'],
						},
						RULE_POSITIVEINTEGERONLY_STARTOFZERO,
						ruleDigit(3, '坊别编号必须为'),
						{ validator: validateCode, trigger: 'blur' },
					],
					alleyName: [
						{
							required: true,
							message: '请输入坊别名称',
							trigger: 'change',
						},
						ruleMaxLength(20, '坊别名称'),
					],
					orgCodeList: [
						{
							required: true,
							message: '请选择所属营业分公司',
							trigger: 'change',
						},
					],
				},
			},
		}
	},
	methods: {
		// 生成唯一坊别编号
		async getCode() {
			const res = await obtainCode()
			this.formData.alleyCode = res
		},

		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				const params = JSON.parse(JSON.stringify(this.formData))
				delete params.orgCode
				delete params.orgName
				if (this.editType === 'add') {
					await addAlley(params)
				} else {
					await updateAlley(params)
				}
				this.$message.success(`${this.typeText}坊别成功`)
				this.$emit('success')
				this.isShow = false
			}
		},

		async handleOpen() {
			const isEdit = this.editType === 'edit'
			this.formItems[0].attrs.disabled = isEdit

			if (!isEdit) {
				this.getCode()
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.formData = {
				alleyCode: '',
				alleyName: '',
				orgCodeList: [],
			}
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, {
				...data,
				orgCodeList: data.orgCode?.split(',') || [],
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
</style>
