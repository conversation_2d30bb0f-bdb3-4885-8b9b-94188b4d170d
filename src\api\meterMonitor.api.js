import service from './request'

/**
 * 获取设备类型列表，
 * @param {*} subUrl 远传表：device-type/tenant/meter-type，采集器：device-type/tenant/dtu-type
 * @param {*} params
 * @param {*} others
 * @returns
 */
export const apiGetDeviceTypeList = (deviceSort, params) => {
	let obj = {
		url: 'cpm/device-type/tenant/' + (deviceSort == '0' ? 'meter-type' : 'dtu-type'),
		method: 'GET',
		params: params,
	}
	return service(obj)
}

// 系统管理员获取租户组织结构列表
export function apiGetTenantOrgList(parameter, others) {
	let obj = {
		url: 'v1/tos/organization/sys/tree',
		method: 'GET',
		params: parameter,
	}
	if (others && others.constructor === Object) {
		obj = { ...obj, ...others }
	}
	return service(obj)
}

export const apiGetLongConnectDeviceTypeList = params => {
	return service({
		url: 'cpm/device-type/long-connect/list',
		method: 'get',
		params: params,
	})
}
//查询表具类型
export const apiGetMeterTypeList = params => {
	return service({
		url: '/cpm/device-type/list',
		method: 'get',
		params: params,
	})
}

// 表具监控列表查询
export const apiGetMonitorList = (parameter, others) => {
	let obj = {
		url: 'cpm/device-monitor/meter-list',
		method: 'POST',
		data: parameter,
	}
	if (others && others.constructor === Object) {
		obj = { ...obj, ...others }
	}
	return service(obj)
}

export const apiGetBatchSendCount = (parameter, others) => {
	let obj = {
		url: 'cpm/code/web/batch/send/count ',
		method: 'POST',
		data: parameter,
	}
	if (others && others.constructor === Object) {
		obj = { ...obj, ...others }
	}
	return service(obj)
}

// 长连接表具监控列表查询
export const apiGetLongConnectMonitorList = (parameter, others) => {
	let obj = {
		url: 'cpm/device-monitor/long-connect/list',
		method: 'POST',
		data: parameter,
	}
	if (others && others.constructor === Object) {
		obj = { ...obj, ...others }
	}
	return service(obj)
}

// 获取报警记录
export const apiGetAlarmRecordList = parameter => {
	let obj = {
		url: 'cpm/alarm/get-alarmrecord',
		method: 'POST',
		data: parameter,
	}
	return service(obj)
}
// 报警记录标记为已恢复
// export const apiRestoreAlarmRecord = (parameter) => {
//   let obj = {
//     url: "/cpm/alarm/restore-alarm-record",
//     method: "POST",
//     data: parameter,
//   };
//   return service(obj);
// };
// 报警解除
export const apiCancelAlarm = param =>
	service({
		url: '/cpm/alarm/restore-alarm-record',
		method: 'POST',
		data: param,
	})

// dtu报警详细信息获取
export const apiGetMeterListForDtu = parameter => {
	let obj = {
		url: 'cpm/dtu/meter-list',
		method: 'GET',
		params: parameter,
	}
	return service(obj)
}

export const apiGetMeterDetail = parameter => {
	let obj = {
		url: 'cpm/device-monitor/meter-detail',
		method: 'GET',
		params: parameter,
	}
	return service(obj)
}

//获取指令名称
export const apiGetCommandNameList = parameter => {
	let obj = {
		url: 'cpm/code/command-name-list',
		method: 'GET',
		params: parameter,
	}
	return service(obj)
}

// 获取指令分类
export const apiGetCommandSort = parameter => {
	let obj = {
		url: 'cpm/code/command-sort',
		method: 'GET',
		params: parameter,
	}
	return service(obj)
}

// 获取指令明细
export const apiGetCommandDetailList = parameter => {
	let obj = {
		url: 'cpm/code/command-detail-list',
		method: 'POST',
		data: parameter,
	}
	return service(obj)
}
// 取消发送指令
export const apiCancelCommand = parameter => {
	let obj = {
		url: 'cpm/code/cancel-command',
		method: 'PUT',
		data: parameter,
	}
	return service(obj)
}
// 重新发送指令
export const apiResendCommand = parameter => {
	let obj = {
		url: 'cpm/code/resend-command',
		method: 'PUT',
		data: parameter,
	}
	return service(obj)
}

// 获取表具日志
export const apiGetMeterLogRecord = parameter => {
	let obj = {
		url: 'cpm/device-monitor/detail-log',
		method: 'POST',
		data: parameter,
	}
	return service(obj)
}
// 获取采集记录的列表
export const apiGetCollectionRecord = parameter => {
	let obj = {
		url: 'cpm/device-monitor/collection',
		method: 'POST',
		data: parameter,
	}
	return service(obj)
}
// 获取采集记录的图表
export const apiGetCollectionRecordChart = parameter => {
	let obj = {
		url: '/cpm/device-monitor/collection-curve',
		method: 'POST',
		data: parameter,
	}
	return service(obj)
}

// 获取指令操作列表
export const apiGetCommandList = parameter => {
	let obj = {
		url: 'cpm/code/web/commandlist',
		method: 'GET',
		params: parameter,
	}
	return service(obj)
}
// 获取指令参数列表
export const apiGetCommandParams = parameter => {
	let obj = {
		url: 'cpm/code/params/paramslist',
		method: 'GET',
		params: parameter,
	}
	return service(obj)
}

// 参数设置指令下发
export const apiSendCommand = parameter => {
	let obj = {
		url: 'cpm/code/web/send',
		method: 'POST',
		data: parameter,
	}

	return service(obj)
}

// 批量指令下发
export const apiBatchSendCommand = parameter => {
	let obj = {
		url: 'cpm/code/web/batch/send',
		method: 'POST',
		data: parameter,
	}

	return service(obj)
}

// FEAT: 重复表具管理相关
// 重复表具列表查询
export const apiGetDuplicateMeterList = parameter => {
	let obj = {
		url: 'cpm/device-monitor/duplicate-meter/list',
		method: 'GET',
		params: parameter,
	}
	return service(obj)
}
// 删除tb平台设备
export const apiDelDuplicateMeter = parameter => {
	let obj = {
		url: 'cpm/device-monitor/del-duplicate-meter',
		method: 'DELETE',
		params: parameter,
	}
	return service(obj)
}
// 获取批量指令列表
export const apiGetBatchCommandList = parameter => {
	let obj = {
		url: 'cpm/code/command-batch-list',
		method: 'POST',
		data: parameter,
	}
	return service(obj)
}
// 获取批量指令详情表具列表
export const apiGetBatchCommandMeter = parameter => {
	let obj = {
		url: 'cpm/code/command-batch-detail',
		method: 'POST',
		data: parameter,
	}
	return service(obj)
}
// 获取批量指令详情
export const apiGetBatchCommandDetail = param =>
	service({
		url: 'cpm/code/command-batch-info',
		method: 'POST',
		data: param,
	})
// 表具切换组织机构
export const apiDuplicateMeterChangeOrg = data =>
	service({
		url: '/cpm/device-monitor/duplicate-meter/change-orgCode',
		method: 'PUT',
		data,
	})
// 翻新表
export const apiDuplicateMeterRenovate = data =>
	service({
		url: '/cpm/device-monitor/duplicate-meter/renovate',
		method: 'PUT',
		data,
	})
