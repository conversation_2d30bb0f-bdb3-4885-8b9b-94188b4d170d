<template>
	<div class="wrapper">
		<div class="left-wrapper">
			<div class="left-title">总分表简目录</div>
			<GcTab :tabList="tabList" @changeTab="changeTab" :defaultTab="activeTab" />
		</div>
		<div class="right-wrapper">
			<!-- 总表信息 -->
			<TotalMeterInfo
				ref="totalMeterInfoRef"
				v-show="activeTab == 'totalMeterInfo'"
				@changeTab="changeTab"
				@getValid="getValid"
				@getDmaArchivesId="val => (dmaArchivesId = val)"
			/>
			<!-- 分表选择 -->
			<SubMeterSelect
				ref="subMeterSelectRef"
				v-show="activeTab == 'subMeterSelect'"
				:dmaArchivesId="dmaArchivesId"
				@changeTab="changeTab"
			/>
			<!-- 分表列表 -->
			<SubMeterList
				ref="subMeterListRef"
				v-show="activeTab == 'subMeterList'"
				:dmaArchivesId="dmaArchivesId"
				@changeTab="changeTab"
				@getValid="getValid"
			/>
		</div>
	</div>
</template>

<script>
import TotalMeterInfo from './total-meter-info/index.vue' // 总表信息
import SubMeterSelect from './sub-meter-select/index.vue' // 分表选择
import SubMeterList from './sub-meter-list/index.vue' // 分表列表
export default {
	components: { TotalMeterInfo, SubMeterSelect, SubMeterList },
	data() {
		return {
			tabList: [
				{
					label: '总表信息',
					value: 'totalMeterInfo',
					status: 1,
					disabled: false,
					tip: '请确认建档',
				},
				{
					label: '分表选择',
					value: 'subMeterSelect',
					status: 1,
					disabled: false,
					tip: ' ',
				},
				{
					label: '分表列表',
					value: 'subMeterList',
					status: 1,
					disabled: false,
					tip: ' ',
				},
			],
			activeTab: '',
			dmaArchivesId: '',
		}
	},
	activated() {
		const { dmaArchivesIdentity } = this.$route.query
		if (dmaArchivesIdentity) {
			this.$refs.totalMeterInfoRef.formData.dmaArchivesIdentity = dmaArchivesIdentity
			this.$refs.totalMeterInfoRef.handleSearch()
		}
	},
	methods: {
		changeTab(v) {
			this.activeTab = v
			if (v === 'subMeterList') {
				this.$refs.subMeterListRef.handlePageChange({ page: 1 })
			}
		},
		getValid(key, flag, message) {
			// 没有建档, 其余菜单不可点击
			if (key === 'totalMeterInfo') {
				this.tabList
					.filter(item => item.value !== 'totalMeterInfo')
					.forEach(item => {
						item.disabled = !flag
					})
			}
			const obj = this.tabList.find(item => item.value === key)
			if (obj) {
				obj.tip = message ? message : obj.tip
				obj.status = flag ? 2 : 1
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-wrap: wrap;
	height: 100%;
}
.left-wrapper {
	position: relative;
	padding: 20px;
	flex-grow: 0;
	flex-shrink: 0;
	width: 240px;
	height: calc(100% - 40px);
	background-color: #fff;
	.left-title {
		height: 48px;
		line-height: 48px;
		color: #000000;
		font-family: Source Han Sans CN;
		font-size: 16px;
		font-weight: 500;
	}
}
.left-wrapper:after {
	position: absolute;
	top: 20px;
	bottom: 20px;
	right: 0;
	content: '';
	display: block;
	clear: both;
	width: 1px;
	border-right: 1px dashed #eef0f3;
}
.right-wrapper {
	width: 0;
	flex: 1;
	padding: 20px;
	height: calc(100% - 40px);
	background-color: #fff;
}
.button-group {
	width: 100%;
	height: 40px;
	.btn-create,
	.btn-preview {
		margin-top: 20px;
		border-radius: 4px;
		height: 32px;
	}
	.btn-create {
		width: 216px;
	}
	.btn-preview {
		width: 110px;
		border: 1px solid #2f87fe;
		color: #2f87fe;
	}
}
</style>
