export function getColumn(_this) {
	let columns = []
	const baseColumns = [
		{
			key: 'streetName',
			name: '街道/乡镇',
			tooltip: true,
		},
		{
			key: 'areaName',
			name: '小区',
			tooltip: true,
		},
	]
	if (_this.activeTab === 'status') {
		columns = baseColumns.concat([
			{
				key: 'sumMeters',
				name: '总表数',
				tooltip: true,
			},
			{
				key: 'archivesCount',
				name: '用户表卡总数',
				tooltip: true,
			},
			{
				key: 'takeOver',
				name: '接管状态',
				tooltip: true,
				render: (h, row) => {
					return h('span', {}, row.takeOver ? '已接管' : '未接管')
				},
			},
			{
				key: 'operate',
				name: '下载小区用户表卡',
				fixed: 'right',
				hide: !_this.$has(['cpm_report_area_archives_export_excel']),
			},
		])
	} else {
		columns = baseColumns.concat([
			{
				key: 'receiveDate',
				name: '接管时间',
				tooltip: true,
			},
			{
				key: 'receiver',
				name: '接管人',
				tooltip: true,
			},
			{
				key: 'archivesCount',
				name: '接管用户表数',
				tooltip: true,
			},
		])
	}
	return columns
}
