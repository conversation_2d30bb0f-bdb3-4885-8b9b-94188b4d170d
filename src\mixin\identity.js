import { isBlank } from '@/utils/validate.js'
export default {
	computed: {
		// 是否是管理员
		isAdmin() {
			return this.$store.getters.userInfo.isAdmin
		},
		// FEAT 3.7.0取消跨域运维角色
		isCrossDomain() {
			return this.$store.getters.userInfo.isCrossDomain
		},
		// tenantId
		tenantId() {
			return this.$store.getters.tenant.id
		},
		// tenantName
		tenantName() {
			return this.$store.getters.tenant.name
		},
		// 用户等级
		userLevel() {
			return this.$store.getters.userInfo.userLevel
		},
		userInfo() {
			return this.$store.getters.userInfo
		},
		tenant() {
			return this.$store.getters.tenant
		},
		// 业务领域 gas-燃气 water-水务
		realm() {
			return this.tenant.realm
		},
	},
	methods: {
		// 接口增加tenantId字段
		addTenantId(obj) {
			if (this.userLevel == 0) {
				!isBlank(this.tenantId) ? (obj['tenantId'] = this.tenantId) : null
			}
			return obj
		},
	},
}
