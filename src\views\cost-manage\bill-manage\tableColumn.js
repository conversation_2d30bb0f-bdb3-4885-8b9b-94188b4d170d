import { getfilterName } from '@/utils'
export function getColumn(_this) {
	return [
		{
			key: 'billDate',
			name: '账期',
			tooltip: true,
			minWidth: 100,
		},
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'userName',
			name: '用户名称',
			tooltip: true,
		},
		{
			key: 'billStatus',
			name: '账单状态',
			render: (h, row, total, scope) => {
				const { billStatus = [] } = _this.$store.getters.dataList || {}
				const valueStr = getfilterName(billStatus, row[scope.column.property], 'sortValue', 'sortName')
				return h('span', {}, valueStr)
			},
			minWidth: 100,
		},
		{
			key: 'useAmount',
			name: '水量',
			tooltip: true,
		},
		{
			key: 'priceCode',
			name: '价格编号',
			tooltip: true,
		},
		{
			key: 'useAmt',
			name: '水费',
			tooltip: true,
		},
		{
			key: 'billItemAmt',
			name: '污水费',
			tooltip: true,
		},
		{
			key: 'billItemAmt2',
			name: '附加费',
			tooltip: true,
		},
		{
			key: 'receivableAmount',
			name: '应缴金额',
			tooltip: true,
		},
		{
			key: 'pushFlag',
			name: '是否推送',
			render: (h, row) => {
				const valueStr = row.pushFlag == 1 ? '是' : '否'
				return h('span', {}, valueStr)
			},
		},
		{
			key: 'invoiceStatus',
			name: '开票状态',
			render: (h, row) => {
				const { invoiceState = [] } = _this.$store.getters.dataList || {}
				const valueStr = getfilterName(invoiceState, row.invoiceStatus, 'sortValue', 'sortName')
				return h('span', {}, valueStr)
			},
		},
		{
			key: 'billNo',
			name: '账单编号',
			tooltip: true,
			minWidth: 250,
		},
		{
			name: '操作',
			key: 'operate',
			fixed: 'right',
			minWidth: 320,
			tooltip: true,
			hide: !_this.$has([
				'billing_bill-adjust_reduction',
				'billing_bill-adjust_adjustment',
				'billing_bill_clear',
				'billing_bill-adjust_partial-payment',
			]),
		},
	]
}
