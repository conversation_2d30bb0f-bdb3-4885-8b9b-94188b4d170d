<template>
	<div class="title-wrapper">
		<div class="left">
			<div class="icon">
				<img :src="titleInfo.icon" />
			</div>
			<div class="label">{{ titleInfo.label }}</div>
		</div>
		<div class="more">
			<slot></slot>
		</div>
	</div>
</template>
<script>
export default {
	name: 'TitleWrapper',
	props: {
		titleKey: {},
	},
	data() {
		const titles = {
			meterStatus: {
				icon: require('@/assets/images/icon/title-status.png'),
				label: '表具状态',
			},
			meterStatusDetail: {
				icon: require('@/assets/images/icon/title-meter-detail.png'),
				label: '状态明细',
			},
			alarmList: {
				icon: require('@/assets/images/icon/title-alarm.png'),
				label: '报警列表',
			},
			batchCommandDetail: {
				icon: require('@/assets/images/icon/title-order.png'),
				label: '批量指令详情',
			},
			batchCommandMeterList: {
				icon: require('@/assets/images/icon/title-meter.png'),
				label: '指令执行表具监控',
			},
			permissionTree: {
				icon: require('@/assets/images/icon/title-structure.png'),
				label: '权限组结构',
			},
			permissionDetail: {
				icon: require('@/assets/images/icon/title-jurisdiction.png'),
				label: '权限详情',
			},
		}
		return {
			titles,
		}
	},
	computed: {
		titleInfo() {
			return this.titleKey ? this.titles[this.titleKey] : {}
		},
	},
}
</script>
<style lang="scss" scoped>
.title-wrapper {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 72px;
	padding: 20px 20px 30px;
	box-sizing: border-box;
	border-radius: 4px;
	.left {
		display: flex;
		align-items: center;
		height: 22px;
		line-height: 22px;
		vertical-align: middle;
		.label {
			font-size: 18px;
			font-weight: 600;
			color: #444;
		}
		.icon {
			margin-right: 6px;
			width: 22px;
			height: 22px;
			img {
				display: block;
				width: 100%;
				height: 100%;
			}
		}
	}
	.more {
		display: flex;
		align-items: center;
	}
}
</style>
