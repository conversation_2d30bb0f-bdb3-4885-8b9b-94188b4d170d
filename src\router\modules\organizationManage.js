import Layout from '@/layout'
export default [
	{
		path: '/organizationManage',
		name: 'OrganizationManage',
		component: Layout,
		redirect: '/organizationManage/roleManage',
		meta: {
			title: '组织',
			icon: 'icon-cis_yj_zuzhi',
			permissions: [
				'v1_tos_role_list',
				'v1_tos_role_create',
				'v1_tos_role_modify',
				'v1_tos_role_delete',
				'v1_tos_department_queryTree',
				'v1_tos_department_addDepartment',
				'v1_tos_department_updateDepartment',
				'v1_tos_department_deleteDepartment',
				'v1_tos_staff_create',
				'v1_tos_staff_modify',
				'v1_tos_staff_modify2',
				'auth_aggregation_password_tenant_manage_update',
			],
		},
		children: [
			{
				path: 'roleManage',
				name: 'RoleManage',
				component: () => import('@/views/organization-manage/role-manage/index.vue'),
				meta: {
					title: '角色管理',
					keepAlive: true,
					icon: 'icon-cis_ej_juese',
					permissions: ['v1_tos_role_list', 'v1_tos_role_create', 'v1_tos_role_modify', 'v1_tos_role_delete'],
				},
			},
			{
				path: 'staffManage',
				name: 'StaffManage',
				component: () => import('@/views/organization-manage/staff-manage/index.vue'),
				meta: {
					title: '部门员工管理',
					keepAlive: true,
					icon: 'icon-cis_ej_bumenyuangong',
					permissions: [
						'v1_tos_department_queryTree',
						'v1_tos_department_addDepartment',
						'v1_tos_department_updateDepartment',
						'v1_tos_department_deleteDepartment',
						'v1_tos_staff_create',
						'v1_tos_staff_modify',
						'v1_tos_staff_modify2',
						'auth_aggregation_password_tenant_manage_update',
					],
				},
			},
		],
	},
]
