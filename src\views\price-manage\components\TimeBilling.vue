<template>
	<div class="model-tiered-bill">
		<div class="title">
			<p>分时计价策略</p>
		</div>
		<div class="time-table">
			<el-form :model="timePriceForm" ref="timePriceForm">
				<el-table
					:data="timePriceForm.tableData"
					style="width: 100%"
					:header-cell-style="{
						background: '#DDE7FA',
						color: '#222',
					}"
				>
					<el-table-column label="顺序" width="60">
						<template slot-scope="scope">{{ scope.$index + 1 }}</template>
					</el-table-column>
					<el-table-column label="调整时间">
						<template slot-scope="scope">
							<el-form-item
								:prop="`tableData[${scope.$index}].time`"
								class="form-label"
								:rules="[
									ruleRequired('请选择时间'),
									{ validator: scope.$index === 1 ? secondTimeRule : '' },
								]"
							>
								<el-date-picker
									style="width: 100%"
									v-model="scope.row.time"
									format="yyyy-MM-dd HH:mm:ss"
									value-format="yyyy-MM-dd HH:mm:ss"
									:type="timeUnitCodeMap[timeUnitCode] && timeUnitCodeMap[timeUnitCode].type"
									:picker-options="timeUnitCode === '1' ? pickerOptions : null"
									placeholder="选择日期时间"
								></el-date-picker>
							</el-form-item>
						</template>
					</el-table-column>
					<el-table-column label="时长" width="130">
						<template slot-scope="scope">
							<el-form-item
								:prop="`tableData[${scope.$index}].period`"
								class="form-label"
								:rules="[ruleRequired('请输入时长')]"
							>
								<el-input v-model.number="scope.row.period">
									<i slot="suffix" style="font-size: 12px">
										{{ timeUnitCodeMap[timeUnitCode] && timeUnitCodeMap[timeUnitCode].unit }}
									</i>
								</el-input>
							</el-form-item>
						</template>
					</el-table-column>
					<el-table-column :label="`价格（元/${fieldName.baseUnit}）`" width="130">
						<template slot-scope="scope">
							<el-form-item
								class="form-label"
								:prop="`tableData[${scope.$index}].price`"
								:rules="[
									ruleRequired('请输入价格'),
									metrologicalVerification ? RULE_ZERO_PRICE : RULE_PRICE,
								]"
							>
								<el-input v-model="scope.row.price"></el-input>
							</el-form-item>
						</template>
					</el-table-column>
				</el-table>
			</el-form>
		</div>
	</div>
</template>

<script>
import { ruleRequired, RULE_PRICE, RULE_ZERO_PRICE } from '@/utils/rules'
import getFieldName from '@/mixin/getFieldName.js'
import { mapState } from 'vuex'
export default {
	props: {
		timeUnitCode: String,
		adjustTime: String,
		timePeriod: String,
		timePrice: String,
	},
	mixins: [getFieldName],
	data() {
		return {
			ruleRequired,
			RULE_PRICE,
			RULE_ZERO_PRICE,
			timeUnitCodeMap: {
				0: { unit: '小时', type: 'datetime', base: 60 * 60 * 1000 },
				1: { unit: '天', type: 'date', base: 60 * 60 * 1000 * 24 },
				2: { unit: '月', type: 'month', base: 60 * 60 * 1000 * 24 * 30 },
				3: { unit: '天', type: 'date', base: 60 * 60 * 1000 * 24 },
			},
			timePriceForm: {
				tableData: [
					{ time: '', period: '', price: '' },
					{ time: '', period: '', price: '' },
				],
			},
			pickerOptions: {
				disabledDate(time) {
					return new Date(time).getDate() > 28
				},
			},
		}
	},

	created() {
		if (this.adjustTime) {
			this.initBackRenderTable()
		}
	},

	methods: {
		/**
		 * 调整价格初始化分时计价策略表格数据回显
		 */
		initBackRenderTable() {
			const adjustTime = this.adjustTime.split('|')
			const timePeriod = this.timePeriod.split('|')
			const timePrice = this.timePrice.split('|')
			for (let i = 0; i < this.timePriceForm.tableData.length; i++) {
				this.timePriceForm.tableData[i].time = adjustTime[i]
				this.timePriceForm.tableData[i].period = timePeriod[i]
				this.timePriceForm.tableData[i].price = timePrice[i]
			}
		},

		packageParams() {
			const data = this.timePriceForm.tableData
			return {
				adjustTime: data.map(o => o.time).join('|'),
				timePeriod: data.map(o => o.period).join('|'),
				timePrice: data.map(o => o.price).join('|'),
			}
		},

		/**
		 * 时间单位为小时,循环周期为自然天调整时间规则校验
		 * 1、调整时间1和调整时间2的年月日必须相等
		 * 2、调整时间2选择日期时间中的小时 + 时长2 <= 24小时
		 * @param { String } firstTime 调整时间1
		 * @param { String } secondTime 调整时间2
		 */
		checkTimeRuleOne(firstTime, secondTime) {
			const startdate = firstTime.split(' ')[0]
			const enddate = secondTime.split(' ')[0]
			const endH = Number(secondTime.substr(11, 2))
			const period = Number(this.timePriceForm.tableData[1].period)
			if (startdate !== enddate) {
				return '调整时间1和调整时间2选择的日期不是同一天'
			} else if (endH + period > 24) {
				return '调整时间2选择的小时数 加上 时长2 必须小于等于 24小时'
			}
		},

		/**
		 * 时间单位为天,循环周期为自然月
		 * 1、调整时间1和调整时间2的年月必须相等
		 * 2、调整时间2选择日期时间中的小时 + 时长2 <= 24小时
		 * @param { String } firstTime 调整时间1
		 * @param { String } secondTime 调整时间2
		 */
		checkTimeRuleTwo(firstTime, secondTime) {
			const startdate = firstTime.substr(0, 7)
			const enddate = secondTime.substr(0, 7)
			const endD = Number(secondTime.substr(8, 2))
			const period = Number(this.timePriceForm.tableData[1].period)
			const Y = Number(firstTime.substr(0, 4))
			const D = Number(firstTime.substr(5, 2))
			if (startdate !== enddate) {
				return '调整时间1和调整时间2选择的日期不是同一个月'
			} else if (endD + period > this.getMonthdays(Y, D)) {
				return '调整时间2选择的日数 加上 时长2 必须小于等于 选中月的最多天数'
			}
		},

		/**
		 * 时间单位为月，循环周期为顺延年
		 * 1、调整时间2+时长2的日期 <= 调整时间1一年后相同月日的日期
		 * @param { String } firstTime 调整时间1
		 * @param { String } secondTime 调整时间2
		 */
		checkTimeRuleThree(firstTime, secondTime) {
			const [SY, SM] = [Number(firstTime.substr(0, 4)) + 1, Number(firstTime.substr(5, 2))]
			let [EY, EM] = [Number(secondTime.substr(0, 4)), Number(secondTime.substr(5, 2))]
			const period = Number(this.timePriceForm.tableData[1].period)
			EY += Math.floor((period + EM) / 12)
			EM = (EM + period) % 12
			if (EY > SY || (EY === SY && EM > SM)) {
				return '调整时间2+时长2后的日期必须小于等于调整时间1一年后的日期'
			}
		},

		/**
		 * 时间单位为天，循环周期为顺延年
		 * 1、时长1 + 时长2  <=  365/366天
		 * 2、调整时间2 + 时长2的日期 <= 调整时间1一年后相同月日的日期
		 * @param { String } firstTime 调整时间1
		 * @param { String } secondTime 调整时间2
		 */
		checkTimeRuleFour(firstTime, secondTime) {
			const SY = Number(firstTime.substr(0, 4))
			const alldays = this.getMonthdays(SY, 2) === 29 ? 366 : 365
			const [period1, period2] = [
				Number(this.timePriceForm.tableData[0].period),
				Number(this.timePriceForm.tableData[1].period),
			]
			const firstStamp = new Date(SY + 1 + firstTime.substr(4)).getTime()
			const secondStamp = new Date(secondTime).getTime()
			if (period1 + period2 > alldays) {
				return '时长1+时长2 必须小于等于 一年天数（365、366天）'
			} else if (secondStamp + period2 * 86400000 > firstStamp) {
				return '调整时间2+时长2 必须 小于等于 调整时间1一年后的日期'
			}
		},

		/**
		 * 根据年份和月份获取该月的天数
		 * @param { Number } Y 年份
		 * @param { Number } M 月份
		 */
		getMonthdays(Y, M) {
			const isR = (Y % 4 === 0 && Y % 100 !== 0) || Y % 400 === 0
			const monthMap = {
				1: 31,
				2: isR ? 29 : 28,
				3: 31,
				4: 30,
				5: 31,
				6: 30,
				7: 31,
				8: 31,
				9: 30,
				10: 31,
				11: 30,
				12: 31,
			}
			return monthMap[M]
		},
	},

	computed: {
		secondTimeRule() {
			return (rule, value, callback) => {
				const startTime = new Date(this.timePriceForm.tableData[0].time).getTime()
				const endTime = new Date(value).getTime()
				if (
					startTime + this.timeUnitCodeMap[this.timeUnitCode].base * this.timePriceForm.tableData[0].period >
					endTime
				) {
					return callback(
						new Error(
							this.timeUnitCode === '0'
								? '调整时间1选择的小时数 加上 时长1 必须小于等于 调整时间2选择的小时'
								: this.timeUnitCode === '2'
								? '调整时间2选择的日期（年月）必须 大于等于 调整时间1的日期（月份）加 时长1'
								: '调整时间1选择时间加上时长1必须小于等于调整时间2选择的日期',
						),
					)
				} else {
					const firstTime = this.timePriceForm.tableData[0].time
					if (this.timeUnitCode === '0') {
						const errMessage = this.checkTimeRuleOne(firstTime, value)
						if (errMessage) {
							return callback(new Error(errMessage))
						}
					} else if (this.timeUnitCode === '1') {
						const errMessage = this.checkTimeRuleTwo(firstTime, value)
						if (errMessage) {
							return callback(new Error(errMessage))
						}
					} else if (this.timeUnitCode === '2') {
						const errMessage = this.checkTimeRuleThree(firstTime, value)
						if (errMessage) {
							return callback(new Error(errMessage))
						}
					} else if (this.timeUnitCode === '3') {
						const errMessage = this.checkTimeRuleFour(firstTime, value)
						if (errMessage) {
							return callback(new Error(errMessage))
						}
					}
				}
				callback()
			}
		},

		...mapState({
			metrologicalVerification: state => {
				return state.user.tenant?.business_config?.is_metrological_verification || false
			},
		}),
	},

	watch: {
		timeUnitCode(newVal) {
			if (newVal) {
				this.timePriceForm = {
					tableData: [
						{ time: '', period: '', price: '' },
						{ time: '', period: '', price: '' },
					],
				}
			}
		},
	},
}
</script>

<style scoped lang="scss">
.model-tiered-bill {
	padding: 20px 25px;
	background: #f5f8ff;
	box-sizing: border-box;
	.title {
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		p {
			color: #222;
		}
	}
	.form-label {
		position: relative;
		::v-deep .el-input--small {
			position: relative;
			&::after {
				content: '*';
				position: absolute;
				color: #ff0000;
				left: 10px;
				top: 50%;
				transform: translateY(-50%);
				line-height: normal;
				margin-top: 2px;
			}
			.el-input__inner {
				padding-left: 20px;
			}
		}
		::v-deep .el-date-editor {
			.el-input__prefix {
				left: 14px;
			}
			.el-input__inner {
				padding-left: 38px;
			}
		}
	}
	::v-deep .el-form {
		padding: 0;
		.el-form-item {
			margin-bottom: 0;
		}
		.el-form-item__error {
			position: relative;
		}
	}
}
</style>
