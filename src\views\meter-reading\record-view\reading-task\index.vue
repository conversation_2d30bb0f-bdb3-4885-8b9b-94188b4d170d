<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-02 19:07:24
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 20:33:47
-->
<template>
	<div class="container">
		<div class="btn-box">
			<el-button
				v-has="'plan-collection_meterReadingTask_createFirstTask2'"
				type="primary"
				@click="handleCreateTask"
			>
				生成抄表任务
			</el-button>
		</div>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:deal="{ row }">
					<el-button
						v-has="'plan-collection_meterReadingTask_getRecordList'"
						v-if="row.taskStatus === 2 || row.taskStatus === 3"
						type="text"
						size="medium"
						@click="
							handleTableBtnClick('/meterReading/meterReadingRecords', {
								taskYear: row.taskYear,
								bookNo: row.bookNo,
								orgCode: row.orgCode,
							})
						"
					>
						查看抄表记录
					</el-button>
					<div v-else class="inline-btn-box">
						<el-button
							v-has="'plan-collection_meterReadingTask_getRecordList2'"
							type="text"
							size="medium"
							@click="
								handleTableBtnClick('/meterReading/meterReadingInput', {
									taskStatus: row.taskStatus,
									bookId: row.bookId,
									bookNo: row.bookNo,
									taskYear: row.taskYear,
									meterReadingTaskId: row.meterReadingTaskId,
								})
							"
						>
							抄表录入
						</el-button>
						<el-button
							v-has="'plan-collection_meterReadingTask_autoReviewByTask'"
							type="text"
							size="medium"
							@click="handleSendReview(row.meterReadingTaskId)"
						>
							送内复
						</el-button>
					</div>
					<!-- 非关闭状态 展示'关闭任务'按钮 -->
					<el-button
						v-has="'plan-collection_meterReadingTask_closeTask1'"
						v-if="row.taskStatus !== 3"
						type="text"
						size="medium"
						@click="handleCloseTask(row.meterReadingTaskId)"
					>
						关闭任务
					</el-button>
				</template>
			</GcTable>
		</div>
	</div>
</template>

<script>
import { getTaskList, autoReviewByTask, closeTask1, createFirstTask2 } from '@/api/meterReading.api.js'

export default {
	name: '',
	components: {},
	data() {
		return {
			loading: false,
			columns: [
				{
					key: 'taskYear',
					name: '抄表年',
					tooltip: true,
				},
				{
					name: '抄表月份',
					key: 'taskMonth',
					tooltip: true,
				},
				{
					name: '表册编号',
					key: 'bookNo',
					tooltip: true,
				},
				{
					name: '抄表员',
					key: 'staffName',
					tooltip: true,
				},
				{
					name: '是否全部移交',
					key: 'handOverDesc',
					tooltip: true,
				},
				{
					name: '任务状态',
					key: 'taskStatusDesc',
					tooltip: true,
				},
				{
					hide: !this.$has([
						'plan-collection_meterReadingTask_getRecordList',
						'plan-collection_meterReadingTask_getRecordList2',
						'plan-collection_meterReadingTask_autoReviewByTask',
						'plan-collection_meterReadingTask_closeTask1',
					]),
					key: 'deal',
					name: '操作',
					minWidth: 200,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	computed: {},
	created() {
		this.getList()
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		async getList(curPage) {
			const bookId = this.$route.query.bookId
			if (!bookId) return

			this.loading = true

			if (curPage) {
				this.pageData.current = curPage
			}

			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getTaskList({
					bookId,
					current,
					size,
				})
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleTableBtnClick(path, query) {
			if (path) {
				this.$router.push({
					path,
					query,
				})
			}
		},
		// 送内复
		async handleSendReview(meterReadingTaskId) {
			this.$confirm('确定送内复吗?').then(async () => {
				await autoReviewByTask({
					meterReadingTaskId,
				})
				this.$message.success('送内复成功')
				this.getList(1)
			})
		},
		// 关闭任务
		async handleCloseTask(taskId) {
			this.$confirm('任务关闭后，将无法继续抄表录入，请确认是否继续关闭？', '关闭抄表任务').then(async () => {
				await closeTask1({ taskId })
				this.$message.success('关闭成功')
				this.getList(1)
			})
		},
		// 生成抄表任务
		async handleCreateTask() {
			try {
				await createFirstTask2({
					bookId: this.$route.query.bookId,
				})
				this.$message.success('生成抄表任务成功')
				this.getList(1)
			} catch (error) {
				console.error(error)
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.container {
	padding: 20px;
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
}
.table-container {
	flex: 1;
	height: 0;
}
.btn-box {
	display: flex;
	justify-content: flex-end;
	margin-bottom: 10px;
}
.inline-btn-box {
	display: inline-flex;
	margin-right: 10px;
}
</style>
