<template>
	<GcElDialog
		:show="isShow"
		title="恢复用水登记"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
	</GcElDialog>
</template>

<script>
import { getFormItems } from './formItem.js'
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiEnableArchives, apiEnableArchives2, apiEnableArchives4 } from '@/api/meterManage.api.js'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		detailData: {
			type: Object,
			default: () => ({}),
		},
		permissionCode: {
			type: String,
			default: 'cpm_archives_enable',
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	watch: {
		isShow: {
			handler(val) {
				if (val) {
					this.formData.archivesIdentity = this.detailData?.archivesIdentity || ''
					this.formData.meterNo = this.detailData?.meterNo || ''
				}
			},
		},
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
				meterNo: '',
				enableDate: '',
				enablePerson: this.$store.getters.userInfo.staffName,
				enableReason: '',
			},
			formItems: getFormItems(),
			formAttrs: {
				rules: {
					enableDate: [ruleRequired('必填')],
					enableReason: [ruleRequired('必填')],
				},
			},
		}
	},
	methods: {
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			let formObj = trimParams(removeNullParams(this.formData))
			const { archivesId } = this.detailData
			delete formObj.meterNo
			delete formObj.archivesIdentity
			const enableDateStr = this.dayjs(formObj.enableDate).format('YYYY-MM-DD')
			Object.assign(formObj, { enableDate: enableDateStr, archivesId })

			const apiMethods = {
				cpm_archives_enable: apiEnableArchives,
				cpm_archives_enable2: apiEnableArchives2,
				cpm_archives_enable4: apiEnableArchives4,
			}
			await apiMethods[this.permissionCode](formObj)
			this.$message.success('恢复用水成功')
			this.handleClose()
			this.$emit('refresh')
		},
	},
}
</script>
