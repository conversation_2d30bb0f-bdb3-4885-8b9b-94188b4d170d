<template>
	<GcElDialog :show="isShow" title="修改账户信息" width="480px" :showFooter="false" @close="isShow = false">
		<GcFormRow ref="formRef" v-model="passwordForm" :formItems="formItems" :formAttrs="formAttrs">
			<template #loginAccount>
				<div style="display: flex">
					<el-input
						v-model="passwordForm.loginAccount"
						size="small"
						:disabled="!editState.accountName"
						placeholder="请输入"
					></el-input>
					<div class="group_btn">
						<el-button
							v-has="'v1_tos_staff_modify2'"
							v-show="!editState.accountName"
							type="primary"
							plain
							size="small"
							@click="
								editState.accountName = true
								editState.password = false
							"
						>
							修改
						</el-button>
						<div v-show="editState.accountName">
							<el-button type="primary" @click="changeAccount">确认</el-button>
							<el-button
								type="info"
								plain
								@click="
									editState.accountName = false
									passwordForm.loginAccount = accountInfo.account_name
								"
							>
								取消
							</el-button>
						</div>
					</div>
				</div>
			</template>
			<template #newPwd>
				<div style="display: flex">
					<el-input
						v-model="passwordForm.newPwd"
						type="password"
						size="small"
						show-password
						:disabled="!editState.password"
						:placeholder="editState.password ? '请输入' : '********'"
					></el-input>
					<div class="group_btn">
						<el-button
							v-has="'auth_aggregation_password_tenant_manage_update'"
							v-show="!editState.password"
							type="primary"
							plain
							@click="
								editState.password = true
								editState.accountName = false
								passwordForm.loginAccount = accountInfo.account_name || ''
							"
						>
							修改
						</el-button>
						<div v-show="editState.password">
							<el-button type="primary" @click="changePassword">确认</el-button>
							<el-button type="info" plain @click="resetChangePassword">取消</el-button>
						</div>
					</div>
				</div>
			</template>
		</GcFormRow>
	</GcElDialog>
</template>
<script>
import { encrypt } from '@/utils'
import { ruleRequired, ruleMaxLength, ruleMinLength, ruleComplexPassValidate } from '@/utils/rules'
import { apiUpdateAccount, apiModifyStaff2 } from '@/api/organize.api'
import { apiGetQrcode } from '@/api/login.api'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		accountInfo: {
			type: Object,
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},

	data() {
		return {
			passwordForm: {
				loginAccount: '',
				newPwd: '',
			},
			formAttrs: {
				displayItem: 'block',
				labelPosition: 'top',
				rules: {
					loginAccount: [ruleRequired('请输入登录名'), ruleMaxLength(32)],
					newPwd: [
						ruleRequired('请输入密码'),
						ruleMinLength(6),
						ruleMaxLength(18, '密码长度'),
						ruleComplexPassValidate(),
					],
				},
			},
			formItems: [
				{
					type: 'slot',
					slotName: 'loginAccount',
					prop: 'loginAccount',
					label: '登录账号',
					attrs: {
						col: 24,
					},
				},
				{
					type: 'slot',
					slotName: 'newPwd',
					prop: 'newPwd',
					label: '登录密码',
					attrs: {
						col: 24,
					},
				},
			],
			editState: {
				accountName: false,
				password: false,
			},
		}
	},
	watch: {
		isShow(newVal) {
			if (!newVal) {
				this.$emit('close-model')
			} else {
				this.passwordForm.loginAccount = this.accountInfo.account_name
				this.editState.accountName = false
				this.resetChangePassword()
			}
		},
		'passwordForm.newPwd'() {
			this.passwordForm.newPwd = this.passwordForm.newPwd.replace(/[\u4E00-\u9FA5]/g, '')
		},
	},
	methods: {
		/* 修改登录账号 */
		changeAccount() {
			if (this.passwordForm.loginAccount.includes(' ')) {
				this.$message.error('账号中不能包含空格')
				return
			}
			const searchParams = {
				account_name: this.passwordForm.loginAccount,
				department_code: this.accountInfo.department_code,
			}
			apiModifyStaff2(searchParams, this.accountInfo.id).then(() => {
				this.$emit('success')
				this.isShow = false
				apiGetQrcode({
					account: this.passwordForm.loginAccount,
				}).then(res => {
					const h = this.$createElement
					const content = res ? h('el-image', { attrs: { src: res } }) : h('span', null, '二维码加载失败')
					this.$confirm(null, '提示', {
						showCancelButton: false,
						showConfirmButton: false,
						message: h('div', null, [
							h('span', null, '登录账户修改成功，请重新绑定登录令牌'),
							h(
								'div',
								{
									style: {
										marginTop: '10px',
										display: 'flex',
										justifyContent: 'center',
										alignItems: 'center',
										height: '370px',
									},
								},
								[content],
							),
						]),
					})
				})
			})
		},
		/* 修改密码 */
		async changePassword() {
			if (this.passwordForm.newPwd.includes(' ')) {
				this.$message.error('密码中不能包含空格')
				return
			}
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			apiUpdateAccount({
				...this.passwordForm,
				newPwd: encrypt(this.passwordForm.newPwd),
				department_code: this.accountInfo.department_code,
			}).then(() => {
				this.$message.success('修改密码成功')
				this.$emit('success')
				this.isShow = false
			})
		},
		resetChangePassword() {
			this.passwordForm.newPwd = ''
			this.editState.password = false
			this.$nextTick(() => {
				this.$refs.formRef.clearValidate()
			})
		},
	},
}
</script>

<style scoped lang="scss">
::v-deep {
	.el-form {
		padding: 0 20px;
	}
	.el-form-item__label {
		font-weight: 600;
		color: #4e4e4e;
	}

	.el-form-item__content {
		display: flex;
		.group_btn {
			width: 215px;
			padding-left: 10px;
			button {
				&.is-plain {
					background: transparent !important;
					&.el-button--info {
						color: #4c4c4c !important;
					}
					&.el-button--primary {
						color: #2f87fe !important;
					}
				}
			}
		}
	}
}
</style>
