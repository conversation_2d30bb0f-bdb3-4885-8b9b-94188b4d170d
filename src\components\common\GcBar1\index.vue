<template>
	<div class="gc-bar1" ref="chartRef"></div>
</template>

<script>
import options from './options.js'
export default {
	name: 'GcBar1',
	props: {
		seriesName: String,
		seriesData: Array,
		barColor: String,
		stackBarColor: String,
		dataZoom: Object,
		xAxis: Object,
		yAxis: Object,
		grid: Object,
		tooltip: Object,
	},
	components: {},
	data() {
		return {
			myChart: null,
		}
	},
	computed: {},
	watch: {
		seriesData: {
			handler: function (arr) {
				if (arr && arr.length) {
					this.init()
				}
			},
			deep: true,
		},
		seriesName() {
			this.init()
		},
	},
	methods: {
		init() {
			this.myChart && this.myChart.clear()
			this.myChart && this.myChart.dispose()
			this.myChart = null
			!this.myChart && (this.myChart = window.echarts.init(this.$refs.chartRef))
			this.handleResize()
			this.setOptions()
			window.addEventListener('resize', this.handleResize)
		},
		handleResize() {
			if (this.myChart) {
				this.myChart.resize()
			}
		},
		setOptions() {
			const props = {
				xAxis: this.xAxis,
				yAxis: this.yAxis,
				seriesName: this.seriesName,
				barColor: this.barColor,
				stackBarColor: this.stackBarColor,
				dataZoom: this.dataZoom,
				grid: this.grid,
				tooltip: this.tooltip,
			}
			this.myChart && this.myChart.setOption(options(this.seriesData, props))
		},
	},
	mounted() {},
	beforeDestroy() {
		window.removeEventListener('resize', this.handleResize)
	},
}
</script>

<style lang="scss" scoped>
.gc-bar1 {
	height: 100%;
}
</style>
