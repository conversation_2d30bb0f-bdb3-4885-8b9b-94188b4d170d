<template>
	<CardContainer :cardDetail="cardDetail">
		<div class="content-box">
			<div class="top" style="justify-content: center">
				<el-radio-group v-model="tabValue" @input="getList">
					<el-radio-button :label="2">日</el-radio-button>
					<el-radio-button :label="0">月</el-radio-button>
				</el-radio-group>
			</div>
			<div class="bottom">
				<GcBar1
					:seriesData="chartOptions.seriesData"
					:seriesName="chartOptions.seriesName"
					barColor="rgba(172,215,186,0.6)"
					stackBarColor="rgba(172,215,186)"
					:dataZoom="{
						show: false,
					}"
					:xAxis="{
						data: chartOptions.xData,
						axisLabel: {
							rotate: 0,
						},
					}"
					:yAxis="{
						name: chartOptions.unit,
					}"
					:grid="{
						left: 80,
						right: 20,
						top: 55,
						bottom: 30,
					}"
				/>
			</div>
		</div>
	</CardContainer>
</template>

<script>
import CardContainer from './CardContainer.vue'
import { apiGetObtainUserPayChart } from '@/api/home.api'
export default {
	name: 'UserPayment',
	components: { CardContainer },
	props: {
		orgCode: {
			type: String,
			default: '',
		},
	},
	watch: {
		orgCode(v) {
			if (v) {
				if (!this.$has('cpm_home_charts_obtainUserPayChart')) {
					return
				}
				this.getList()
			}
		},
		'cardDetail.activeTab'(v) {
			const typeMap = this.cardDetail.tabList.reduce((map, item) => {
				map[item.value] = item.name.replace('表卡', '')
				return map
			}, {})
			this.chartOptions.seriesName = typeMap[v] || ''
			this.getList()
		},
	},
	data() {
		return {
			cardDetail: {
				name: '用户缴费',
				bg: require('@/assets/images/bg/home-bg2.png'),
				titleList: [
					{
						label: '当日累计',
						value: '--',
						unit: '万元',
					},
					{
						label: '当月累计',
						value: '--',
						unit: '万元',
					},
					{
						label: '去年同期累计',
						value: '--',
						unit: '万元',
					},
					{
						label: '本年缴费',
						value: '--',
						unit: '万元',
					},
					{
						label: '去年缴费',
						value: '--',
						unit: '万元',
					},
				],
				activeTab: '3',
				tabList: [
					{
						name: '居民表卡',
						value: '3',
					},
					{
						name: '企业表卡',
						value: '4',
					},
				],
			},

			tabValue: 2,
			chartOptions: {
				unit: '万元',
				xData: [],
				seriesData: [],
				seriesName: '居民',
				axisLabelRotate: 0,
			},
		}
	},
	methods: {
		async getList() {
			try {
				const {
					dayPayAmt,
					monthPayAmt,
					beforeYearMonthAmt,
					yearAmt,
					beforeYearAmt,
					payChannelAmtList = [],
				} = await apiGetObtainUserPayChart({
					dimension: this.tabValue,
					orgCode: this.orgCode,
					userType: this.cardDetail.activeTab,
				})
				this.cardDetail.titleList[0].value = dayPayAmt
				this.cardDetail.titleList[1].value = monthPayAmt
				this.cardDetail.titleList[2].value = beforeYearMonthAmt
				this.cardDetail.titleList[3].value = yearAmt
				this.cardDetail.titleList[4].value = beforeYearAmt
				if (Array.isArray(payChannelAmtList) && payChannelAmtList.length) {
					this.chartOptions.xData = payChannelAmtList.map(item => item.payChannel)
					this.chartOptions.seriesData = payChannelAmtList.map(item =>
						this.tabValue === 2 ? item.dayAmt : item.monthAmt,
					)
				}
			} catch (error) {
				console.error(error)
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.user-payment {
	height: 100%;
	display: flex;
	flex-direction: column;

	.bottom {
		flex: 1;
	}
}
.top {
	display: flex;
	justify-content: flex-end !important;
}
</style>
