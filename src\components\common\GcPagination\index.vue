<template>
	<!-- 分页器组件 -->
	<div class="gc-cloud-pagination">
		<slot>
			<span class="gc-cloud-pagination-left" v-if="!isRightAlign">共{{ total }}条搜索结果</span>
		</slot>

		<div class="gc-cloud-pagination-right">
			<span v-if="isRightAlign">共{{ total }}条搜索结果，</span>

			<span>{{ currentPageOut ? currentPageOut : currentPageSelf }}/{{ Math.ceil(total / pageSize) }}页</span>
			<el-pagination
				background
				@current-change="handleCurrentChange"
				@size-change="handleSizeChange"
				:current-page="currentPageOut ? currentPageOut : currentPageSelf"
				:page-size="pageSize"
				:total="total"
				:layout="layout"
				:page-sizes="[10, 100, 300, 500]"
			></el-pagination>
		</div>
	</div>
</template>

<script>
export default {
	name: 'GcPagination',
	props: {
		//  页宽
		pageSize: {
			type: Number,
			default: 10,
		},
		//  总条数
		total: {
			type: Number,
			default: 0,
		},
		//  当前页码
		currentPage: {
			type: [Number, String],
			default: 0,
		},
		// 是否展示回到首页和去到末页
		showTopAndEnd: {
			type: Boolean,
			default: true,
		},
		isRightAlign: {
			type: Boolean,
			default: false,
		},
		// 是否展示每页显示个数选择器
		showPageSizes: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			currentPageSelf: 1,
		}
	},
	computed: {
		layout() {
			const arr = ['total', 'prev', 'next']
			if (!this.isRightAlign) {
				arr.push('jumper')
			}
			if (this.showPageSizes) {
				arr.push('sizes')
			}
			return arr.join(',')
		},
		currentPageOut: {
			get() {
				return this.currentPage
			},
			set(val) {
				this.$emit('update:currentPage', val)
			},
		},
	},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		handleCurrentChange(curPage) {
			this.currentPageSelf = curPage
			this.$emit('current-page-change', {
				page: curPage,
				size: this.pageSize,
			})
		},
		handleSizeChange(curSize) {
			this.currentPageSelf = 1
			this.$emit('current-page-change', {
				page: 1,
				size: curSize,
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-cloud-pagination {
	display: flex;
	justify-content: space-between;
	align-items: center;
	color: #000000;
	font-size: 12px;
	&-right {
		display: flex;
		justify-content: space-between;
		align-items: center;
		span {
			margin-right: 5px;
		}
	}
	.gc-cloud-pagination-right {
		.el-pagination.is-background {
			padding: 16px 0 16px 5px;
			::v-deep .el-pagination__jump {
				margin-left: 20px;
			}
		}
	}
	::v-deep .el-pagination__sizes {
		margin-right: 0;
		margin-left: 10px;
	}
	::v-deep .el-pagination__total {
		display: none;
	}
}
</style>
