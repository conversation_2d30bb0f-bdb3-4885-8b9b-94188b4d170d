<template>
	<div id="app">
		<router-view />
	</div>
</template>

<script>
import { getSS, setSS, removeSS, getToken } from '@/utils/storage'
import { fieldNameVersion } from '@/consts/waterGasFieldName'

export default {
	name: 'App',
	onIdle() {
		if (this.$store.getters.token) {
			this.$store.dispatch('user/removeToken')
			removeSS('sToken')
			this.$alert('页面超过30分钟未操作，为保证账户安全，即将退出系统，请重新登录后操作', {
				confirmButtonText: '确定',
				callback: () => {
					this.$store.dispatch('user/resetUserLS').then(() => location.reload())
				},
			})
		}
	},
	data() {
		return {
			changed: false,
		}
	},
	computed: {
		token() {
			return this.$store.getters.token
		},
		// 水气差异化版本号
		version() {
			return this.$store.getters.fieldName?.version
		},
		// 业务领域 gas-燃气 water-水务
		realm() {
			return this.$store.getters.userInfo?.realm || 'gas'
		},
	},
	created() {},
	mounted() {
		const token = getToken()
		token && setSS('sToken', token)
		document.addEventListener('visibilitychange', () => {
			!document.hidden && this.handleVisibilityChange()
		})
	},
	methods: {
		handleVisibilityChange() {
			if (this.$route.path === '/login') return
			if (document.visibilityState !== 'visible') return
			const sToken = getSS('sToken')
			const token = getToken()
			const changed = token !== sToken
			if (changed && !sToken) {
				location.reload()
				setSS('sToken', token)
			} else if (changed && sToken) {
				this.$alert('登录信息发生变化, 页面即将刷新，请重新操作', {
					confirmButtonText: '确定',
					callback: () => {
						if (token) {
							setSS('sToken', token)
							// 提示登录信息发生变化并关闭所有标签页
							this.$store.dispatch('tagsView/delAllViews').then(tagList => {
								const lastView = tagList.slice(-1)[0]
								if (lastView) {
									this.$router.push(lastView.fullPath)
								} else {
									this.$router.push('/')
								}
								location.reload()
							})
						} else {
							// 提示登录信息发生变化并退出登录
							this.$store.dispatch('user/resetUserLS').then(() => location.reload())
						}
					},
				})
			}
		},
	},
	updated() {
		// fieldName版本变更时更新本地和store数据
		if (this.version !== fieldNameVersion) {
			this.$store.dispatch('user/setFieldName', this.realm)
		}
	},
}
</script>
