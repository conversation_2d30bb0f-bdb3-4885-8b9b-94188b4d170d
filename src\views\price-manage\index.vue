<template>
	<div class="co-pricemanage">
		<div class="left-w-model">
			<gc-model-header title="价格列表" :icon="require('@/assets/images/icon/title-cash.png')">
				<div v-if="userLevel === 0" slot="middle">
					<cascader-org :value.sync="branchCompanyOrgCode" need-default-first simple />
				</div>
				<div v-has="'cpm_prices_add'" slot="right">
					<el-button
						class="btn-add"
						type="primary"
						@click="
							operateType = 0
							priceVisible = true
						"
					>
						新增
					</el-button>
				</div>
			</gc-model-header>
			<div class="co-pricemanage-search-bar">
				<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems"></GcFormSimple>
			</div>
			<div class="lprice-list" v-loading="loading">
				<vue-scroll :ops="{ bar: { background: '#e3e3e3' } }" v-if="priceList.length">
					<div
						class="price-block"
						:class="{ _active: currentPrice.priceId === item.priceId }"
						v-for="item in priceList"
						:key="item.priceId"
						@click="selectPriceInfo(item)"
					>
						<p>{{ `（${item.priceCode}）${item.priceName}` }}</p>
						<span
							:class="{
								offline:
									item.statusMask === '禁用' ||
									item.statusMask === '已删除' ||
									item.statusMask === '停用',
								online: item.statusMask === '在用' || item.statusMask === '待生效',
							}"
						>
							{{ item.statusMask }}
						</span>
					</div>
				</vue-scroll>
				<gc-empty v-else></gc-empty>
			</div>
		</div>
		<div class="right-w-model">
			<div v-if="currentPrice" style="height: 100%">
				<gc-model-header
					:title="`（${currentPrice.priceCode}）${currentPrice.priceName}`"
					:icon="require('@/assets/images/icon/title-cash.png')"
				>
					<div slot="right" class="right_btngroup">
						<el-button
							type="primary"
							plain
							size="small"
							@click="
								operateType = 1
								priceVisible = true
							"
							v-has="'cpm_prices_adjust'"
						>
							调价
						</el-button>
						<el-button
							@click="
								operateType = 2
								priceVisible = true
							"
							type="info"
							plain
							size="small"
							v-has="'cpm_prices_modify'"
						>
							编辑
						</el-button>
						<el-button
							v-if="currentPrice.enableFlag === 1"
							v-has="'cpm_prices_disable'"
							@click="useOrdisPrice"
							type="info"
							plain
							size="small"
						>
							禁用
						</el-button>
						<el-button
							v-if="currentPrice.enableFlag === 2"
							v-has="'cpm_prices_enable'"
							@click="useOrdisPrice"
							type="info"
							plain
							size="small"
						>
							启用
						</el-button>
						<el-button
							:disabled="currentPriceInfo.priceVersion === 1 || currentPriceInfo.enableFlag === 3"
							v-has="'cpm_prices_delete'"
							@click="deleteTargetPrice"
							type="info"
							plain
							size="small"
						>
							删除
						</el-button>
					</div>
				</gc-model-header>

				<div class="b-scroll">
					<!-- 价格详情 -->
					<gc-group-detail :data="pricedata" style="margin: 10px 0">
						<template v-slot:status>
							<div class="_status">
								<span
									:class="{
										offline:
											currentPriceInfo.statusMask === '禁用' ||
											currentPriceInfo.statusMask === '已删除' ||
											currentPriceInfo.statusMask === '停用',
										online:
											currentPriceInfo.statusMask === '在用' ||
											currentPriceInfo.statusMask === '待生效',
									}"
								>
									{{ currentPriceInfo.statusMask }}
								</span>
							</div>
						</template>
					</gc-group-detail>

					<!-- 阶梯计价策略 -->
					<div class="level-v-price" v-if="currentPriceInfo.billingTypeId === 2">
						<h5>阶梯计价策略</h5>
						<el-table
							:data="levelTableData"
							border
							:header-cell-style="{
								background: 'rgb(240, 244, 250)',
								color: '#222',
							}"
						>
							<el-table-column prop="levelName" label="阶梯"></el-table-column>
							<el-table-column
								prop="gasUsecount"
								:label="`${fieldName.baseName}量（${fieldName.baseUnit}）`"
							></el-table-column>
							<el-table-column
								prop="warnLevelBorder"
								:label="`报警阈值（${fieldName.baseUnit}）`"
							></el-table-column>
							<el-table-column
								prop="unitPrice"
								:label="`价格（元/${fieldName.baseUnit}）`"
							></el-table-column>
						</el-table>
					</div>

					<!-- 分时计价策略 -->
					<div class="time-v-price" v-if="currentPriceInfo.billingTypeId === 3">
						<h5>分时计价策略</h5>
						<el-table
							:data="timeTableData"
							border
							:header-cell-style="{
								background: 'rgb(240, 244, 250)',
								color: '#222',
							}"
						>
							<el-table-column prop="index" label="顺序"></el-table-column>
							<el-table-column prop="adjustTime" label="调整时间"></el-table-column>
							<el-table-column prop="timePeriod" label="时长"></el-table-column>
							<el-table-column
								prop="timePrice"
								:label="`价格（元/${fieldName.baseUnit}）`"
							></el-table-column>
						</el-table>
					</div>

					<!-- 附加费（水务） -->
					<div class="price-v-detail" v-if="realm === 'water' && extraBillings.length">
						<h5>附加费</h5>
						<el-table
							:data="extraBillings"
							border
							:header-cell-style="{
								background: 'rgb(240, 244, 250)',
								color: '#222',
							}"
						>
							<el-table-column prop="itemName" label="附加费用名称"></el-table-column>
							<el-table-column prop="billItemPrice" label="价格（元/吨）"></el-table-column>
						</el-table>
					</div>

					<!-- 产品版本明细 -->
					<div class="price-v-detail">
						<h5>产品版本明细</h5>
						<el-table
							:data="versionTableData"
							border
							:header-cell-style="{
								background: 'rgb(240, 244, 250)',
								color: '#222',
							}"
							@row-click="row => findPriceInfo(row.priceVersion)"
							highlight-current-row
						>
							<el-table-column prop="priceName" label="价格名称"></el-table-column>
							<el-table-column prop="priceVersion" label="版本号"></el-table-column>
							<el-table-column prop="effectiveTime" label="生效日期"></el-table-column>
							<el-table-column prop="statusMask" label="状态">
								<template slot-scope="scope">
									<div class="_status">
										<span
											:class="{
												offline:
													scope.row.statusMask === '禁用' ||
													scope.row.statusMask === '已删除' ||
													scope.row.statusMask === '停用',
												online:
													scope.row.statusMask === '在用' ||
													scope.row.statusMask === '待生效',
											}"
										>
											{{ scope.row.statusMask }}
										</span>
									</div>
								</template>
							</el-table-column>
						</el-table>
						<gc-pagination
							@current-page-change="handlePageChange"
							:pageSize="pagination.pageSize"
							:total="pagination.total"
							:currentPage="pagination.pageNo"
							style="margin-top: 16px"
						></gc-pagination>
					</div>
				</div>
			</div>
			<gc-empty v-else></gc-empty>
		</div>

		<!-- 新增编辑价格 -->
		<price-operate-model
			v-if="priceVisible"
			:operateType="operateType"
			:editPriceForm="currentPriceInfo"
			:waterNatureTreeData="waterNatureTreeData"
			@close-model="priceVisible = false"
			@operate-success="findPriceList"
		/>
	</div>
</template>

<script>
import mixin from './mixins'
import identity from '@/mixin/identity'
import { accAdd } from '@/utils/calc'
import PriceOperateModel from './components/PriceOperateModel'
import CascaderOrg from '@/components/CascaderOrg'
import {
	apiPriceList,
	apiPriceInfo,
	apiVersionInfo,
	apiDeletePrice,
	apiDisabledPrice,
	apiEnablePrice,
} from '@/api/priceManage.api'
import { queryWaterNatureTree } from '@/api/basicConfig.api'
export default {
	components: { PriceOperateModel, CascaderOrg },
	mixins: [mixin, identity],
	data() {
		return {
			priceList: [],
			currentPrice: '', // 当前选中价格常规数据
			currentPriceInfo: {}, // 当前选中价格详细数据
			loading: false,
			priceVisible: false,
			// 0：新增 1：调价 2：编辑
			operateType: 0,
			extraBillings: [], // 附加费
			branchCompanyOrgCode: null,
			waterNatureTreeData: [],
			formData: {
				// 价格状态
				enableFlag: 1,
			},
			formItems: [
				{
					type: 'el-select',
					label: '状态',
					prop: 'enableFlag',
					options: [
						{
							label: '全部',
							value: '',
						},
						{
							label: '在用',
							value: 1,
						},
						{
							label: '禁用',
							value: 2,
						},
					],
					attrs: {
						clearable: false,
						filterable: false,
						placeholder: '请选择状态',
					},
				},
			],
		}
	},

	created() {
		if (this.userLevel !== 0) {
			this.findPriceList()
		}

		this.getWaterNatureTree()
	},

	methods: {
		// 初始化用水性质级联下拉数据
		async getWaterNatureTree() {
			try {
				const res = await queryWaterNatureTree()
				this.waterNatureTreeData = res || []
			} catch (error) {
				this.waterNatureTreeData = []
				console.error(error)
			}
		},
		findPriceList() {
			this.loading = true
			const { formData } = this
			let params = { size: 1000, ...(formData || {}) }
			// 3.7.0迭代，管理员级别接口新增入参组织机构和租户id
			if (this.userLevel === 0) {
				params.tenantId = this.tenantId
				params.branchCompanyOrgCode = this.branchCompanyOrgCode
			}
			apiPriceList(params)
				.then(res => {
					this.loading = false
					this.priceList = res.records || []
					if (this.priceList.length) {
						if (this.currentPrice) {
							this.currentPrice =
								this.priceList.find(o => o.priceId === this.currentPrice.priceId) || this.priceList[0]
						} else {
							this.currentPrice = this.priceList[0]
						}
					} else {
						this.currentPrice = ''
					}
				})
				.catch(() => {
					this.loading = false
				})
		},

		findPriceInfo(priceVersion) {
			// 3.7.0迭代，管理员级别接口新增入参组织机构和租户id
			const searchParams = Object.assign(
				{
					priceId: this.currentPrice.priceId,
					priceVersion,
				},
				this.userLevel === 0
					? {
							tenantId: this.tenantId,
							branchCompanyOrgCode: this.branchCompanyOrgCode,
					  }
					: {},
			)
			apiPriceInfo(searchParams).then(res => {
				this.currentPriceInfo = {
					...(res || {}),
					timeUnitCode: !isNaN(res.timeUnitCode) ? String(res.timeUnitCode) : res.timeUnitCode,
				}
				this.extraBillings = res?.priceBillItemList || []
				if (this.currentPriceInfo.billingTypeId === 2 && this.currentPriceInfo.levelBorder) {
					this.packageLevelPriceTable()
				}
				if (this.currentPriceInfo.billingTypeId === 3 && this.currentPriceInfo.adjustTime) {
					this.packageTimePriceTable()
				}
				this.packagePriceInfoFomat()
			})
		},

		/**
		 * 根据价格详情返回levelBorder和levelPrice字段自定义处理阶梯计价策略表格数据
		 * levelBorder 12|18|26|33|99999999
		 * levelPrice 1.11|1.12|1.13|1.14|1.15
		 * 将levelBorder每个一|分割数据前求和得到类似[0, 12, 30, 56, 89, 99999999]格式的数据
		 */
		packageLevelPriceTable() {
			const enumLevel = {
				0: '一',
				1: '二',
				2: '三',
				3: '四',
				4: '五',
			}
			const levelBorder = this.currentPriceInfo.levelBorder.split('|').map(o => parseInt(o))
			const levelPrice = this.currentPriceInfo.levelPrice.split('|')
			const warnLevelBorder = this.currentPriceInfo?.warnLevelBorder
				? this.currentPriceInfo?.warnLevelBorder.split('|')
				: []
			// 计算数据前几个数据之和得到新的list
			const beforeSumLevel = levelBorder.reduce(
				(prev, cur) => {
					const sum = Math.min(prev[prev.length - 1] + cur, 99999999)
					prev.push(sum)
					return prev
				},
				[0],
			)
			this.levelTableData = levelPrice.map((o, index) => {
				return {
					unitPrice: o,
					gasUsecount: `${beforeSumLevel[index]} ~ ${beforeSumLevel[index + 1]}`,
					warnLevelBorder: warnLevelBorder[index]
						? index === levelPrice.length - 1
							? warnLevelBorder[index]
							: accAdd(beforeSumLevel[index], warnLevelBorder[index])
						: '--',
					levelName: `第${enumLevel[index]}阶梯`,
				}
			})
		},

		/**
		 * 根据价格详情返回的adjustTime，timePrice，timePeriod，字段处理分时计价策略表格数据信息
		 */
		packageTimePriceTable() {
			const adjustTime = this.currentPriceInfo.adjustTime.split('|')
			const timePeriod = this.currentPriceInfo.timePeriod.split('|')
			const timePrice = this.currentPriceInfo.timePrice.split('|')
			this.timeTableData = adjustTime.map((o, index) => {
				return {
					adjustTime: o,
					timePeriod: timePeriod[index],
					timePrice: timePrice[index],
					index: index + 1,
				}
			})
		},

		/* 产品版本明细列表查询更新处理生效日期 */
		findVersionInfo() {
			// 3.7.0迭代，管理员级别接口新增入参组织机构和租户id
			const searchParams = Object.assign(
				{
					priceId: this.currentPrice.priceId,
					size: this.pagination.pageSize,
					current: this.pagination.pageNo,
				},
				this.userLevel === 0
					? {
							tenantId: this.tenantId,
							branchCompanyOrgCode: this.branchCompanyOrgCode,
					  }
					: {},
			)
			apiVersionInfo(searchParams).then(res => {
				this.versionTableData = res.records || []
				this.pagination.total = res.total || 0
			})
		},

		deleteTargetPrice() {
			this.$confirm('删除后该版本的价格将不能被用户选择，并且无法回退', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}).then(() => {
				const searchParams = {
					priceId: this.currentPrice.priceId,
					priceVersion: this.currentPrice.priceVersion,
				}
				apiDeletePrice(searchParams).then(() => {
					this.$message.success('删除价格成功')
					this.findPriceList()
				})
			})
		},

		useOrdisPrice() {
			const enableFlag = this.currentPrice.enableFlag
			const API = enableFlag === 1 ? apiDisabledPrice : apiEnablePrice
			this.$confirm(
				enableFlag === 1 ? '禁用后将不能被用户继续选择，已经在选的价格将不受影响' : '启用后该价格可以使用',
				'提示',
				{
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning',
				},
			).then(() => {
				API(this.currentPrice.priceId).then(() => {
					this.$message.success(enableFlag === 1 ? '禁用价格成功' : '启用价格成功')
					this.findPriceList()
				})
			})
		},

		handlePageChange({ page, size }) {
			this.pagination.pageNo = page
			this.pagination.pageSize = size
			this.findVersionInfo()
		},

		selectPriceInfo(priceItem) {
			if (this.currentPrice.priceId === priceItem.priceId) return
			this.currentPrice = priceItem
		},
	},

	watch: {
		currentPrice: {
			handler(price) {
				if (price) {
					this.packagePriceInfoFomat()
					this.findPriceInfo(this.currentPrice.priceVersion)
					this.findVersionInfo()
				}
			},
			deep: true,
		},
		branchCompanyOrgCode(val) {
			if (val) {
				this.priceList = []
				this.currentPrice = ''
				this.findPriceList()
			}
		},
		formData: {
			handler() {
				this.findPriceList()
			},
			deep: true,
		},
	},
}
</script>

<style lang="scss" scoped>
.co-pricemanage {
	display: flex;
	height: 100%;
	.left-w-model {
		height: 100%;
		display: flex;
		flex-direction: column;
		.icon-add-price {
			cursor: pointer;
			color: #2f87fe;
		}
		.lprice-list {
			flex: 1;
			// height: calc(100% - 60px);
			overflow: hidden;
			.price-block {
				display: flex;
				height: 40px;
				align-items: center;
				cursor: pointer;
				p {
					flex: 1;
					padding: 0 20px;
					box-sizing: border-box;
					width: 0;
					color: #666;
					white-space: nowrap;
					text-overflow: ellipsis;
					overflow: hidden;
				}
				span {
					position: relative;
					flex-shrink: 0;
					padding-right: 20px;
					color: #666;
					&:after {
						content: '';
						position: absolute;
						width: 6px;
						height: 6px;
						border-radius: 50%;
						left: -12px;
						top: 50%;
						transform: translateY(-50%);
					}
					&.online {
						&::after {
							background: #19be6b;
						}
					}
					&.offline {
						&::after {
							background: #ec6b60;
						}
					}
				}
				&._active {
					background: #dce8ff;
					p {
						color: #2f87fe;
					}
				}
			}
		}
		.btn-add {
			margin-left: 8px;
		}
	}
	.right-w-model {
		width: 0;
		.b-scroll {
			height: calc(100% - 80px);
			overflow-y: scroll;
		}
		.right_btngroup {
			.el-button {
				width: 68px;
				background: transparent !important;
				&--primary {
					color: #2f87fe !important;
					border: 1px solid #2f87fe !important;
				}
				&--info {
					color: #4e4e4e !important;
					border: 1px solid #d8d8d8 !important;
				}
				&.is-disabled {
					opacity: 0.6;
				}
			}
		}
		.level-v-price,
		.time-v-price,
		.price-v-detail {
			padding: 0 20px;
			margin-top: 20px;
			h5 {
				color: #222222;
				font-size: 14px;
				margin-bottom: 20px;
				font-weight: bold;
			}
			::v-deep .el-table {
				.cell {
					font-size: 14px;
				}
			}
		}
		._status {
			padding-left: 14px;
			span {
				position: relative;
				flex-shrink: 0;
				padding-right: 20px;
				color: #666;
				&:after {
					content: '';
					position: absolute;
					width: 6px;
					height: 6px;
					border-radius: 50%;
					left: -12px;
					top: 50%;
					transform: translateY(-50%);
				}
				&.online {
					&::after {
						background: #19be6b;
					}
				}
				&.offline {
					&::after {
						background: #ec6b60;
					}
				}
			}
		}
	}
}
.co-pricemanage-search-bar {
	padding: 0 20px;
}
</style>
