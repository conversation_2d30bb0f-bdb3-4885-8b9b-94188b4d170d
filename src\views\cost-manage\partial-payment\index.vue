<template>
	<div class="wrapper">
		<div class="page-layout">
			<div class="page-left">
				<GcModelHeader
					class="info-title"
					title="账单信息"
					:icon="require('@/assets/images/icon/title-common-parameters.png')"
				></GcModelHeader>
				<div class="form-container">
					<div class="form-item" v-for="(item, index) in formList" :key="index">
						<div class="label">{{ item.label }}</div>
						<div class="value">{{ item.value }}</div>
					</div>
				</div>
			</div>
			<div class="page-right">
				<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
					<!-- 按水量时显示水费水量输入 -->
					<template v-slot:changes>
						<div v-if="formData.type === '1'">
							<el-form-item label="水费水量" prop="useAmount" style="margin-right: 6px; margin-bottom: 0">
								<el-input v-model="formData.useAmount" placeholder="请输入"></el-input>
							</el-form-item>
							<el-form-item :label="totalValue" style="margin-bottom: 0"></el-form-item>
						</div>
						<div v-if="formData.type === '2'">
							<el-form-item
								label="缴费金额"
								prop="paymentAmount"
								style="margin-right: 6px; margin-bottom: 0"
							>
								<el-input
									v-model="formData.paymentAmount"
									placeholder="请输入"
									:disabled="!formData.paymentItem"
								></el-input>
							</el-form-item>
							<el-form-item :label="currentAmt" style="margin-bottom: 0"></el-form-item>
						</div>
					</template>
					<template>
						<el-form-item label="缴费方式" prop="payMode">
							<el-select v-model="formData.payMode" placeholder="请选择" style="width: 120px">
								<el-option
									v-for="item in payModeList"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								></el-option>
							</el-select>
						</el-form-item>
					</template>
					<el-button type="primary" @click="handleReduction">收费计算</el-button>
				</GcFormSimple>
				<div class="table-container">
					<div class="table">
						<div class="flex">
							<h5>账单信息</h5>
							<div class="total-container flex" v-show="this.tableData0.length">
								<div>合计: 水量{{ getTotalValue(this.tableData0).useAmount }}</div>
								<div>金额 {{ getTotalValue(this.tableData0).useAmt }}</div>
							</div>
						</div>
						<GcTable :columns="columns" :table-data="tableData0" />
					</div>

					<div class="table">
						<div class="flex">
							<h5>剩余信息</h5>
							<div class="total-container flex" v-show="this.tableData2.length">
								<div>合计: 水量{{ getTotalValue(this.tableData2).useAmount }}</div>
								<div>金额 {{ getTotalValue(this.tableData2).useAmt }}</div>
							</div>
						</div>
						<GcTable :columns="columns" :table-data="tableData2" />
					</div>
					<div class="table">
						<div class="flex">
							<h5>收费信息</h5>
							<div class="total-container flex" v-show="this.tableData1.length">
								<div>合计: 水量{{ getTotalValue(this.tableData1).useAmount }}</div>
								<div>金额 {{ getTotalValue(this.tableData1).useAmt }}</div>
							</div>
						</div>
						<GcTable :columns="columns" :table-data="tableData1" />
					</div>
				</div>
			</div>
		</div>

		<div class="button-group">
			<el-button class="btn-preview" @click="handleCancel">取消</el-button>
			<el-button class="btn-create" type="primary" :disabled="!tableData2.length" @click="handleSubmit">
				保存并缴费
			</el-button>
		</div>
	</div>
</template>

<script>
import { ruleRequired, RULE_POSITIVEINTEGERONLY_STARTOFZERO, ruleMaxLength, validateValue } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { accMul, accSub, accAdd } from '@/utils/calc.js'
import { isBlank } from '@/utils/validate.js'
import { priceLevelEnum } from '@/consts/enums.js'
import { apiGetBillDetail, apiBillPartial } from '@/api/costManage.api.js'

export default {
	data() {
		return {
			formData: {
				type: '1',
				reason: '',
				useAmount: '',
				payMode: '1',
				paymentItem: '', // 缴费项
				paymentAmount: '', // 缴费金额
			},
			formItems: [
				{
					type: 'el-radio',
					label: '收费方式',
					prop: 'type',
					options: [
						{
							label: '按水量',
							value: '1',
						},
						{
							label: '按费用项',
							value: '2',
						},
					],
					events: {
						change: this.handleChangeType,
					},
				},
				{
					type: 'el-input',
					label: '原因',
					prop: 'reason',
				},
				{
					type: 'el-select',
					label: '缴费项',
					prop: 'paymentItem',
					options: this.paymentItemOptions,
					hide: this.formData.type !== '2',
					attrs: {
						style: {
							width: '150px',
						},
					},
				},
				{
					type: 'slot',
					slotName: 'changes',
				},
				{
					type: 'slot',
					slotName: 'payMode',
				},
			],
			formAttrs: {
				inline: true,
				rules: {
					useAmount: [],
					reason: [ruleMaxLength(32)],
					paymentItem: [ruleRequired('请选择缴费项')],
					paymentAmount: [ruleRequired('请输入缴费金额')],
				},
			},
			detailData: {},
			columns: [
				{
					key: 'itemName',
					name: '费用名称',
					tooltip: true,
				},
				{
					key: 'priceLevel',
					name: '阶梯',
					tooltip: true,
					render: (h, row, total, scope) => {
						return h('span', {}, priceLevelEnum[row[scope.column.property]])
					},
				},
				{
					key: 'usePrice',
					name: '价格',
					tooltip: true,
				},
				{
					key: 'useAmount',
					name: '水量',
					tooltip: true,
				},
				{
					key: 'useAmt',
					name: '金额',
					tooltip: true,
				},
			],
			tableData1: [],
			tableData2: [],
		}
	},
	computed: {
		formList() {
			const list = [
				{
					label: '账单编号',
					value: this.detailData?.billNo || '--',
				},
				{
					label: '价格编号',
					value: this.detailData?.priceCode || '--',
				},
				{
					label: '用水性质',
					value: this.detailData?.natureName || '--',
				},
			]
			return list
		},
		payModeList() {
			return this.$store.getters.dataList.payMode
				? this.$store.getters.dataList.payMode
						.filter(item => item.sortValue != 3)
						.map(item => {
							return {
								label: item.sortName,
								value: item.sortValue,
							}
						})
				: []
		},
		// 缴费项选项（从账单信息中获取费用名称）
		paymentItemOptions() {
			if (!this.tableData0.length) return []

			return this.tableData0.map(item => ({
				label: item.itemName,
				value: `${item.itemId}_${item.priceLevel || 0}`, // 使用itemId和priceLevel组合作为唯一标识
				itemId: item.itemId,
				priceLevel: item.priceLevel,
				useAmt: item.useAmt, // 保存金额用于限制最大值
				originalItem: item, // 保存原始数据
			}))
		},
		tableData0() {
			return this.detailData.billDetailList || []
		},
		totalValue() {
			const { useAmount } = this.detailData
			return isBlank(useAmount) ? '' : `<${useAmount}`
		},
		currentAmt() {
			// 如果是按费用项收费，显示所选项的金额
			if (!this.formData.paymentItem) {
				return '' // 没选择缴费项时显示空字符串
			}

			// 找到选中的缴费项
			const selectedItem = this.paymentItemOptions.find(item => item.value === this.formData.paymentItem)
			if (selectedItem) {
				return `<${selectedItem.useAmt}`
			}
			return ''
		},
		maxValue() {
			const value = isBlank(this.detailData.useAmount) ? 0 : this.detailData.useAmount
			return value
		},
		maxAmt() {
			const selectedItem = this.paymentItemOptions.find(item => item.value === this.formData.paymentItem)
			if (selectedItem) {
				return selectedItem.useAmt
			}
			return ''
		},
	},
	watch: {
		maxValue: {
			immediate: true,
			handler(val) {
				this.formAttrs.rules.useAmount = [
					ruleRequired('必填'),
					RULE_POSITIVEINTEGERONLY_STARTOFZERO,
					validateValue(val, 'greaterOrEqual'),
					validateValue(0, 'lessOrEqual'),
				]
			},
		},
		maxAmt: {
			immediate: true,
			handler(val) {
				if (val) {
					this.formAttrs.rules.paymentAmount = [
						ruleRequired('必填'),
						RULE_POSITIVEINTEGERONLY_STARTOFZERO,
						validateValue(val, 'lessOrEqual'),
						validateValue(0, 'greaterOrEqual'),
					]
				} else {
					this.formAttrs.rules.paymentAmount = [ruleRequired('必填'), RULE_POSITIVEINTEGERONLY_STARTOFZERO]
				}
				console.log('watch maxAmt', val, this.formAttrs.rules)
			},
		},
	},
	activated() {
		const { id, year } = this.$route.query
		if (id) {
			this._apiGetBillDetail({ billId: id, year })
		}
	},
	methods: {
		async _apiGetBillDetail(params) {
			const res = await apiGetBillDetail(params)
			this.detailData = res
		},
		// 处理缴费项变化
		handlePaymentItemChange() {
			// 清空缴费金额
			this.formData.paymentAmount = ''
			// 校验规则会通过 watch maxAmt 自动设置
		},
		getTotalValue(arr) {
			const useAmount = arr
				.filter(item => item.itemId === 11)
				.map(item => item.useAmount)
				.reduce((acc, curr) => accAdd(acc, curr), 0)
			const useAmt = arr.map(item => item.useAmt).reduce((acc, curr) => accAdd(acc, curr), 0)
			return {
				useAmount,
				useAmt,
			}
		},
		// 调整计算
		async handleReduction() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return

			if (this.formData.type === '1') {
				// 按水量
				this.handleReductionByAmount()
			} else if (this.formData.type === '2') {
				// 按费用项
				this.handleReductionByItem()
			}
		},
		// 按水量调整计算
		handleReductionByAmount() {
			this.tableData2 = this._.cloneDeep(this.tableData0)
			this.tableData1 = this._.cloneDeep(this.tableData0)
			let accSubValue = this.formData.useAmount
			// 剩余信息
			const arr = [...this.tableData2].reverse()
			arr.forEach(item => {
				// 如果污水费用为0，直接跳过
				if (item.itemId === 10001 && item.useAmt === 0) {
					return
				}
				if (item.priceLevel) {
					const subResult = accSub(item.useAmount, accSubValue)
					if (subResult < 0) {
						accSubValue = Math.abs(subResult)
						item.useAmount = 0
					} else {
						item.useAmount = subResult
						accSubValue = 0 // 终止后续计算
					}
				} else {
					item.useAmount = accSub(item.useAmount, accSubValue)
				}
				item.useAmt = accMul(item.useAmount, item.usePrice)
			})
			this.tableData2 = arr.reverse()
			// 收费信息
			this.tableData1 = this.tableData1.map((item, index) => {
				item.useAmount = accSub(this.tableData0[index].useAmount, this.tableData2[index].useAmount)
				item.useAmt = accSub(this.tableData0[index].useAmt, this.tableData2[index].useAmt)
				return item
			})
			this.$message({
				message: '计算成功',
				type: 'success',
				duration: '1500',
			})
		},
		// 按费用项调整计算
		handleReductionByItem() {
			// 找到选中的缴费项
			const selectedItem = this.paymentItemOptions.find(item => item.value === this.formData.paymentItem)
			if (!selectedItem) {
				this.$message.error('请选择缴费项')
				return
			}

			const paymentAmount = parseFloat(this.formData.paymentAmount)
			if (isNaN(paymentAmount) || paymentAmount <= 0) {
				this.$message.error('请输入有效的缴费金额')
				return
			}

			// 复制原始数据
			this.tableData2 = this._.cloneDeep(this.tableData0)
			this.tableData1 = []

			// 找到对应的费用项
			const targetIndex = this.tableData2.findIndex(
				item =>
					item.itemId === selectedItem.itemId && (item.priceLevel || 0) === (selectedItem.priceLevel || 0),
			)

			if (targetIndex !== -1) {
				const targetItem = this.tableData2[targetIndex]

				// 创建收费信息项
				const paymentItem = this._.cloneDeep(targetItem)
				paymentItem.useAmt = paymentAmount
				// 根据价格计算水量（如果有价格的话）
				if (paymentItem.usePrice > 0) {
					paymentItem.useAmount = paymentAmount / paymentItem.usePrice
				}
				this.tableData1.push(paymentItem)

				// 更新剩余信息
				targetItem.useAmt = targetItem.useAmt - paymentAmount
				if (targetItem.usePrice > 0) {
					targetItem.useAmount = targetItem.useAmt / targetItem.usePrice
				}

				// 如果剩余金额为0或负数，从剩余信息中移除
				if (targetItem.useAmt <= 0) {
					this.tableData2.splice(targetIndex, 1)
				}
			}

			this.$message({
				message: '计算成功',
				type: 'success',
				duration: '1500',
			})
		},
		// 改变调整方式
		handleChangeType() {
			this.$refs.formRef.clearValidate()
			this.formData.useAmount = ''
			this.formData.priceId = ''
			this.formData.paymentItem = ''
			this.formData.paymentAmount = ''
			this.tableData1 = []
			this.tableData2 = []
		},
		async handleSubmit() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const formParams = trimParams(removeNullParams(this.formData))
			const { id, year } = this.$route.query
			// 纯水费用水量
			const useAmount = this.tableData1
				.filter(item => item.itemId === 11)
				.map(item => item.useAmount)
				.reduce((acc, curr) => accAdd(acc, curr), 0)
			// 纯水费用水金额
			const useAmt = this.tableData1
				.filter(item => item.itemId === 11)
				.map(item => item.useAmt)
				.reduce((acc, curr) => accAdd(acc, curr), 0)
				.toFixed(2)
			// 附加费金额
			const billItemAmt = this.tableData1
				.filter(item => item.itemId === 10001)
				.map(item => item.useAmt)
				.reduce((acc, curr) => accAdd(acc, curr), 0)
				.toFixed(2)
			// 应收金额
			const receivableAmount = this.tableData1
				.map(item => item.useAmt)
				.reduce((acc, curr) => accAdd(acc, curr), 0)
				.toFixed(2)
			// 价格版本
			formParams.priceVersion = this.detailData.priceVersion
			formParams.priceId = this.detailData.priceId
			formParams.priceCode = this.detailData.priceCode
			formParams.adjustInput = this.formData.useAmount
			Object.assign(formParams, {
				restBillDetailList: this.tableData2,
				billDetailList: this.tableData1,
				billId: id,
				year,
				billItemAmt,
				receivableAmount,
				useAmt,
				useAmount,
			})
			await apiBillPartial(formParams)
			if (this.detailData.billStatus == 0) {
				this.$alert('已成功调整，当前账单未开帐无法缴费', '提示', {
					confirmButtonText: '确定',
					callback: () => {
						this.$store.dispatch('tagsView/delView', this.$route).then(() => {
							this.$router.push({
								path: '/costManage/billManage',
							})
						})
					},
				})
			} else {
				this.$message.success('调整成功')
				this.$store.dispatch('tagsView/delView', this.$route).then(() => {
					this.$router.push({
						path: '/costManage/billManage',
					})
				})
			}
		},
		handleCancel() {
			this.$store.dispatch('tagsView/delView', this.$route).then(tags => {
				const { fullPath } = tags.slice(-1)[0]
				this.$router.push(fullPath || '/')
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
}

.page-layout {
	overflow: auto;
}
.page-left {
	padding-top: 0;
	.model-header {
		padding: 0;
	}
	.form-container {
		padding-top: 20px;
		flex: 1;
		overflow: auto;
		.form-item {
			margin-bottom: 25px;
		}
		.label {
			font-family: Source Han Sans CN;
			font-size: 14px;
			font-weight: 350;
			margin-bottom: 10px;
			color: #9092a0;
		}
		.value {
			font-family: Source Han Sans CN;
			font-size: 13px;
			font-weight: 400;
			color: #3f435e;
		}
	}
}
.page-right {
	::v-deep {
		.el-radio-group {
			.el-radio {
				margin-right: 10px;
			}
		}
		.el-form-item--small.el-form-item {
			margin-bottom: 5px;
		}
	}
	.flex {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.total-container {
		gap: 18px;
		margin-right: 20px;
		color: rgb(34, 34, 34);
		font-weight: 600;
	}
	.table-container {
		flex: 1;
		overflow: auto;
	}
	.table {
		margin-bottom: 16px;
		.gc-table {
			height: 180px;
		}
	}
	h5 {
		position: relative;
		margin-bottom: 16px;
		padding-left: 10px;
		font-family: Source Han Sans CN;
		font-size: 14px;
		font-weight: 500;
		color: #000000;
	}
	h5:before {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		content: '';
		display: block;
		clear: both;
		width: 2px;
		background-color: #2f87fe;
	}
}
.button-group {
	width: 100%;
	height: 40px;
	.btn-create,
	.btn-preview {
		margin-top: 20px;
		border-radius: 4px;
		height: 32px;
	}
	.btn-create {
		width: 216px;
	}
	.btn-preview {
		width: 110px;
		border: 1px solid #2f87fe;
		color: #2f87fe;
	}
}
</style>
