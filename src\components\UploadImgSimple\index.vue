<template>
	<div class="image-box">
		<div class="img">
			<el-image
				v-if="value"
				:src="value.split(',')[0]"
				:previewSrcList="value ? value.split(',') : []"
				style="width: 60px; height: 60px"
			/>
			<div v-else class="empty">暂无图片</div>
		</div>
		<div v-show="canUpload" class="btns-box">
			<el-upload
				ref="uploadRef"
				:action="uploadUrl"
				:show-file-list="false"
				accept="image/png, image/jpg, image/jpeg"
				:limit="1"
				:before-upload="handleBeforeUpload"
				:on-success="handleSuccess"
				:on-error="handlError"
				:on-exceed="handleExceed"
			>
				<el-button size="mini" type="primary">上传</el-button>
			</el-upload>
			<el-button size="mini" plain :disabled="!value" type="primary" style="margin-top: 4px" @click="handleClear">
				清空
			</el-button>
		</div>
	</div>
</template>

<script>
import { CPM } from '@/consts/moduleNames'

export default {
	components: {},
	props: {
		canUpload: Boolean,
		value: {
			type: String,
			default: '',
		},
	},
	data() {
		return {
			imageUrl: '',
		}
	},
	created() {
		this.imageUrl = this.value
	},
	watch: {
		//有些页面初始初始src没有赋值
		value(newValue) {
			this.imageUrl = newValue
		},
	},
	computed: {
		uploadUrl() {
			return `${process.env.VUE_APP_API_BASE_URL}${CPM}/file/manage/upload`
		},
	},
	methods: {
		handleBeforeUpload(file) {
			// 检查文件大小
			const isLt3M = file.size / 1024 / 1024 < 3
			if (!isLt3M) {
				this.$message.error('图片大小不能超过3MB!')
			}
			return isLt3M
		},
		handleSuccess(res) {
			if (res.code == 0) {
				this.imageUrl = res.data
				this.$emit('input', res.data)
			} else {
				this.imageUrl = ''
				this.$message.error('上传失败')
			}
		},
		handlError() {
			this.$message.error('上传失败')
		},
		handleExceed(files) {
			this.$refs.uploadRef.clearFiles()
			this.$refs.uploadRef.handleStart(files[0])
			this.$refs.uploadRef.submit()
		},
		handleClear() {
			this.$emit('input', '')
		},
	},
}
</script>

<style lang="scss" scoped>
.image-box {
	display: flex;
	align-items: center;
	.img {
		display: flex;
		.empty {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 60px;
			height: 60px;
			font-size: 12px;
			color: #c0c4cc;
			background-color: #f5f7fa;
		}
	}
}
.btns-box {
	display: flex;
	flex-direction: column;
	margin-left: 8px;
	justify-content: space-between;
}
</style>
