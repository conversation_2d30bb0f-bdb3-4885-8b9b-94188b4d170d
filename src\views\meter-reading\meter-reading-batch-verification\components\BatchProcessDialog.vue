<template>
	<GcElDialog
		ref="dialog"
		:show="isShow"
		:title="title"
		width="500px"
		class="process-dialog"
		:okText="canClosed ? '确定' : ''"
		:hiddenCancelBtn="true"
		@close="handleClose"
		@ok="handleClose"
	>
		<div class="content">
			<div class="item">
				<div class="label">审核进度</div>
				<div class="value">
					<el-progress :percentage="processRate" class="progress-bar"></el-progress>
				</div>
			</div>
			<div v-if="data.failMsg && data.failMsg.length" class="item fail-msg">
				<div class="label">错误消息</div>
				<div class="value">
					<p v-for="(msg, i) in data.failMsg" :key="i">{{ msg }}</p>
				</div>
			</div>
		</div>
	</GcElDialog>
</template>

<script>
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		data: {
			type: Object,
			default: () => {
				return {}
			},
		},
		canClosed: {
			type: Boolean,
			default: false,
		},
		title: {
			type: String,
			default: '批量审核进度',
		},
	},
	data() {
		return {
			selectedInvoiceInfo: null,
			loading: null,
			processRate: 0,
		}
	},
	watch: {
		data: {
			handler() {
				this.processRate = +(this.data.processRate || 0)
			},
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	methods: {
		handleClose() {
			if (!this.canClosed) {
				this.$message.error('批量审核中')
				return
			}
			this.isShow = false
			//todo 关闭时刷新页面数据
			this.$emit('closed:refresh')
		},
	},
}
</script>
<style lang="scss" scoped>
.process-dialog {
	.item {
		display: flex;
		align-items: center;
		margin-bottom: 20px;
		.label {
			margin-right: 12px;
			width: 70px;
		}
		.value {
			flex: 1;
		}
		p {
			line-height: 20px;
			margin-bottom: 10px;
			border-bottom: 1px solid #eeeeee;
			padding-bottom: 10px;
		}
		.progress-bar {
			width: 360px;
		}
		&.fail-msg {
			align-items: flex-start;
		}
	}
}
</style>
