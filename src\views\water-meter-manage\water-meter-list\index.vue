<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>

			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div class="right-top">
				<div>
					<el-button v-has="'cpm_meter_create-meter'" type="primary" @click="handleAdd">新增水表</el-button>
					<!-- TODO -->
					<!-- <el-button v-has="'cpm_meter_batch-create-meter'" type="primary"
            >水表导入</el-button
          > -->
					<el-button
						v-has="'cpm_report_meter_export_excel'"
						v-show="tableData.length"
						type="primary"
						@click="handleExport"
					>
						全部导出
					</el-button>
				</div>
				<div v-show="tableData.length">
					<!-- 检修 才能进行 报废 -->
					<el-button
						v-has="'cpm_meter_batch-scrap-meter'"
						v-show="formData.meterStatus === '2'"
						type="primary"
						:disabled="selectedData.length === 0"
						@click="handleBatchScrap"
					>
						批量报废
					</el-button>
					<!-- 在用 才能进行 停水 -->
					<el-button
						v-has="'cpm_archives_batch-disable'"
						v-show="formData.meterStatus === '1'"
						type="primary"
						:disabled="selectedData.length === 0"
						@click="handleBatchCut"
					>
						批量停水
					</el-button>
					<!-- 停水 才能进行 恢复用水 -->
					<el-button
						v-has="'cpm_archives_batch-enable'"
						v-show="formData.meterStatus === '5'"
						type="primary"
						:disabled="selectedData.length === 0"
						@click="handleBatchRestore"
					>
						批量恢复用水
					</el-button>
					<!-- 待安装、检修、报废 可以变更状态 -->
					<el-button
						v-has="'cpm_meter_modify-meter-status'"
						v-show="modifyStatusValueArr.includes(formData.meterStatus)"
						type="primary"
						:disabled="selectedData.length === 0"
						@click="handleModifyStatus"
					>
						水表状态变更
					</el-button>
				</div>
			</div>
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				:selectable="selectable"
				showPage
				:needType="needTypeSelectionComp"
				@current-page-change="handlePageChange"
				@selectChange="handleSelectChange"
				@dblclick="handleRowDbClick"
			></GcTable>
		</div>

		<!-- 新增水表弹窗 -->
		<UpdateMeterDialog :show.sync="showUpdateMeterDialog" @success="getList(1)" />
		<!-- 批量报废弹窗 -->
		<ScrapMeterDialog :show.sync="showScrapDialog" isBatch :data="chosedData" @success="getList(1)" />
		<!-- 批量停水弹窗 -->
		<CutMeterDialog :show.sync="showCutDialog" isBatch :data="chosedData" @success="getList(1)" />
		<!-- 批量恢复用水弹窗 -->
		<RestoreMeterDialog :show.sync="showRestoreDialog" isBatch :data="chosedData" @success="getList(1)" />
		<!-- 水表状态变更弹窗 -->
		<StatusMeterDialog
			:show.sync="showStatusDialog"
			isBatch
			:data="selectedData"
			:current-meter-status="formData.meterStatus"
			@success="getList(1)"
		/>
	</div>
</template>

<script>
import { getColumn } from './tableColumn'
import { exportBlob } from '@/utils/index'
import { removeNullParams, trimParams } from '@/utils/index.js'
import UpdateMeterDialog from '../components/update-meter-dialog.vue'
import ScrapMeterDialog from '../components/scrap-meter-dialog.vue'
import CutMeterDialog from '../components/cut-meter-dialog.vue'
import RestoreMeterDialog from '../components/restore-meter-dialog.vue'
import StatusMeterDialog from '../components/status-meter-dialog.vue'
import { modifyStatusValueArr } from './options'
import { getWaterList, exportWaterMeter } from '@/api/waterMeter.api'
import { queryBusinessHallAll } from '@/api/basicConfig.api'
export default {
	name: 'OutStandingBills',
	components: {
		UpdateMeterDialog,
		ScrapMeterDialog,
		CutMeterDialog,
		RestoreMeterDialog,
		StatusMeterDialog,
	},
	data() {
		return {
			modifyStatusValueArr,

			// 左侧查询
			formData: {
				orgCode: '',
				meterNo: '',
				meterStatus: '',
				years: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '水表编号',
					prop: 'meterNo',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入水表编号',
					},
				},
				{
					type: 'el-select',
					label: '水表状态',
					prop: 'meterStatus',
					options:
						this.$store.getters?.dataList?.meterStatus?.map(item => {
							return {
								label: item.sortName,
								value: item.sortValue,
							}
						}) || [],
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择水表状态',
					},
				},
				{
					type: 'el-select',
					label: '距离首次已超过（年）',
					prop: 'years',
					options: [
						{
							label: 3,
							value: 3,
						},
						{
							label: 4,
							value: 4,
						},
						{
							label: 5,
							value: 5,
						},
						{
							label: 6,
							value: 6,
						},
						{
							label: 7,
							value: 7,
						},
						{
							label: 8,
							value: 8,
						},
					],
					attrs: {
						col: 24,
						clearable: true,
					},
				},
				{
					type: 'el-select',
					label: '表卡所属分公司',
					prop: 'orgCode',
					options: [],
				},
			],
			formAttrs: {
				rules: {},
			},
			// 右侧列表
			loading: false,
			columns: getColumn(),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			// 列表选中数据
			selectedData: [],

			// 批量报废弹窗
			showScrapDialog: false,
			// 批量停水弹窗
			showCutDialog: false,
			// 批量恢复供水操作
			showRestoreDialog: false,
			// 水表状态变更
			showStatusDialog: false,

			// 新增水表弹窗
			showUpdateMeterDialog: false,
		}
	},
	computed: {
		chosedData() {
			return this.selectedData.map(item => item.meterId)
		},
		needTypeSelectionComp() {
			let selectionValue = ''
			switch (this.formData.meterStatus) {
				case '1':
					selectionValue = this.$has('cpm_archives_batch-disable') ? 'selection' : ''
					break
				case '2':
					selectionValue = this.$has(['cpm_meter_batch-scrap-meter', 'cpm_meter_modify-meter-status'])
						? 'selection'
						: ''
					break
				case '5':
					selectionValue = this.$has('cpm_archives_batch-enable') ? 'selection' : ''
					break
				case '0':
				case '4':
					selectionValue = this.$has('cpm_meter_modify-meter-status') ? 'selection' : ''
					break
			}
			return selectionValue
		},
		userOrgCode() {
			return this.$store.getters.userInfo?.orgCode
		},
	},
	async created() {
		await this._queryBusinessHallAll()
	},
	methods: {
		// 查询营业分公司全部
		async _queryBusinessHallAll() {
			try {
				const res = await queryBusinessHallAll()
				this.formItems[3].options = res.map(item => {
					return {
						label: item.orgName,
						value: item.orgCode,
					}
				})

				if (this.formItems[3].options.length === 0) return

				const findOrg = this.formItems[3].options.find(
					item => item.value === this.$store.getters.userInfo?.orgCode,
				)
				if (findOrg) {
					this.formData.orgCode = findOrg.value
				} else {
					this.formData.orgCode = this.formItems[3].options[0].value
				}
			} catch (error) {
				console.error(error)
			}
		},
		selectable(row) {
			if (!row.orgCode) return true
			return row.orgCode.indexOf(this.userOrgCode) === 0
		},
		handleSelectChange(data) {
			this.selectedData = data
		},
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetFormSmooth()
			this.getList(1)
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			this.selectedData = []
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const params = trimParams(removeNullParams(this.formData))
				Object.assign(params, {
					size,
					current,
				})
				if (params.orgCode) {
					params['orgCode'] = [params.orgCode]
				}
				const { total = 0, records = [] } = await getWaterList(params)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},

		// 新增水表
		handleAdd() {
			this.showUpdateMeterDialog = true
		},
		// 水表导入、导出
		handleImport() {},
		async handleExport() {
			const res = await exportWaterMeter({
				...this.formData,
				orgCode: this.formData.orgCode ? [this.formData.orgCode] : [],
			})
			exportBlob(res, '水表')
		},
		// 批量报废操作
		handleBatchScrap() {
			this.$confirm('确定对所选水表进行批量报废吗？').then(() => {
				this.showScrapDialog = true
			})
		},
		// 批量停水
		handleBatchCut() {
			this.$confirm('确定对所选水表进行批量停水吗？').then(() => {
				this.showCutDialog = true
			})
		},
		// 批量恢复用水
		handleBatchRestore() {
			this.$confirm('确定对所选水表进行批量恢复用水吗？').then(() => {
				this.showRestoreDialog = true
			})
		},
		// 水表状态变更
		handleModifyStatus() {
			this.showStatusDialog = true
		},

		// 行双击 跳转水表视图页面
		handleRowDbClick({ row }) {
			const isHasPer = this.$has('cpm_archives_meter-detail')
			if (!isHasPer) return
			if (row) {
				this.$router.push({
					path: '/waterMeterManage/waterMeterView',
					query: {
						meterId: row.meterId,
					},
				})
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.right-top {
	display: flex;
	justify-content: space-between;
	align-items: center;
	.el-button {
		margin-bottom: 12px;
	}
}
.upload-box {
	display: inline-block;
	margin: 0 10px;
}
</style>
