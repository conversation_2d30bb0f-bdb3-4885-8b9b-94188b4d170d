export function getFormItems(_this) {
	const baseFormItemsArr = [
		{
			type: 'slot',
			slotName: 'recordsInfo',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-select',
			label: '营业所',
			prop: 'orgCode',
			options: _this.$store.getters.orgList,
			attrs: {
				col: 10,
			},
			events: {
				change: () => {
					_this._getAlleyMap()
				},
			},
		},
		{
			type: 'el-select',
			label: '坊别',
			prop: 'alleyId',
			loading: false,
			options: [],
			attrs: {
				col: 10,
			},
			events: {
				change: () => {
					_this._getBookList()
				},
			},
		},
		{
			type: 'el-select',
			label: '表册',
			prop: 'bookId',
			options: [],
			loading: false,
			attrs: {
				col: 10,
			},
		},
		{
			type: 'slot',
			slotName: 'addressInfo',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-select',
			label: '区/县',
			prop: 'regionCode',
			options: [],
			attrs: {
				col: 10,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'regionCode'),
			},
		},
		{
			type: 'el-select',
			label: '街道/乡镇',
			prop: 'streetCode',
			options: [],
			attrs: {
				col: 10,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'streetCode'),
			},
		},
		{
			type: 'el-select',
			label: '小区/村庄',
			prop: 'communityCode',
			options: [],
			attrs: {
				col: 10,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'communityCode'),
			},
		},
		{
			type: 'el-radio',
			label: '是否接管',
			prop: 'takeOver',
			options: [
				{ label: '未接管', value: 0 },
				{ label: '已接管', value: 1 },
			],
			attrs: {
				col: 10,
				disabled: true,
			},
		},
		{
			type: 'slot',
			slotName: 'priceInfo',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-select',
			label: '价格编号',
			prop: 'priceCode',
			options: [],
			attrs: {
				col: 10,
			},
			events: {
				change: _this.handleChangePrice,
			},
		},
		{
			type: 'el-select',
			label: '价格名称',
			prop: 'priceName',
			options: [],
			attrs: {
				col: 10,
			},
			events: {
				change: _this.handleChangePrice,
			},
		},
		{
			type: 'el-select',
			label: '计费类型',
			prop: 'billingTypeId',
			options: _this.$store.getters.dataList.billingType
				? _this.$store.getters.dataList.billingType.map(item => {
						return {
							label: item.sortName,
							value: Number(item.sortValue),
						}
				  })
				: [],
			attrs: {
				col: 10,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '用水性质',
			prop: 'natureName',
			attrs: {
				col: 10,
				disabled: true,
			},
		},
	]
	return baseFormItemsArr
}
