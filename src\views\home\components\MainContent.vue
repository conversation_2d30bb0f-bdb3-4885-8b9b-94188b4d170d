<template>
	<div class="main-content">
		<GcFormSimple
			ref="formRef"
			v-has="[
				'cpm_home_charts_waterVolumeChart',
				'cpm_home_charts_waterCostChart',
				'cpm_home_charts_waterPayCostChart',
				'cpm_home_charts_waterFeeArrearsChart',
				'cpm_home_charts_meterReadingRateChart',
				'cpm_home_charts_obtainUserPayChart',
				'cpm_home_charts_obtainUserBigAmountChart',
				'cpm_home_charts_obtainUserLadderLevelChart',
			]"
			v-model="formData"
			:formItems="formItems"
			:formAttrs="formAttrs"
		></GcFormSimple>
		<div class="chart-content" v-show="formData.orgCode">
			<!-- 用户水量 	-->
			<UserWater
				v-has="'cpm_home_charts_waterVolumeChart'"
				:natureOptions="natureOptions"
				:orgCode="formData.orgCode"
			/>
			<!-- 销售收入  -->
			<SalesInput
				v-has="'cpm_home_charts_waterCostChart'"
				:natureOptions="natureOptions"
				:orgCode="formData.orgCode"
			/>
			<!-- 水费回收率  -->
			<WaterCollection
				v-has="'cpm_home_charts_waterPayCostChart'"
				:natureOptions="natureOptions"
				:orgCode="formData.orgCode"
			/>
			<!-- 用户欠费   -->
			<OwesFees v-has="'cpm_home_charts_waterFeeArrearsChart'" :orgCode="formData.orgCode" />
			<!-- 实际抄表率  -->
			<ReadingRate v-has="'cpm_home_charts_meterReadingRateChart'" :orgCode="formData.orgCode" />
			<!-- 用户缴费  -->
			<UserPayment v-has="'cpm_home_charts_obtainUserPayChart'" :orgCode="formData.orgCode" />
			<!-- 当月用水大量提示 -->
			<WaterWarning v-has="'cpm_home_charts_obtainUserBigAmountChart'" :orgCode="formData.orgCode" />
			<!-- 阶梯用户提示 -->
			<LadderUser v-has="'cpm_home_charts_obtainUserLadderLevelChart'" :orgCode="formData.orgCode" />
		</div>
		<div class="chart-content no-data" v-show="!formData.orgCode">
			<img src="@/assets/images/empty.svg" alt="" />
		</div>
	</div>
</template>

<script>
import { queryWaterNatureTree } from '@/api/basicConfig.api'
import UserWater from './UserWater.vue'
import SalesInput from './SalesInput.vue'
import ReadingRate from './ReadingRate.vue'
import UserPayment from './UserPayment.vue'
import WaterCollection from './WaterCollection.vue'
import OwesFees from './OwesFees.vue'
import WaterWarning from './WaterWarning.vue'
import LadderUser from './LadderUser.vue'
export default {
	name: '',
	components: {
		UserWater,
		SalesInput,
		WaterCollection,
		ReadingRate,
		UserPayment,
		OwesFees,
		WaterWarning,
		LadderUser,
	},
	data() {
		return {
			formData: {
				orgCode: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业所',
					prop: 'orgCode',
					options: [],
					attrs: {
						style: {
							width: '578px',
						},
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '80px',
			},
			natureOptions: [],
		}
	},
	computed: {
		orgList() {
			return this.$store.getters.orgList
		},
	},
	watch: {
		orgList: {
			async handler(newVal) {
				const orgSelect = this.formItems.find(item => item.prop === 'orgCode')
				if (orgSelect) {
					orgSelect.options = newVal || []
					if (Array.isArray(newVal) && newVal.length) {
						await this.getNatureTree()
						this.formData.orgCode = newVal[0].value
					}
				}
			},
			immediate: true,
			deep: true,
		},
	},
	methods: {
		async getNatureTree() {
			try {
				const res = await queryWaterNatureTree()
				this.natureOptions = res || []
			} catch (error) {
				this.natureOptions = []
				console.error(error)
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.no-data {
	display: flex;
	justify-content: center !important;
	align-items: center;
	img {
		width: 240px;
		height: 240px;
	}
}
.main-content {
	padding-top: 25px;
	min-width: 714px;
	flex: 1;
	display: flex;
	flex-direction: column;
	background-color: #fff;
	.el-form--inline {
		align-self: center;
	}
	.chart-content {
		flex: 1;
		overflow: auto;
		padding: 0 12px 25px 12px;
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;
		gap: 24px;
		::v-deep {
			.chart-wrapper {
				display: flex;
				flex-direction: column;
				border: 1px solid #d5d8e2;
				border-radius: 8px;
				padding: 10px;
				width: calc(50% - 15px);
				height: 468px;

				.title-wrapper {
					margin-bottom: 10px;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					border-radius: 8px;
					padding: 12px;
					height: 123px;
					.title-top {
						display: flex;
						justify-content: space-between;
						align-items: center;
						.top-name {
							font-family: Alimama ShuHeiTi;
							font-weight: 700;
							font-size: 18px;
							color: #000000;
						}

						.el-menu-custom {
							background: transparent;
							border-bottom: none;
							padding: 0 10px;
						}
						.el-menu-item:nth-child(1) {
							margin-right: 10px;
						}
						.el-menu-item {
							padding: 0;
							height: 32px;
							line-height: 32px;
						}
						.el-menu--horizontal > .el-menu-item:not(.is-disabled):hover,
						.el-menu--horizontal > .el-menu-item:not(.is-disabled):focus {
							background-color: transparent;
						}
					}
					.title-content {
						display: flex;
						gap: 25px;
						.title-name {
							font-family: Source Han Sans CN;
							font-weight: 400;
							font-size: 14px;
							color: #3f465e;
							margin-bottom: 12px;
						}
						.title-value {
							display: flex;
							flex-wrap: nowrap;
							align-items: center;
							font-family: DIN Alternate;
							font-weight: 700;
							font-size: 22px;
							color: #000000;
							span {
								font-family: Source Han Sans CN;
								font-weight: 400;
								font-size: 14px;
								color: #3f465e;
							}
						}
					}
				}
				.chart-box {
					flex: 1;
				}
				.content-box {
					height: 100%;
					display: flex;
					flex-direction: column;
					.top {
						display: flex;
						justify-content: space-between;
					}
					.bottom {
						flex: 1;
					}
				}
			}
		}
	}
}
</style>
