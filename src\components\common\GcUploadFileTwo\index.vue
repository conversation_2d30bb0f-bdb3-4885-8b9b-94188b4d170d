<template>
	<div class="gc-upload-file">
		<el-upload
			ref="uploadRef"
			:action="apiUrl"
			:limit="limit"
			:file-list="fileList"
			:show-file-list="false"
			:accept="accept"
			:auto-upload="false"
			:on-change="handleChange"
			:disabled="isDisabled"
			:name="name"
			:data="fieldData"
			:headers="uploadHeaders"
		>
			<template #trigger>
				<slot name="trigger">
					<el-button type="primary" :loading="loading" :disabled="isDisabled">{{ buttonName }}</el-button>
				</slot>
			</template>
		</el-upload>
	</div>
</template>

<script>
import { getToken } from '@/utils/storage'
import { exportBlob } from '@/utils/index.js'
export default {
	name: 'GcUploadFileTwo',
	props: {
		accept: {
			type: String,
			default: '.xls,.xlsx', //限制excel
		},
		limit: {
			type: Number,
			default: 15,
		},
		value: {
			type: Array,
			default: () => [],
		},
		isDisabled: {
			type: Boolean,
			default: false,
		},
		buttonName: {
			type: String,
			default: '选择文件',
		},
		uploadApi: {
			type: String,
			default: '',
		},
		fieldData: {
			type: Object,
			default: () => {},
		},
		name: {
			type: String,
			default: 'file',
		},
	},
	components: {},
	data() {
		return {
			progress: 0,
			loading: false,
			file: null,
			uploadHeaders: {
				Accesstoken: getToken(),
			},
		}
	},
	computed: {
		fileList: {
			get() {
				return this.value
			},
			set(newVal) {
				this.$emit('input', newVal)
			},
		},
		apiUrl() {
			const IS_PROD = ['production', 'prod'].includes(process.env.NODE_ENV)
			const preUrl = IS_PROD ? '' : 'api'
			return preUrl + this.uploadApi
		},
	},
	methods: {
		handleChange(file) {
			if (!this.beforeUpload(file.raw)) {
				return false
			}
			const formData = new FormData()
			formData.append(this.name, file.raw)
			for (const key in this.fieldData) {
				if (Object.prototype.hasOwnProperty.call(this.fieldData, key)) {
					formData.append(key, this.fieldData[key])
				}
			}

			const xhr = new XMLHttpRequest()
			xhr.open('POST', this.apiUrl, true)
			xhr.setRequestHeader('Accesstoken', getToken())
			xhr.responseType = 'blob'

			xhr.onload = () => {
				if (xhr.status === 200) {
					const contentType = xhr.getResponseHeader('Content-Type')
					const mainContentType = contentType.split(';')[0].trim()
					// Content-Type 是 application/json; 上传成功
					if (mainContentType === 'application/json') {
						try {
							// 由于 responseType 是 'blob'，需要将 Blob 转换为文本
							const reader = new FileReader()
							reader.onload = () => {
								const response = JSON.parse(reader.result)
								this.handleSuccess(response, file, xhr)
							}
							reader.readAsText(xhr.response)
						} catch (error) {
							this.handleError(error)
						}
					} else {
						// Content-Type 不是 application/json，代表部分导入失败，下载文件
						console.warn('Unexpected Content-Type:', mainContentType, xhr)
						exportBlob(xhr.response, '导入失败', 'xlsx')
						this.loading = false
					}
				} else {
					this.handleError(xhr.statusText)
				}
			}

			xhr.upload.onprogress = event => {
				if (event.lengthComputable) {
					const progress = (event.loaded / event.total) * 100
					console.log('长传进度:', progress)
				}
			}

			xhr.send(formData)
		},
		beforeUpload(file) {
			const validExtensions = this.accept.split(',').map(ext => ext.trim().toLowerCase().replace(/^\./, ''))
			const fileExtension = file.name.split('.').pop().toLowerCase()
			if (!validExtensions.includes(fileExtension)) {
				this.$message.error(`只能上传 ${this.accept}格式的文件！`)
				return false
			}
			if (file.size / 1024 / 1024 > this.limit) {
				this.$message.error(`文件大小最大为${this.limit}M`)
				return false
			}
			this.loading = true
			this.file = { file: file, name: file.name, uid: file.uid }
			return true
		},
		handleSuccess(res) {
			this.loading = false
			if (res.code !== 0) {
				this.$message.error(res.message)
				return
			} else {
				this.$message.success(res.message)
			}

			this.fileList.push({
				...this.file,
				url: res.data,
			})
			this.$emit('on-success', {
				...this.file,
				url: res.data,
			})
		},
		handleError(error) {
			console.error(error)
			this.$message.error(error)
			this.loading = false
		},
	},
}
</script>

<style lang="scss" scoped>
.gc-upload-file {
	width: 100%;
}
</style>
