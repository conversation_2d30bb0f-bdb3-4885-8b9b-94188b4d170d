export default function (_this) {
	const resident = [
		{
			type: 'slot',
			label: '纳税人识别号',
			slotName: 'taxpayerIdentity',
			attrs: {
				col: 16,
			},
		},
		{
			type: 'el-input',
			label: '开票抬头',
			prop: 'buyerName',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 16,
			},
		},
		{
			type: 'el-input',
			label: '开户银行',
			prop: 'openBank',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 16,
			},
		},

		{
			type: 'el-input',
			label: '银行账户',
			prop: 'bankAccount',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 16,
			},
		},
	]
	const company = [
		{
			type: 'el-select',
			label: '开票类型',
			prop: 'invoiceType',
			options:
				_this.$store.getters?.dataList?.invoiceType?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 16,
			},
		},
		{
			type: 'slot',
			label: '纳税人识别号',
			slotName: 'taxpayerIdentity',
			attrs: {
				col: 16,
			},
		},
		{
			type: 'el-input',
			label: '开票抬头',
			prop: 'buyerName',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 16,
			},
		},
		{
			type: 'el-input',
			label: '开户银行',
			prop: 'openBank',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 16,
			},
		},

		{
			type: 'el-input',
			label: '银行账户',
			prop: 'bankAccount',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 16,
			},
		},
	]

	return {
		3: resident,
		4: company,
	}
}
