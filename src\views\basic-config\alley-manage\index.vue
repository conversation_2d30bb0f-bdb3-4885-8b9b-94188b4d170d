<template>
	<div class="page-layout">
		<div class="page-left">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div v-has="'cpm_alley_addAlley'" class="right-top">
				<el-button type="primary" @click="handleAdd">新增坊别</el-button>
			</div>
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:deal="{ row }">
					<el-button v-has="'cpm_alley_updateAlley'" type="text" size="medium" @click="handleEdit(row)">
						修改
					</el-button>
					<el-button v-has="'cpm_alley_deleteAlley'" type="text" size="medium" @click="handleDelete(row)">
						删除
					</el-button>
				</template>
			</GcTable>
		</div>

		<!-- 新增、编辑弹窗 -->
		<UpdateDialog ref="updateDialogRef" :show.sync="showUpdate" :editType="editType" @success="getList(1)" />
	</div>
</template>

<script>
import UpdateDialog from './components/UpdateDialog.vue'
import { queryAlleyPage, deleteAlley } from '@/api/basicConfig.api'

export default {
	name: 'AlleyManage',
	components: { UpdateDialog },
	data() {
		return {
			formData: {
				alleyCode: '',
				orgCode: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '坊别编号',
					prop: 'alleyCode',
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请输入坊别编号',
					},
				},
				{
					type: 'el-select',
					label: '所属营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						col: 24,
						clearable: true,
						placeholder: '请选择所属营业分公司',
					},
				},
			],
			formAttrs: {
				rules: {},
			},
			// 右侧列表
			loading: false,
			columns: [
				{
					key: 'alleyCode',
					name: '坊别编号',
					tooltip: true,
				},
				{
					key: 'alleyName',
					name: '坊别名称',
					tooltip: true,
				},
				{
					key: 'orgName',
					name: '所属营业分公司',
					tooltip: true,
				},
				{
					key: 'archivesIdentity',
					name: '最大表卡编号',
					tooltip: true,
				},
				{
					hide: !this.$has(['cpm_alley_updateAlley', 'cpm_alley_deleteAlley']),
					key: 'deal',
					name: '操作',
					fixed: 'right',
					width: 120,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 新增坊别弹窗
			editType: 'add',
			showUpdate: false,
		}
	},
	computed: {},
	created() {
		this.getList()
	},
	methods: {
		handleReset() {
			this.$refs.formRef.resetFormSmooth()
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await queryAlleyPage({
					size,
					current,
					...this.formData,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		// 编辑
		handleEdit(data) {
			this.editType = 'edit'
			this.showUpdate = true
			this.$nextTick(() => {
				this.$refs.updateDialogRef.setFormData(data)
			})
		},
		// 删除
		handleDelete({ alleyId, alleyCode }) {
			this.$confirm(`正在对【坊别编号：${alleyCode ?? '--'}】进行删除，请确认是否删除？`).then(async () => {
				await deleteAlley({
					alleyId,
				})
				this.$message.success('删除成功')
				this.getList(1)
			})
		},
		// 新增坊别
		handleAdd() {
			this.editType = 'add'
			this.showUpdate = true
		},
	},
}
</script>

<style lang="scss" scoped>
.page-left {
	overflow: auto;
}
.right-top {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-bottom: 12px;
}
.info-title {
	height: auto;
	padding: 0;
	margin-bottom: 12px;
}
.icon-more {
	transform: rotate(90deg);
}
.list-box {
	flex: 1;
	overflow: auto;
	.list-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 42px;
		padding: 0 12px;
		cursor: pointer;
		.label {
			flex: 1;
			margin-right: 12px;
			@include text-overflow;
		}
		&.active {
			color: #2f87fe;
			background-color: rgba(196, 221, 255, 0.5);
			.el-dropdown {
				display: block;
			}
		}
	}
	.el-dropdown {
		display: none;
	}
}
</style>
