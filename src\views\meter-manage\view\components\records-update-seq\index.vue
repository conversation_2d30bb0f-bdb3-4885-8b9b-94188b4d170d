<template>
	<GcElDialog
		:show="isShow"
		title="修改册内序号"
		width="500px"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleOk"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
	</GcElDialog>
</template>

<script>
import { updateArchivesSeq1, updateArchivesSeq2 } from '@/api/meterReading.api.js'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		permissionCode: {
			type: String,
			default: 'plan-collection_meterReadingBook_updateArchivesSeq1',
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				recordSeq: '',
			},
			formItems: [
				{
					type: 'el-input-number',
					label: '册内序号',
					prop: 'recordSeq',
					attrs: {
						col: 24,
						max: 9999999,
						min: 1,
						stepStrictly: true,
						placeholder: '请输入',
					},
				},
			],
			formAttrs: {
				rules: {
					recordSeq: [{ required: true, message: '请输入', trigger: 'blur' }],
				},
			},
			archivesId: '',
		}
	},
	methods: {
		handleClose() {
			this.$refs.formRef.resetForm()
			this.isShow = false
		},
		async handleOk() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				const apiMethods = {
					'plan-collection_meterReadingBook_updateArchivesSeq1': updateArchivesSeq1,
					'plan-collection_meterReadingBook_updateArchivesSeq2': updateArchivesSeq2,
				}
				await apiMethods[this.permissionCode]({
					archivesId: this.archivesId,
					recordSeq: this.formData.recordSeq,
				})
				this.$message.success('修改成功')
				this.$emit('success')
				this.handleClose()
			}
		},
		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
			this.archivesId = data.archivesId
		},
	},
}
</script>
