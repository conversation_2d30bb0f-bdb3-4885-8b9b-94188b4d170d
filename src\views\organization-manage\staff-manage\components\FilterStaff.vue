<template>
	<GcElDialog :show="isShow" title="员工筛选" @close="handleClose" @cancel="handleClose" @ok="handleSave">
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
	</GcElDialog>
</template>

<script>
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				name: '',
				loginAccount: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '员工姓名',
					prop: 'name',
					attrs: {
						clearable: true,
						col: 24,
					},
				},
				{
					type: 'el-input',
					label: '登录账号',
					prop: 'loginAccount',
					attrs: {
						clearable: true,
						col: 24,
					},
				},
			],
			formAttrs: {
				displayItem: 'block',
				labelPosition: 'top',
			},
		}
	},
	methods: {
		handleClose() {
			this.isShow = false
		},
		async handleSave() {
			this.isShow = false
			this.$emit('search', this.formData)
		},
		reset() {
			this.formData = {
				name: '',
				loginAccount: '',
			}
		},
	},
}
</script>
