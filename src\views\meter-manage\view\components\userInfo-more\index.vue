<template>
	<GcElDialog :show="isShow" title="用户信息" width="500px" :showFooter="false" @close="isShow = false">
		<GcGroupDetail :data="userInfo"></GcGroupDetail>
	</GcElDialog>
</template>

<script>
import { getfilterName } from '@/utils'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		userInfo() {
			// 居民
			const list = [
				{
					key: '用户名称',
					value: '--',
					field: 'userName',
				},
				{
					key: '联系人',
					value: '--',
					field: 'contactPeople',
				},
				{
					key: '手机号',
					value: '--',
					field: 'userMobile',
				},
				{
					key: '收费方式',
					value: '--',
					field: 'chargingMethod',
				},
				{
					key: '曾用名',
					value: '--',
					field: 'nameUsedBefore',
				},
				{
					key: '电话',
					value: '--',
					field: 'contactPhone',
				},

				{
					key: '户数',
					value: '--',
					field: 'households',
				},
				{
					key: '人口数',
					value: '--',
					field: 'resiPopulation',
				},
				{
					key: '产权人名称',
					value: '--',
					field: 'propertyOwner',
				},
				{
					key: '邮编',
					value: '--',
					field: 'zipCode',
				},
				{
					key: '身份证号',
					value: '--',
					field: 'certificateNo',
				},
				{
					key: '其他证件',
					value: '--',
					field: 'certificateType',
				},
				{
					key: '证件号码',
					value: '--',
					field: 'otherCertificateNo',
				},
				{
					key: '电子邮箱',
					value: '--',
					field: 'email',
				},
				{
					key: '用户类型',
					value: '--',
					field: 'userSubType',
				},
				{
					key: '邮寄地址',
					value: '--',
					field: 'mailingAddress',
				},
				{
					key: '其他手机号',
					value: '--',
					field: 'otherContactPhone',
				},
				{
					key: '购房合同',
					field: 'purchaseContractUrl',
					col: 4,
				},
			]
			// 企业
			const list2 = [
				{
					key: '用户名称',
					value: '--',
					field: 'userName',
				},
				{
					key: '联系人',
					value: '--',
					field: 'contactPeople',
				},
				{
					key: '手机号',
					value: '--',
					field: 'userMobile',
				},
				{
					key: '电话',
					value: '--',
					field: 'contactPhone',
				},
				{
					key: '邮编',
					value: '--',
					field: 'zipCode',
				},
				{
					key: '电子邮箱',
					value: '--',
					field: 'email',
				},
				{
					key: '用户类型',
					value: '--',
					field: 'userSubType',
				},
				{
					key: '邮寄地址',
					value: '--',
					field: 'mailingAddress',
				},
				{
					key: '其他手机',
					value: '--',
					field: 'otherContactPhone',
				},
				{
					key: '购房合同',
					field: 'purchaseContractUrl',
				},
				{
					key: '营业执照',
					field: 'businessLicenseUrl',
				},
			]
			const extractedData = Object.assign({}, ...Object.values(this.tabData))
			const newList = extractedData.userType == 3 ? list : list2 //3 居民 4 企业
			const getValue = (field, value) => {
				const {
					chargingMethod = [],
					certificateType = [],
					resident = [],
					business = [],
				} = this.$store.getters.dataList || {}
				switch (field) {
					case 'chargingMethod':
						return getfilterName(chargingMethod, value, 'sortValue', 'sortName')
					case 'certificateType':
						return getfilterName(certificateType, value, 'sortValue', 'sortName')
					case 'userSubType': {
						const userSubTypeList = extractedData.userType == 3 ? resident : business
						userSubTypeList.forEach(subItem => {
							subItem.sortValue = Number(subItem.sortValue)
						})

						return getfilterName(userSubTypeList, value, 'sortValue', 'sortName')
					}
					case 'purchaseContractUrl':
					case 'businessLicenseUrl': {
						const url = value ? JSON.parse(value) : []
						return url.length > 0 ? url[0].name : '--'
					}
					default:
						return value
				}
			}
			newList.forEach(item => {
				item.value = getValue(item.field, extractedData[item.field])
			})
			return {
				list: newList,
			}
		},
	},
}
</script>
