<template>
	<div class="container">
		<div class="search-container">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" @click="handleSearch">
						<i class="iconfontCis icon-small-search"></i>
						筛选
					</el-button>
					<el-button @click="handleReset">
						<i class="iconfontCis icon-reset"></i>
						重置
					</el-button>
				</el-form-item>
			</GcFormSimple>
			<el-button type="primary" :disabled="!selectedData.length" @click="goPay">缴费</el-button>
		</div>

		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				needType="selection"
				@selectChange="handleSelectChange"
				@current-page-change="handleChangePage"
			/>
		</div>
	</div>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiGetBillArrearsList1 } from '@/api/costManage.api'
export default {
	data() {
		return {
			formData: {
				archivesIdentity: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
				},
			],
			formAttrs: {
				inline: true,
			},
			columns: [
				{
					key: 'billNo',
					name: '账单编号',
					tooltip: true,
					minWidth: 220,
				},
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'receivableAmount',
					name: '欠费金额',
					tooltip: true,
				},
				{
					key: 'useAmount',
					name: '水量',
					tooltip: true,
				},
				{
					key: 'priceCode',
					name: '价格编码',
					tooltip: true,
				},
				{
					key: 'priceVersion',
					name: '价格版本号',
					tooltip: true,
				},
				{
					key: 'useAmt',
					name: '水费',
					tooltip: true,
				},
				{
					key: 'billItemAmt',
					name: '污水费',
					tooltip: true,
				},
				{
					key: 'billDate',
					name: '账期',
					tooltip: true,
				},
				{
					key: 'billOpenTime',
					name: '开账时间',
					tooltip: true,
					minWidth: 200,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
			selectedData: [],
		}
	},
	methods: {
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
					userId: this.$route.query.userId || '',
				})

				const { records, total } = await apiGetBillArrearsList1(formParams)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 点击缴费
		goPay() {
			const billNos = this.selectedData.map(item => item.billNo)
			this.$router.push({
				path: '/costManage/paymentPage',
				query: {
					billNos: billNos?.length ? billNos.join(',') : '',
				},
			})
		},
		handleSelectChange(list) {
			this.selectedData = list
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleSearch() {
			this.handleChangePage({ page: 1 })
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.handleSearch()
		},
	},
}
</script>

<style lang="scss" scoped>
.search-container {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}
</style>
