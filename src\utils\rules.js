import { isBlank } from './validate'
import { isComplexPass } from './index'

/**
 * string 类型必填项校验
 * @param { string } message 校验提示说明文字
 * @return { object }
 */
export function ruleRequired(message, trigger = '') {
	return {
		required: true,
		message: message,
		trigger,
		validator: (rule, value, callback) => {
			if (isBlank(value)) {
				return callback(new Error(message))
			}
			callback()
		},
	}
}

/**
 * int 类型必填项校验
 * @param { dtring } message 校验提示说明文字
 * @return { object }
 */
export function ruleRequiredInt(message) {
	return {
		type: 'number',
		required: true,
		message: message,
		trigger: '',
	}
}

/**
 * 具体位数限制校验
 * @param { number } value
 * @param { String } label
 * @return { object }
 */
export function ruleDigit(value, label = '只能输入') {
	return {
		max: value,
		min: value,
		message: `${label}${value}位`,
		trigger: '',
	}
}

/**
 * 最大长度限制校验
 * @param { number } max
 * @param { String } label
 * @return { object }
 */
export function ruleMaxLength(max, label = '输入字符') {
	return {
		max,
		message: `${label}不能超过${max}位`,
		trigger: '',
	}
}

/**
 * 最小长度限制校验
 * @param { number } min
 * @param { String } label
 * @return { object }
 */
export function ruleMinLength(min, label = '请输入') {
	return {
		min,
		message: `${label}至少${min}位字符`,
		trigger: '',
	}
}

// 手机号格式校验
export const RULE_PHONE = {
	pattern: /^1[3456789]\d{9}$/,
	message: '输入手机号格式不对，11位且13-19开头',
	trigger: '',
}

// 周期累积量格式校验
export const RULE_CYCLE_CUMULATIVE = {
	pattern: /^\d{1,8}(\.\d{1,4})?$/,
	message: '输入格式不对，最多输入8位整数，最多4位小数',
	trigger: '',
}

// 限购金额格式校验
export const RULE_TOP_UP_LIMIT = {
	pattern: /^\d{1,8}(\.\d{1,2})?$/,
	message: '输入格式不对，最多输入8位整数，最多2位小数',
	trigger: '',
}

// 旧表表底数格式校验
export const RULE_METER_READING = {
	pattern: /^\d{1,10}(\.\d{1})?$/,
	message: '输入格式不对，最多输入10位整数，1位小数',
	trigger: '',
}

// 启用表底数格式校验
export const RULE_STARTMETER_READING = {
	pattern: /^\d{1,10}(\.\d{1,4})?$/,
	message: '输入格式不对，最多10位整数，4位小数',
	trigger: '',
}

// 价格格式校验
export const RULE_PRICE = {
	pattern: /^(0\.\d{0,3}[1-9]|\+?[1-9][0-9]{0,3})(\.\d{1,2})?$/,
	message: '输入格式不对，最多4位正整数，2位小数',
	trigger: '',
}

export const RULE_ZERO_PRICE = {
	pattern: /^(0|[1-9]\d{0,3})(\.\d{1,2})?$/,
	message: '输入格式不对，最多4位正整数，2位小数或者0',
	trigger: '',
}

// 费用格式校验
export const RULE_FEES = {
	pattern: /^(0\.\d{0,1}[0-9]|\+?[1-9][0-9]{0,9})(\.\d{1,2})?$/,
	message: '输入格式不对，最多10位正整数，2位小数',
	trigger: '',
}

// 计费周期格式校验
export const BILLINGCYCLE_RULE = {
	pattern: /^(([1-9])|(1\d)|(2[0-4]))$/,
	message: '只能输入1-24正整数',
	trigger: '',
}

// 限购周期格式校验
export const RULE_LIMIT_TOPUP_CYCLE = {
	pattern: /^([1-9]|1[0-2])$/,
	message: '只能输入1-12正整数',
	trigger: '',
}

// 表类型·使用年限格式校验
export const USEYEARS_RULE = {
	pattern: /^(([0-9])|([1-9]\d))$/,
	message: '只能输入0-99正整数',
	trigger: '',
}

// IC卡类型·IC卡号长度格式校验
export const ICCARDNUM_RULE = {
	pattern: /^(([4-9])|([1]\d)|([2][0]))$/,
	message: '只能输入4-20正整数',
	trigger: '',
}

// 多天不用气强关、普关天数
export const FORCED_CLOSE_RULE = {
	pattern: /^([3-9]|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])$/,
	message: '只能输入3-255正整数',
	trigger: '',
}

// 人口基数格式校验
export const POPULATION_BASE_RULE = {
	pattern: /^([12][0-9]|30|[1-9])$/,
	message: '请输入正整数，大于0，小于等于30',
	trigger: '',
}

// 人口递增值格式校验
export const POPULATION_BASE_INCREASE_RULE = {
	pattern: /^(0\.\d{0,1}[1-9]|\+?[1-9][0-9]{0,2})(\.\d{1,2})?$/,
	message: '输入格式不对，最多输入3位整数，2位小数',
	trigger: '',
}

// 税率校验
export const TAXRATE_RULE = {
	pattern: /^([1-9]\d*|0)(\.\d{1,4})?$/,
	message: '输入税率只能处于0~1之间的小数，且最多输入四位小数',
	trigger: '',
}

export const RULE_INT_ENGLISH = {
	pattern: /^[a-z0-9]+$/i,
	message: '只能输入数字和英文字母',
	trigger: '',
}

export const RULE_POSITIVENUMBER = {
	pattern: /^[+]?(\d+(\.\d*)?|\.\d+)$/i,
	message: '必须输入正数',
	trigger: '',
}

export const RULE_POSITIVEINTEGERONLY_STARTOFZERO = {
	pattern: /^(?:0|0*[1-9]\d*)$/,
	message: '必须输入正整数',
	trigger: '',
}

export const RULE_POSITIVEINTEGERONLY = {
	pattern: /^[1-9]\d*$/,
	message: '必须输入正整数',
	trigger: '',
}

export const RULE_INTEGERONLY = {
	pattern: /^[0-9]\d*$/,
	message: '必须输入整数',
	trigger: '',
}

export const RULE_INCORRECTIDCARD = {
	pattern: /(^\d{15}$)|(^\d{17}(\d|X|x)$)/,
	message: '身份证格式错误',
	trigger: '',
}

export const RULE_INCORRECTEMAIL = {
	pattern: /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/,
	message: '邮箱格式错误',
	trigger: '',
}

export const RULE_INCORRECTIP = {
	required: true,
	pattern: /^(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)$/,
	message: 'IP格式错误',
	trigger: '',
}

export const RULE_INVALIDINPUT = {
	pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
	message: '必须为字母开头，由字母、数字、下划线组成',
	trigger: '',
}

export const RULE_INVALIDSMALLINPUT = {
	pattern: /^[a-z][a-z0-9_]*$/,
	message: '必须为小写字母开头，由小写字母、数字、下划线组成',
	trigger: '',
}

export const RULE_PASSWORD = {
	pattern: /^.*(?=.{8,})(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@+._#$%^&*? ]).*$/,
	message: '需至少包含以下4种字符：大、小写字母、数字、特殊字符, 长度大于等于8位',
	trigger: '',
}

export const RULE_CORRECTINPUT = {
	pattern: /^[a-zA-Z0-9_]*$/,
	message: '仅允许输入字母、数字、下划线',
	trigger: '',
}

export const RULE_CORRECTLOWERLETTER = {
	pattern: /^[a-z0-9_]*$/,
	message: '仅允许输入小写字母、数字、下划线',
	trigger: '',
}

export const RULE_LETTERFIRST = {
	pattern: /^[a-zA-Z][a-z0-9_]*$/,
	message: '仅允许输入小写字母、数字、下划线, 且必须由字母开头',
	trigger: '',
}

export const RULE_SPECIALPARAMS = {
	pattern: /^((?![\\、@#$%^&*]).)*$/,
	message: '不能包含\\、@#$%^&*',
	trigger: '',
}

export const RULE_CHINESE = {
	required: true,
	pattern: /[\u4e00-\u9fa5]/,
	message: '必须输入中文',
	trigger: '',
}

export const RULE_ENGLISH = {
	required: true,
	pattern: /^[^\u4e00-\u9fa5]+$/,
	message: '不能输入中文',
	trigger: '',
}

export const RULE_CASE_NUM = {
	pattern: /^[A-Za-z0-9]+$/,
	message: '只能输入大小写字母和数字',
	trigger: '',
}

// 输入范围校验
export const RULE_RANGE = {
	trigger: '',
	validator: (rule, value, callback) => {
		if (isBlank(value)) return callback()
		const reg = /^(0|[1-9][0-9]*)$/s
		if (!reg.test(value) || value < 0 || value > 2147483647) {
			return callback(new Error('10位正整数，范围为0-2147483647'))
		}
		callback()
	},
}

/**
 * 等保密码复杂度校验
 * @return { object }
 */
export function ruleComplexPassValidate() {
	const passvalidate = (rule, value, callback) => {
		if (!isComplexPass(value)) {
			callback(new Error('至少两种字符组合(数字/字母/特殊符号)!'))
		} else {
			callback()
		}
	}
	return { validator: passvalidate, trigger: '' }
}

/**
 * 密码前后是否一致
 * @return { object }
 */
export function ruleEqualPassValidate(beforeVal) {
	const passvalidate = (rule, value, callback) => {
		if (value !== beforeVal) {
			callback(new Error('两次输入密码不一致!'))
		} else {
			callback()
		}
	}
	return { validator: passvalidate, trigger: '' }
}
// 验证邮政编码
export const RULE_POSTALCODE = {
	pattern: /^[0-9]{6}$/,
	message: '请输入有效的邮政编码（6位数字）',
	trigger: 'blur',
}
// 验证最小值
export function validateMinValue(inputValue) {
	const valueValidate = (rule, value, callback) => {
		if (value < inputValue) {
			callback(new Error(`输入值不能小于${inputValue}`))
		} else {
			callback()
		}
	}
	return { validator: valueValidate, trigger: '' }
}
// 验证最大值
export function validateMaxValue(inputValue) {
	const valueValidate = (rule, value, callback) => {
		if (value > inputValue) {
			callback(new Error(`输入值不能大于${inputValue}`))
		} else {
			callback()
		}
	}
	return { validator: valueValidate, trigger: '' }
}
// 验证最大值，最小值
export function validateValue(inputValue, comparison = 'greater') {
	const valueValidate = (rule, value, callback) => {
		if (comparison === 'greater' && value > inputValue) {
			callback(new Error(`输入值不能大于${inputValue}`))
		} else if (comparison === 'greaterOrEqual' && value >= inputValue) {
			callback(new Error(`输入值不能大于或等于${inputValue}`))
		} else if (comparison === 'less' && value < inputValue) {
			callback(new Error(`输入值不能小于${inputValue}`))
		} else if (comparison === 'lessOrEqual' && value <= inputValue) {
			callback(new Error(`输入值不能小于或等于${inputValue}`))
		} else {
			callback()
		}
	}
	return { validator: valueValidate, trigger: '' }
}
