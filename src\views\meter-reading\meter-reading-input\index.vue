<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-02 19:07:24
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 19:18:18
-->
<template>
	<div class="container">
		<div class="container-title">
			<gc-model-header
				class="info-title"
				:title="`表册编号：${$route.query.bookNo || '--'}`"
				:icon="require('@/assets/images/icon/title-common-parameters.png')"
			>
				<template v-slot:left>
					<div class="data-item ml12">
						<div class="label">抄表月：</div>
						<div class="value">
							{{
								taskDetailData.taskYear && taskDetailData.taskMonth
									? `${taskDetailData.taskYear}-${taskDetailData.taskMonth}`
									: '--'
							}}
						</div>
					</div>
				</template>
				<template v-slot:right>
					<div class="data-box">
						<div class="data-item">
							<div class="label">表卡总数：</div>
							<div class="value">{{ taskDetailData.meterNum }}</div>
						</div>
						<div class="data-item ml12">
							<div class="label">移交表卡数：</div>
							<div class="value">{{ taskDetailData.overNum }}</div>
						</div>
						<div class="data-item ml12">
							<div class="label">送内复表卡数：</div>
							<div class="value">{{ taskDetailData.reviewsNum }}</div>
						</div>
					</div>
				</template>
			</gc-model-header>
		</div>
		<div class="container-search">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" @click="getList(1)">筛选</el-button>
					<el-button @click="handleReset">重置</el-button>
				</el-form-item>
			</GcFormSimple>
			<div class="btn-group">
				<!-- <el-button type="primary">抄表结果批量导入</el-button> -->
				<el-button
					v-has="'plan-collection_meterReadingTask_importMeterReadingRecordExcel'"
					type="primary"
					:disabled="isBatchImportButtonDisabled"
					@click="handleBatchImport"
				>
					批量导入
				</el-button>
				<el-button
					v-has="'plan-collection_meterReadingTask_autoReviewByRecord'"
					type="primary"
					:disabled="!selectedData.length"
					@click="handleSendReview"
				>
					送内复
				</el-button>
				<el-button
					v-has="'plan-collection_meterReadingTask_autoReviewByTask3'"
					type="primary"
					@click="handleTotalSendReview"
				>
					整册送内复
				</el-button>
			</div>
		</div>
		<div class="table-container">
			<el-form class="table-form" ref="tableFormRef" :model="tableForm" style="height: 100%">
				<GcTable
					ref="gcTableRef"
					:loading="loading"
					:columns="columns"
					:table-data="tableForm.tableData"
					:page-size="pageData.size"
					:total="pageData.total"
					:current-page="pageData.current"
					showPage
					needType="selection"
					:selectable="handleSelectable"
					@selectChange="handleSelectChange"
					@current-page-change="handlePageChange"
				>
					<!-- 上次指针 -->
					<template v-slot:lastMeterReading="{ row }">
						<span :class="{ 'reading-error': judgeBlank(row.lastMeterReading) }">
							{{ !judgeBlank(row.lastMeterReading) ? row.lastMeterReading : '--（指针异常）' }}
						</span>
					</template>
					<!-- 本次指针 -->
					<template v-slot:curMeterReading="{ row, $index }">
						<span v-show="!row.isEditing">
							{{ !judgeBlank(row.curMeterReading) ? row.curMeterReading : '--' }}
						</span>
						<el-form-item
							v-show="row.isEditing"
							:prop="`tableData[${$index}].curMeterReading`"
							:rules="{
								validator: validFn('curMeterReading', $index),
								trigger: 'blur',
							}"
						>
							<el-input-number
								v-model="tableForm.tableData[$index].curMeterReading"
								:controls="false"
								:min="0"
								:max="999999999"
								step-strictly
								placeholder="请输入本次指针"
								@change="handleCurMeterReadingChange($index)"
							/>
						</el-form-item>
					</template>
					<!-- 本次水量 -->
					<template v-slot:useAmount="{ row, $index }">
						<span v-show="!row.isEditing">{{ !judgeBlank(row.useAmount) ? row.useAmount : '--' }}</span>
						<el-form-item
							v-show="row.isEditing"
							:prop="`tableData[${$index}].useAmount`"
							:rules="{
								validator: validFn('useAmount', $index),
								trigger: 'blur',
							}"
						>
							<el-input-number
								v-model="tableForm.tableData[$index].useAmount"
								:controls="false"
								:min="-999999999"
								:max="999999999"
								step-strictly
								placeholder="请输入本次水量"
							/>
						</el-form-item>
					</template>
					<!-- 抄表情况 -->
					<template v-slot:checkStatus="{ row, $index }">
						<span v-show="!row.isEditing">{{ row.checkStatusDesc || '--' }}</span>
						<el-form-item
							v-show="row.isEditing"
							:prop="`tableData[${$index}].checkStatus`"
							:rules="[
								{
									required: true,
									message: '请选择抄表情况',
									trigger: 'change',
								},
							]"
						>
							<el-select
								v-model="tableForm.tableData[$index].checkStatus"
								placeholder="请选择抄表情况"
								filterable
								@change="handleCheckStatusChange($index)"
							>
								<el-option
									v-for="item in checkStatusOptionsData"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
					</template>
					<!-- 照片 -->
					<template v-slot:imageUrl="{ row, $index }">
						<UploadImgSimple v-model="tableForm.tableData[$index].imageUrl" :can-upload="row.isEditing" />
					</template>
					<!-- 抄表员工 -->
					<template v-slot:meterReadingStaffId="{ row, $index }">
						<span v-show="!row.isEditing">{{ row.meterReadingStaffName || '--' }}</span>
						<el-select
							v-show="row.isEditing"
							v-model="tableForm.tableData[$index].meterReadingStaffId"
							filterable
							placeholder="请选择抄表员工"
						>
							<el-option
								v-for="item in staffOptionsData"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</template>
					<!-- 抄表时间 -->
					<template v-slot:thisRecordDate="{ row, $index }">
						<span v-show="!row.isEditing">{{ row.thisRecordDate || '--' }}</span>
						<el-form-item
							v-show="row.isEditing"
							:prop="`tableData[${$index}].thisRecordDate`"
							:rules="{
								required: true,
								message: '请选择抄表时间',
								trigger: 'change',
							}"
						>
							<el-date-picker
								v-model="tableForm.tableData[$index].thisRecordDate"
								type="datetime"
								:picker-options="recordDatePickerOptions(row)"
								value-format="yyyy-MM-dd HH:mm:ss"
								placeholder="请输入抄表时间"
							/>
						</el-form-item>
					</template>
					<template v-slot:deal="{ row, $index }">
						<el-button
							v-has="'plan-collection_meterReadingTask_updateMeterReadingRecord'"
							v-show="!row.isEditing"
							type="text"
							size="medium"
							@click="handleAdjust(row, $index)"
						>
							修改
						</el-button>
						<div v-has="'plan-collection_meterReadingTask_updateMeterReadingRecord'" v-show="row.isEditing">
							<el-button type="text" size="medium" @click="handleAdjustSave(row, $index)">保存</el-button>
							<el-button type="text" size="medium" @click="handleAdjustCancel($index)">取消</el-button>
						</div>
					</template>
				</GcTable>
			</el-form>
		</div>
		<!-- 导入弹窗 -->
		<ImportDialog ref="importDialogRef" :show.sync="showImport" :params="importParams" @success="refresh" />
	</div>
</template>

<script>
import UploadImgSimple from '@/components/UploadImgSimple'
import { CPM } from '@/consts/moduleNames'
import { isBlank } from '@/utils/validate.js'
import { getColumn } from './tableColumn.js'
import {
	readingStatusOptions,
	checkStatusOptions,
	specialCheckStatus,
	CHECK_STATUS_CALC_METHODS,
} from '@/consts/optionList.js'
import ImportDialog from './components/ImportDialog.vue'
import {
	getRecordList2,
	getTaskDetail,
	queryStaffByType,
	updateMeterReadingRecord,
	autoReviewByRecord,
	autoReviewByTask3,
} from '@/api/meterReading.api.js'

export default {
	name: '',
	components: { UploadImgSimple, ImportDialog },
	data() {
		return {
			taskDetailData: {
				taskYear: '',
				taskMonth: '',
				// 表卡总数
				meterNum: '--',
				// 移交表卡数
				overNum: '--',
				// 送内复表卡数
				reviewsNum: '--',
			},
			formData: {
				archivesIdentity: this.$route.query.archivesIdentity || '',
				handOver: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
				{
					type: 'el-select',
					label: '移交状态',
					prop: 'handOver',
					options: [
						{
							label: '已移交',
							value: 1,
						},
						{
							label: '未移交',
							value: 0,
						},
					],
					attrs: {
						clearable: true,
						placeholder: '请选择移交状态',
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '80px',
			},
			loading: false,
			columns: getColumn(this),
			tableData: [],
			tableForm: {
				tableData: [],
			},
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			selectedData: [],

			// 编辑的当前行数据（用于取消时恢复）
			currentEditData: {},

			// 下拉数据
			readingStatusOptionsData: readingStatusOptions,
			checkStatusOptionsData: checkStatusOptions,
			staffOptionsData: [],
			// 导入弹窗
			showImport: false,
		}
	},
	computed: {
		uploadUrl() {
			return `${process.env.VUE_APP_API_BASE_URL}${CPM}/file/manage/upload`
		},
		importParams() {
			const {
				taskYear = '',
				bookId = '',
				bookNo = '',
				meterReadingTaskId = '',
				taskStatus = '',
				archivesIdentity = '',
			} = this.$route.query
			const { current, size } = this.pageData
			return {
				current,
				size,
				bookId,
				bookNo,
				meterReadingTaskId,
				taskYear,
				taskMonth: this.taskDetailData.taskMonth,
				queryFlag: true,
				taskStatus,
				archivesIdentity,
				...this.formData,
			}
		},
		isBatchImportButtonDisabled() {
			return !(this.tableForm.tableData && this.tableForm.tableData.length)
		},
	},
	methods: {
		// 抄表时间日期选择禁用配置
		recordDatePickerOptions(row) {
			const { lastRecordDate = '' } = row

			return {
				disabledDate: time => {
					const timestamp = time.getTime()

					// 禁用 lastRecordDate之前的日期
					if (timestamp <= this.dayjs(lastRecordDate).startOf('day').valueOf()) {
						return true
					}

					// 禁用未来的所有日期
					if (timestamp > this.dayjs().endOf('day').valueOf()) {
						return true
					}

					return false
				},
				// 不需要限制时间范围，因为时分秒是可以选择的
				selectableRange: '00:00:00 - 23:59:59',
			}
		},

		handleReset() {
			this.$refs.formRef.resetFields()
			this.getList(1)
		},
		handleSelectable(row) {
			// 只有状态为已抄表的才可以选择 进行送内复
			return row.readingStatus === 1
		},
		handleSelectChange(data) {
			this.selectedData = data
		},
		// 获取抄表任务详情
		async getReadingTaskDetail() {
			const meterReadingTaskId = this.$route.query.meterReadingTaskId
			if (!meterReadingTaskId) return

			try {
				const res = await getTaskDetail({
					meterReadingTaskId,
				})
				this.taskDetailData = res
			} catch (error) {
				console.error(error)
				this.taskDetailData = {
					taskYear: '',
					taskMonth: '',
					meterNum: '--',
					overNum: '--',
					reviewsNum: '--',
				}
			}
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		// 获取抄表记录列表
		async getList(curPage) {
			this.selectedData = []
			const { taskYear = '', bookNo = '', meterReadingTaskId = '' } = this.$route.query
			if (!taskYear || !bookNo) return

			this.loading = true

			if (curPage) {
				this.pageData.current = curPage
			}

			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getRecordList2({
					current,
					size,
					queryFlag: true,
					taskYear,
					bookNo,
					meterReadingTaskId,
					...this.formData,
				})
				this.tableForm.tableData = records.map(item => {
					return {
						...item,
						curMeterReading: !isBlank(item.curMeterReading) ? item.curMeterReading : undefined,
						useAmount: !isBlank(item.useAmount) ? item.useAmount : undefined,
					}
				})
				this.pageData.total = total
			} catch (error) {
				console.error(error)
				this.tableForm.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 获取抄表员工数据
		async getStaffMapData() {
			try {
				const orgCode = this.currentEditData ? this.currentEditData.orgCode : ''
				const res = await queryStaffByType({
					staffType: 0,
					status: 0,
					orgCode,
				})
				this.staffOptionsData = res.map(item => {
					const { staffId, staffName } = item
					return {
						value: staffId,
						label: staffName,
					}
				})
			} catch (error) {
				console.error(error)
				this.staffOptionsData = []
			}
		},
		// 本次指针、本次水量验证函数
		validFn(key, index) {
			return (rule, value, callback) => {
				if (!specialCheckStatus.includes(this.tableForm.tableData[index].checkStatus) && isBlank(value)) {
					callback(new Error(`请输入本次${key === 'curMeterReading' ? '指针' : '水量'}`))
				} else {
					callback()
				}
			}
		},
		// 抄表情况选择改变事件
		handleCheckStatusChange(index) {
			const value = this.tableForm.tableData[index].checkStatus
			this.handleCalcUseAmt(this.tableForm.tableData[index])
			if (value && specialCheckStatus.includes(value)) {
				// 本次指针、本次水量可为空
				this.$refs.tableFormRef.clearValidate([
					`tableData[${index}].curMeterReading`,
					`tableData[${index}].useAmount`,
				])
			} else {
				this.$refs.tableFormRef.validateField([
					`tableData[${index}].curMeterReading`,
					`tableData[${index}].useAmount`,
				])
			}
		},
		// 修改
		async handleAdjust(row, index) {
			this.currentEditData = row
			this.$set(this.tableForm.tableData[index], 'isEditing', true)
			this.$refs.gcTableRef.scrollLeft(500)

			if (isBlank(this.tableForm.tableData[index].thisRecordDate)) {
				// 抄表日期默认当前
				this.tableForm.tableData[index].thisRecordDate = this.dayjs().format('YYYY-MM-DD HH:mm:ss')
			}
			if (isBlank(this.tableForm.tableData[index].checkStatus)) {
				// 抄表情况默认正常
				this.tableForm.tableData[index].checkStatus = 0
			}
			//
			await this.getStaffMapData()
			const isStaffExist = this.staffOptionsData.find(s => s.value == row.meterReadingStaffId)
			if (!isStaffExist) {
				this.tableForm.tableData[index].meterReadingStaffId = ''
			}
		},
		// 本次指针改变
		handleCurMeterReadingChange(index) {
			this.handleCalcUseAmt(this.tableForm.tableData[index])
			// 重新验证本次水量字段
			this.$refs.tableFormRef.validateField(`tableData[${index}].useAmount`)
		},
		// 计算用水量
		handleCalcUseAmt(data) {
			const { curMeterReading, lastMeterReading, meterRange, checkStatus } = data
			const methods = CHECK_STATUS_CALC_METHODS[checkStatus] || CHECK_STATUS_CALC_METHODS.default
			const { curMeterReading: newCurMeterReading, useAmount } = methods({
				curMeterReading,
				lastMeterReading,
				range: meterRange,
			})
			data.useAmount = useAmount
			data.curMeterReading = newCurMeterReading
		},
		// 修改保存
		async handleAdjustSave(row, index) {
			const promise1 = new Promise(resolve => {
				this.$refs.tableFormRef.validateField(`tableData[${index}].curMeterReading`, errorMessage => {
					resolve(!errorMessage)
				})
			})
			const promise2 = new Promise(resolve => {
				this.$refs.tableFormRef.validateField(`tableData[${index}].useAmount`, errorMessage => {
					resolve(!errorMessage)
				})
			})
			const promise3 = new Promise(resolve => {
				this.$refs.tableFormRef.validateField(`tableData[${index}].checkStatus`, errorMessage => {
					resolve(!errorMessage)
				})
			})
			const promise4 = new Promise(resolve => {
				this.$refs.tableFormRef.validateField(`tableData[${index}].thisRecordDate`, errorMessage => {
					resolve(!errorMessage)
				})
			})

			const valids = await Promise.all([promise1, promise2, promise3, promise4])
			if (!valids.includes(false)) {
				const {
					meterReadingRecordId,
					checkStatus,
					curMeterReading,
					lastMeterReading,
					useAmount,
					imageUrl,
					meterReadingStaffId,
					taskYear,
					thisRecordDate,
					lastRecordDate,
				} = row

				await updateMeterReadingRecord({
					meterReadingRecordId,
					checkStatus,
					curMeterReading,
					lastMeterReading,
					useAmount,
					imageUrl,
					meterReadingStaffId,
					taskYear,
					thisRecordDate,
					lastRecordDate,
				})
				this.$message.success('抄表录入成功')
				this.getList(1)
			}
		},
		// 修改取消
		handleAdjustCancel(index) {
			this.tableForm.tableData.splice(index, 1, {
				...this.currentEditData,
				isEditing: false,
			})
			this.currentEditData = {}
			this.$refs.tableFormRef.clearValidate([
				`tableData[${index}].curMeterReading`,
				`tableData[${index}].useAmount`,
				`tableData[${index}].checkStatus`,
				`tableData[${index}].thisRecordDate`,
			])
		},
		// 送内复
		handleSendReview() {
			this.$confirm('确定送内复吗?').then(async () => {
				await autoReviewByRecord({
					meterReadingRecordIdList: this.selectedData.map(item => item.meterReadingRecordId),
					taskYear: this.$route.query.taskYear,
				})
				this.$message.success('送内复成功')
				this.refresh()
			})
		},
		// 整册送内复
		handleTotalSendReview() {
			this.$confirm('确定整册送内复吗?').then(async () => {
				await autoReviewByTask3({
					meterReadingTaskId: this.$route.query.meterReadingTaskId,
				})
				this.$message.success('整册送内复成功')
				this.refresh()
			})
		},
		judgeBlank(val) {
			return isBlank(val)
		},
		// 批量导入
		handleBatchImport() {
			this.showImport = true
		},

		refresh() {
			this.getReadingTaskDetail()
			this.getList(1)
		},
	},
	beforeRouteEnter(to, from, next) {
		next(vm => {
			vm.$nextTick(() => {
				vm.getReadingTaskDetail()
				vm.getList()
			})
		})
	},
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
}
.container-title {
	display: flex;
	align-items: center;
}
.container-search {
	padding: 0 20px;
	display: flex;
	justify-content: space-between;
	.btn-group {
		align-self: flex-start;
	}
}
.table-container {
	flex: 1;
	height: 0;
	padding: 0 20px;
}
::v-deep {
	.model-header {
		width: 100%;
	}
	.el-date-editor.el-input {
		width: 100%;
	}
	.el-input-number {
		width: 100%;
		.el-input__inner {
			text-align: left;
		}
	}
}

.data-box {
	display: flex;
	align-items: center;
}
.data-item {
	display: flex;
	align-items: center;
	.label {
		color: #4e4e4e;
		font-size: 14px;
	}
	.value {
		font-weight: bold;
	}
}
.ml12 {
	margin-left: 12px;
}
.image-box {
	display: flex;
	align-items: center;
	.img {
		display: flex;
		.empty {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 60px;
			height: 60px;
			font-size: 14px;
			color: #c0c4cc;
			background-color: #f5f7fa;
		}
	}
}
.btns-box {
	display: flex;
	flex-direction: column;
	margin-left: 8px;
	justify-content: space-between;
}
.table-form {
	height: 100%;
	::v-deep {
		.el-form-item {
			margin: 12px 0;
		}
		.el-form-item__error {
			padding-top: 0;
		}
	}
}
.reading-error {
	color: #ec6b60;
}
</style>
