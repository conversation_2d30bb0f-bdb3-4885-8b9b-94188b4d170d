.search-dialog {
  ::v-deep {
    div {
      box-sizing: border-box;
    }
    .el-dialog {
      border-radius: 4px;
      overflow: hidden;
    }

    .el-dialog__header {
      height: 48px !important;
      padding: 14px 10px 14px 24px;
      background: linear-gradient(90deg, #1571e9 0%, #3b90ff 100%);
      .el-dialog__headerbtn .el-dialog__close {
        color: #fff;
      }
      .el-dialog__title {
        font-size: 14px;
        color: #fff;
      }
    }

    .el-dialog__body {
      padding: 20px 20px 40px 20px;
      .content {
        .tab-switch {
          display: flex;
          padding-bottom: 24px;
          .tab-item {
            width: 76px;
            height: 28px;
            margin-right: 10px;
            line-height: 26px;
            text-align: center;
            border: 1px solid #66a6fe;
            border-radius: 4px;
            color: #66a6fe;
            background: #fff;
            cursor: pointer;
          }
          .tab-item:last-child{
            margin-right: 0;
          }
          .tab-item.active {
            background: $base-color-blue;
            border: 1px solid $base-color-blue;
            color: #fff;
          }
        }
        .content-operation {
          padding-bottom: 10px;
          .el-input__prefix{
            left: -5px;
          }
          .el-input__inner {
            border-radius: 0;
            border: none;
            border-bottom: 1px solid $base-color-blue;
            outline: medium;
            font-size: 14px;
            height: 42px;
            line-height: 42px;
            
          }
          .el-icon-search {
            font-size: 16px;
            color: $base-color-blue;
          }
          .el-input__suffix {
            line-height: 40px;
            cursor: pointer;
            i {
              font-style: normal;
              font-size: 14px;
              color: $base-color-blue;
            }
          }
          .address-special {
            .el-input__inner {
              padding-left: 0;
            }
            .select {
              display: flex;
              justify-content: space-between;
              padding-bottom: 20px;
              .el-select {
                width: 48% !important;
                .el-input__inner {
                  width: 100%;
                }
              }
            }
          }
        }
        .filter-condition {
          font-size: 12px;
          .all {
            color: #999999;
            .el-button{
              margin-left: 10px;
              color: $base-color-blue;
              border: none;
            }
            .el-button[disabled]{
              color: #cccccc;
              border: none;
              background: #fff;
            }
          }
          .collection-one {
            span {
              display: inline-block;
              padding: 4px 12px;
              border: 1px solid #d9d9d9;
              border-radius: 10px;
              margin: 4px 4px 0 0;
              i {
                font-style: normal;
                padding: 0 4px;
                cursor: pointer;
              }
            }
            .collection-empty{
              font-size: 12px;
              color: #cccccc;
              padding-top: 3px;
            }
          }
        }
      }
    }
  }

}

