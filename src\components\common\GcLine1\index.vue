<template>
	<div class="gc-line1" ref="chartRef"></div>
</template>

<script>
import options from './options.js'
export default {
	name: 'Gcline1',
	props: {
		seriesData: Array,
		legend: Object,
		dataZoom: Object,
		xAxis: Object,
		yAxis: Object,
		tooltip: Object,
	},
	components: {},
	data() {
		return {
			myChart: null,
		}
	},
	computed: {},
	watch: {
		seriesData: {
			handler: function (arr) {
				if (arr && arr.length) {
					this.init()
				}
			},
			deep: true,
		},
	},
	methods: {
		init() {
			this.myChart && this.myChart.clear()
			this.myChart && this.myChart.dispose()
			this.myChart = null
			this.myChart = window.echarts.init(this.$refs.chartRef)
			this.setOptions()
			this.handleResize()
			window.addEventListener('resize', this.handleResize)
		},
		handleResize() {
			if (this.myChart) {
				this.myChart.resize()
			}
		},
		setOptions() {
			const props = {
				seriesData: this.seriesData,
				legend: this.legend,
				xAxis: this.xAxis,
				yAxis: this.yAxis,
				dataZoom: this.dataZoom,
				tooltip: this.tooltip,
			}
			this.myChart && this.myChart.setOption(options(props))
		},
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.handleResize)
	},
}
</script>

<style lang="scss" scoped>
.gc-line1 {
	height: 100%;
}
</style>
