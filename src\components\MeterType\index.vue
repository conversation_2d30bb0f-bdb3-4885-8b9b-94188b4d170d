<template>
	<div class="meter-type-wrapper">
		<el-select
			class="meter-type"
			@change="handleDeviceTypeChange"
			:popper-class="'meterTypePopper'"
			v-model="deviceTypeId"
			placeholder="请选择"
			clearable
			filterable
		>
			<el-option
				v-for="item in deviceTypeList"
				:key="item.deviceTypeId"
				:label="item.deviceTypeName"
				:value="item.deviceTypeId"
			></el-option>
		</el-select>
	</div>
</template>

<script>
import { apiGetMeterTypeList } from '@/api/meterMonitor.api'
// import Service from "@/api/baseAPI.js";
export default {
	name: 'MeterType',
	components: {},
	props: {
		queryType: {
			type: Number,
			default: 1, //  1:表类型，2：DTU类型
		},
	},
	data() {
		return {
			deviceTypeList: [],
			deviceTypeId: '',
		}
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {
		this.getDeviceTypeList()
	},
	methods: {
		// 表类型变化
		handleDeviceTypeChange(val) {
			const meter = this.deviceTypeList.filter(item => item.deviceTypeId == val)
			this.$emit('on-meter-type-change', meter)
		},
		// 获取表类型列表
		getDeviceTypeList() {
			apiGetMeterTypeList({ type: this.queryType }).then(res => {
				this.deviceTypeList = res.records
			})
		},
	},
}
</script>
<style lang="scss" scoped>
.meter-type-wrapper {
	display: flex;
	width: 100%;
	align-items: center;
	height: 32px;
	.meter-type {
		flex: 1;
		::v-deep {
			.el-input__inner {
				border: none;
				width: 100%;
			}
			.el-input {
				width: 100%;
			}
			.el-input--suffix {
				width: 100%;
				height: 32px;
				line-height: 30px;
				border: 1px solid #d9d9d9;
				box-sizing: border-box;
				border-radius: 4px;
				transition: all ease-in 0.1s;
				.el-input__inner {
					height: 30px;
					line-height: 32px;
				}
				&.is-focus {
					border: 1px solid #404cbf;
				}
			}
			.el-input.el-input--suffix.is-focus {
			}
			.el-input__icon {
				line-height: 32px;
			}
		}
	}
}
</style>
