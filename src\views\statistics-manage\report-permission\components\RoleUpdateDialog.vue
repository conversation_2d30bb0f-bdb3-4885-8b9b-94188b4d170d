<template>
	<gc-el-dialog :show="isShow" :title="`${typeText}角色`" custom-top="120px" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { ruleMaxLength } from '@/utils/rules'
import { addReportRole, editReportRole } from '@/api/statisticsManage.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		editType: {
			type: String,
			default: 'add',
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},

		typeText() {
			return this.editType === 'add' ? '新增' : '编辑'
		},
	},
	data() {
		return {
			formData: {
				roleName: '',
				remark: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '角色名称',
					prop: 'roleName',
					attrs: {
						col: 24,
						placeholder: '请输入角色名称',
					},
				},
				{
					type: 'el-input',
					label: '描述',
					prop: 'remark',
					attrs: {
						col: 24,
						type: 'textarea',
						placeholder: '请选择角色',
						clearable: true,
						maxlength: '300',
						showWordLimit: true,
						autosize: {
							minRows: 4,
							maxRows: 8,
						},
					},
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					roleName: [
						{ required: true, message: '请输入角色名称', trigger: 'blur' },
						ruleMaxLength(30, '角色名称'),
					],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				if (this.editType === 'add') {
					await addReportRole(this.formData)
				} else {
					await editReportRole(this.formData)
				}
				this.$message.success(`${this.typeText}角色成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
.el-cascader {
	width: 100%;
}
</style>
