# 表格的基础使用
    <gc-table
        ref="tenantTable"
        :columns="columns"
        :need-type="tableType--------展示表格type列, selection-多选框；index-从1开始的序号；expand-显示为一个可展开的按钮
        :table-data="tableData"------表格展示数据
        :total="100" ----------------使用分页器的时候需要传入
        :filterDropList="true" ----------------表头筛选时是否同步对筛选项做筛选，默认不做
        :totalHide="false"-----------表格无数据是展示全屏的空状态还是保留表头展示空页面
        showPage---------------------显示分页器
        pageBottom-------------------分页器在页面最底部------2021/06/01该属性失效，不再暴露
        @selectChange="selectChange"-表格选项变化
        @rowCLick="rowCLick"---------表格行点击事件
        @current-page-change="currentPageChange"---------分页器页码变化
        @click-header-filter="clickHeaderFilter"---------表格表头筛选事件回传
      ></gc-table>
# 表格columns配置基础使用：
# render：（h,row）=> {}  // row 为当前行的全部信息
# tooltip: true, // 文字溢出显示省略号，并且添加tooltip提示
# noResize: true, // 表格列是否能缩放，false或者不传默认缩放，true为禁止缩放
# isRH: true, //  表格当前列自定义筛选表头展示，并具有筛选功能
columns: [
        {
          key: "name",
          name: "租户名称",
        },
        {
          key: "state",
          name: "租户状态",
        },
        {
          key: "subdomain",
          name: "域名",
        },
        {
          key: "create_time",
          name: "创建时间",
          render: (h, row) => {
            return h(
              "span",
              {},
              this.dayjs.utc(row.create_time).format("YYYY/MM/DD HH:mm:ss")
            );
          },
        },
        {
          key: "remark",
          name: "备注",
          noResize: true, // 表格列是否能缩放，false或者不传默认缩放，true为禁止缩放
          tooltip: true, // 文字溢出显示省略号，并且添加tooltip提示
        },
        {
          key: "operate",
          name: "操作",
          render: (h, row) => {
            //  自定义表格内容展示
            return h("div", {}, [
              h(
                "el-button",
                {
                  props: {
                    type: "text",
                    size: "small",
                  },
                  on: {
                    click: this.clickTableBtn("edit", row.name), // 注册自定义事件
                  },
                },
                "编辑"
              ),
              h(
                "el-button",
                {
                  props: {
                    type: "text",
                    size: "small",
                  },
                  on: {
                    click: this.clickTableBtn("delete", row.create_time),
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
##  columns内的按钮注册点击事件需要注意与行点击事件的冒泡关联，使用方法如下
##  return () => { 处理逻辑 }
    clickTableBtn(...params) {
      return () => {
        console.log("params", params);
        逻辑处理代码
      };
    },
### 表格按钮点击事件或者复选点击会触发行点击事件，可以做如下处理
### params是一个对象，{
###    row:当前行的全部信息；
###    key:点击位置所在列的key，例如上面columns的操作列，就是‘operate’
###    event：点击事件对象，没啥用0.0
###    }
    rowCLick(params) {
      if (this.tableType === "selection") return;
      if (params.key === "operate") return;
      console.log("rowClick", params);
      this.$router.push({
        path: "/tenant/tenantConfig-detail",
        query: { tenantID: params.row.name },
      });
    },
#### 目前能用到的差不多就这些属性了，有什么疑问，随时沟通~