<template>
	<div
		class="upload-file"
		v-loading.fullscreen="innerLoading"
		:element-loading-text="innerLoadingText"
		element-loading-spinner="el-icon-loading"
	>
		<div class="upload">
			<img class="unchecked" v-show="innerUploadStep == 1" :src="defaultImg" alt="" />
			<img class="checked" v-show="innerUploadStep == 2" src="@/assets/images/pic/excel-pic.png" alt="" />
			<img class="success" v-show="innerUploadStep == 3" src="@/assets/images/pic/upload-success.png" alt="" />
			<img class="fail" v-show="innerUploadStep == 4" src="@/assets/images/pic/upload-fail.png" alt="" />
			<div class="checked-text" v-if="innerUploadStep == 2">已选择的文件：{{ fileName }}</div>
			<div class="upload-done" v-if="innerUploadStep == 3 || innerUploadStep == 4">
				<div class="done-text">{{ msg }}</div>
				<slot name="success-ops"></slot>
				<slot name="fail-ops">
					<div class="fail-download" v-if="innerUploadStep == 4">
						<img src="@/assets/images/pic/fail-xls.png" alt="" />
						<span @click="uploadDownFail">下载错误记录</span>
					</div>
				</slot>
			</div>
			<div class="upload-ops" :class="[innerUploadStep == 2 ? 'checked-margin' : '']">
				<el-upload
					action
					:auto-upload="false"
					:on-change="fileChange"
					:show-file-list="false"
					v-if="innerUploadStep == 1 || innerUploadStep == 2"
					ref="upload"
				></el-upload>
				<el-button
					size="small"
					class="select"
					@click="beforeUploadFile"
					v-if="innerUploadStep == 1 || innerUploadStep == 2"
					v-click-blur
				>
					{{ selectText }}
				</el-button>
				<el-button
					size="small"
					type="primary"
					class="upload-confirm"
					:disabled="uploadDisabled"
					v-if="innerUploadStep == 2"
					@click="uploadConfirm"
					v-click-blur
				>
					上传
				</el-button>
				<el-button
					size="small"
					type="primary"
					class="upload-confirm"
					v-if="innerUploadStep == 3 || innerUploadStep == 4"
					@click="uploadDone"
					v-click-blur
				>
					{{ innerUploadStep == 4 ? '关闭' : '完成' }}
				</el-button>
			</div>
			<div v-if="dataValidateResult" class="require-check-result">
				<el-tag v-for="(item, key) in dataValidateResult" :key="key" type="warning">
					{{ createValidateTip(item, key) }}
				</el-tag>
			</div>
		</div>
	</div>
</template>

<script>
import { excelVerList, excelVerListWater } from '@/consts/templateVersion.js'
import XLSX from 'xlsx'
import identity from '@/mixin/identity.js'

export default {
	name: 'GcUploadFile',
	mixins: [identity],
	props: {
		uploadStep: {
			type: Number,
			default: () => 1,
		},
		msg: {
			type: String,
			default: '',
		},
		loadingText: {
			type: String,
			default: () => '拼命加载中',
		},
		loading: {
			type: Boolean,
			default: false,
		},
		defaultImg: {
			type: String,
			default: require('@/assets/images/pic/batch-recharge.png'),
		},
		selectTextInital: {
			type: String,
			default: '上传文件',
		},
	},
	data() {
		return {
			fileName: '', //文件名
			uploadBtnShow: false, //上传按钮的显示
			excelList: [], //解析后的数组
			excelVersion: null, //版本号
			templateType: null, //模板类型
			versionList: [], //版本号列表
			uploadDisabled: false,
			dataValidateResult: null,
		}
	},
	computed: {
		innerUploadStep: {
			//上传进度，1-未选择文件，初始状态，2-选择文件，3-上传成功，4-上传失败
			get: function () {
				return this.uploadStep
			},
			set: function (val) {
				this.$emit('update:upload-step', val)
			},
		},
		innerLoadingText: {
			get: function () {
				return this.loadingText
			},
			set: function (val) {
				this.$emit('update:loading-text', val)
			},
		},
		innerLoading: {
			get: function () {
				return this.loading
			},
			set: function (val) {
				this.$emit('update:loading', val)
			},
		},
		selectText: {
			get: function () {
				return this.selectTextInital
			},
			set: function (val) {
				this.$emit('update:select-text-inital', val)
			},
		},
	},
	methods: {
		// 上传前校验
		//批量建档需要进行校验,缴费可直接使用回调给予一个true即可
		beforeUploadFile() {
			this.$emit('before-upload-file', '', result => {
				if (result) {
					this.$refs['upload'].$refs['upload-inner'].handleClick()
				}
			})
		},
		createValidateTip(item, key) {
			const typeTextMap = {
				r: '未填写',
				s: '超出长度要求',
				v: '超出数值范围',
				e: '格式错误',
			}
			const textConstruct = function (value, type) {
				const isMoreThen2 = value.length > 2
				return `${value.splice(0, 2).join()}${isMoreThen2 ? '等' : ''}${typeTextMap[type] || ''}`
			}
			let tipText = ''
			if (key !== 'd') {
				tipText = `第 ${key} 行: `
				for (const type in item) {
					const arr = item[type]
					if (!arr.length) {
						continue
					}
					tipText += textConstruct(arr, type) + ';'
				}
			} else {
				tipText = []
				for (const key in item) {
					const arr = item[key][0]
					if (!arr.length) {
						continue
					}
					console.log(arr)
					tipText.push(`${key}${textConstruct(arr, 'd')}`)
				}
				tipText = tipText.join('/') + '输入重复'
			}
			return tipText
		},
		/**
		 * @function 数据校验方法
		 * 在同一 key 包含多个校验规则时，若必填校验未通过，将跳过后续类型校验
		 * @param rules {object} 校验规则集，由上传模板获取
		 * @param rules.r {array<string>} 必填校验 key 数组
		 * @param rules.s {object} 字符数校验 key 数组
		 * @param rules.v {object} 数值范围校验 key 数组
		 * @param rules.e {object} 正则表达式校验 key 数组
		 * @param rules.d {array<string>} 重复校验 key 数组
		 * @param list {array<object>} 上传的excel数据
		 * @returns {string} 校验结果，ok为通过，fail为不通过
		 */
		dataValidate(rules = {}, list) {
			// 最大导入数量校验
			if (list.length > 500) {
				this.$message.error('一次性最多可导入 500 条记录，请拆分文档！')
				return 'fail'
			}
			if (!rules.r) {
				rules.r = []
			}
			this.dataValidateResult = {}
			const result = this.dataValidateResult
			const collectResult = (rowNum, key, type) => {
				if (result[rowNum]) {
					if (result[rowNum][type]) {
						result[rowNum][type].push(key)
					} else {
						result[rowNum][type] = [key]
					}
				} else {
					result[rowNum] = {
						[type]: [key],
					}
				}
			}
			const duplicationCheckCache = {}
			const useDuplicationCheck = rules.d && rules.d.length
			for (let index in list) {
				const row = list[index]
				row.rowNum = +index + 2
				const rCheckCache = {}
				for (const key of rules.r) {
					const v = row[key]
					if (v === undefined || v === null || (typeof v === 'string' && !v.trim())) {
						collectResult(row.rowNum, key, 'r')
						rCheckCache[key] = true
					}
				}
				if (useDuplicationCheck) {
					for (const key of rules.d) {
						if (rCheckCache[key]) {
							continue
						}
						const v = row[key]
						if (v === undefined) {
							continue
						}
						if (duplicationCheckCache[key]) {
							duplicationCheckCache[key].push(v)
						} else {
							duplicationCheckCache[key] = [v]
						}
					}
				}
				for (const key in rules.s) {
					if (rCheckCache[key]) {
						continue
					}
					const rule = rules.s[key]
					let v = row[key]
					if (typeof v !== 'string') {
						continue
					}
					if (v.length > rule.max || v.length < rule.min) {
						collectResult(row.rowNum, key, 's')
					}
				}
				for (const key in rules.v) {
					if (rCheckCache[key]) {
						continue
					}
					const rule = rules.v[key]
					let v = row[key]
					if (typeof v !== 'number' && typeof v !== 'string') {
						continue
					}
					v = +v
					if (v > rule.max || v < rule.min) {
						collectResult(row.rowNum, key, 'v')
					}
				}
				for (const key in rules.e) {
					if (rCheckCache[key]) {
						continue
					}
					const reg = rules.e[key]
					let v = row[key]
					if (typeof v !== 'string' && typeof v !== 'number') {
						continue
					}
					v = v.toString()
					if (!reg.test(v)) {
						collectResult(row.rowNum, key, 'e')
					}
				}
			}
			// 重复校验
			if (useDuplicationCheck) {
				for (const key of rules.d) {
					const keyValue = duplicationCheckCache[key]
					if (!keyValue) {
						continue
					}
					const seen = new Set()
					const duplicates = new Set()
					for (const item of keyValue) {
						if (seen.has(item)) {
							duplicates.add(item)
						} else {
							seen.add(item)
						}
					}
					if (duplicates.size > 0) {
						collectResult('d', Array.from(duplicates), key)
					}
				}
			}
			if (!Object.keys(this.dataValidateResult).length) {
				this.dataValidateResult = null
				return 'ok'
			}
			this.$message.error('数据校验未通过，请按照下方提示逐一检查！')
			return 'fail'
		},
		parseValidateSetting(workbook) {
			const rs = XLSX.utils.sheet_to_json(workbook.Sheets['校验设置'])
			if (!rs || !(rs instanceof Array) || !rs.length) {
				return {}
			}
			const rules = {}
			let r = rs[0]['必填']
			if (r) {
				rules.r = r.split(',').filter(item => item.trim())
			}
			let s = rs[0]['字符长度']
			if (s) {
				s = s.split(';').filter(item => item.trim())
				rules.s = s.reduce((rule, item) => {
					const [key, val] = item.split(':')
					const [min, max] = val.split(',')
					rule[key] = {
						min: +min,
						max: +max,
					}
					return rule
				}, {})
			}
			let v = rs[0]['数值范围']
			if (v) {
				v = v.split(';').filter(item => item.trim())
				rules.v = v.reduce((rule, item) => {
					const [key, val] = item.split(':')
					const [min, max] = val.split(',')
					rule[key] = {
						min: +min,
						max: +max,
					}
					return rule
				}, {})
			}
			let e = rs[0]['正则表达式']
			if (e) {
				e = e.split(';').filter(item => item.trim())
				rules.e = e.reduce((rule, item) => {
					const [key, val] = item.split(':')
					rule[key] = new RegExp(val)
					return rule
				}, {})
			}
			let d = rs[0]['唯一值']
			if (d) {
				rules.d = d.split(',').filter(item => item.trim())
			}
			return rules
		},
		// 选择文件
		fileChange(file) {
			if (!/\.(xlsm|xlsx)$/.test(file.name.toLowerCase())) {
				this.$message.error('上传文件格式不正确，请上传xlsm或xlsx格式文档')
				return false
			}
			this.innerLoadingText = 'excel读取中'
			this.innerLoading = true
			this.fileName = file.name
			this.selectText = '重选文件'
			this.uploadBtnShow = true
			this.innerUploadStep = 2
			const fileReader = new FileReader()
			fileReader.onload = ev => {
				try {
					const data = ev.target.result
					const workbook = XLSX.read(data, {
						type: 'binary',
					})
					const sheetNames = workbook.SheetNames
					const wsname = sheetNames[0] //取第一张表
					const ws = XLSX.utils.sheet_to_json(workbook.Sheets[wsname]) //生成json表格
					const rules = this.parseValidateSetting(workbook)
					if (this.dataValidate(rules, ws) !== 'ok') {
						this.uploadDisabled = true
						this.innerLoading = false
						this.innerUploadStep = 1
						this.innerLoadingText = '拼命加载中'
						this.selectText = '选择文件'
						return false
					}
					this.uploadDisabled = false
					this.excelList = ws
					if (!this.excelList || !(this.excelList instanceof Array)) {
						this.excelList = []
						this.$message.error('文件内容解析错误，请检查文件格式和内容！')
						this.innerLoading = false
						this.innerUploadStep = 1
						this.innerLoadingText = '拼命加载中'
						this.selectText = '选择文件'
						return false
					}
					if (this.excelList.length <= 0) {
						this.excelList = []
						this.$message.error('文件内容为空！')
						this.innerLoading = false
						this.innerUploadStep = 1
						this.innerLoadingText = '拼命加载中'
						this.selectText = '选择文件'
						return false
					}
					const v = XLSX.utils.sheet_to_json(workbook.Sheets['版本号'])
					if (v && v instanceof Array && v.length) {
						this.excelVersion = v[0]['版本号']
					} else {
						this.$message.error('未获取到文件版本号，请下载使用最新模板编辑后重新上传！')
						this.excelVersion = null
						this.innerLoading = false
						this.innerUploadStep = 1
						this.innerLoadingText = '拼命加载中'
						this.selectText = '选择文件'
						return false
					}
					const type = XLSX.utils.sheet_to_json(workbook.Sheets['模板类型'])
					/*地址+表具模板-meter
            地址+用户模板-user
            批量补气模板-supplement
            批量充值模板-recharge
            批量登记其他费用-register
            完整档案模板-complete
          */
					if (type && type instanceof Array && type.length) {
						this.templateType = type[0]['模板类型']
					} else {
						this.$message.error('未获取到文件模板类型，请下载使用最新模板编辑后重新上传！')
						this.templateType = null
						this.innerLoading = false
						this.innerUploadStep = 1
						this.innerLoadingText = '拼命加载中'
						this.selectText = '选择文件'
						return false
					}

					// 不同租户取不同的版本号列表
					if (this.realm === 'water') {
						this.versionList = excelVerListWater
					} else {
						this.versionList = excelVerList
					}
					if (this.templateType) {
						const customVersion = this.versionList[this.templateType]
						if (customVersion != this.excelVersion) {
							this.$message.error('文件版本校验未通过，请从页面上下载最新的模板！')
							this.innerLoading = false
							this.innerUploadStep = 1
							this.innerLoadingText = '拼命加载中'
							this.selectText = '选择文件'
							return false
						}
					}
					setTimeout(() => {
						this.innerLoading = false
					}, 1000)
				} catch (e) {
					this.innerLoading = false
					this.$message.error('文件读取发生错误，请重新选择文件！')
					this.innerUploadStep = 1
					this.innerLoadingText = '拼命加载中'
					this.selectText = '选择文件'
					console.error(e)
					return false
				}
			}
			fileReader.readAsBinaryString(file.raw)
		},
		// 上传
		uploadConfirmCommon() {
			let obj = {}
			obj['list'] = this.excelList //解析并处理后的数据list
			obj['version'] = this.excelVersion //版本号
			obj['type'] = this.templateType //模板类型
			this.$emit('upload-confirm', obj)
		},
		uploadConfirm() {
			// 批量补气需要判断余额是否为负数
			if (this.templateType === 'supplement') {
				let totalGasFill = 0
				this.excelList.forEach(item => {
					if (Object.keys(item).includes('补气金额')) {
						totalGasFill += item['补气金额']
					}
				})
				if (totalGasFill < 0) {
					this.$confirm('补气金额总数为负，如果扣余额会产生欠费关阀风险，请确认是否进行？', '提示')
						.then(() => {
							this.uploadConfirmCommon()
						})
						.catch(() => {
							return
						})
				} else {
					this.uploadConfirmCommon()
				}
			} else {
				this.uploadConfirmCommon()
			}
		},
		// 上传完成后 完成/关闭按钮
		uploadDone() {
			// 数据还原
			this.innerUploadStep = 1
			this.innerLoadingText = '拼命加载中'
			this.selectText = '选择文件'
			this.$emit('upload-done')
		},
		// 上传失败后下载错误记录
		uploadDownFail() {
			this.$emit('upload-down-fail')
		},
	},
}
</script>
<style lang="scss" scoped>
.upload-file {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	.upload {
		display: flex;
		flex-direction: column;
		align-items: center;
		.unchecked {
			width: 160px;
		}
		.checked {
			width: 100px;
		}
		.success,
		.fail {
			width: 200px;
		}
		button {
			border-radius: $base-font-size-big;
			width: 80px;
			&.is-disabled {
				background-color: gray;
				border-color: gray;
			}
		}
		.select {
			color: $base-color-blue;
			border-color: $base-color-blue;
		}
		.upload-ops {
			display: flex;
			padding-top: 35px;
		}
		.upload-ops.checked-margin {
			padding-top: 24px;
		}
		.upload-confirm {
			margin-left: 10px;
			background: $base-color-blue;
		}
		.checked-text {
			font-size: $base-font-size-default;
			color: $base-color-6;
			padding-top: $base-padding;
		}
		.upload-done {
			margin-top: 24px;
			text-align: center;
			font-size: $base-font-size-default;
			color: $base-color-4;
			.done-text {
				padding-bottom: $base-padding;
			}
			.jump,
			.fail-download {
				font-size: $base-font-size-small;
				cursor: pointer;
				img,
				span {
					vertical-align: middle;
				}
				img {
					width: 15px;
					margin-right: 5px;
				}
			}
			.jump {
				color: $base-color-blue;
			}
			.fail-download {
				color: $base-color-red;
			}
			.border-blue {
				padding-bottom: 2px;
				border-bottom: 1px solid $base-color-blue;
			}
		}
	}
	.require-check-result {
		display: flex;
		margin-top: 30px;
		gap: 10px;
		flex-wrap: wrap;
		justify-content: center;
		max-height: 100px;
		overflow-x: hidden;
	}
}
</style>
