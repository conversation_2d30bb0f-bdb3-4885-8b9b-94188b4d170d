.tab-content {
	width: 100%;
	height: 100%;
	padding-right: 3px;
	background: #fff;
}
.bg-overflow {
	width: 100%;
	height: 100%;
	background: #eceff8;
	overflow: auto;
}
.content-bg {
	background: #fff;
}
.data-container {
	background: #fff;
	border-radius: 4px;
	.footer {
		padding: 0 $base-padding $base-padding $base-padding;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		span + span {
			margin-left: 16px;
		}
		span {
			font-size: $base-font-size-default;
			cursor: pointer;
			display: flex;
			align-items: center;
			i {
				padding-right: 3px;
			}
		}
		.footer-left,
		.footer-right {
			display: flex;
		}
		.footer-left {
			.color-blue {
				color: $base-color-blue;
			}
			.color-red {
				color: $base-color-red;
			}
		}
		.footer-right {
			color: $base-color-yellow;
		}
	}
}

// 左右布局样式
.layout-overview {
	display: flex;
	justify-content: flex-end;
	align-items: stretch;
	position: relative;
	min-height: 100%;
	.left {
		width: 70%;
		margin-right: $base-margin;
		display: flex;
		flex-direction: column;
		// position: absolute;
		// left: 0;
		// height: 100%;
		.data-container + .data-container {
			margin-top: $base-margin;
		}
	}
	.right {
		flex: 1;
		background: #fff;
		border-radius: 4px;
	}
}

// 表格布局公共样式
.table-layout {
	border-radius: 4px;
	padding: $base-padding;
	padding-bottom: 0;
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
	.conditions {
		display: flex;
		flex-wrap: nowrap;
		.specific-conditions {
			display: flex;
			max-width: calc(100% - 160px);
		}
		.condition-item {
			flex: 1;
			padding-right: $base-padding;
			padding-bottom: 16px;
			max-width: 366px;
			display: flex;
			align-items: center;
			span {
				color: $base-color-6;
				font-size: $base-font-size-default;
				padding-right: 10px;
				display: inline-block;
				min-width: 70px;
			}
			::v-deep {
				.el-select,
				.el-date-editor {
					flex: 1;
				}
				.el-input__inner {
					flex: 1;
					width: 100%;
					// max-width: 150px;
				}
				.el-date-editor.el-input,
				.el-date-editor.el-input__inner {
					flex: 1 !important;
					width: 100% !important;
					max-width: 260px;
					min-width: 200px;
				}
				.el-range-separator {
					margin-right: 10px;
				}
			}
		}
		.ops-button {
			padding-bottom: 16px;
			width: 160px;
			i {
				font-size: $base-font-size-small;
				padding-right: 5px;
			}
			.el-button {
				height: 32px;
				color: #666;
			}
			.el-button--primary {
				color: #fff;
				background: $base-color-blue;
				border-color: $base-color-blue;
			}
		}
	}
	.table-show {
		flex: 1;
		height: 0;
	}
}

::v-deep {
	.detail-button {
		color: $base-color-blue;
		font-size: $base-font-size-default;
	}
}

// 部分弹窗公共样式
.info-show {
	::v-deep .el-dialog__body {
		padding: 15px 0 !important;
	}
}
.modify,
.close {
	border-radius: 16px;
	padding: 0;
	width: 80px;
	height: 32px;
	font-size: $base-font-size-default;
}
button + button {
	margin-left: 10px;
}
.close {
	color: $base-color-4;
}
.modify {
	border-color: $base-color-blue;
	color: $base-color-blue;
}
