<template>
	<GcElDialog
		ref="dialog"
		:show="isShow"
		title="收费明细打印"
		width="80%"
		okText=""
		:hiddenCancelBtn="true"
		class="fee-dialog"
		@close="handleClose"
		@cancel="handleClose"
	>
		<div v-loading="loading" class="report-content">
			<iframe
				v-if="currentReportUrl"
				:src="currentReportUrl"
				frameborder="0"
				width="100%"
				height="100%"
				@load="onIframeLoad"
			></iframe>
			<gc-empty v-else />
		</div>
	</GcElDialog>
</template>

<script>
import { queryReportInfo } from '@/api/statisticsManage.api'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			loading: false,
			currentReportUrl: '',
		}
	},
	methods: {
		handleClose() {
			this.isShow = false
		},
		handleError(message) {
			this.$message.error(message)
			this.isShow = false
		},
		async queryReportInfo() {
			this.loading = true
			try {
				const data = await queryReportInfo()
				const { orgCode, staffId } = this.$store.getters.userInfo || {}
				this.currentReportUrl = data.reportUrl + '&code=' + orgCode + '&ystaffId=' + staffId
			} catch (e) {
				this.loading = false
			}
		},
		onIframeLoad() {
			this.loading = false
		},
	},
	watch: {
		show: {
			handler(val) {
				if (val) {
					this.queryReportInfo()
				} else {
					this.currentReportUrl = ''
				}
			},
		},
	},
}
</script>
<style lang="scss" scoped>
.fee-dialog {
	.report-content {
		height: calc(-330px + 100vh);
	}
	&::v-deep {
		.el-dialog {
			max-height: calc(100vh - 200px);
		}
		.el-dialog__body {
			padding-top: 0;
		}
	}
}
</style>
