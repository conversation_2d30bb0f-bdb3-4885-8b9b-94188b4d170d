<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-11 15:00:52
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 16:17:13
-->
<template>
	<div class="real-meter">
		<div class="title">
			<span>实表抄表记录</span>
			<div class="btn-group">
				<el-checkbox
					v-model="checkedAllPage"
					label="选中所有页"
					border
					:disabled="tableData.length === 0"
					class="select-all"
					@change="handleCheckedAllPage"
				></el-checkbox>
				<el-button
					v-has="'plan-collection_meterReadingReview_asyncBatchReviewPassAndCreateBill4'"
					type="primary"
					:disabled="selectedData.length === 0"
					@click="handlePassAndCreateBill"
				>
					批量通过且账单开账
				</el-button>
				<el-button
					v-has="'plan-collection_meterReadingReview_asyncBatchReviewPassAndCreateBill3'"
					type="primary"
					:disabled="selectedData.length === 0"
					@click="handlePass(true)"
				>
					批量复核通过
				</el-button>
				<el-button
					v-has="'plan-collection_meterReadingReview_v2_batchReviewReject2'"
					type="primary"
					:disabled="selectedData.length === 0"
					@click="handleReject(true)"
				>
					批量复核驳回
				</el-button>
			</div>
		</div>
		<div class="container-search">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" @click="getList(1)">筛选</el-button>
					<el-button @click="handleReset">重置</el-button>
				</el-form-item>
			</GcFormSimple>
		</div>
		<div class="table-container">
			<GcTable
				ref="gcTableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				rowKey="meterReadingRecordId"
				isHighlightCurrent
				:current-row-key="currentRowKey"
				needType="selection"
				@click="handleRowClick"
				@selectChange="handleSelectChange"
				@current-page-change="handlePageChange"
			>
				<!-- 本次水量 -->
				<template v-slot:useAmount="{ row, $index }">
					<span v-show="!row.isEditing">
						{{ row.useAmount || row.useAmount === 0 ? row.useAmount : '--' }}
					</span>
					<el-form
						v-show="row.isEditing"
						:ref="`useAmountFormRef${$index}`"
						class="table-form"
						:model="tableData[$index]"
					>
						<el-form-item
							prop="useAmount"
							:rules="{
								validator: validFn($index),
								trigger: 'blur',
							}"
						>
							<el-input-number
								v-model="tableData[$index].useAmount"
								class="input-number"
								placeholder="请输入本次水量"
								:min="-999999999"
								:max="999999999"
								step-strictly
								:controls="false"
							/>
						</el-form-item>
					</el-form>
				</template>
				<!-- 抄表情况 -->
				<template v-slot:checkStatusDesc="{ row, $index }">
					<span v-show="!row.isEditing">{{ row.checkStatusDesc || '--' }}</span>
					<el-form
						v-if="row.isEditing"
						:ref="`checkStatusFormRef${$index}`"
						class="table-form"
						:model="tableData[$index]"
					>
						<el-form-item
							prop="checkStatus"
							:rules="[
								{
									required: true,
									message: '请选择抄表情况',
									trigger: 'change',
								},
								{ validator: checkStatusValidtor($index), trigger: 'change' },
							]"
						>
							<el-select
								v-model="tableData[$index].checkStatus"
								clearable
								filterable
								placeholder="请选择抄表情况"
							>
								<el-option
									v-for="item in checkStatusList"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
					</el-form>
				</template>
				<template v-slot:deal="{ row, $index }">
					<div v-show="!row.isEditing">
						<el-button
							v-has="'plan-collection_meterReadingReview_v2_batchCheckWater'"
							v-if="row.splitFlag === 0"
							type="text"
							size="medium"
							@click.stop="handleSplit(row.meterReadingRecordId)"
						>
							水量拆分
						</el-button>
						<el-button
							v-has="'plan-collection_meterReadingReview_reviewPass4'"
							type="text"
							size="medium"
							@click.stop="handlePass(false, row)"
						>
							复核通过
						</el-button>
						<el-button
							v-has="'plan-collection_meterReadingReview_reviewReject4'"
							type="text"
							size="medium"
							@click.stop="handleReject(false, row)"
						>
							驳回
						</el-button>
						<el-button
							v-has="'plan-collection_meterReadingTask_updateMeterReadingRecord5'"
							type="text"
							size="medium"
							@click.stop="handleAdjust(row, $index)"
						>
							修改
						</el-button>
					</div>
					<div v-has="'plan-collection_meterReadingTask_updateMeterReadingRecord5'" v-show="row.isEditing">
						<el-button type="text" size="medium" @click.stop="handleAdjustSave(row, $index)">
							保存
						</el-button>
						<el-button type="text" size="medium" @click.stop="handleAdjustCancel($index)">取消</el-button>
					</div>
				</template>
			</GcTable>
		</div>
		<!-- 批量通过进度显示 -->
		<BatchProcessDialog
			:show.sync="processDialogShow"
			:can-closed="canClosedDialog"
			:data="processResult"
			@refresh="getList(1)"
		/>
	</div>
</template>

<script>
import { getColumn } from './tableColumn.js'
import { isBlank } from '@/utils/validate.js'
import { checkStatusOptions, specialCheckStatus } from '@/consts/optionList.js'
import BatchProcessDialog from '../../components/BatchProcessDialog'
import {
	getReviewDetailListNewV2,
	updateMeterReadingRecord3,
	waterSpilt,
	checkWaterV2,
	batchReviewRejectV2,
	asyncBatchReviewPassAndCreateBill,
	getBatchReviewResult,
	reviewPass2,
	reviewReject2,
	checkWater,
} from '@/api/meterReading.api.js'

export default {
	name: '',
	components: { BatchProcessDialog },
	props: {
		type: Number,
		topParams: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
				splitFlag: 1,
			},
			formItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},

				{
					type: 'el-select',
					label: '本次水量是否拆分',
					prop: 'splitFlag',
					attrs: {
						clearable: true,
						placeholder: '请选择本次水量是否拆分',
					},
					options: [
						{ label: '是', value: 1 },
						{ label: '否', value: 0 },
					],
				},
			],
			formAttrs: {
				inline: true,
			},
			loading: false,
			columns: getColumn(this),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			// 抄表情况下拉选择
			checkStatusList: checkStatusOptions,
			// 当前选中行的key
			currentRowKey: '',
			// 列表选中数据
			selectedData: [],
			// 修改行‘本次水量’原始值对象 key:meterReadingRecordId; value:useAmount
			// 在保存时 对比是否有更改
			useAmountValueMap: {},

			// 编辑的当前行数据（用于取消时恢复）
			currentEditData: {},
			// 选中所有页
			checkedAllPage: false,
			progressInterval: null,
			processDialogShow: false,
			// 批量通过且账单开账/批量通过结果
			processResult: {},
			canClosedDialog: false,
		}
	},
	computed: {
		selectedDataRecordIds() {
			return this.selectedData.map(item => item.meterReadingRecordId)
		},
	},
	mounted() {
		this.getList()
	},
	methods: {
		handleReset() {
			this.$refs.formRef.resetForm()
			this.getList(1)
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		async getList(curPage) {
			this.loading = true

			this.selectedData = []
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getReviewDetailListNewV2({
					type: this.type,
					current,
					size,
					...this.formData,
					...this.topParams,
				})
				this.tableData = records.map(item => {
					return {
						...item,
						useAmount: !isBlank(item.useAmount) ? item.useAmount : undefined,
					}
				})
				// this.tableData = [
				//   {
				//     meterReadingRecordId: 10,
				//     useAmount: 10,
				//     checkStatus: 23,
				//     archivesIdentity: "测试数据",
				//   },
				// ];
				this.pageData.total = total

				if (this.tableData.length > 0) {
					this.$nextTick(() => {
						this.currentRowKey = this.tableData[0].meterReadingRecordId
						this.$emit('click', this.tableData[0])
					})
				} else {
					this.$emit('click', null)
				}
				// 勾选 选中所有页
				if (this.checkedAllPage && this.tableData.length) {
					this.$nextTick(() => {
						this.tableData.forEach(item => {
							this.$refs.gcTableRef.toggleRowSelectionUseInnerData(copyTableData => {
								return copyTableData.find(row => row.meterReadingRecordId === item.meterReadingRecordId)
							}, true)
						})
					})
				} else {
					this.checkedAllPage = false
				}
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleSelectChange(data) {
			this.selectedData = data
			this.$nextTick(() => {
				if (this.checkedAllPage && this.selectedData.length !== this.tableData.length) {
					this.checkedAllPage = false
				}
			})
		},
		handleRowClick({ row }) {
			this.$emit('click', row)
		},
		// 水量拆分
		handleSplit(meterReadingRecordId) {
			this.$confirm('确定水量拆分吗?').then(async () => {
				await waterSpilt({
					taskYear: this.topParams.taskYear,
					meterReadingRecordId,
				})
				this.$message.success('水量拆分成功')
				this.getList(1)
			})
		},
		// 复核通过
		async handlePass(isBatch = false, row) {
			if (isBatch) {
				const taskIdsAndRecordIdsMap = this.getTaskIdsAndRecordIdsMap(isBatch, row)
				const params = {
					type: this.type,
					createBill: false,
					reviewPass: true,
					...this.formData,
					...this.topParams,
					taskIdsAndRecordIdsMap,
				}
				const res = await checkWaterV2(params)
				this.$confirm(res ? '确定复核通过吗?' : '本次水量拆分总水量不平衡，是否确定要复核通过').then(
					async () => {
						try {
							const data = await asyncBatchReviewPassAndCreateBill(params)
							this.handleBatchReviewResult(data)
							this.processResult = {}
							this.processDialogShow = true
							this.canClosedDialog = false
						} catch (e) {
							this.$message.error('批量复核通过失败')
						}
					},
				)
			} else {
				const res = await checkWater({
					meterReadingRecordId: [row.meterReadingRecordId],
					taskYear: this.topParams.taskYear,
				})
				this.$confirm(res ? '确定复核通过吗?' : '本次水量拆分总水量不平衡，是否确定要复核通过').then(
					async () => {
						await reviewPass2({
							meterReadingTaskId: row.meterReadingTaskId,
							taskYear: this.topParams.taskYear,
							meterReadingRecordId: [row.meterReadingRecordId],
						})
						this.$message.success('复核通过成功')
						this.getList(1)
					},
				)
			}
		},
		// 驳回
		handleReject(isBatch = false, row) {
			this.$confirm('确定要驳回吗?').then(async () => {
				if (isBatch) {
					const taskIdsAndRecordIdsMap = this.getTaskIdsAndRecordIdsMap(isBatch, row)
					await batchReviewRejectV2({
						type: this.type,
						...this.formData,
						...this.topParams,
						taskIdsAndRecordIdsMap,
					})
				} else {
					await reviewReject2({
						taskYear: this.topParams.taskYear,
						meterReadingRecordId: [row.meterReadingRecordId],
					})
				}
				this.checkedAllPage = false
				this.$message.success('驳回成功')
				this.getList(1)
			})
		},

		// 本次水量验证函数
		validFn(index) {
			return (rule, value, callback) => {
				if (!specialCheckStatus.includes(this.tableData[index].checkStatus) && isBlank(value)) {
					callback(new Error('请输入本次水量'))
				} else {
					callback()
				}
			}
		},
		// 抄表情况验证函数
		checkStatusValidtor(index) {
			return (rule, value, callback) => {
				if (value && specialCheckStatus.includes(value)) {
					this.$refs[`useAmountFormRef${index}`].clearValidate()
					callback()
				} else {
					this.$refs[`useAmountFormRef${index}`].validate(valid => {
						if (valid) callback()
					})
				}
			}
		},
		// 修改
		handleAdjust(row, index) {
			this.currentEditData = row
			this.$set(this.tableData[index], 'isEditing', true)
			this.$refs.gcTableRef.scrollLeft(500)
			this.useAmountValueMap[row.meterReadingRecordId] = row.useAmount
		},
		// 修改保存
		async handleAdjustSave(row, index) {
			const form1Promise = new Promise(resolve => {
				this.$refs[`useAmountFormRef${index}`].validate(valid => {
					resolve(valid)
				})
			})
			const form2Promise = new Promise(resolve => {
				this.$refs[`checkStatusFormRef${index}`].validate(valid => {
					resolve(valid)
				})
			})

			const valids = await Promise.all([form1Promise, form2Promise])
			if (!valids.includes(false)) {
				const {
					meterReadingRecordId,
					checkStatus,
					splitFlag,
					curMeterReading,
					lastMeterReading,
					useAmount,
					meterReadingStaffId,
					imageUrl,
					thisRecordDate,
				} = row
				let flag = true
				// 已拆分 且 本次水量值发生修改 则进行弹窗提示
				if (splitFlag === 1 && useAmount !== this.useAmountValueMap[meterReadingRecordId]) {
					const result = await this.$confirm('本次水量变化，需重新进行水量拆分')
					// result为undefined 则点击了确定按钮
					flag = typeof result === 'undefined'
				}
				if (flag) {
					await updateMeterReadingRecord3({
						meterReadingRecordId,
						checkStatus,
						curMeterReading,
						lastMeterReading,
						useAmount,
						meterReadingStaffId,
						imageUrl,
						thisRecordDate,
						taskYear: this.topParams.taskYear,
						addModifyLog: true,
					})
					this.$message.success('修改成功')
					this.getList(1)
				}
			}
		},
		handleAdjustCancel(index) {
			this.tableData.splice(index, 1, {
				...this.currentEditData,
				isEditing: false,
			})
			this.currentEditData = {}
			this.$refs[`useAmountFormRef${index}`].clearValidate()
			this.$refs[`checkStatusFormRef${index}`].clearValidate()
		},
		// 选中所有页
		handleCheckedAllPage(value) {
			if (value) {
				this.tableData.forEach(item => {
					this.$refs.gcTableRef.toggleRowSelectionUseInnerData(copyTableData => {
						return copyTableData.find(row => row.meterReadingRecordId === item.meterReadingRecordId)
					}, true)
				})
			} else {
				this.$refs.gcTableRef.clearCheckTableSelection()
			}
		},
		// 获取选中记录数据
		getTaskIdsAndRecordIdsMap(isBatch, row) {
			let taskIdsAndRecordIdsMap = {}
			if (isBatch) {
				if (!this.checkedAllPage) {
					this.selectedData.forEach(item => {
						if (taskIdsAndRecordIdsMap[item.meterReadingTaskId]) {
							taskIdsAndRecordIdsMap[item.meterReadingTaskId].push(item.meterReadingRecordId)
						} else {
							taskIdsAndRecordIdsMap[item.meterReadingTaskId] = [item.meterReadingRecordId]
						}
					})
				}
			} else {
				taskIdsAndRecordIdsMap[row.meterReadingTaskId] = [row.meterReadingRecordId]
			}
			return taskIdsAndRecordIdsMap
		},
		// 批量通过且账单开账
		async handlePassAndCreateBill() {
			const taskIdsAndRecordIdsMap = this.getTaskIdsAndRecordIdsMap(true)
			const params = {
				type: this.type,
				createBill: true,
				reviewPass: true,
				...this.formData,
				...this.topParams,
				taskIdsAndRecordIdsMap,
			}
			const res = await checkWaterV2(params)
			this.$confirm(
				res ? '确定复核通过且账单开账吗?' : '本次水量拆分总水量不平衡，是否确定要复核通过且账单开账',
			).then(async () => {
				try {
					const data = await asyncBatchReviewPassAndCreateBill(params)
					this.handleBatchReviewResult(data, true)
					this.processResult = {}
					this.processDialogShow = true
					this.canClosedDialog = false
				} catch (e) {
					this.$message.error('批量复核通过且账单开账失败')
				}
			})
		},
		// 查询审批进度
		async handleBatchReviewResult(syncId, isOpen) {
			try {
				const data = await getBatchReviewResult({ syncId })
				this.processResult = data || {}
				if (data.processRate == 100) {
					clearInterval(this.progressInterval)
					this.canClosedDialog = true
					if (!data.failMsg || !data.failMsg.length) {
						this.checkedAllPage = false
						this.$message.success(`批量复核通过${isOpen ? '且账单开账' : ''}成功`)
						this.getList(1)
					} else {
						this.$message.error(`批量复核通过${isOpen ? '且账单开账' : ''}失败`)
					}
				} else {
					this.progressInterval = setTimeout(() => this.handleBatchReviewResult(syncId, isOpen), 3000)
				}
			} catch (error) {
				this.canClosedDialog = true
				console.error('查询审批进度失败', error)
				clearInterval(this.progressInterval)
			}
		},
	},
	beforeDestroy() {
		this.progressInterval && clearInterval(this.progressInterval)
	},
}
</script>

<style lang="scss" scoped>
.real-meter {
	height: 100%;
	display: flex;
	flex-direction: column;
	padding: 0 10px 0 20px;
	.title {
		display: flex;
		justify-content: space-between;
		padding: 20px;
		position: relative;
		&::before {
			position: absolute;
			content: '';
			display: block;
			width: 2px;
			height: 10px;
			left: 12px;
			top: 23px;
			background-color: blue;
		}
	}
}
.table-container {
	flex: 1;
	height: 0;
}
::v-deep {
	.el-form-item__label {
		padding-right: 10px;
	}
}

.input-number {
	width: 100%;
	::v-deep {
		.el-input__inner {
			text-align: left;
		}
	}
}
.table-form {
	::v-deep {
		.el-form-item {
			margin: 12px 0;
		}
		.el-form-item__error {
			padding-top: 0;
		}
	}
}
.select-all {
	margin-right: 10px;
}
</style>
