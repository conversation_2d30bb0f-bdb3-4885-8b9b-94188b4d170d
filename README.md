# 大连水务项目重构

## 项目运行

```
npm install

npm run serve

npm run build
```

## 目录结构

```
├── src
│   ├── assets               # 本地静态资源
│   ├── components           # 公共组件
│   │   ├── common           # 全局公用组件
│   │   ···                  # 局部模块公用组件
│   ├── config               # 系统配置
│   ├── consts               # 常量配置
│   ├── directive            # 指令
│   ├── filters              # 全局过滤器
│   ├── global               # 全局文件
│   ├── layout               # 框架布局
│   │   ├── components       # layout相关组件
│   │   ├── LayoutColumn     # layout分栏布局
│   │   ├── LayoutHorizontal # layout横向布局
│   │   ├── LayoutVertical   # layout纵向布局
│   │   └── index.vue        # layout入口文件
│   ├── router               # 路由配置
│   ├── store                # 状态管理配置
│   ├── styles               # 全局样式
│   ├── utils                # 工具库
│   ├── views                # 页面
│   │   ├── login            # 登录
│   │   ├── home             # 首页
│   │   ···                  # 其它
│   ├── App.vue              # 应用入口组件
│   └── main.js              # 应用入口js
```

## 约定

### 一、git 提交约定

feat/add: 新功能
fix: 修复 bug
update: 更新
style: 格式
docs: 文档
revert: 撤销之前的 commit
refact: 重构
perf: 优化相关
build: 构建或打包变动，例如更换依赖、webpack 升级等

### 二、组件约定

1、组件文件夹或文件统一使用大驼峰命名（index 除外）
2、跨模块公用组件使用频次大于 3 次，命名统一使用 Gc 开头并放在@/components/common 文件下自动导入并注册
3、跨模块公用组件使用频次小于或等于 3 次，放在@/components 目录下，使用时需要自行导入
4、其它组件可放置当前文件夹或当前文件夹下的 components 目录下
5、组件调用时统一使用短横线连接，例如 <gc-logo /> 或 <gc-logo></gc-logo>
