<template>
	<div class="page-layout">
		<div class="page-left">
			<el-tabs v-model="activeTab" type="border-card" @tab-click="handleChangeTab">
				<el-tab-pane v-for="(tab, index) in tabs" :key="index" :label="tab.label" :name="tab.name">
					<GcFormSimple
						:ref="'formRef' + index"
						v-model="formData"
						:formItems="formItems"
						:formAttrs="formAttrs"
					/>
				</el-tab-pane>
			</el-tabs>

			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div v-if="tableData.length && activeTab === 'status'" class="right-top">
				<el-button
					v-has="'cpm_address-areas_take-over'"
					type="primary"
					:disabled="selectedData.length === 0"
					@click="showCheckIn = true"
				>
					小区接管登记
				</el-button>
				<el-button
					v-has="'cpm_report_area_export_excel'"
					type="primary"
					:disabled="tableData.length === 0"
					@click="handleExport('baseInfo')"
				>
					下载小区基本信息
				</el-button>
			</div>
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:selectable="selectable"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				:needType="activeTab === 'status' ? 'selection' : ''"
				@current-page-change="handlePageChange"
				@selectChange="handleSelectChange"
				@dblclick="rowDbclick"
			>
				<template v-slot:operate="{ row }">
					<el-button v-has="'cpm_report_area_archives_export_excel'" type="text" @click="handleExport(row)">
						下载
					</el-button>
				</template>
			</GcTable>
		</div>
		<!-- 小区接管登记 -->
		<TakerOverCheckInDialog :show.sync="showCheckIn" :data="selectedData" @success="handlePageChange({ page: 1 })" />
	</div>
</template>

<script>
import { getColumn } from './tableColumn'
import { getFormItems } from './formItem.js'
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams, exportBlob } from '@/utils/index.js'
import { apiGetRegion, apiGetAddressAreaMap } from '@/api/addressManage.api.js'
import {
	apiGetRecordArchivesList,
	apiGetRecordTakeOver,
	apiExportArea,
	apiExportAreaArchives,
} from '@/api/meterManage.api.js'
import TakerOverCheckInDialog from './components/TakerOverCheckInDialog.vue'

export default {
	name: 'CommunityTakeoverManage',
	components: { TakerOverCheckInDialog },
	computed: {
		columns() {
			return getColumn(this)
		},
		tabs() {
			const arr = []
			if (this.$has('cpm-address-areas-record-archives-list')) {
				arr.push({ label: '小区接管情况', name: 'status' })
			}
			if (this.$has('cpm_address-areas_record-take-over')) {
				arr.push({ label: '小区接管记录', name: 'records' })
			}
			return arr
		},
		refName() {
			return `formRef${this.tabs.findIndex(item => item.name === this.activeTab)}`
		},
	},
	watch: {
		tabs: {
			handler() {
				this.activeTab = this.tabs[0]?.name
			},
			immediate: true,
		},
	},
	async activated() {
		const { streetCode } = this.formData || {}
		const promises = []

		await this._getCityOriRegionData(21, 'cityCode')
		if (!this.formData.cityCode) {
			this.formData.cityCode = this.formItems[0].options[0]?.value
		}
		if (this.formData.cityCode) {
			await this._getCityOriRegionData(this.formData.cityCode, 'regionCode')
		}
		if (!this.formData.regionCode) {
			this.formData.regionCode = this.formItems[1].options[0]?.value
		}
		if (this.formData.regionCode) {
			promises.push(this._getStreetData(this.formData.regionCode))
		}
		await Promise.all(promises)
		const cityIndex = this.formItems.findIndex(item => item.prop === 'cityCode')
		const regionIndex = this.formItems.findIndex(item => item.prop === 'regionCode')
		const streetIndex = this.formItems.findIndex(item => item.prop === 'streetCode')
		if (this.formData.cityCode) {
			const ifExist = this.formItems[cityIndex].options.some(item => item.value === this.formData.cityCode)
			if (!ifExist) {
				this.formData.cityCode = ''
				this.formItems[regionIndex].options = []
				this.formItems[streetIndex].options = []
			}
		}
		if (this.formData.regionCode) {
			const ifExist = this.formItems[regionIndex].options.some(item => item.value === this.formData.regionCode)
			if (!ifExist) {
				this.formData.regionCode = ''
				this.formItems[streetIndex].options = []
			}
		}
		if (streetCode) {
			const ifExist = this.formItems[streetIndex].options.some(item => item.value === streetCode)
			if (!ifExist) {
				this.formData.streetCode = ''
			}
		}

		this.handleSearch()
	},
	data() {
		return {
			activeTab: 'status',
			// 左侧查询
			formData: {
				cityCode: '',
				regionCode: '',
				streetCode: '',
				areaName: '',
				takeOver: '',
				takeOverTime: '',
			},
			formItems: getFormItems(this),
			formAttrs: {
				rules: {
					cityCode: [ruleRequired('必填')],
					regionCode: [ruleRequired('必填')],
				},
			},
			// 右侧列表
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			// 列表选中数据
			selectedData: [],
			showCheckIn: false,
		}
	},
	methods: {
		selectable(row) {
			return !row.takeOver
		},
		// 获取市、区县数据
		async _getCityOriRegionData(value, key) {
			const { records } = await apiGetRegion({ regionCode: value })
			const obj = this.formItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = records.map(item => {
				return {
					value: item.regionCode,
					label: item.regionName,
				}
			})
		},
		// 获取街道数据
		async _getStreetData() {
			const data = await apiGetAddressAreaMap({
				parentCode: this.formData.regionCode,
			})
			const obj = this.formItems.find(item => item.prop === 'streetCode')
			if (!obj) return
			obj.options = data.map(item => {
				return {
					value: item.addressAreaCode,
					label: item.addressAreaName,
				}
			})
		},
		async getList() {
			this.loading = true

			try {
				const { current, size } = this.pageData
				const params = trimParams(removeNullParams(this.formData))
				Object.assign(params, {
					current,
					size,
				})
				if (this.activeTab === 'records') {
					if (params.takeOverTime && params.takeOverTime.length > 1) {
						params.receiveStartDate = this.dayjs(params.takeOverTime[0]).format('YYYY-MM-DD')
						params.receiveEndDate = this.dayjs(params.takeOverTime[1]).format('YYYY-MM-DD')
						delete params.takeOverTime
					}
				}
				const apiMethods = {
					status: params => apiGetRecordArchivesList(params),
					records: params => apiGetRecordTakeOver(params),
				}
				const apiMethod = apiMethods[this.activeTab]
				const { total = 0, records = [] } = await apiMethod(params)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
				this.selectedData = []
			}
		},
		// 改变地址
		async handleChangeAddress(value, type) {
			const regionIndex = this.formItems.findIndex(item => item.prop === 'regionCode')
			const streetIndex = this.formItems.findIndex(item => item.prop === 'streetCode')

			if (type === 'cityCode') {
				this.formItems[regionIndex].options = []
				this.formItems[streetIndex].options = []
				this.formData.regionCode = ''
				this.formData.streetCode = ''
				if (value) {
					await this._getCityOriRegionData(value, 'regionCode')
				}
			} else if (type === 'regionCode') {
				this.formData.streetCode = ''
				this.formItems[streetIndex].options = []
				if (value) {
					await this._getStreetData(value)
				}
			}
		},
		handleChangeTab() {
			const isStatusTab = this.activeTab === 'status'
			const newItem = isStatusTab
				? {
						type: 'el-select',
						label: '接管状态',
						prop: 'takeOver',
						options: [
							{
								label: '已接管',
								value: 1,
							},
							{
								label: '未接管',
								value: 0,
							},
						],
				  }
				: {
						type: 'el-date-picker',
						label: '接管时间',
						prop: 'takeOverTime',
						attrs: {
							type: 'daterange',
							startPlaceholder: '开始日期',
							endPlaceholder: '结束日期',
						},
				  }
			if (isStatusTab) {
				this.formData.takeOverTime = ''
			} else {
				this.formData.takeOver = ''
			}
			this.formItems.splice(this.formItems.length - 1, 1, newItem)
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.handleSearch()
		},
		handleSelectChange(data) {
			this.selectedData = data
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleReset() {
			this.$refs[this.refName][0].resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.handleSearch()
		},
		async handleSearch() {
			const valid = await this.$refs[this.refName][0].validate()
			if (!valid) return
			this.handlePageChange({ page: 1 })
		},
		rowDbclick(obj) {
			if (this.activeTab === 'records') {
				const id = obj.row.addressAreaId
				this.$router.push({
					path: '/meterManage/communityView',
					query: {
						id,
					},
				})
			}
		},
		async handleExport(data) {
			if (data === 'baseInfo') {
				const { current, total } = this.pageData
				const params = trimParams(removeNullParams(this.formData))
				Object.assign(params, {
					current,
					size: total,
				})
				await apiExportArea(params).then(res => {
					exportBlob(res, '小区基本信息')
				})
			} else {
				const { areaName, areaCode } = data
				apiExportAreaArchives({
					addressAreaCode: areaCode,
					addressAreaName: areaName,
				}).then(res => {
					exportBlob(res, areaName)
				})
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.page-left {
	padding-top: 0;
	padding-left: 0;
	padding-right: 0;
	.btn-group {
		padding: 0 20px;
	}
	// 隐掉tab的边框
	::v-deep {
		.el-tabs {
			width: 100%;
			height: 100%;
			box-shadow: none;
			border: none;
			border-radius: 4px 4px 0 0;
			overflow: hidden;
			.el-tabs__content {
				padding: 0;
				height: calc(100% - 38px);
				.el-tab-pane {
					height: 100%;
				}
			}
		}
		.el-tabs--border-card > .el-tabs__header {
			border: none;
			height: 38px;
			margin-bottom: 10px;
			.el-tabs__nav {
				display: flex;
				align-items: center;
				width: 100%;
				border: none;
				height: 38px;
				.el-tabs__item {
					margin: 0;
					background: #e1ebfa;
					border: none;
					font-size: 14px;
					color: #6d7480;
					flex: 1;
					padding: 0;
					text-align: center;
					height: 38px;
					&.is-active {
						background: #ffffff;
						font-weight: 500;
						color: #2f87fe;
					}
				}
			}
		}
		.el-tabs__item:focus.is-active.is-focus:not(:active) {
			-webkit-box-shadow: none;
			box-shadow: none;
		}
		.el-form {
			padding: 0 20px;
		}
	}
}
.right-top {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-bottom: 12px;
}
</style>
