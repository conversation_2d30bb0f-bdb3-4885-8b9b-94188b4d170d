<template>
	<i :class="[fontFamily, icon]" :style="styleObj" @click="handleClick" />
</template>

<script>
export default {
	name: 'GcIcon',
	props: {
		icon: {
			type: String,
			required: true,
		},
		size: {
			type: [String, Number],
		},
		color: {
			type: String,
		},
		fontFamily: {
			type: String,
			default: 'iconfontCis',
		},
	},
	computed: {
		styleObj() {
			const fontSize = this.size
			const color = this.color
			if (fontSize && color) return { fontSize: fontSize + 'px', color }
			if (fontSize) return { fontSize: fontSize + 'px' }
			if (color) return { color }
			return ''
		},
	},
	methods: {
		handleClick() {
			this.$emit('click')
		},
	},
}
</script>

<style lang="scss" scoped></style>
