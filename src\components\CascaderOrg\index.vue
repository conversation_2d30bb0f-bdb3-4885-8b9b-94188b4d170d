<template>
	<div class="orgStru-range">
		<el-cascader
			v-if="!simple"
			v-model="adminOrg"
			:options="orgCodeArr"
			:show-all-levels="false"
			:props="{ checkStrictly: true, ...props }"
			placeholder="全部"
			:clearable="clearable"
			ref="refHandle"
		></el-cascader>
		<el-tooltip v-if="simple && adminOrg" class="item" effect="dark" content="点击可以切换组织机构" placement="top">
			<el-dropdown trigger="click" @command="handleCommand" style="cursor: pointer">
				<span class="el-dropdown-link">
					{{ adminOrg | showName(orgCodeArr) }}
					<i class="el-icon-arrow-down el-icon--right"></i>
				</span>
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item v-for="item in orgCodeArr" :key="item.org_code" :command="item.org_code">
						{{ item.name }}
					</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>
		</el-tooltip>
	</div>
</template>

<script>
import { apiOrganizeTree, apiGetTenantOrgList } from '@/api/organize.api'
import identity from '@/mixin/identity.js'
import object from 'element-resize-detector/src/detection-strategy/object'
export default {
	name: 'CascaderOrg',
	mixins: [identity],
	filters: {
		showName(val, list) {
			return list.filter(item => item.org_code === val)[0]?.name || ''
		},
	},
	props: {
		value: {
			type: String,
		},
		needDefaultFirst: {
			type: Boolean,
			default: false,
		},
		simple: {
			type: Boolean,
			default: false,
		},
		clearable: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			// 管理员的所属机构(包含租户和所属分公司)
			orgCodeArr: [],
			farr: [],
			props: {
				label: 'name',
				value: 'org_code',
				children: 'children',
			},
		}
	},
	computed: {
		adminOrg: {
			get() {
				return this.value
			},
			set(val) {
				this.$emit('update:value', val.length ? val[val.length - 1] : '')
			},
		},
	},
	watch: {
		// 修改elemenui组件级联菜单不会自动收起
		adminOrg() {
			if (this.$refs.refHandle) {
				this.$refs.refHandle.dropDownVisible = false //监听值发生变化就关闭它
			}
		},
		tenantId() {
			this.adminOrg = ''
			this.getTenantOrgList({ tid: this.tenantId }) //获取所属机构
		},
	},
	created() {
		this.init()
	},
	methods: {
		info(arr) {
			let newArr = []
			for (let i = 0; i < arr.length; i++) {
				newArr[i] = { ...arr[i].tenant_organization }
				let middleOrgCode = newArr[i].org_code && newArr[i].org_code
				if (middleOrgCode) {
					let arrOrgCode = middleOrgCode.split('-')
					if (arrOrgCode.length - 1 < 3) {
						if (object.hasOwnProperty.call(arr[i], 'children')) {
							if (arr[i].children.length > 0) {
								let newArr2 = this.info(arr[i].children)
								newArr[i].children = newArr2
							}
						}
					}
				}
			}
			return newArr
		},
		init() {
			let orgsTemplateTree = this.$ls.get('orgsTemplateTree')
			if (this.userLevel != 0) {
				//  租户级
				let uf = this.userInfo
				// 租户管理员
				if (uf.isTenantAdmin) {
					this.orgCodeArr = this.getorgCodeArr(orgsTemplateTree)
					return
				}
				/*
        跨域运维 - FEAT 3.7.0取消跨域运维角色
        */
				if (uf.isCrossDomain) {
					if (uf.orgCode.split('-').length < 4) {
						//分公司以上层级
						orgsTemplateTree.map(item => {
							if (item.tenant_organization.org_code == uf.orgCode) {
								this.orgCodeArr = this.getorgCodeArr(item.children)
							}
						})
						return
					}
				}
				// 成员公司级别-当前成员公司下的所有分公司
				if (uf.orgStruName === '成员公司') {
					this.gettenantOrgList(apiOrganizeTree)
					return
				}
				this.orgCode = uf.branchCompanyOrgCode
				this.orgCodeArr = [{ name: uf.branchCompanyOrgName, org_code: uf.branchCompanyOrgCode }]
				return
			}
			if (this.userLevel == 0 && this.tenantId) {
				//系统级
				this.gettenantOrgList(apiGetTenantOrgList)
			}
		},
		//所属机构发生变化的时候
		orgChange(item) {
			this.$emit('orgChange', item)
		},
		getQuitCondition(orgcode) {
			let arr = orgcode.split('-')
			return arr.length == 4 ? true : false
		},
		// 获取分公司列表
		getSubComList(arr) {
			//过滤出分公司的数据
			if (arr.length > 0) {
				arr.map(item => {
					if (this.getQuitCondition(item.tenant_organization.org_code)) {
						this.farr.push({
							name: item.tenant_organization.name,
							org_code: item.tenant_organization.org_code,
						})
					} else {
						if (item.children) {
							this.getSubComList(item.children)
						}
					}
				})
				return this.farr
			}
		},
		// 分公司级别
		getorgCodeArr(arr) {
			this.farr = []
			let result = this.getSubComList(arr)
			return result
		},
		gettenantOrgList(api) {
			api({ tid: this.tenantId }).then(res => {
				this.orgCodeArr = this.getorgCodeArr(res)
				if (this.userLevel === 0 && this.needDefaultFirst) {
					this.$emit('update:value', this.orgCodeArr[0]?.org_code)
				}
			})
		},
		handleCommand(val) {
			this.$emit('update:value', val)
		},
	},
}
</script>
<style lang="scss" scoped>
.orgStru-range {
	position: relative;
	.el-cascader {
		width: 100%;
	}
	.tool-tip {
		position: relative;
		top: 50%;
		right: 0;
		font-size: 16px;
		border-radius: 50%;
		color: #2f87fe;
	}
}
</style>
