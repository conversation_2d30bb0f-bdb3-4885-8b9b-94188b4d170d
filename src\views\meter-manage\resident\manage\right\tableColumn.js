import { getfilterName } from '@/utils'
import { yesOrNoEnum } from '@/consts/enums.js'
export function getColumn(_this) {
	const { virtualMeterType = [], chargingMethod = [], archiveState = [], meterStatus = [] } =
		_this.$store.getters.dataList || {}
	return [
		{
			key: 'archivesStatus',
			name: '表卡状态',
			tooltip: true,
			render: (h, row) => {
				const archivesStatus = row.archives.archivesStatus
				const colorMap = {
					1: '#19BE6B',
					2: '#CCCCCC',
					3: '#CCCCCC',
					4: '#EC6B60',
				}
				return (
					<div
						style={{
							display: 'flex',
							'justify-content': 'flex-start',
							'align-items': 'center',
							color: archivesStatus === 4 ? '#EC6B60' : '#4E4E4E',
							cursor: archivesStatus === 4 ? 'pointer' : 'default',
						}}>
						<span
							style={{
								display: 'inline-block',
								width: '6px',
								height: '6px',
								'border-radius': '50%',
								'background-color': colorMap[archivesStatus],
								'margin-right': '6px',
							}}></span>
						<span
							style={{
								'border-bottom': archivesStatus === 4 ? '1px solid #EC6B60' : 'none',
							}}>
							{getfilterName(archiveState, archivesStatus, 'sortValue', 'sortName')}
						</span>
					</div>
				)
			},
		},
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
			render: (h, row) => {
				return h('span', {}, row.archives && row.archives.archivesIdentity)
			},
		},
		{
			key: 'virtualMeterType',
			name: '表卡类型',
			tooltip: true,
			render: (h, row) => {
				if (row.archives) {
					const valueStr = getfilterName(
						virtualMeterType,
						row.archives.virtualMeterType,
						'sortValue',
						'sortName',
					)
					return h('span', {}, valueStr)
				}
			},
		},
		{
			key: 'alleyCode',
			name: '坊别',
			tooltip: true,
			render: (h, row) => {
				return h('span', {}, row.archives && row.archives.alleyCode)
			},
		},
		{
			key: 'userName',
			name: '用户名称',
			tooltip: true,
			render: (h, row) => {
				return h('span', {}, row.user && row.user.userName)
			},
		},
		{
			key: 'addressFullName',
			name: '地址',
			tooltip: true,
			render: (h, row) => {
				return h('span', {}, row.address && row.address.addressFullName)
			},
			minWidth: 220,
		},

		{
			key: 'chargingMethod',
			name: '收费方式',
			tooltip: true,
			render: (h, row) => {
				if (row.user) {
					const valueStr = getfilterName(chargingMethod, row.user.chargingMethod, 'sortValue', 'sortName')
					return h('span', {}, valueStr)
				}
			},
		},
		{
			key: 'priceCode',
			name: '价格',
			tooltip: true,
			render: (h, row) => {
				const price = row.price || {}
				const str = price.priceCode + '-' + price.priceName

				return h('span', {}, str)
			},
		},
		{
			key: 'summaryArchives',
			name: '小区总表',
			tooltip: true,
			render: (h, row) => {
				return h('span', {}, row.archives && yesOrNoEnum[row.archives.summaryArchives])
			},
		},
		{
			key: 'userMobile',
			name: '手机号码',
			tooltip: true,
			render: (h, row) => {
				return h('span', {}, row.user && row.user.userMobile)
			},
		},
		{
			key: 'archivesTime',
			name: '建档时间',
			tooltip: true,
			render: (h, row) => {
				return h('span', {}, row.archives && row.archives.archivesTime)
			},
			minWidth: 180,
		},
		{
			key: 'meterStatus',
			name: '水表状态',
			tooltip: true,
			render: (h, row) => {
				if (row.meter) {
					const valueStr = getfilterName(meterStatus, row.meter.meterStatus, 'sortValue', 'sortName')
					return h('span', {}, valueStr)
				}
			},
		},
	]
}
