<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-11 19:59:52
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-14 16:17:36
-->
<template>
	<gc-el-dialog
		:show="isShow"
		:title="`${typeText}册本`"
		custom-top="120px"
		width="800px"
		@open="handleOpen"
		@close="handleClose"
	>
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:range>
				<el-form-item class="range-form-item" label="册本范围" :rules="rangeRule">
					<el-form-item class="range-inner-form-item" prop="rangeStart">
						<el-input v-model="formData.rangeStart" placeholder="起始号" @change="handleStartRangeChange" />
					</el-form-item>
					&nbsp;&nbsp;
					<span>-</span>
					&nbsp;&nbsp;
					<el-form-item class="range-inner-form-item" prop="rangeEnd">
						<el-input v-model="formData.rangeEnd" placeholder="结束号" @change="handleEndRangeChange" />
					</el-form-item>
				</el-form-item>
			</template>
		</GcFormRow>
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { isBlank } from '@/utils/validate.js'
import recordForm from './recordForm.js'
import { ruleDigit, ruleMaxLength, RULE_POSITIVEINTEGERONLY_STARTOFZERO } from '@/utils/rules.js'
import {
	getAlleyMap,
	queryStaffByType,
	addMeterReadingBook,
	generateBookNo,
	updateMeterReadingBook,
	updateMeterReadingBook2,
} from '@/api/meterReading.api.js'

const permissionsMap = {
	'plan-collection_meterReadingBook_updateBook': updateMeterReadingBook,
	'plan-collection_meterReadingBook_updateBook2': updateMeterReadingBook2,
}
export default {
	name: '',
	props: {
		// 弹窗显示/隐藏
		show: {
			type: Boolean,
			default: false,
		},
		editType: {
			type: String,
			default: 'add',
		},
		permissions: {
			type: String,
			default: 'plan-collection_meterReadingBook_updateBook',
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		typeText() {
			return this.editType === 'add' ? '新建' : '编辑'
		},
	},
	data() {
		return {
			formData: {
				orgCode: '',
				alleyId: '',
				bookNo: '',
				bookType: '',
				meterReadingStaffId: '',
				staffPhone: '',
				meterReadingCycle: '',
				meterReadingNumber: '',
				rangeStart: '',
				rangeEnd: '',
				remark: '',
			},
			formItems: recordForm(this).formItems,
			formAttrs: {
				labelWidth: '110px',
				labelPosition: 'right',
				rules: {
					orgCode: [{ required: true, message: '请选择营业分公司', trigger: 'change' }],
					alleyId: { required: true, message: '请选择坊别', trigger: 'change' },
					bookNo: [
						{ message: '表册编号' },
						{
							required: true,
							message: '请输入表册编号',
							trigger: ['blur', 'change'],
						},
						ruleMaxLength(10, '表册编号'),
						RULE_POSITIVEINTEGERONLY_STARTOFZERO,
					],
					bookType: [{ required: true, message: '请选择册本类型', trigger: 'change' }],
					meterReadingStaffId: [{ required: true, message: '请选择抄表员', trigger: 'change' }],
					meterReadingCycle: [{ required: true, message: '请选择抄表周期', trigger: 'change' }],
					meterReadingNumber: [{ required: true, message: '请选择抄表次数', trigger: 'change' }],
					rangeStart: [
						{ message: '起始号' },
						{ required: true, message: '请输入起始号', trigger: 'blur' },
						RULE_POSITIVEINTEGERONLY_STARTOFZERO,
						{
							validator: (rule, value, cb) => {
								if (
									value &&
									(this.formData.rangeEnd || this.formData.rangeEnd === 0) &&
									Number(value) > Number(this.formData.rangeEnd)
								) {
									cb(new Error('不能大于结束号'))
								}
								cb()
							},
							trigger: ['blur', 'change'],
						},
					],
					rangeEnd: [
						{ message: '结束号' },
						{ required: true, message: '请输入结束号', trigger: 'blur' },
						RULE_POSITIVEINTEGERONLY_STARTOFZERO,
						{
							validator: (rule, value, cb) => {
								if (
									value &&
									(this.formData.rangeStart || this.formData.rangeStart === 0) &&
									Number(value) < Number(this.formData.rangeStart)
								) {
									cb(new Error('不能小于起始号'))
								}
								cb()
							},
							trigger: ['blur', 'change'],
						},
					],
					remark: [ruleMaxLength(128, '表册编号')],
				},
			},
			rangeRule: { required: true },

			// 标记是否为编辑时表单初始化
			isEditInit: false,
		}
	},
	watch: {
		'formData.orgCode': function (newVal) {
			// 获取坊别数据
			this.getAlleyMapData(newVal)
			// 获取抄表员工数据
			this.getStaffMapData(newVal)
			// 生成抄表编号
			if (this.formData.bookType === 1 && !this.isEditInit) {
				this.generateBookNoData()
			}
			this.isEditInit = false
		},
	},
	methods: {
		// 获取坊别数据
		async getAlleyMapData(orgCode) {
			try {
				if (!orgCode) return

				const res = await getAlleyMap({
					orgCode,
				})

				if (!res.length) {
					this.formItems[1].attrs.noDataText = ''
				}

				this.formItems[1].options = res.map(item => {
					return {
						value: item.id,
						label: item.alleyName,
					}
				})
			} catch (error) {
				console.error(error)
				this.formItems[1].options = []
			}
		},
		// 获取抄表员工数据
		async getStaffMapData(orgCode) {
			if (!orgCode) return

			try {
				const res = await queryStaffByType({
					orgCode,
					staffType: 0,
					status: 0,
				})
				if (!res.length) {
					this.formItems[4].attrs.noDataText = ''
				}
				this.formItems[4].options = res.map(item => {
					const { staffId, staffName, staffPhone } = item
					return {
						value: staffId,
						label: staffName,
						staffPhone,
					}
				})
			} catch (error) {
				console.error(error)
				this.formItems[4].options = []
			}
		},
		// 生成抄表编号
		async generateBookNoData() {
			const { orgCode, alleyId = '', bookType } = this.formData
			if (!orgCode) return

			// 册本类型为企业时 坊别非必填 根据营业分公司、册本类型 生成抄表编号
			// 册本类型为居民时 坊别必填 根据营业分公司、坊别、册本类型 生成抄表编号
			let flag = false
			if (bookType === 1) {
				flag = true
			} else if (bookType) {
				flag = !!alleyId
			}

			if (flag) {
				try {
					const res = await generateBookNo({
						orgCode,
						alleyId,
						bookType,
					})
					this.formData.bookNo = res
				} catch (error) {
					console.error(error)
					this.formData.bookNo = ''
				}
			}
		},
		// 册本范围 起始、结束改变
		handleStartRangeChange(value) {
			if (value) {
				this.$refs.formRef.$refs.formRef.validateField('rangeEnd')
			} else {
				this.$refs.formRef.$refs.formRef.clearValidate('rangeEnd')
			}
		},
		handleEndRangeChange(value) {
			if (value) {
				this.$refs.formRef.$refs.formRef.validateField('rangeStart')
			} else {
				this.$refs.formRef.$refs.formRef.clearValidate('rangeStart')
			}
		},
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				if (this.editType === 'add') {
					await addMeterReadingBook(this.formData)
				} else {
					await permissionsMap[this.permissions](this.formData)
				}
				this.$message.success(`册本${this.typeText}成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			// 清空坊别、抄表员下拉数据
			this.formItems[1].options = this.formItems[4].options = []
			// 重置坊别验证规则
			this.formAttrs.rules.alleyId.required = true
			// 重置册本范围起始号、结束号验证规则
			this.formAttrs.rules.rangeStart[0] = { message: '起始号' }
			this.formAttrs.rules.rangeEnd[0] = { message: '结束号' }
			this.isShow = false
		},
		handleOpen() {
			this.formItems[0].attrs.disabled =
				this.formItems[1].attrs.disabled =
				this.formItems[2].attrs.disabled =
				this.formItems[3].attrs.disabled =
					this.editType === 'edit'
		},

		// 修改表册编号位数验证规则
		handleBookNoRule(bookType) {
			if (!bookType) {
				this.formAttrs.rules.bookNo[0] = { message: '表册编号' }
				this.$refs.formRef.$refs.formRef.clearValidate('bookNo')
				return
			}

			const isBusiness = bookType === 1
			const min = isBusiness ? 5 : 6
			const message = isBusiness ? '企业' : '居民'
			this.formAttrs.rules.bookNo[0] = {
				min,
				message: `${message}表册编号最小为${min}位`,
				trigger: 'blur',
			}
			if (!isBlank(this.formData.bookNo)) {
				this.$refs.formRef.$refs.formRef.validateField('bookNo')
			}
		},

		// 修改坊别是否必填、表册范围验证规则
		handleRangeRule(bookType = 2) {
			const isBusiness = bookType === 1

			this.formAttrs.rules.alleyId.required = !isBusiness

			const digit = isBusiness ? 9 : 6
			const message = isBusiness ? '企业类型编号为' : '居民类型编号为'
			this.formAttrs.rules.rangeStart[0] = ruleDigit(digit, message)
			this.formAttrs.rules.rangeEnd[0] = ruleDigit(digit, message)
			//  企业类型时 表册范围非必填
			this.rangeRule.required = !isBusiness
			this.formAttrs.rules.rangeStart[1].required = !isBusiness
			this.formAttrs.rules.rangeEnd[1].required = !isBusiness

			if (isBusiness) {
				this.$refs.formRef.$refs.formRef.validateField(['rangeStart', 'rangeEnd'])
			}
		},
		// 设置表单数据
		setFormData(data) {
			this.isEditInit = true
			this.handleRangeRule(data.bookType)
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}

.range-form-item {
	::v-deep .el-form-item__content {
		display: flex;
		align-items: center;
	}
}
.range-inner-form-item {
	margin: 0;
}
</style>
