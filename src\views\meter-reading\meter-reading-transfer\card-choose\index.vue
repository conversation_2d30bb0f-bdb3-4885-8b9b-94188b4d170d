<template>
	<div class="wrapper">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch(false)">筛选</el-button>
				<el-button @click="handleBottomReset">重置</el-button>
			</el-form-item>
		</GcFormSimple>
		<div class="right-top">
			<el-button type="primary" :disabled="!tableData.length" @click="handleChooseAll">选择全部</el-button>
		</div>
		<GcTable
			ref="tableRef"
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			row-key="archivesId"
			showPage
			needType="selection"
			reserve-selection
			@select="handleSelect"
			@select-all="handleSelectAll"
			@current-page-change="handlePageChange"
		/>
		<div class="button-group">
			<button class="gc-button gc-button-three" type="button" @click="handleBottomReset">重 置</button>
			<button class="gc-button gc-button-two" type="button" @click="handleNext(false)">下一项</button>
		</div>
	</div>
</template>

<script>
import { bookTypeOptions } from '@/consts/optionList.js'
import { meterCardOptionsData, meterCardOpsPage, meterCardChoose } from '@/api/meterReading.api'

export default {
	components: {},
	data() {
		return {
			initialForm: {},
			formData: {
				orgCode: '',
				bookType: '',
				bookNoList: [],
				meterReadingNum: 3,
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
					attrs: {
						clearable: true,
						placeholder: '请选择营业分公司',
					},
					formItemAttrs: {
						labelWidth: '102px',
					},
					events: {
						change: value => {
							this.getBooksOptionsFn()
							if (!value) {
								this.tableData = []
								this.$emit('choose', false)
							}
						},
					},
				},
				{
					type: 'el-select',
					label: '册本类型',
					prop: 'bookType',
					options: bookTypeOptions,
					attrs: {
						clearable: true,
						placeholder: '请选择册本类型',
					},
					events: {
						change: () => {
							this.getBooksOptionsFn()
						},
					},
				},
				{
					type: 'el-select',
					label: '表册编号',
					prop: 'bookNoList',
					options: [],
					attrs: {
						clearable: true,
						multiple: true,
						filterable: true,
						collapseTags: true,
						noDataText: '',
						placeholder: '请选择表册编号',
					},
				},
				{
					type: 'el-input-number',
					label: '抄表次数≥',
					prop: 'meterReadingNum',
					attrs: {
						min: 0,
						max: 99999,
						stepStrictly: true,
						placeholder: '',
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '90px',
				rules: {
					orgCode: {
						required: true,
						message: '请选择营业分公司',
						trigger: 'change',
					},
					bookType: {
						required: true,
						message: '请选择册本类型',
						trigger: 'change',
					},
				},
			},

			loading: false,
			tableData: [],
			columns: [
				{
					key: 'bookNo',
					name: '表册编号',
					tooltip: true,
				},
				{
					key: 'archivesNo',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'addressName',
					name: '地址',
					tooltip: true,
				},
				{
					key: 'firstRecordTime',
					name: '首次抄表时间',
					tooltip: true,
				},
				{
					key: 'meterReadingNum',
					name: '抄表次数',
					tooltip: true,
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 列表选中数据
			selectedData: [],
			isChooseAll: false,
		}
	},
	computed: {
		// 查询条件是否右边变更标记
		isFormModified: function () {
			return JSON.stringify(this.formData) !== JSON.stringify(this.initialForm)
		},
	},
	created() {
		const { orgCode = '', bookType = '', bookNo = '' } = this.$route.query
		const orgList = this.$store.getters.orgList || []
		const firstOrgItem = orgList[0] || {}
		this.formData = {
			...this.formData,
			orgCode: orgCode || firstOrgItem.value || '',
			bookType: bookType ? Number(bookType) : '',
			bookNoList: bookNo ? [bookNo] : [],
		}
		if (orgCode) {
			this.getList(1)
			if (bookType) {
				this.getBooksOptions()
			}
		}
	},
	methods: {
		getBooksOptionsFn() {
			const { orgCode, bookType } = this.formData
			if (!orgCode || !bookType) {
				this.formItems[2].options = []
				// this.formItems[2].attrs.noDataText = "请先选择营业分公司、册本类型";
			} else {
				// this.formItems[2].attrs.noDataText = "无数据";
				this.getBooksOptions()
			}
			this.formData.bookNoList = []
		},
		// 根据营业公司、册本类型 查询表册编号下拉数据
		async getBooksOptions() {
			try {
				const res = await meterCardOptionsData({
					orgCode: this.formData.orgCode,
					bookType: this.formData.bookType,
				})
				this.formItems[2].options =
					res?.map(item => {
						return {
							label: item,
							value: item,
						}
					}) || []
			} catch (error) {
				console.error(error)
				this.formItems[2].options = []
			}
		},
		handleBottomReset() {
			this.tableData = []
			this.$refs.formRef.resetFormSmooth()
			this.$refs.tableRef.clearCheckTableSelection()
			this.$emit('reset')
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},

		// 列表选择
		async handleSelect(selection, row) {
			this.selectedData = selection
			// 判断当前行是否在选中的数组中
			let selected = !!(selection.length && selection.indexOf(row) !== -1)
			try {
				await meterCardChoose({
					archivesIdList: [row.archivesId],
					// 移交状态 0-未移交 1-已移交 2-待移交
					handOver: selected ? 2 : 0,
				})
				this.$emit('choose', selection.length > 0 || this.isChooseAll)
			} catch (error) {
				console.error(error)
			}
		},
		// 当前页列表全选
		async handleSelectAll(selection) {
			// 过滤出取消选择的项
			const filteredData = this.selectedData.filter(
				item => !selection.some(selectedItem => selectedItem.archivesId === item.archivesId),
			)
			try {
				if (filteredData.length) {
					await meterCardChoose({
						archivesIdList: filteredData.map(item => item.archivesId),
						handOver: 0,
					})
				}
				if (selection.length) {
					await meterCardChoose({
						archivesIdList: selection.map(item => item.archivesId),
						handOver: 2,
					})
					this.selectedData = this._.cloneDeep(selection)
					this.$emit('choose', selection.length > 0 || this.isChooseAll)
				}
			} catch (error) {
				console.error(error)
			}
		},

		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		async getList(curPage) {
			this.loading = true

			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await meterCardOpsPage({
					size,
					current,
					isClear: this.isFormModified,
					...this.formData,
				})
				this.pageData.total = total
				this.tableData = records

				// 查询条件变更 则重置选中数据
				if (this.isFormModified) {
					this.$refs?.tableRef?.clearCheckTableSelection()
					this.selectedData = []
					this.isChooseAll = false
				}

				this.initialForm = Object.assign({}, this.formData)

				// 是否修改右侧tab状态
				let isChangeTab = false
				this.$nextTick(() => {
					// 初始化选中数据
					this.tableData.forEach(item => {
						this.$refs.tableRef.toggleRowSelectionUseInnerData(copyTableData => {
							return copyTableData.find(row => row.archivesId === item.archivesId)
						}, item.handOverStatus === 2)
						// 初始化时 若列表中有选中的数据 则抛出事件修改右侧tab状态
						if (!isChangeTab && item.handOverStatus === 2) {
							isChangeTab = true
						}
					})

					this.$emit('choose', isChangeTab || this.selectedData.length > 0 || this.isChooseAll)
				})
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.selectedData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},

		// 选中全部
		async handleChooseAll() {
			try {
				await meterCardChoose({
					// 移交状态 0-未移交 1-已移交 2-待移交
					handOver: 2,
					...this.formData,
				})
				this.isChooseAll = true
				this.getList()
				this.$emit('choose', true, this.formData)
			} catch (error) {
				console.error(error)
			}
		},

		// 下一步
		handleNext() {
			if (!this.selectedData.length && !this.isChooseAll) {
				this.$message.error('未选择表册')
				return
			}
			this.$emit('next', 'transferListCheck')
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
}
.button-group {
	width: 100%;
	.gc-button {
		margin-right: 8px;
	}
}

::v-deep {
	.el-form-item {
		margin-bottom: 12px;
	}
}
.right-top {
	display: flex;
	justify-content: flex-end;
	margin-bottom: 12px;
}
</style>
