<template>
	<div class="container-wrapper">
		<GcModelHeader
			class="info-title"
			title="地址选择"
			:icon="require('@/assets/images/icon/title-common-parameters.png')"
		></GcModelHeader>
		<div class="container">
			<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<template v-slot:fullAddress>
					<el-form-item label="详细地址" prop="addressName">
						<AddressSearch
							:searchName.sync="formData.addressName"
							:isDisabled="!formData.regionCode"
							:regionCode="formData.regionCode"
							:addressAreaCode="addressAreaCode"
							style="width: 50%"
						/>
					</el-form-item>
				</template>
				<template v-slot:otherInfo>
					<h5 class="gap-title">其它信息</h5>
				</template>
			</GcFormRow>
			<div class="button-group">
				<button class="gc-button gc-button-three" type="button" @click="handleReset">重 置</button>
				<button class="gc-button gc-button-two" type="button" @click="changeTab('recordChoose')">上一项</button>
				<button class="gc-button gc-button-two" type="button" @click="changeTab('userInfo')">下一项</button>
			</div>
		</div>
	</div>
</template>
<script>
import { apiGetRegion, apiGetAddressAreaMap } from '@/api/addressManage.api.js'
import { getFormItems } from './addressForm.js'
import { ruleRequired, ruleMaxLength } from '@/utils/rules'
import AddressSearch from '../../../components/AddressSearch.vue'
export default {
	components: { AddressSearch },
	props: {
		detailData: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			formData: {
				regionCode: '',
				streetCode: '',
				communityCode: '',
				buildingCode: '',
				takeOver: 0,
				addressName: '', // 详细地址
				gisCode: '',
				tapWaterNo: '',
				houseYear: '',
				floorNum: '',
				pressureZone: '',
				pipeNetworkCode: '',
			},
			addressAreaCode: '', // 街道/小区/乡镇/村庄
			addressFullName: '', // 完整地址
			formItems: getFormItems(this),
			formAttrs: {
				labelWidth: '110px',
				labelPosition: 'right',
				rules: {
					regionCode: [ruleRequired('必填', 'blur')],
					streetCode: [ruleRequired('必填', 'blur')],
					addressName: [ruleRequired('必填', 'blur'), ruleMaxLength(64)],
					tapWaterNo: [ruleMaxLength(16)],
					pipeNetworkCode: [ruleMaxLength(32)],
					gisCode: [ruleMaxLength(32)],
					pressureZone: [ruleMaxLength(32)],
					houseYear: [ruleMaxLength(16)],
					floorNum: [
						{
							pattern: /^(?:[0-9]{1,4}|9999)$/,
							message: '请输入0-9999的整数',
							trigger: '',
						},
					],
				},
			},
		}
	},
	watch: {
		formData: {
			handler: 'validateForm',
			deep: true,
		},
	},
	methods: {
		// 页面激活时：地址接口和表单信息更新
		async updateAddress() {
			this.formData =
				this.$route.name === 'CompanyMeterCreateModify'
					? Object.assign(this.formData, this.detailData)
					: this.formData

			const {
				regionCode,
				streetCode,
				communityCode,
				buildingCode,
				addressAreaCode,
				addressFullName,
				addressName,
			} = this.formData || {}
			const promises = []

			promises.push(this._getRegionData())
			if (regionCode) {
				promises.push(this._getAddressAreaMap(regionCode, 'streetCode'))
			}
			if (streetCode) {
				promises.push(this._getAddressAreaMap(streetCode, 'communityCode'))
			}
			if (communityCode) {
				promises.push(this._getAddressAreaMap(communityCode, 'buildingCode'))
			}
			await Promise.all(promises)
			const regionIndex = this.formItems.findIndex(item => item.prop === 'regionCode')
			const streetIndex = this.formItems.findIndex(item => item.prop === 'streetCode')
			const communityIndex = this.formItems.findIndex(item => item.prop === 'communityCode')
			const buildingIndex = this.formItems.findIndex(item => item.prop === 'buildingCode')
			const takeOverIndex = this.formItems.findIndex(item => item.prop == 'takeOver')
			if (regionCode) {
				const ifExist = this.formItems[regionIndex].options.some(item => item.value === regionCode)
				if (!ifExist) {
					this.formData.regionCode = ''
					this.formItems[streetIndex].options = []
					this.formItems[communityIndex].options = []
					this.formItems[buildingIndex].options = []
				}
			}
			if (streetCode) {
				const ifExist = this.formItems[streetIndex].options.some(item => item.value === streetCode)
				if (!ifExist) {
					this.formData.streetCode = ''
					this.formItems[communityIndex].options = []
					this.formItems[buildingIndex].options = []
				}
			}
			if (communityCode) {
				const ifExist = this.formItems[communityIndex].options.some(item => item.value === communityCode)
				if (!ifExist) {
					this.formData.communityCode = ''
					this.formItems[buildingIndex].options = []
					if (takeOverIndex !== -1) {
						this.formItems.splice(takeOverIndex, 1)
					}
				} else if (takeOverIndex === -1) {
					this.formItems.splice(4, 0, {
						type: 'el-radio',
						label: '是否接管',
						prop: 'takeOver',
						options: [
							{ label: '未接管', value: 0 },
							{ label: '已接管', value: 1 },
						],
						attrs: {
							col: 8,
							disabled: true,
						},
					})
				}
			}
			if (buildingCode) {
				const ifExist = this.formItems[buildingIndex].options.some(item => item.value === buildingCode)
				if (!ifExist) {
					this.formData.buildingCode = ''
				}
			}
			if (addressAreaCode) {
				this.addressAreaCode = addressAreaCode
			}
			if (addressFullName) {
				this.addressFullName = addressFullName.endsWith(addressName)
					? addressFullName.slice(0, -addressName.length)
					: addressFullName
			}
		},
		// 获取区县数据
		async _getRegionData() {
			const { records } = await apiGetRegion({ regionCode: 2102 })
			const obj = this.formItems.find(item => item.prop === 'regionCode')
			if (!obj) return
			obj.options = records.map(item => {
				return {
					value: item.regionCode,
					label: item.regionName,
				}
			})
		},
		// 获取街道、小区、楼栋下拉数据
		async _getAddressAreaMap(value, key) {
			const data = await apiGetAddressAreaMap({
				parentCode: value,
				status: 1, // 启用
			})
			const obj = this.formItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = data.map(item => {
				let label = item.addressAreaName
				if (key === 'buildingCode') {
					label = label + item.unit
				}
				return {
					value: item.addressAreaCode,
					label,
					...item,
				}
			})
		},
		// 改变地址
		async handleChangeAddress(value, type) {
			const regionIndex = this.formItems.findIndex(item => item.prop === 'regionCode')
			const streetIndex = this.formItems.findIndex(item => item.prop === 'streetCode')
			const communityIndex = this.formItems.findIndex(item => item.prop === 'communityCode')
			const buildingIndex = this.formItems.findIndex(item => item.prop === 'buildingCode')
			let takeOverIndex = this.formItems.findIndex(item => item.prop == 'takeOver')
			const regionObj = this.formItems[regionIndex].options.find(item => item.value === this.formData.regionCode)
			const streetObj = this.formItems[streetIndex].options.find(item => item.value === this.formData.streetCode)
			const communityObj = this.formItems[communityIndex].options.find(
				item => item.value === this.formData.communityCode,
			)
			const buildingObj = this.formItems[buildingIndex].options.find(
				item => item.value === this.formData.buildingCode,
			)
			const regionFullName = regionObj?.label || ''
			const streetFullName = regionFullName + (streetObj?.label || '')
			const communityFullName = streetFullName + (communityObj?.label || '')
			const buildingFullName = communityFullName + (buildingObj?.label || '')

			if (takeOverIndex !== -1 && type !== 'buildingCode') {
				this.formItems.splice(takeOverIndex, 1)
				takeOverIndex = -1
			}

			if (type === 'regionCode') {
				this.formData.streetCode = ''
				this.formData.communityCode = ''
				this.formData.buildingCode = ''
				this.formItems[streetIndex].options = []
				this.formItems[communityIndex].options = []
				this.formItems[buildingIndex].options = []
				if (value) {
					await this._getAddressAreaMap(value, 'streetCode')
					this.addressAreaCode = regionObj?.value || ''
					this.addressFullName = regionFullName
				} else {
					this.addressFullName = ''
					this.addressAreaCode = ''
				}
			} else if (type === 'streetCode') {
				this.formData.communityCode = ''
				this.formData.buildingCode = ''
				this.formItems[communityIndex].options = []
				this.formItems[buildingIndex].options = []
				if (value) {
					await this._getAddressAreaMap(value, 'communityCode')
					this.addressFullName = streetFullName
					this.addressAreaCode = value
				} else {
					this.addressFullName = regionFullName
					this.addressAreaCode = regionObj?.value || ''
				}
			} else if (type === 'communityCode') {
				this.formData.buildingCode = ''
				this.formItems[buildingIndex].options = []
				if (value) {
					if (takeOverIndex === -1) {
						this.formItems.splice(4, 0, {
							type: 'el-radio',
							label: '是否接管',
							prop: 'takeOver',
							options: [
								{ label: '未接管', value: 0 },
								{ label: '已接管', value: 1 },
							],
							attrs: {
								col: 8,
								disabled: true,
							},
						})
					}
					await this._getAddressAreaMap(value, 'buildingCode')
					this.addressFullName = communityFullName
					this.addressAreaCode = value
					this.formData.takeOver = communityObj?.takeOver || 0
				} else {
					this.addressFullName = streetFullName
					this.addressAreaCode = streetObj?.value || ''
				}
			} else if (type === 'buildingCode') {
				if (value) {
					this.addressFullName = buildingFullName
					this.addressAreaCode = value
				} else {
					this.addressFullName = communityFullName
					this.addressAreaCode = communityObj?.value || ''
				}
			}
		},
		async validateForm() {
			const valid = await this.$refs.formRef.validate()
			this.$emit('getValid', 'addressChoose', valid)
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.validateForm()
		},
		changeTab(v) {
			this.$emit('changeTab', v)
		},
	},
	mounted() {
		this.validateForm()
	},
}
</script>

<style lang="scss" scoped>
.container-wrapper {
	height: 100%;
}
.container {
	padding-right: 20px;
	height: calc(100% - 60px);
	overflow-y: auto;
	overflow-x: hidden;
}
.gap-title {
	padding: 0 20px;
	color: #222222;
	font-size: 14px;
	font-weight: bold;
}
.button-group {
	width: 100%;
	margin-top: 20px;
	padding: 0 20px;
	.gc-button {
		margin-right: 8px;
	}
}
</style>
