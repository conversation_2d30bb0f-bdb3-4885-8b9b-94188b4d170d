<template>
	<div class="page-layout">
		<div class="page-left">
			<el-tabs v-model="activeTab" type="border-card" @tab-click="handleChangeTab">
				<el-tab-pane
					v-for="(tab, index) in tabs"
					:key="index"
					:label="tab.label"
					:name="tab.name"
				></el-tab-pane>
			</el-tabs>
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
			<div class="btn-group">
				<el-button round @click="handleReset">重置</el-button>
				<el-button type="primary" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div class="right-top">
				<el-button
					v-has="'billing_unionCollection_createUnionSendFile'"
					v-show="activeTab === 'give'"
					type="primary"
					@click="createUnionSendFile"
				>
					生成送盘文件
				</el-button>
			</div>
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				showPage
				:current-page="pageData.current"
				@current-page-change="handlePageChange"
			>
				<template v-slot:giveOperation="{ row }">
					<div class="row-actions">
						<el-button
							v-if="row.status !== sendFileStatusEnum.已取消 && row.status !== sendFileStatusEnum.生成中"
							type="text"
							@click="gotoBillDetail(row)"
						>
							查看账单
						</el-button>
						<el-button
							v-has="'billing_unionCollection_queryUnionCard'"
							type="text"
							@click="queryUnionCard(row)"
						>
							查看新增用户
						</el-button>
						<el-button
							v-has="'billing_unionCollection_queryUnionSendFileDetail'"
							v-if="row.status !== sendFileStatusEnum.生成中"
							type="text"
							@click="queryUnionSendFileDetail(row)"
						>
							查看文件明细
						</el-button>
						<el-button
							v-has="'billing_unionCollection_cancelUnionSendFile'"
							v-if="
								row.status === sendFileStatusEnum.生成失败 || row.status === sendFileStatusEnum.已生成
							"
							type="text"
							@click="cancelUnionSendFile(row)"
						>
							取消
						</el-button>
						<el-button
							v-has="'billing_unionCollection_sendUnionSendFile'"
							v-if="row.status === sendFileStatusEnum.已生成"
							type="text"
							@click="sendUnionSendFile(row)"
						>
							推送送盘文件
						</el-button>
						<el-button
							v-has="'billing_unionCollection_complete'"
							v-if="row.status === sendFileStatusEnum.已回盘"
							type="text"
							@click="completeUnionCollection(row)"
						>
							完成对账
						</el-button>
					</div>
				</template>
				<template v-slot:backOperation="{ row }">
					<el-button
						v-has="'billing_unionCollection_queryUnionSendFileDetail'"
						v-if="
							row.status === returnFileStatusEnum.解析完成 || row.status === sendFileStatusEnum.对账完成
						"
						type="text"
						@click="gotoBillDetail(row)"
					>
						查看对账明细
					</el-button>
				</template>
			</GcTable>
		</div>
		<!-- 回盘文件 -->
		<GiveBackDialog :show.sync="showDialog" :tab="activeTab" />
		<fileDetail :show.sync="showBillDetailDialog" :rowData="currentRow" />
		<unionCard :show.sync="showUnionCardDialog" :rowData="currentRow" />
	</div>
</template>

<script>
import { getColumn } from './tableColumn'
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import fileDetail from './components/fileDetail.vue'
import unionCard from './components/unionCard.vue'
import GiveBackDialog from './components/GiveBackDialog.vue'
import {
	cancelUnionSendFile,
	completeUnionCollection,
	sendUnionSendFile,
	createUnionSendFile,
	queryUnionReturnFile,
	queryUnionSendFile,
} from '@/api/costManage.api'
import { sendFileStatusEnum, returnFileStatusEnum } from '@/consts/enums'

export default {
	name: 'JoinChargeManage',
	components: {
		unionCard,
		GiveBackDialog,
		fileDetail,
	},
	data() {
		this.sendFileStatusEnum = sendFileStatusEnum
		this.returnFileStatusEnum = returnFileStatusEnum
		return {
			activeTab: 'give',
			formData: {
				fileMonth: '',
				fileDay: '',
			},
			formItems: [],
			formAttrs: {
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
				},
			},
			// 右侧列表
			loading: false,
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			showDialog: false,
			showBillDetailDialog: false,
			showUnionCardDialog: false,
			currentRow: null,
		}
	},
	computed: {
		columns() {
			return getColumn(this)
		},
		tabs() {
			const arr = []
			arr.push({ label: '送盘文件', name: 'give' })
			arr.push({ label: '回盘文件', name: 'back' })
			return arr
		},
	},
	watch: {
		tabs: {
			handler() {
				this.activeTab = this.tabs[0]?.name
			},
			immediate: true,
		},
	},
	mounted() {
		this.handleChangeTab()
	},
	methods: {
		async getList() {
			this.loading = true
			this.currentRow = null
			try {
				const { current, size } = this.pageData
				const params = trimParams(removeNullParams(this.formData))
				Object.assign(params, {
					current,
					size,
				})
				const apiMethods = {
					give: params => queryUnionSendFile(params),
					back: params => queryUnionReturnFile(params),
				}
				const apiMethod = apiMethods[this.activeTab]
				const { total = 0, records = [] } = await apiMethod(params)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				this.$message.error(error.message || '查询失败！')
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		createUnionSendFile() {
			this.$confirm(
				'生成送盘文件后，相关账单将被锁定，无法进行减免、调整以及部分缴费，请确认账单是否调整完毕!',
				'生成送盘文件确认',
			).then(() => {
				createUnionSendFile()
					.then(() => {
						this.$message.success('生成送盘文件成功')
						this.getList()
					})
					.catch(error => {
						this.$message.error(error.message || '生成送盘文件失败')
					})
			})
		},
		sendUnionSendFile(row) {
			this.$confirm('将对该文件进行送盘，请确认是否进行送盘文件操作？', '发送送盘文件确认').then(() => {
				sendUnionSendFile({
					batchId: row.batchId,
				})
					.then(() => {
						this.$message.success('发送送盘文件成功')
						this.getList()
					})
					.catch(error => {
						this.$message.error(error.message || '发送送盘文件失败')
					})
			})
		},
		completeUnionCollection(row) {
			this.$confirm('完成对账后，将作为结账处理，结账后不能进行手动销账！', '完成对账确认').then(() => {
				completeUnionCollection({
					batchId: row.batchId,
				})
					.then(() => {
						this.$message.success('完成对账操作成功！')
						this.getList()
					})
					.catch(error => {
						this.$message.error(error.message || '完成对账操作失败！')
					})
			})
		},
		queryUnionSendFileDetail(row) {
			this.showBillDetailDialog = true
			this.currentRow = row
		},
		queryUnionCard(row) {
			this.showUnionCardDialog = true
			this.currentRow = row
		},
		cancelUnionSendFile(row) {
			this.$confirm('请谨慎取消, 取消送盘文件后, 将导致回盘文件无法对账！', '确认要取消这份送盘文件吗？').then(
				() => {
					cancelUnionSendFile({
						batchId: row.batchId,
					})
						.then(() => {
							this.$message.success('取消成功')
							this.getList()
						})
						.catch(error => {
							this.$message.error(error.message || '取消失败')
						})
				},
			)
		},
		handleChangeTab() {
			if (this.activeTab === 'give') {
				this.formData.fileDay = ''
				this.formItems = [
					{
						type: 'el-date-picker',
						label: '送盘文件生成月',
						prop: 'fileMonth',
						attrs: {
							type: 'month',
							valueFormat: 'yyyy-MM',
						},
					},
				]
			} else {
				this.formItems = [
					{
						type: 'el-date-picker',
						label: '回盘日期',
						prop: 'fileDay',
						attrs: {
							type: 'date',
							valueFormat: 'yyyy-MM-dd',
						},
					},
					{
						type: 'el-date-picker',
						label: '对账文件月份',
						prop: 'fileMonth',
						attrs: {
							type: 'month',
							valueFormat: 'yyyy-MM',
						},
					},
				]
			}
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.handleSearch()
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		gotoBillDetail(row) {
			if (this.activeTab === 'give') {
				this.$router.push({
					name: 'UnionChargebackRecords',
					query: {
						fileMonth: row.fileMonth,
					},
				})
			} else {
				this.$router.push({
					name: 'UnionChargebackRecords',
					query: {
						fileDay: row.fileDay,
						fileMonth: row.fileMonth,
						orgCode: row.orgCode,
					},
				})
			}
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.handleSearch()
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.handlePageChange({ page: 1 })
		},
		exportData() {
			this.$message.success('导出成功')
		},
	},
}
</script>

<style lang="scss" scoped>
.page-left {
	padding-top: 0;
	padding-left: 0;
	padding-right: 0;
	.btn-group {
		padding: 0 20px;
		.el-button {
			width: 50%;
		}
	}
	// 隐掉tab的边框
	::v-deep {
		.el-tabs {
			width: 100%;
			height: 48px;
			box-shadow: none;
			border: none;
			border-radius: 4px 4px 0 0;
			overflow: hidden;
			.el-tabs__content {
				display: none;
			}
		}
		.el-tabs--border-card > .el-tabs__header {
			border: none;
			height: 38px;
			margin-bottom: 10px;
			.el-tabs__nav {
				display: flex;
				align-items: center;
				width: 100%;
				border: none;
				height: 38px;
				.el-tabs__item {
					margin: 0;
					background: #e1ebfa;
					border: none;
					font-size: 14px;
					color: #6d7480;
					flex: 1;
					padding: 0;
					text-align: center;
					height: 38px;
					&.is-active {
						background: #ffffff;
						font-weight: 500;
						color: #2f87fe;
					}
				}
			}
		}
		.el-tabs__item:focus.is-active.is-focus:not(:active) {
			-webkit-box-shadow: none;
			box-shadow: none;
		}
		.el-form {
			padding: 0 20px;
		}
	}
}
.right-top {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-bottom: 12px;
}
.row-actions {
	display: flex;
	gap: 0 10px;
	flex-wrap: wrap;
	&::v-deep {
		.el-button {
			margin-left: 0 !important;
		}
	}
}
</style>
