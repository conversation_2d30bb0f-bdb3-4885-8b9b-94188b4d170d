<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-11 15:00:52
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 16:08:20
-->
<template>
	<div class="wrapper">
		<div class="left">
			<RealMeter ref="realMeter" :type="type" :top-params="topParams" @click="handleRowClick" />
		</div>
		<div class="right">
			<VirtualMeter :data="meterRowData" :top-params="topParams" />
		</div>
	</div>
</template>

<script>
import RealMeter from './real-meter/index.vue'
import VirtualMeter from './virtual-meter/index.vue'
export default {
	name: '',
	props: {
		type: Number,
		topParams: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	components: { RealMeter, VirtualMeter },
	data() {
		return {
			meterRowData: null,
		}
	},
	computed: {},
	created() {},
	methods: {
		handleRowClick(data) {
			this.meterRowData = data
		},
		getList(curPage) {
			this.$refs.realMeter.getList(curPage)
		}
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	flex: 1;
	display: flex;
	height: 0;
	gap: 10px;
	.left {
		width: 0;
		flex: 1;
	}
	.right {
		width: 0;
		flex: 1;
	}
}
</style>
