export function getFormItems(_this) {
	const communityFormItem = [
		{
			type: 'el-select',
			label: '所属市',
			prop: 'cityCode',
			options: [],
			events: {
				change: value => {
					const regionIndex = _this.formItems.findIndex(item => item.prop === 'regionCode')
					const streetIndex = _this.formItems.findIndex(item => item.prop === 'streetCode')
					_this.formItems[regionIndex].options = []
					_this.formItems[streetIndex].options = []
					_this.formData.regionCode = ''
					_this.formData.streetCode = ''
					if (value) {
						_this._getCityOriRegionData(value, 'regionCode')
					}
				},
			},
		},
		{
			type: 'el-select',
			label: '区/县',
			prop: 'regionCode',
			options: [],
			events: {
				change: value => {
					const streetIndex = _this.formItems.findIndex(item => item.prop === 'streetCode')
					_this.formItems[streetIndex].options = []
					_this.formData.streetCode = ''
					if (value) {
						_this._getStreetData(value)
					}
				},
			},
		},
		{
			type: 'el-select',
			label: '街道/乡镇',
			prop: 'streetCode',
			options: [],
		},
		{
			type: 'el-input',
			label: '小区/村庄',
			prop: 'addressAreaName',
		},
	]
	const streetFormItem = [
		{
			type: 'el-select',
			label: '所属市',
			prop: 'cityCode',
			options: [],
			events: {
				change: value => {
					const regionIndex = _this.formItems.findIndex(item => item.prop === 'regionCode')
					_this.formItems[regionIndex].options = []
					_this.formData.regionCode = ''
					if (value) {
						_this._getCityOriRegionData(value, 'regionCode')
					}
				},
			},
		},
		{
			type: 'el-select',
			label: '区/县',
			prop: 'regionCode',
			options: [],
		},
		{
			type: 'el-input',
			label: '街道/乡镇',
			prop: 'addressAreaName',
		},
	]
	const regionFormItem = [
		{
			type: 'el-select',
			label: '所属市',
			prop: 'cityCode',
			options: [],
		},
		{
			type: 'el-input',
			label: '区/县',
			prop: 'regionName',
		},
	]
	let formItems = []
	if (_this.tab === 'community') {
		formItems = communityFormItem
	} else if (_this.tab === 'street') {
		formItems = streetFormItem
	} else if (_this.tab === 'region') {
		formItems = regionFormItem
	}

	formItems.forEach(item => {
		_this.$set(_this.formData, item.prop, '')
	})
	return formItems
}
