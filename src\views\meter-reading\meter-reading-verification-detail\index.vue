<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-02 19:07:24
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 15:01:53
-->
<template>
	<div class="container">
		<div class="container-title">
			<gc-model-header
				class="info-title"
				:title="`表册编号：${$route.query.bookNo || '--'}`"
				:icon="require('@/assets/images/icon/title-common-parameters.png')"
			>
				<template v-slot:left>
					<div class="data-item ml12">
						<div class="label">抄表月：</div>
						<div class="value">
							{{ $route.query.date || '--' }}
						</div>
					</div>
				</template>
			</gc-model-header>
		</div>
		<el-radio-group v-model="type">
			<el-radio-button :label="1">复核异常</el-radio-button>
			<el-radio-button
				v-has="'plan-collection_meterReadingReview_getReviewDetailList2'"
				v-if="!isTaskClosed"
				:label="2"
			>
				自动审核通过
			</el-radio-button>
			<el-radio-button
				v-has="'plan-collection_meterReadingReview_getReviewDetailList3'"
				v-if="!isTaskClosed"
				:label="3"
			>
				水量拆分
			</el-radio-button>
			<el-radio-button v-has="'plan-collection_meterReadingReview_getReviewDetailList4'" :label="4">
				复核通过
			</el-radio-button>
		</el-radio-group>
		<Abnormal v-if="type === 1" :type="type" />
		<AutoPass v-if="type === 2" :type="type" />
		<WaterSplit v-if="type === 3" :type="type" />
		<Pass v-if="type === 4" :type="type" />
	</div>
</template>

<script>
import Abnormal from './abnormal/index.vue'
import AutoPass from './auto-pass/index.vue'
import Pass from './pass/index.vue'
import WaterSplit from './water-split/index.vue'
export default {
	name: '',
	components: { Abnormal, AutoPass, Pass, WaterSplit },
	data() {
		return {
			type: 1,
		}
	},
	computed: {
		isTaskClosed() {
			return this.$route.query.taskStatus === '3'
		},
	},
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
}
.container-title {
	display: flex;
	align-items: center;
}
.el-radio-group {
	padding: 0 20px;
}
.data-item {
	display: flex;
	align-items: center;
	.label {
		color: #4e4e4e;
		font-size: 14px;
	}
	.value {
		font-weight: bold;
	}
}
.ml12 {
	margin-left: 12px;
}
</style>
