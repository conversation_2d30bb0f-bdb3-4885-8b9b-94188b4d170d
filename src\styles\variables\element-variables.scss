/*  全局修改element-ui主题颜色，对应变量可在 element-ui/packages/theme-chalk/src/common/var.scss 文件中查找 */

/* 颜色 */
$--color-primary: $base-color-blue !default;
$--color-success: $base-color-green !default;
$--color-warning: $base-color-yellow !default;
$--color-danger: $base-color-red !default;
$--color-info: $base-color-grey !default;

/* 按钮 */
$--button-font-size: $base-font-size-default !default;

/* Tooltip
-------------------------- */
$--tooltip-bg-color: $base-color-6;
/// color|1|Color|0
$--tooltip-fill: $--tooltip-bg-color !default;
/// color|1|Color|0
$--tooltip-color: $base-color-white !default;
/// color||Color|0
$--tooltip-border-color: $--tooltip-bg-color !default;
/// padding||Spacing|3
$--tooltip-padding: 12px !default;

/* message */
$--message-min-width: 200px !default;
$--message-padding: 0 30px !default;
$--message-background-color: #12b3c7 !default;

/* 动画 */
$--all-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) !default;

/* 改变 icon 字体路径变量，必需 */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

// @import 'element-ui/packages/theme-chalk/src/index';

/*
  解决element图标乱码
  分析：scss中引用的字体不能被对应的loader正常解析
  方案：将入口文件拆出来引用，icon.css单独用js进行引入加载
*/
@import "~element-ui/packages/theme-chalk/src/pagination.scss";
@import "~element-ui/packages/theme-chalk/src/dialog.scss";
@import "~element-ui/packages/theme-chalk/src/autocomplete.scss";
@import "~element-ui/packages/theme-chalk/src/dropdown.scss";
@import "~element-ui/packages/theme-chalk/src/dropdown-menu.scss";
@import "~element-ui/packages/theme-chalk/src/dropdown-item.scss";
@import "~element-ui/packages/theme-chalk/src/menu.scss";
@import "~element-ui/packages/theme-chalk/src/submenu.scss";
@import "~element-ui/packages/theme-chalk/src/menu-item.scss";
@import "~element-ui/packages/theme-chalk/src/menu-item-group.scss";
@import "~element-ui/packages/theme-chalk/src/input.scss";
@import "~element-ui/packages/theme-chalk/src/input-number.scss";
@import "~element-ui/packages/theme-chalk/src/radio.scss";
@import "~element-ui/packages/theme-chalk/src/radio-group.scss";
@import "~element-ui/packages/theme-chalk/src/radio-button.scss";
@import "~element-ui/packages/theme-chalk/src/checkbox.scss";
@import "~element-ui/packages/theme-chalk/src/checkbox-button.scss";
@import "~element-ui/packages/theme-chalk/src/checkbox-group.scss";
@import "~element-ui/packages/theme-chalk/src/switch.scss";
@import "~element-ui/packages/theme-chalk/src/select.scss";
@import "~element-ui/packages/theme-chalk/src/button.scss";
@import "~element-ui/packages/theme-chalk/src/button-group.scss";
@import "~element-ui/packages/theme-chalk/src/table.scss";
@import "~element-ui/packages/theme-chalk/src/table-column.scss";
@import "~element-ui/packages/theme-chalk/src/date-picker.scss";
@import "~element-ui/packages/theme-chalk/src/time-select.scss";
@import "~element-ui/packages/theme-chalk/src/time-picker.scss";
@import "~element-ui/packages/theme-chalk/src/popover.scss";
@import "~element-ui/packages/theme-chalk/src/tooltip.scss";
@import "~element-ui/packages/theme-chalk/src/message-box.scss";
@import "~element-ui/packages/theme-chalk/src/breadcrumb.scss";
@import "~element-ui/packages/theme-chalk/src/breadcrumb-item.scss";
@import "~element-ui/packages/theme-chalk/src/form.scss";
@import "~element-ui/packages/theme-chalk/src/form-item.scss";
@import "~element-ui/packages/theme-chalk/src/tabs.scss";
@import "~element-ui/packages/theme-chalk/src/tab-pane.scss";
@import "~element-ui/packages/theme-chalk/src/tag.scss";
@import "~element-ui/packages/theme-chalk/src/tree.scss";
@import "~element-ui/packages/theme-chalk/src/alert.scss";
@import "~element-ui/packages/theme-chalk/src/notification.scss";
@import "~element-ui/packages/theme-chalk/src/slider.scss";
@import "~element-ui/packages/theme-chalk/src/loading.scss";
@import "~element-ui/packages/theme-chalk/src/row.scss";
@import "~element-ui/packages/theme-chalk/src/col.scss";
@import "~element-ui/packages/theme-chalk/src/upload.scss";
@import "~element-ui/packages/theme-chalk/src/progress.scss";
@import "~element-ui/packages/theme-chalk/src/spinner.scss";
@import "~element-ui/packages/theme-chalk/src/message.scss";
@import "~element-ui/packages/theme-chalk/src/badge.scss";
@import "~element-ui/packages/theme-chalk/src/card.scss";
@import "~element-ui/packages/theme-chalk/src/rate.scss";
@import "~element-ui/packages/theme-chalk/src/steps.scss";
@import "~element-ui/packages/theme-chalk/src/step.scss";
@import "~element-ui/packages/theme-chalk/src/carousel.scss";
@import "~element-ui/packages/theme-chalk/src/scrollbar.scss";
@import "~element-ui/packages/theme-chalk/src/carousel-item.scss";
@import "~element-ui/packages/theme-chalk/src/collapse.scss";
@import "~element-ui/packages/theme-chalk/src/collapse-item.scss";
@import "~element-ui/packages/theme-chalk/src/cascader.scss";
@import "~element-ui/packages/theme-chalk/src/color-picker.scss";
@import "~element-ui/packages/theme-chalk/src/transfer.scss";
@import "~element-ui/packages/theme-chalk/src/container.scss";
@import "~element-ui/packages/theme-chalk/src/header.scss";
@import "~element-ui/packages/theme-chalk/src/aside.scss";
@import "~element-ui/packages/theme-chalk/src/main.scss";
@import "~element-ui/packages/theme-chalk/src/footer.scss";
@import "~element-ui/packages/theme-chalk/src/timeline.scss";
@import "~element-ui/packages/theme-chalk/src/timeline-item.scss";
@import "~element-ui/packages/theme-chalk/src/link.scss";
@import "~element-ui/packages/theme-chalk/src/divider.scss";
@import "~element-ui/packages/theme-chalk/src/image.scss";
@import "~element-ui/packages/theme-chalk/src/calendar.scss";
@import "~element-ui/packages/theme-chalk/src/backtop.scss";
@import "~element-ui/packages/theme-chalk/src/infinite-scroll.scss";
@import "~element-ui/packages/theme-chalk/src/page-header.scss";
@import "~element-ui/packages/theme-chalk/src/cascader-panel.scss";
@import "~element-ui/packages/theme-chalk/src/avatar.scss";
@import "~element-ui/packages/theme-chalk/src/drawer.scss";
@import "~element-ui/packages/theme-chalk/src/popconfirm.scss";
