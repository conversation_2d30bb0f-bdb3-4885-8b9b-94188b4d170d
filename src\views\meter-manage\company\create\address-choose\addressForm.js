export function getFormItems(_this) {
	const baseFormItemsArr = [
		{
			type: 'el-select',
			label: '区/县',
			prop: 'regionCode',
			options: [],
			attrs: {
				col: 8,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'regionCode'),
			},
		},
		{
			type: 'el-select',
			label: '街道/乡镇',
			prop: 'streetCode',
			options: [],
			attrs: {
				col: 8,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'streetCode'),
			},
		},
		{
			type: 'el-select',
			label: '小区/村庄',
			prop: 'communityCode',
			options: [],
			attrs: {
				col: 8,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'communityCode'),
			},
		},
		{
			type: 'el-select',
			label: '楼栋',
			prop: 'buildingCode',
			options: [],
			attrs: {
				col: 8,
			},
			events: {
				change: value => _this.handleChangeAddress(value, 'buildingCode'),
			},
		},

		{
			type: 'slot',
			slotName: 'fullAddress',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'slot',
			slotName: 'otherInfo',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-input',
			label: '自来水编号',
			prop: 'tapWaterNo',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '管网编号',
			prop: 'pipeNetworkCode',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: 'GIS编号',
			prop: 'gisCode',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '压力区',
			prop: 'pressureZone',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '房屋建设年代',
			prop: 'houseYear',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '层数',
			prop: 'floorNum',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '站点号',
			prop: 'stationNo',
			attrs: {
				col: 8,
			},
		},
	]

	return baseFormItemsArr
}
