<template>
	<div :class="'search-container-' + theme.layout">
		<gc-icon icon="icon-overall-search" @click="openSearchDialog" />
		<!-- 搜索弹窗 -->
		<gc-search-dialog
			:isGlobal="true"
			title=" "
			:dialogVisible="showSearchDialog"
			:search-condition="searchCondition"
			:active-condition.sync="activeCondition"
			:filter-condition-visible="false"
			:menuVisible.sync="menuVisible"
			:menuList.sync="menuList"
			:loading="loading"
			:page="page"
			:cityCode="curCityCode"
			:valueObj="valueObj"
			append-to-body
			@public-search="searchTable"
			@dialog-close="dialogClose"
			@page-change="pageChange"
		></gc-search-dialog>
	</div>
</template>

<script>
import { mapGetters } from 'vuex'
import { apiGetArchivesList } from '@/api/archives.api'
import { searchKeys as searchCondition } from '@/consts/search'
import identity from '@/mixin/identity.js'
export default {
	name: 'GcSearchOld',
	mixins: [identity],
	data() {
		return {
			showSearchDialog: false,
			// 搜索弹窗tab数据
			searchCondition,
			activeCondition: 'archivesNo',
			menuList: [],
			menuVisible: false,
			page: {},
			loading: false,
			curCityCode: '',
			valueObj: {},
			queryParam: {},
		}
	},
	computed: {
		...mapGetters({
			theme: 'settings/theme',
		}),
	},
	watch: {
		showSearchDialog(val) {
			if (val) {
				this.curCityCode = this.$store.getters.tenant?.address?.city
			}
		},
	},

	methods: {
		openSearchDialog() {
			if (this.userLevel == 0 && !this.tenantId) {
				this.$message.warning('请先选择租户后再使用此功能')
				return
			}
			// FEAT 3.7.0取消跨域运维角色
			if (this.isCrossDomain && !this.userInfo.orgId) {
				this.$message.warning('请先选择组织机构后再使用此功能')
				return
			}
			this.showSearchDialog = true
		},
		searchTable(params) {
			if (params['key']) {
				this.queryParam = {
					[params['key']]: params['value'].trim(),
				}
				if (!params['value'] || !params['value'].trim()) {
					this.$message.error('请输入' + params['label'])
					return
				}
				if (params['key'] == 'userName' && params['value'].length < 2) {
					this.$message.error('用户名称搜索关键字请至少输入两位')
					return
				}
			}
			if (!params['key'] && params.length > 2) {
				this.queryParam = {} // 搜索条件切换到表具地址，清空之前的请求参数
				//表具地址
				let fulladdress = ''
				params.map(it => {
					this.queryParam[it['key']] = it['value'].trim()
					fulladdress += it['value'].trim()
				})
				if (!fulladdress) {
					this.$message.error('请输入表具地址')
					return
				}
			}
			if (this.queryParam['addressName'] && this.queryParam['addressName'].length < 2) {
				this.$message.error('详细地址搜索关键字请至少输入两位')
				return
			}
			if (this.userLevel == 0) {
				this.queryParam['tenantId'] = this.tenantId
			}
			this.loading = true
			this.menuVisible = true
			apiGetArchivesList({
				...this.queryParam,
				current: params.page || 1,
				size: 3,
			})
				.then(res => {
					this.menuList = res.records
					this.loading = false
					const { current, size, total } = res
					this.page = { current, size, total }
				})
				.catch(() => {
					this.loading = false
				})
		},
		dialogClose() {
			this.menuList = []
			this.valueObj = {}
			this.queryParam = {}
			this.showSearchDialog = false
		},
		pageChange(page) {
			this.page.current = page
			this.searchTable()
		},
	},
}
</script>

<style lang="scss" scoped>
.icon-overall-search {
	margin-left: 26px;
	cursor: pointer;
}
.search-container-horizontal {
	color: $base-color-white;
}
</style>
