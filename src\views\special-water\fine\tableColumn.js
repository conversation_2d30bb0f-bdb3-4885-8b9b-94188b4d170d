import { getfilterName } from '@/utils'
export function getColumn(_) {
	return [
		{
			key: 'orgName',
			name: '营业分公司',
			tooltip: true,
		},
		{
			key: 'createTime',
			name: '登记时间',
			tooltip: true,
			width: 160,
		},
		{
			key: 'archivesNo',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'userName',
			name: '用户名称',
			tooltip: true,
		},
		{
			key: 'phoneNum',
			name: '手机号码',
			tooltip: true,
		},
		{
			key: 'recoverReason',
			name: '追缴原因',
			tooltip: true,
		},
		{
			key: 'recoverWaterVolume',
			name: '追缴水量',
			tooltip: true,
		},
		{
			key: 'recoverAmount',
			name: '追缴金额',
			tooltip: true,
		},
		{
			key: 'payAmount',
			name: '缴费金额',
			tooltip: true,
		},
		{
			key: 'receivableAmount',
			name: '账单应缴金额',
			tooltip: true,
		},
		{
			key: 'billStatus',
			name: '缴费状态',
			tooltip: true,
			render: (h, row) => {
				const { billStatus = [] } = _.$store.getters.dataList || {}
				const valueStr = getfilterName(billStatus, row.billStatus, 'sortValue', 'sortName')
				return h('span', {}, valueStr)
			},
		},
		{
			key: 'payDate',
			name: '最后缴费时间',
			tooltip: true,
		},
		{
			key: 'invoiceStatus',
			name: '开票状态',
			tooltip: true,
			render: (h, row) => {
				const { invoiceState = [] } = _.$store.getters.dataList || {}
				const valueStr = getfilterName(invoiceState, row.invoiceStatus, 'sortValue', 'sortName')
				return h('span', {}, valueStr)
			},
		},
		{
			key: 'invoiceNo',
			name: '发票号码',
			tooltip: true,
		},
		{
			key: 'payPerson',
			name: '缴费人',
			tooltip: true,
		},
		{
			hide: !_.$has(['cpm_fines_update', 'cpm_fines_delete', 'cpm_fines_payment', 'cpm_fines_invoice']),
			key: 'deal',
			name: '操作',
			fixed: 'right',
			width: 200,
		},
	]
}
