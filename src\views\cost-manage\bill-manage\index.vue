<template>
	<div class="page-layout">
		<div class="page-left">
			<div class="left-search-container">
				<GcSearchFake
					@handleclick="handleClickFakeSearch"
					@clear="clearFormComponent"
					:valueObj="valueObj"
				></GcSearchFake>
				<!-- 弹窗 -->
				<GcSearchDialogNew
					title="搜索"
					width="900px"
					:dialogVisible.sync="showSearchPopou"
					:search-condition="searchCondition"
					:active-condition.sync="activeCondition"
					:selected-condition="selectedCondition"
					:valueObj="valueObj"
					@delete-one="deleteOne"
					@empty="deleteAll"
					@dialog-close="closeDialog"
					@public-search="searchTable"
				/>
			</div>
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
			<div class="btn-group">
				<el-button style="width: 50%" round @click="handleReset">重置</el-button>
				<el-button type="primary" style="width: 50%" round @click="handleSearch">筛选</el-button>
			</div>
		</div>
		<div class="page-right">
			<div class="btn-container">
				<el-button
					v-has="'payment_invoice_merge-open-invoice'"
					type="primary"
					:disabled="invoiceOpenDisabled"
					@click="showOpenInvoiceDialog('merge')"
				>
					合并开票
				</el-button>
				<el-button
					v-has="'payment_invoice_batch-open-invoice'"
					type="primary"
					:disabled="invoiceOpenDisabled"
					@click="showOpenInvoiceDialog('batch')"
				>
					批量开票
				</el-button>
				<el-button type="primary" disabled>导出银行送盘文件</el-button>
				<el-button type="primary" disabled>导出联合收费送盘文件</el-button>
				<el-button type="primary" @click="handlePay" :disabled="isPayButtonDisabled">缴费</el-button>
			</div>
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				needType="selection"
				:selectable="selectable"
				@selectChange="selectChange"
				@current-page-change="handleChangePage"
			>
				<template v-slot:operate="{ row }">
					<div class="bill-record-actions">
						<el-button
							v-has="'billing_bill-adjust_reduction'"
							type="text"
							size="medium"
							v-show="feeAdjustable(row)"
							@click="handleClickRow(row, 'reduction')"
						>
							减免
						</el-button>
						<el-button
							v-has="'billing_bill-adjust_adjustment'"
							type="text"
							size="medium"
							v-show="feeAdjustable(row)"
							@click="handleClickRow(row, 'adjustment')"
						>
							费用调整
						</el-button>
						<el-button
							v-has="'billing_bill_clear'"
							type="text"
							size="medium"
							v-show="[2, 3, 5].includes(row.billStatus)"
							@click="handleSinglePay(row)"
						>
							缴费
						</el-button>
						<el-button
							v-has="'billing_bill-adjust_partial-payment'"
							type="text"
							size="medium"
							v-show="
								[2, 5].includes(row.billStatus) && row.pushFlag !== 1 && ![5, 6].includes(row.billType)
							"
							@click="handleClickRow(row, 'partial')"
						>
							部分缴费
						</el-button>
						<el-button
							v-has="'billing_bill-adjust_record'"
							type="text"
							size="medium"
							v-show="[2, 3, 4].includes(row.billType)"
							@click="goToRecordHistory(row, 'partial')"
						>
							账单追溯
						</el-button>
						<el-button
							v-has="'payment_invoice_open-invoice'"
							type="text"
							v-show="
								row.billStatus === 4 &&
								(row.invoiceStatus === 0 || row.invoiceStatus === 2) &&
								row.billType !== 6
							"
							@click="
								billList = [row]
								showOpenInvoiceDialog('single')
							"
						>
							开票
						</el-button>
					</div>
				</template>
			</GcTable>
		</div>
		<PaymentInvoiceDialog
			ref="openInvoiceDialog"
			:type="openInvoiceType"
			:billList="billList"
			:api="invoiceApi"
			:show.sync="openInvoiceDialogShow"
			@success="handleOpenInvoiceSuccess"
		/>
	</div>
</template>

<script>
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { getColumn } from './tableColumn'
import { apiGetBillList, mergeOpenInvoice, singleOpenInvoice, batchOpenInvoice } from '@/api/costManage.api'
import PaymentInvoiceDialog from '@/components/PaymentInvoiceDialog'
export default {
	components: {
		PaymentInvoiceDialog,
	},
	data() {
		this.invoiceApi = {
			single: singleOpenInvoice,
			merge: mergeOpenInvoice,
			batch: batchOpenInvoice,
		}
		return {
			// 左侧查询
			formData: {
				orgCode: '',
				billStatus: '',
				exceedSheetDate: '',
				billDate: '',
				chargingMethod: '',
				year: this.dayjs().format('YYYY'),
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: this.$store.getters.orgList,
				},
				{
					type: 'el-date-picker',
					label: '年份',
					prop: 'year',
					attrs: {
						type: 'year',
						valueFormat: 'yyyy',
						clearable: false,
						pickerOptions: {
							disabledDate(time) {
								const currentYear = new Date().getFullYear()
								const minYear = 2007
								return time.getFullYear() < minYear || time.getFullYear() > currentYear + 1
							},
						},
					},
				},
				{
					type: 'el-select',
					label: '账单状态',
					prop: 'billStatus',
					options: this.$store.getters.dataList.billStatus
						? this.$store.getters.dataList.billStatus
								.filter(item => item.sortValue != 9)
								.map(item => {
									return {
										label: item.sortName,
										value: item.sortValue,
									}
								})
						: [],
				},
				{
					type: 'el-select',
					label: '是否过结账日',
					prop: 'exceedSheetDate',
					options: [
						{ label: '超过', value: 1 },
						{ label: '未超过', value: 0 },
					],
				},
				{
					type: 'el-date-picker',
					label: '账期',
					prop: 'billDate',
					attrs: {
						type: 'month',
						valueFormat: 'yyyy-MM',
					},
				},
				{
					type: 'el-select',
					label: '收费方式',
					prop: 'chargingMethod',
					options: this.$store.getters.dataList.chargingMethod
						? this.$store.getters.dataList.chargingMethod.map(item => {
								return {
									label: item.sortName,
									value: item.sortValue,
								}
						  })
						: [],
				},
				{
					type: 'el-select',
					label: '开票状态',
					prop: 'invoiceStatus',
					options: this.$store.getters.dataList.invoiceState
						? this.$store.getters.dataList.invoiceState.map(item => {
								return {
									label: item.sortName,
									value: item.sortValue - 0,
								}
						  })
						: [],
				},
			],
			formAttrs: {
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
					year: [ruleRequired('必填')],
				},
			},
			valueObj: {
				value: null,
			},
			showSearchPopou: false,
			searchCondition: [
				{
					key: 'archivesIdentity',
					label: '表卡编号',
				},
				{
					key: 'enterpriseNumber',
					label: '企业编号',
				},
				{
					key: 'userName',
					label: '用户名称',
				},
				{
					key: 'bookNo',
					label: '表册编号',
				},
				{
					key: 'collectionAgreementNumber',
					label: '托收协议号',
				},
				{
					key: 'billNo',
					label: '账单编号',
				},
				{
					key: 'userMobile',
					label: '手机号',
				},
				{
					key: 'certificateNo',
					label: '证件号码',
				},
				{
					key: 'taxpayerIdentity',
					label: '纳税人编号',
				},
			],
			activeCondition: 'archivesIdentity',
			selectedCondition: [],
			// 右侧表格
			columns: getColumn(this),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			billList: [],
			loading: false,
			openInvoiceType: '',
			openInvoiceDialogShow: false,
		}
	},
	computed: {
		// 缴费按钮是否禁用
		isPayButtonDisabled() {
			return (
				!this.billList.length ||
				this.billList.some(
					item => item.billStatus != 2 && item.billStatus != 3 && item.billStatus != 5, // 待缴费、已冲正的才能缴费
				)
			)
		},
		orgOptions() {
			return this.formItems[0].options
		},
		invoiceOpenDisabled() {
			if (!this.billList.length) return true
			return this.billList.some(({ billStatus, invoiceStatus, billType }) => {
				// billType === 6 罚没款类型的账单不允许批量开票或合并开票
				return billStatus !== 4 || (invoiceStatus !== 0 && invoiceStatus !== 2) || billType === 6 // 已销账，未开票或者已冲红的才能开票
			})
		},
	},
	watch: {
		orgOptions: {
			handler: function (newVal) {
				if (newVal && newVal.length > 0) {
					this.formData.orgCode = newVal[0].value
					this.$nextTick(() => {
						this.handleSearch()
					})
				}
			},
			deep: true,
			immediate: true,
		},
	},
	activated() {
		if (this.$route.query) {
			const key = Object.keys(this.$route.query)[0]
			if (key) {
				const value = this.$route.query[key]
				this.activeCondition = key
				this.valueObj = { key, value }
			}
		}
		this.handleSearch()
	},
	methods: {
		feeAdjustable(row) {
			return (
				[0, 2, 5].includes(row.billStatus) &&
				row.pushFlag !== 1 &&
				row.invoiceStatus === 0 &&
				![5, 6].includes(row.billType) &&
				// 不为历史账单 历史账单标识：0-非历史账单，1-历史账单
				row.historicalBilling !== 1
			)
		},
		getSearchData() {
			const data = trimParams(removeNullParams(this.formData))
			const newArr = []
			Object.keys(data).forEach(key => {
				const obj = this.formItems.find(item => item.prop === key)
				if (obj && obj.options) {
					const selectItem = obj.options.find(subItem => subItem.value === data[key])
					newArr.push({
						key,
						value: selectItem.label,
					})
				} else {
					newArr.push({
						key,
						value: data[key],
					})
				}
			})
			this.selectedCondition = newArr
		},
		clearFormComponent() {
			this.valueObj = { value: null }
			this.activeCondition = 'archivesIdentity'
		},
		deleteOne(key) {
			this.selectedCondition = this.selectedCondition.filter(item => item.key !== key)
			this.formData[key] = ''
		},
		deleteAll() {
			this.$refs.formRef.resetForm()
			this.selectedCondition = []
		},
		closeDialog(params) {
			this.valueObj = params
		},
		searchTable(params) {
			this.valueObj = params
			this.pageData.current = 1
			this.handleSearch()
		},
		handleClickFakeSearch() {
			this.showSearchPopou = true
			this.getSearchData()
		},
		handleReset() {
			this.$refs.formRef.resetFields()
			this.selectedCondition = []
			this.clearFormComponent()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.pageData.current = 1
			this._apiGetBillList()
		},
		// -------------右侧表格交互
		// 待缴费、已冲正、已推送禁止勾选删除
		selectable(row) {
			return [2, 4, 5].includes(row.billStatus)
		},
		async _apiGetBillList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, {
					current,
					size,
				})
				if (this.valueObj.value) {
					formParams[this.valueObj.key] = this.valueObj.value
				}
				if (formParams.billStatus) {
					formParams['billStatusList'] = [formParams.billStatus]
					delete formParams.billStatus
				}

				const { records, total } = await apiGetBillList(formParams)
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.log(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleChangePage({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this._apiGetBillList()
		},
		// 多选
		selectChange(arr) {
			this.billList = arr
		},
		// 点击行缴费
		handleSinglePay(row) {
			this.billList = [row]
			this.handlePay()
		},
		handlePay() {
			const ids = this.billList.map(item => item.billId)
			this.$router.push({
				path: '/costManage/paymentPage',
				query: {
					ids: ids?.length ? ids.join(',') : '',
					year: this.formData.year,
				},
			})
		},
		// 点击行
		handleClickRow(row, type) {
			if (type === 'reduction') {
				this.$router.push({
					path: '/costManage/costRelief',
					query: {
						id: row.billId,
						year: row.year,
					},
				})
			} else if (type === 'adjustment') {
				this.$router.push({
					path: '/costManage/feeAdjustment',
					query: {
						id: row.billId,
						year: row.year,
					},
				})
			} else if (type === 'partial') {
				this.$router.push({
					path: '/costManage/partialPayment',
					query: {
						id: row.billId,
						year: row.year,
					},
				})
			}
		},
		goToRecordHistory(row) {
			this.$router.push({
				path: '/costManage/BillTrace',
				query: {
					billNo: row.billNo,
				},
			})
		},
		showOpenInvoiceDialog(type) {
			this.openInvoiceType = type
			this.openInvoiceDialogShow = true
		},
		handleOpenInvoiceSuccess() {
			this.handleSearch()
		},
	},
}
</script>

<style lang="scss" scoped>
.bill-record-actions {
	display: flex;
	flex-wrap: wrap;
	gap: 0 10px;
}
.bill-record-actions .el-button {
	margin-left: 0;
}
.left-search-container {
	margin-bottom: 20px;
}
.btn-container {
	margin-bottom: 10px;
}
</style>
