<template>
	<div
		class="gc-group-detail"
		:style="{
			maxWidth: data.row == 1 ? '' : (data.row || 3) * 250 + 'px',
		}"
	>
		<page-group :groupTitle="data.title">
			<group-detail-item
				:style="`width: ${(100 / (data.row || 3)) * Math.min(item.col || 1, data.row || 3)}%`"
				v-for="item in data.list || []"
				:key="item.key"
				:isEllipsis="item.isEllipsis"
				:name="item.key"
			>
				<div v-if="$slots[item.slot]">
					<slot :name="item.slot"></slot>
				</div>
				<p v-else :title="item.isEllipsis && item.value" :style="vauleStyle">
					{{ item.value || item.value === 0 ? item.value : '--' }}
				</p>
			</group-detail-item>
		</page-group>
	</div>
</template>

<script>
import PageGroup from './pageGroup'
import GroupDetailItem from './groupDetailItem'
export default {
	name: 'GcGroupDetail',
	components: {
		PageGroup,
		GroupDetailItem,
	},
	props: {
		data: {
			type: Object,
			default: () => {
				return {
					title: '',
					row: 3,
					list: [],
				}
			},
		},
		vauleStyle: Object,
	},
}
</script>
