<template>
	<div class="gc-customize-col">
		<el-button @click="emitShowDialog" :class="['customizeColBtn', { disabled }]" :disabled="disabled">
			自定义列
		</el-button>
		<gc-el-dialog
			:show.sync="showDialog"
			title="自定义列"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:destroy-on-close="true"
			@close="handleDialogClose"
			@cancel="handleDialogClose"
			@ok="handleDialogConfirm"
			width="640px"
		>
			<div class="customizeCol-wrapper">
				<el-checkbox-group class="col-checkbox" v-model="checkedOptions" @change="handleCheckedOptionsChange">
					<el-checkbox v-for="item in options" :label="item.name" :key="item.key" :disabled="item.disable">
						{{ item.name }}
					</el-checkbox>
				</el-checkbox-group>
			</div>
			<div slot="footer" class="dialog-footer">
				<el-checkbox
					:indeterminate="isIndeterminate"
					v-model="checkAll"
					@change="handleCheckAllChange"
					class="checkAll"
				>
					全选
				</el-checkbox>
				<div class="btn-group">
					<gc-button :btn-type="'three'" @click.native="handleDialogClose">取消</gc-button>
					<gc-button @click.native="handleDialogConfirm">确定</gc-button>
				</div>
			</div>
		</gc-el-dialog>
	</div>
</template>

<script>
const DEBUG = false
export default {
	name: 'gcCustomizeCol',
	components: {},
	props: {
		options: {
			// 全部表头数据，disable表示必须选中的表头
			type: Array,
			default: () => [],
		},
		defaultCheckedOptions: {
			// 默认选中，初次带入的表头
			type: Array,
			default: () => [],
		},
		disabled: {
			type: Boolean,
			default: true,
		},
	},
	watch: {
		defaultCheckedOptions: {
			immediate: true,
			deep: true,
			handler(val) {
				this.checkedOptions = this._.cloneDeep(val)
			},
		},
		checkedOptions: {
			deep: true,
			immediate: true,
			handler() {
				this.checkAll =
					this.checkedOptions.length > 0 && this.checkedOptions.length === this.fullCheckedLabel.length
				this.isIndeterminate = this.getIsIndeterminate()
			},
		},
	},
	data() {
		return {
			checkedOptions: [],
			isIndeterminate: true,
			showDialog: DEBUG,
			checkAll: false,
		}
	},
	computed: {
		fullCheckedLabel() {
			return this.options.map(item => item.name)
		},
		requiredLabel() {
			return this.options.filter(item => item.disable).map(item => item.name)
		},
	},
	created() {},
	mounted() {
		this.initCol()
	},
	methods: {
		emitShowDialog() {
			this.checkedOptions = this._.cloneDeep(this.defaultCheckedOptions)
			this.showDialog = true
		},
		handleDialogConfirm() {
			this.$emit('on-table-header-change', this.checkedOptions)
			this.showDialog = false
		},
		handleDialogClose() {
			this.showDialog = false
		},
		// 初始化表头数据
		initCol() {
			this.checkedOptions = this._.cloneDeep(this.defaultCheckedOptions)

			this.isIndeterminate = this.getIsIndeterminate()
		},
		// 全选 check or not
		handleCheckAllChange(val) {
			this.checkedOptions = val ? this.fullCheckedLabel : this.requiredLabel
			this.isIndeterminate = this.getIsIndeterminate()
		},
		// 表头选项change
		handleCheckedOptionsChange(value) {
			this.checkedOptions = value
			this.isIndeterminate = this.getIsIndeterminate()
		},
		getIsIndeterminate() {
			return this.checkedOptions.length > 0 && this.checkedOptions.length < this.options.length
		},
	},
}
</script>
<style lang="scss" scoped>
.customizeCol-wrapper {
	background: white;
	height: 100%;
	.selectColumn {
		margin-bottom: 20px;
		color: #ababab;
		font-size: 14px;
		.checkAll {
			width: auto;
			float: right;
			margin-bottom: 0;
			margin-right: 40px;
		}
	}
	::v-deep {
		.el-checkbox-group {
			display: flex;
			flex-wrap: wrap;
		}
		.el-checkbox {
			width: 19%;
			margin-bottom: 22px;
			display: flex;
			&.is-disabled .el-checkbox__label {
				color: #4e4e4e;
			}
			.el-checkbox__input {
				margin-top: 3px;
			}
			.el-checkbox__label {
				width: 100%;
				word-break: keep-all;
				white-space: pre-line;
				word-wrap: break-word;
			}
		}
	}
}
::v-deep .el-button {
	padding: 0 16px;
	width: 80px;
	height: 28px;
	margin-right: 16px;
	background: #ffffff;
	border-radius: 4px;
	border: 1px solid #d8d8d8;
	font-size: 12px;
	color: #4e4e4e;
	line-height: 26px;
	box-sizing: border-box;
}
::v-deep .el-button.is-disabled.disabled {
	background: #f2f2f2 !important;
	border-color: #f2f2f2 !important;
	color: #d1d1d1 !important;
}
.dialog-footer {
	display: flex;
	justify-content: space-between;
	.btn-group {
		.gc-button {
			margin-left: 10px;
		}
	}
}
</style>
