<template>
	<div class="container">
		<div class="container-title">
			<gc-model-header
				class="info-title"
				title="虚表分配"
				:icon="require('@/assets/images/icon/title-common-parameters.png')"
			></gc-model-header>
			<div class="btn-group">
				<el-button type="primary" @click="handleCreate">新增虚表</el-button>
				<el-button @click="handleDelete">删除虚表</el-button>
			</div>
		</div>
		<div class="container-radio">
			<span class="label">分配方式：</span>
			<el-radio-group :value="radioValue" @input="changeRadio">
				<el-radio v-for="(item, index) in radioList" :label="item.value" :key="index">
					{{ item.label }}
				</el-radio>
			</el-radio-group>
		</div>
		<div class="container-table">
			<GcTable
				:key="tableKey"
				:columns="columns"
				:table-data="tableData"
				needType="selection"
				:selectable="selectable"
				@selectChange="selectChange"
			>
				<template v-slot:index="{ row, $index }">
					<span>{{ $index + 1 }}</span>
				</template>
				<template v-slot:operateSort="{ row, $index }">
					<i class="el-icon-top move-up" @click="moveUp(row, $index)" v-show="$index !== 0"></i>
					<i
						class="el-icon-bottom move-down"
						@click="moveDown(row, $index)"
						v-show="$index !== tableData.length - 1"
					></i>
				</template>
				<template v-slot:actionSlot="{ row }">
					<el-button
						type="primary"
						size="small"
						v-if="isShowCancellationButton(row)"
						@click="handleCancelButtonClick(row)"
					>
						销卡
					</el-button>
					<el-button
						type="primary"
						size="small"
						v-if="isShowRestoreButton(row)"
						@click="handleRestoreButtonClick(row)"
					>
						恢复表卡
					</el-button>
				</template>
			</GcTable>
		</div>
		<!-- 销卡 -->
		<CardCancel
			:show.sync="showCardCancel"
			:detailData="currentEditData"
			permissionCode="cpm_archives_close2"
			@refresh="handleRefresh"
		/>
		<!-- 恢复表卡 -->
		<CardRecover
			:show.sync="showCardRecover"
			:data="currentEditData"
			permissionCode="cpm_archives_resume-archives4"
			@refresh="handleRefresh"
		/>
	</div>
</template>
<script>
import { distributionModeOptions } from '@/consts/optionList.js'
import { getColumn } from './tableColumn.js'
import { apiGetVirtualArchivesDetail2, apiGetArchivesIdentity, apiGetPriceList_all } from '@/api/meterManage.api.js'
import { v4 as uuidv4 } from 'uuid'
import CardCancel from '@/views/meter-manage/view/components/card-cancel'
import CardRecover from '@/views/meter-manage/view/components/card-recover'
export default {
	name: '',
	components: { CardCancel, CardRecover },
	props: {
		realMeterData: {
			type: Object,
			default: () => {},
		},
		isShow: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			radioValue: 0,
			radioList: distributionModeOptions,
			tableData: [],
			selectableData: [],
			tableKey: 1, // 用于强制刷新表格
			priceOptions: [],
			currentEditData: {},
			showCardCancel: false,
			showCardRecover: false,
		}
	},
	computed: {
		columns() {
			return getColumn(this)
		},
	},
	watch: {
		tableData: {
			handler() {
				this.validateTable()
			},
			deep: true,
		},
		realMeterData: {
			handler(obj) {
				// 新增且有实表已选择
				if (!this.$route.query.archivesId && obj && obj.archivesId) {
					// 该实表拆分过, 获取虚表列表
					if (obj.isVirtual === 1) {
						this._apiGetVirtualArchivesDetail(obj.archivesId)
					} else {
						this._apiGetVirtualArchivesDetail(obj.archivesId, res => {
							if (res && res.length) {
								return
							}
							// 该实表未拆分过, 且虚表列表为空
							if (this.tableData.length === 0) {
								//新增;   创建一条实表数据
								const obj = this.createTableOneData()
								Object.assign(obj, this.realMeterData)

								this.tableData = [obj]
							} else {
								// 该实表未拆分过, 且虚表列表不为空 (TIP: 先选择一个拆分过的实表，查出来虚表列表，再更改实表选择：填写一个为未拆分过的实表，之前的虚表列表还存在)
								// 删除虚表列表
								this.tableData = this.tableData.filter(
									// 接口返回的虚表：有archivesId且虚表类型为1
									item => !(item.archivesId && item.virtualMeterType === 1),
								)
								//替换实表数据
								const realMeterIndex = this.tableData.findIndex(item => item.virtualMeterType === 0)
								if (realMeterIndex !== -1) {
									Object.assign(this.tableData[realMeterIndex], this.realMeterData)
								}
							}
						})
					}
				}
			},
			deep: true,
		},
	},

	created() {},
	methods: {
		// 获取表卡编号
		_apiGetArchivesIdentity() {
			const { bookId, enterpriseNumber, userType, orgCode } = this.realMeterData
			const params =
				userType === 4
					? {
							bookId,
							enterpriseNumber,
							userType,
							orgCode,
					  }
					: {
							bookId,
							userType,
					  }

			return apiGetArchivesIdentity(params)
		},
		// 虚表列表  编辑时获取
		async _apiGetVirtualArchivesDetail(v, callback) {
			const params = {
				archivesId: v,
				tenantId: this.$store.getters.userInfo?.tenantId,
			}
			try {
				const res = await apiGetVirtualArchivesDetail2(params)

				if (!callback || (res && res.length)) {
					this.tableData = res
				}

				if (res && res.length > 0) {
					const realMeter = res.find(item => item.virtualMeterType === 0)
					this.radioValue = realMeter.distributionMode
					// 回显价格信息
					res.map((item, index) => {
						this.assignPriceInfo(index)
					})
				}
				callback && callback(res)
			} catch (error) {
				console.log(error)
			}
		},
		async _apiGetPriceList_all() {
			const { records } = await apiGetPriceList_all()
			this.priceOptions = records.map(item => {
				return {
					label: item.priceCode,
					value: item.priceCode,
					...item,
					disabled: [1, 9].includes(item.enableFlag) ? false : true,
				}
			})
		},
		// 有档案id禁止勾选删除
		selectable(row) {
			return !row.archivesId
		},
		changeRadio(val) {
			this.$confirm('分配方式发生调整，请确认是否继续?')
				.then(() => {
					this.radioValue = val
					this.tableKey += 1
					const obj = this.createTableOneData()
					//  更新表格数据
					this.tableData.map((item, index) => {
						// obj中的属性在item中没有，添加到item中
						for (const key in obj) {
							if (!Object.prototype.hasOwnProperty.call(item, key)) {
								item[key] = obj[key]
							}
						}
						// 将所有数据的编辑状态重置false
						for (const key in item) {
							if (typeof item[key] === 'boolean') {
								item[key] = false
							}
						}
						// 清除可编辑的行的价格信息
						if (!item.archivesId) {
							item.priceCode = ''
							this.assignPriceInfo(index)
						}
						// 分配方式清空
						item.subDistributionMode = ''
						// 用水比例或者占比清空
						item.usageMeasure = ''
						return item
					})
					this.validateTable()
				})
				.catch(() => {})
		},
		selectChange(selection) {
			this.selectableData = selection
		},
		createTableOneData() {
			const obj = this.columns.reduce(
				(acc, item) => {
					acc[item.key] = item.key === 'virtualMeterType' ? 1 : '' // 1虚表  0实表
					return acc
				},
				{ deleteKey: uuidv4() },
			)
			const commonFields = ['isPriceCodeEdit', 'isArchivesIdentityEdit']
			const radioValueFields = {
				0: ['isWaterUseRatioEdit'],
				1: ['isGdWaterEdit'],
				2: ['isSubDistributionModeEdit', 'isAllocationQuotaEdit'],
				3: [],
				4: ['isSubDistributionModeEdit', 'isAllocationQuotaEdit'],
			}
			const fieldsToEdit = [...commonFields, ...(radioValueFields[this.radioValue] || [])]

			fieldsToEdit.forEach(field => {
				obj[field] = false
			})

			return obj
		},
		async handleCreate() {
			try {
				const { archivesIdentity } = await this._apiGetArchivesIdentity()
				const obj = this.createTableOneData()
				if (obj.virtualMeterType === 1) {
					obj.archivesIdentity = archivesIdentity
				}
				this.tableData.push(obj)
			} catch (error) {
				console.log(error)
			}
		},
		handleDelete() {
			if (this.selectableData.length === 0) {
				this.$message.error('请选择要删除的数据')
				return
			}
			const arr = this.tableData.filter(item => {
				return !this.selectableData.some(selectItem => {
					return item.deleteKey === selectItem.deleteKey
				})
			})
			this.tableData = arr
		},
		validateTable() {
			let valid = false
			let errorMessage = ''
			let archivesIdentityPattern = null
			let patternMessage = ''
			if (this.realMeterData.userType === 4) {
				const { archivesIdentity } = this.realMeterData
				const regValue = archivesIdentity.substring(0, 7)
				archivesIdentityPattern = new RegExp(`^${regValue}\\d{4}$`)
				patternMessage = `必须为11位, 且前七位必须为${regValue}`
			} else {
				const { alleyCode } = this.realMeterData
				archivesIdentityPattern = new RegExp(`^${alleyCode}\\d{6}$`)
				patternMessage = `表卡编号必须为9位, 且前三位必须为${alleyCode}`
			}
			const isValidArchivesIdentity = this.tableData.every(item => {
				if (item.archivesStatus === 3) {
					return true //已销档不校验
				}
				return archivesIdentityPattern.test(item.archivesIdentity)
			})

			if (this.radioValue === 0) {
				const totalUsageMeasure = this.tableData.reduce((sum, item) => {
					if (item.archivesStatus === 3) {
						return sum
					} //已销档不校验
					const usageMeasure = item.usageMeasure ? Number(item.usageMeasure) : 0
					return sum + usageMeasure
				}, 0)
				const allFieldsFilled = this.tableData.every(item => {
					if (item.archivesStatus === 3) {
						return true //已销档不校验
					}
					if (item.virtualMeterType === 0) {
						return item.usageMeasure && item.archivesIdentity
					}
					return item.priceCode && (totalUsageMeasure === 100 || item.usageMeasure) && item.archivesIdentity
				})

				if (!allFieldsFilled) {
					errorMessage = '待完善'
				}
				if (totalUsageMeasure !== 100) {
					errorMessage = '用水比例和需等于100'
				}
				if (!isValidArchivesIdentity) {
					errorMessage = patternMessage
				}
				valid = allFieldsFilled && totalUsageMeasure === 100 && isValidArchivesIdentity
			} else if (this.radioValue === 1) {
				const allFieldsFilled = this.tableData.slice(0, -1).every(item => {
					if (item.archivesStatus === 3) {
						return true //已销档不校验
					}
					if (item.virtualMeterType === 0) {
						return item.usageMeasure && item.archivesIdentity
					}
					return item.priceCode && item.usageMeasure && item.archivesIdentity
				})

				// 找到最后一个不是已销档的元素
				const lastElement = this.tableData
					.slice()
					.reverse()
					.find(item => item.archivesStatus !== 3)
				// 检查最后一个元素的 usageMeasure 是否为空
				// 检查最后一个元素的 priceCode 是否根据 virtualMeterType 必填
				const lastElementUsageMeasureEmpty = !lastElement.usageMeasure
				const lastElementPriceCodeValid = lastElement.virtualMeterType === 0 || lastElement.priceCode
				if (!allFieldsFilled || !lastElementPriceCodeValid) {
					errorMessage = '待完善'
				}
				if (!lastElementUsageMeasureEmpty) {
					errorMessage = '最后一块表固定水量需为空'
				}
				if (!isValidArchivesIdentity) {
					errorMessage = patternMessage
				}
				valid =
					allFieldsFilled &&
					lastElementUsageMeasureEmpty &&
					lastElementPriceCodeValid &&
					isValidArchivesIdentity
			} else if (this.radioValue === 2) {
				const allFieldsFilled = this.tableData.every(item => {
					if (item.archivesStatus === 3) {
						return true //已销档不校验
					}
					if (item.virtualMeterType === 0) {
						return item.subDistributionMode != null && item.usageMeasure && item.archivesIdentity
					}
					return (
						item.priceCode && item.subDistributionMode != null && item.usageMeasure && item.archivesIdentity
					)
				})
				// 判断剩余量占比是不是只有一个, 且最后一个是剩余量占比
				const checkRemainRate = array => {
					if (array.length === 0) {
						return false
					}

					const lastElement = array[array.length - 1]
					const remainRateCount = array.filter(item => item.subDistributionMode === 2).length

					return lastElement.subDistributionMode === 2 && remainRateCount === 1
				}
				// 判断总量占比不能之和不能超过100
				const checkTotalRate = array => {
					const totalUsage = array.reduce((sum, item) => {
						if (
							item.archivesStatus !== 3 &&
							item.subDistributionMode !== 2 &&
							item.subDistributionMode !== 1
						) {
							return sum + Number(item.usageMeasure) //已销档、子分配方式为固定量、剩余量占比不校验
						}
						return sum
					}, 0)
					return totalUsage <= 100
				}

				const checkRemainRateValid = checkRemainRate(this.tableData)
				const checkTotalRateValid = checkTotalRate(this.tableData)
				if (!checkRemainRateValid) {
					errorMessage = '剩余量占比只能有一条，且必须为最后一条'
				}
				if (!checkTotalRateValid) {
					errorMessage = '总量占比之和不能超过100'
				}
				if (!allFieldsFilled) {
					errorMessage = '待完善'
				}
				if (!isValidArchivesIdentity) {
					errorMessage = patternMessage
				}
				valid = checkRemainRateValid && checkTotalRateValid && allFieldsFilled && isValidArchivesIdentity
			} else if (this.radioValue === 3) {
				const allFieldsFilled = this.tableData.every(item => {
					if (item.archivesStatus === 3) {
						return true //已销档不校验
					}
					if (item.virtualMeterType === 0) {
						return item.archivesIdentity
					}
					return item.priceCode && item.archivesIdentity
				})
				if (!allFieldsFilled) {
					errorMessage = '待完善'
				}
				if (!isValidArchivesIdentity) {
					errorMessage = patternMessage
				}
				valid = allFieldsFilled && isValidArchivesIdentity
			} else if (this.radioValue === 4) {
				// 所有字段都必填
				const allFieldsFilled = this.tableData.every(item => {
					if (item.archivesStatus === 3) {
						return true //已销档不校验
					}
					if (item.virtualMeterType === 0) {
						return item.subDistributionMode != null && item.usageMeasure && item.archivesIdentity
					}
					return (
						item.priceCode && item.subDistributionMode != null && item.usageMeasure && item.archivesIdentity
					)
				})
				// 必须有固定量和剩余量占比
				const validatePresence = array => {
					const hasMode1 = array.some(item => item.subDistributionMode === 1)
					const hasMode2 = array.some(item => item.subDistributionMode === 2)
					return hasMode1 && hasMode2
				}
				// 判断固定量是不是在剩余量占比之前
				const validateOrder = array => {
					let foundMode2 = false

					for (const item of array) {
						if (item.subDistributionMode === 2) {
							foundMode2 = true
						} else if (foundMode2 && item.subDistributionMode === 1) {
							// 如果已经找到subDistributionMode为2的对象，但当前对象subDistributionMode为1，则顺序不正确
							return false
						}
					}

					return true
				}
				// 判断剩余量占比之和是否为100 （已销档不参与计算）
				const validateUsageMeasure = array => {
					const totalUsageMode2 = array
						.filter(item => item.subDistributionMode === 2 && item.archivesStatus !== 3)
						.reduce((sum, item) => sum + Number(item.usageMeasure), 0)

					return totalUsageMode2 === 100
				}
				const isPresenceValid = validatePresence(this.tableData)
				const isOrderValid = validateOrder(this.tableData)
				const isUsageMeasureValid = validateUsageMeasure(this.tableData)
				if (!allFieldsFilled) {
					errorMessage = '待完善'
				}
				if (!isOrderValid) {
					errorMessage = '固定量必须在剩余量占比之前'
				}
				if (!isUsageMeasureValid) {
					errorMessage = '剩余量占比之和必须为100'
				}
				if (!isPresenceValid) {
					errorMessage = '必须有固定量和剩余量占比'
				}
				valid = isOrderValid && isPresenceValid && isUsageMeasureValid && allFieldsFilled
			}

			this.$emit('getValid', 'virtual', valid, this.tableData, errorMessage)
		},
		assignPriceInfo(index) {
			const priceObj = this.priceOptions.find(item => item.priceCode == this.tableData[index].priceCode)
			if (priceObj) {
				this.tableData[index]['priceName'] = priceObj.priceName
				this.tableData[index]['natureName'] = priceObj.natureName
				this.tableData[index]['priceId'] = priceObj.priceId
			} else {
				this.tableData[index]['priceName'] = ''
				this.tableData[index]['natureName'] = ''
				this.tableData[index]['priceId'] = ''
				this.tableData[index]['priceCode'] = ''
			}
		},
		// 上移
		moveUp(item, index) {
			this.tableData.splice(index - 1, 0, item)
			this.tableData.splice(index + 1, 1)
		},
		// 下移
		moveDown(item, index) {
			this.tableData.splice(index + 2, 0, item)
			this.tableData.splice(index, 1)
		},
		// 是否展示销卡按钮
		isShowCancellationButton(row) {
			const { virtualMeterType, archivesStatus } = row || {}

			// 实表不显示操作
			if (virtualMeterType === 0) {
				return false
			}

			// 状态不为在用则不可销卡
			if (archivesStatus !== 1) {
				return false
			}
			return true
		},
		// 是否展示恢复表卡按钮
		isShowRestoreButton(row) {
			const { virtualMeterType, archivesStatus } = row || {}

			// 实表不显示操作
			if (virtualMeterType === 0) {
				return false
			}

			// 状态不为已销档则不可恢复
			if (archivesStatus !== 3) {
				return false
			}
			return true
		},
		handleCancelButtonClick(row) {
			const { archivesId, archivesIdentity, virtualMeterType } = row
			this.currentEditData = { archivesId, archivesIdentity, virtualMeterType }
			this.showCardCancel = true
		},
		handleRestoreButtonClick(row) {
			const { archivesId, archivesIdentity, virtualMeterType, meterNo, meterId } = row
			this.currentEditData = { archivesId, archivesIdentity, virtualMeterType, meterNo, meterId }
			this.showCardRecover = true
		},
		handleRefresh() {
			const { realMeterData } = this
			const { archivesId } = realMeterData || {}
			this._apiGetVirtualArchivesDetail(archivesId)
		},
	},
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100%;
}
.container-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.container-radio {
	margin-bottom: 20px;
	.label {
		margin-right: 20px;
	}
}
.container-table {
	flex: 1;
	height: 0;
	::v-deep {
		.el-table--medium th,
		.el-table--medium td {
			padding: 8px 0;
		}
	}
	.move-up,
	.move-down {
		cursor: pointer;
		color: #409eff;
		margin: 0 5px;
	}
}
</style>
