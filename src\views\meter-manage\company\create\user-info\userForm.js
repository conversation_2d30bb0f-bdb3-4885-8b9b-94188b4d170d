export default function (_this) {
	return [
		{
			type: 'slot',
			label: '所属企业',
			prop: 'enterpriseNumber',
			slotName: 'enterpriseNumber',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '企业名称',
			prop: 'enterpriseName',
			attrs: {
				col: 8,
				disabled: true,
			},
		},
		{
			type: 'el-input',
			label: '用户名称',
			prop: 'userName',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '联系人',
			prop: 'contactPeople',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '手机号',
			prop: 'userMobile',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '电话',
			prop: 'contactPhone',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '邮编',
			prop: 'zipCode',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '合同编号',
			prop: 'contractNum',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-select',
			label: '用户类型',
			prop: 'userSubType',
			options:
				_this.$store.getters?.dataList?.business?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '电子邮箱',
			prop: 'email',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'el-input',
			label: '邮寄地址',
			prop: 'mailingAddress',
			attrs: {
				col: 16,
				type: 'textarea',
				maxlength: '64',
				showWordLimit: true,
				autosize: {
					minRows: 2,
					maxRows: 2,
				},
			},
		},
		{
			type: 'slot',
			slotName: 'businessLicenseUrl',
			label: '营业执照合同',
			prop: 'businessLicenseUrl',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'slot',
			slotName: 'purchaseContractUrl',
			label: '购房合同',
			prop: 'purchaseContractUrl',
			attrs: {
				col: 8,
			},
		},
		// 其他手机
		{
			type: 'slot',
			slotName: 'otherMobile',
			attrs: {
				col: 8,
			},
		},
		{
			type: 'slot',
			slotName: 'otherInfo',
			attrs: {
				col: 24,
			},
		},
		{
			type: 'el-select',
			label: '开票类型',
			prop: 'invoiceType',
			options:
				_this.$store.getters?.dataList?.invoiceType?.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}) || [],
			attrs: {
				col: 14,
			},
		},
		{
			type: 'slot',
			label: '纳税人识别号',
			prop: 'taxpayerIdentity',
			slotName: 'taxpayerIdentity',
			attrs: {
				col: 14,
			},
		},
		{
			type: 'el-input',
			label: '开票抬头',
			prop: 'buyerName',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 14,
			},
		},
		{
			type: 'el-input',
			label: '开户银行',
			prop: 'openBank',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 14,
			},
		},
		{
			type: 'el-input',
			label: '银行账户',
			prop: 'bankAccount',
			attrs: {
				disabled: _this.billingInfoDisabled,
				col: 14,
			},
		},
	]
}
