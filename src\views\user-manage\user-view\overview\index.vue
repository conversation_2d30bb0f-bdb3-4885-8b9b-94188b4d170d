<template>
	<div class="tab-content overview">
		<div class="bg-overflow">
			<div class="layout-overview">
				<div class="left">
					<!-- 其他信息 -->
					<div class="data-container">
						<GcModelHeader
							title="其他信息"
							:icon="require('@/assets/images/icon/title-file.png')"
						></GcModelHeader>
						<GcGroupDetail :data="otherData"></GcGroupDetail>
					</div>
					<!-- 开票信息 -->
					<div class="data-container">
						<GcModelHeader
							title="开票信息"
							:icon="require('@/assets/images/icon/title-common-parameters.png')"
						/>
						<GcGroupDetail :data="invoiceData"></GcGroupDetail>
						<el-button type="text" class="invoice-button" @click="showInvoiceSetting = true">
							<i class="iconfontCis icon-modify"></i>
							修改
						</el-button>
					</div>
					<!-- 表卡信息 -->
					<div class="data-container">
						<GcModelHeader
							title="表卡信息"
							:icon="require('@/assets/images/icon/title-common-parameters.png')"
						/>
						<div class="archives-container">
							<GcTable :columns="archivesColums" :table-data="archivesTableData" @dblclick="rowDbclick" />
						</div>
					</div>
				</div>
				<!-- 缴费信息 -->
				<div class="right">
					<GcModelHeader
						title="缴费信息"
						:icon="require('@/assets/images/icon/title-ic-card.png')"
					></GcModelHeader>
					<div class="pay-table-container">
						<GcTable
							:columns="columns"
							:table-data="tableData"
							needType="selection"
							@selectChange="selectChange"
						/>
					</div>
					<!-- 分割线 -->
					<div class="devide"></div>
					<div class="pay-field" v-for="(value, name, index) in getTotalAmount" :key="index">
						<div class="field-title">{{ name }}</div>
						<div class="field-value money">{{ value }}</div>
					</div>
					<div class="pay-button-container">
						<el-button
							size="small"
							class="pay-button"
							v-click-blur
							:disabled="isPayButtonDisabled"
							@click="handleSubmit"
						>
							缴 费
						</el-button>
					</div>
				</div>
			</div>
		</div>
		<!-- 弹窗 -->
		<InvoiceSetting :show.sync="showInvoiceSetting" :data="tabData" @success="$emit('refresh')" />
	</div>
</template>

<script>
import { getfilterName } from '@/utils'
import { accAdd } from '@/utils/calc.js'
import { isBlank } from '@/utils/validate.js'
import { yesOrNoEnum } from '@/consts/enums.js'
import { apiGetBillArrearsList } from '@/api/costManage.api'
import InvoiceSetting from '@/views/user-manage/user-view/components/invoice-setting/index.vue'
export default {
	components: { InvoiceSetting },
	props: {
		tabData: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	watch: {
		tabData: {
			handler() {
				this.handleSearch()
			},
			deep: true,
		},
	},
	data() {
		return {
			archivesColums: [
				{
					name: '表卡编号',
					tooltip: true,
					key: 'archivesIdentity',
				},
				{
					name: '表卡类型',
					tooltip: true,
					key: 'virtualMeterType',
					render: (h, row, total, scope) => {
						const key = scope.column.property
						return h(
							'span',
							{},
							this.$store.getters.dataList.virtualMeterType
								? getfilterName(
										this.$store.getters.dataList.virtualMeterType,
										row[key],
										'sortValue',
										'sortName',
								  )
								: '',
						)
					},
				},
				{
					name: '是否总分',
					tooltip: true,
					key: 'summaryArchives',
					width: 90,
					render: (h, row, total, scope) => {
						const key = scope.column.property
						return h('span', {}, yesOrNoEnum[row[key]])
					},
				},
				{
					name: '地址',
					tooltip: true,
					minWidth: 250,
					key: 'addressFullName',
				},
				{
					name: '价格编号',
					tooltip: true,
					key: 'priceCode',
				},
				{
					name: '价格版本号',
					tooltip: true,
					key: 'priceVersion',
				},
				{
					name: '用水性质',
					tooltip: true,
					key: 'natureName',
				},
				{
					name: '表卡状态',
					tooltip: true,
					key: 'archivesStatus',
					render: (h, row, total, scope) => {
						const key = scope.column.property
						return h(
							'span',
							{},
							this.$store.getters.dataList.archiveState
								? getfilterName(
										this.$store.getters.dataList.archiveState,
										row[key],
										'sortValue',
										'sortName',
								  )
								: '',
						)
					},
				},
			],
			columns: [
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
					width: 120,
				},
				{
					key: 'billDate',
					name: '账期',
					width: 90,
					tooltip: true,
				},
				{
					key: 'receivableAmount',
					name: '应缴金额(元)',
					tooltip: true,
				},
			],
			tableData: [],
			totalAmount: 0,
			billNos: [],
			showInvoiceSetting: false,
			selectBillData: [],
		}
	},
	computed: {
		// 其他信息
		otherData() {
			const list = [
				{
					key: '曾用名',
					value: '--',
					field: 'nameUsedBefore',
				},
				{
					key: '其他手机',
					value: '--',
					field: 'otherContactPhone',
				},
				{
					key: '其他证件',
					value: '--',
					field: 'certificateType',
				},
				{
					key: '证件号码',
					value: '--',
					field: 'otherCertificateNo',
				},
				{
					key: '电子邮箱',
					value: '--',
					field: 'email',
				},
				{
					key: '邮编',
					value: '--',
					field: 'zipCode',
				},
				{
					key: '邮寄地址',
					value: '--',
					field: 'mailingAddress',
					col: 24,
				},
			]
			const getValue = (field, value) => {
				const { certificateType = [] } = this.$store.getters.dataList || {}
				switch (field) {
					case 'certificateType':
						return getfilterName(certificateType, value, 'sortValue', 'sortName')
					default:
						return value
				}
			}
			list.forEach(item => {
				item.value = getValue(item.field, this.tabData?.user?.[item.field] ?? '--')
			})
			return {
				list,
				row: 6,
			}
		},
		// 开票信息
		invoiceData() {
			const list = [
				{
					key: '开票类型',
					value: '--',
					field: 'invoiceType',
				},
				{
					key: '纳税人识别号',
					value: '--',
					field: 'taxpayerIdentity',
				},
				{
					key: '开户银行',
					value: '--',
					field: 'openBank',
				},
				{
					key: '银行账户',
					value: '--',
					field: 'bankAccount',
				},
			]
			const getValue = (field, value) => {
				const { invoiceType = [] } = this.$store.getters.dataList || {}

				switch (field) {
					case 'invoiceType':
						return getfilterName(invoiceType, value, 'sortValue', 'sortName')
					default:
						return value
				}
			}
			list.forEach(item => {
				item.value = getValue(item.field, (this.tabData.user && this.tabData.user[item.field]) || null)
			})
			return {
				list,
				row: 4,
			}
		},
		getTotalAmount() {
			let arr = { 总应缴金额: '￥ 0.00' }
			if (!isBlank(this.totalAmount)) {
				let num = 0
				let money = this.totalAmount.toFixed(2)
				// 解决保留两位小数后为-0.00的显示问题
				if (money == 0) {
					num = num.toFixed(2)
				} else {
					num = money
				}
				arr = {
					总应缴金额: '￥ ' + num,
				}
			} else {
				arr = { 总应缴金额: '￥ 0.00' }
			}
			return arr
		},
		// 缴费按钮是否禁用
		isPayButtonDisabled() {
			return !this.billNos.length
		},
		archivesTableData() {
			return this.tabData?.archivesList || []
		},
	},
	methods: {
		rowDbclick(obj) {
			const archivesId = obj.row.archivesId
			const userType = this.tabData?.user?.userType
			const isPermission = obj.row.isPermission
			if (!isPermission) {
				const orgName = this.$store.getters.userInfo.orgName
				this.$notify({
					message: `非${orgName}或管理人员，暂无操作权限`,
					title: '提示',
					offset: 60,
					type: 'warning',
				})
				return
			}

			if (!this.$has('cpm_archives_detail') && userType === 3) {
				this.$notify({
					message: '暂无权限访问，请联系管理员！',
					title: '提示',
					offset: 60,
					type: 'warning',
				})
				return
			}
			if (!this.$has('cpm_archives_detail5') && userType === 4) {
				this.$notify({
					message: '暂无权限访问，请联系管理员！',
					title: '提示',
					offset: 60,
					type: 'warning',
				})
				return
			}
			const path = userType === 3 ? '/meterManage/residentMeterView' : '/meterManage/companyMeterView'
			this.$router.push({
				path,
				query: {
					archivesId,
				},
			})
		},
		// 待缴费列表
		async getList() {
			this.tableData = []
			this.selectBillData = []
			const params = {
				current: 1,
				size: 99999,
				archivesIdentity: this.tabData.archives && this.tabData.archives.archivesIdentity,
				userId: this.$route.query.userId,
			}
			const { records } = await apiGetBillArrearsList(params)
			this.tableData = records
		},
		selectChange(arr) {
			this.selectBillData = arr
			const amountList = arr.map(item => item.receivableAmount)
			this.billNos = arr.map(item => item.billNo)
			this.totalAmount = this.accAddMultiple(amountList)
		},
		accAddMultiple(args) {
			return args.reduce((acc, curr) => accAdd(acc, curr), 0)
		},
		handleSearch() {
			this.getList()
		},
		handleSubmit() {
			this.$router.push({
				path: '/costManage/paymentPage',
				query: {
					billNos: this.billNos?.length ? this.billNos.join(',') : '',
				},
			})
		},
	},
}
</script>

<style lang="scss" scoped>
@import '../style/common.scss';
// 其他信息
.underline {
	color: #2e59c4;
	cursor: pointer;
	text-decoration: underline;
}
.left {
	overflow: hidden;
	.invoice-button {
		float: right;
		padding-top: 0;
		margin-right: 20px;
		::v-deep {
			.iconfontCis {
				padding-right: 2px;
			}
			span {
				display: flex;
				align-items: center;
			}
		}
	}
	.data-container:nth-of-type(3) {
		flex: 1;
		overflow: hidden;
		// 表卡信息
		.archives-num {
			margin-left: 10px;
		}
		.archives-container {
			padding: 0 20px 20px 20px;
			height: calc(100% - 70px);
		}
	}
}
// 缴费信息
.right {
	padding: 0 20px;
	width: 0;
	.model-header {
		padding: 0 0;
	}
	.pay-table-container {
		height: 300px;
	}
	// 分割线
	.devide {
		border-bottom: 1px dashed #cccccc;
		margin: $base-margin 0;
	}
	.pay-field {
		margin-bottom: $base-margin;
		.field-title {
			color: $base-color-9;
			font-size: $base-font-size-small;
		}
		.field-value {
			margin-top: 10px;
		}
		.field-value.allowance {
			color: $base-color-4;
			font-size: $base-font-size-bigger;
		}
		.field-value.money {
			color: $base-color-red;
			font-size: 20px;
		}
	}
	// 缴费按钮
	.pay-button-container {
		padding-top: $base-padding;
		display: flex;
		.el-button {
			flex: 1;
			border-radius: 17px;
		}
		.pay-button {
			background: linear-gradient(180deg, #789fff 0%, #3565df 100%);
			color: #ffffff;
			&:hover {
				background: linear-gradient(180deg, #93b2ff 0%, #5e84e6 100%);
			}
			&:focus {
				background: linear-gradient(180deg, #6c8fe6 0%, #2e59c4 100%);
			}
			&.is-disabled {
				background: linear-gradient(180deg, #aec5ff 0%, #86a3ec 100%);
			}
		}
	}
}
.charge-panel-actions {
	margin-top: 20px;
	display: flex;
	gap: 10px;
	color: #ff9d57;
	justify-content: flex-end;
	span {
		display: flex;
		align-items: center;
		cursor: pointer;
		gap: 5px;
		margin-right: 20px;
	}
}
</style>
