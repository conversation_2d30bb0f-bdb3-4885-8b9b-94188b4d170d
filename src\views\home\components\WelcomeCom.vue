<template>
	<div class="welcome">{{ welcomeMsg }}</div>
</template>

<script>
export default {
	name: 'WelcomeCom',
	data() {
		return {
			welcomeMsg: '',
			timeHour: this.dayjs().hour(),
		}
	},
	watch: {
		timeHour: function (nval) {
			if ([6, 13, 18, 23].has(nval)) {
				this.getWelcomMsg()
			}
		},
	},
	computed: {
		userName() {
			return this.$store.getters.userInfo.staffName || this.$store.getters.userInfo.nickName
		},
	},
	mounted() {
		// 获取欢迎语
		this.getWelcomMsg()
	},
	methods: {
		getWelcomMsg() {
			const tnodes = [
				[0, 6, '夜深啦', '记得早点休息哦！'],
				[6, 12, '早上好', '欢迎回到工作岗位。'],
				[12, 18, '下午好', '有些累了吧，喝杯咖啡提提神~'],
				[18, 23, '晚上好', '辛苦一天了，记得按时吃晚饭哦~'],
				[23, 24, '夜深啦', '记得早点休息哦！'],
			]
			let t = this.dayjs().hour()
			let tnode = tnodes.find(it => t >= it[0] && t < it[1])

			this.welcomeMsg = `${tnode[2]}，${this.userName}！${tnode[3]}`
		},
	},
}
</script>
<style lang="scss" scoped>
.welcome {
	padding: 0 20px;
	font-size: 12px;
	color: #8c8c8c;
}
</style>
