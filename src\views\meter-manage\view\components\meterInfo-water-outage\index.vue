<template>
	<GcElDialog
		:show="isShow"
		title="停水登记"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs"></GcFormSimple>
	</GcElDialog>
</template>

<script>
import { getFormItems } from './formItem.js'
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiDisableArchives, apiDisableArchives4 } from '@/api/meterManage.api.js'
export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		detailData: {
			type: Object,
			default: () => ({}),
		},
		permissionCode: {
			type: String,
			default: 'cpm_archives_disable',
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	watch: {
		isShow: {
			handler(val) {
				if (val) {
					this.formData.archivesIdentity = this.detailData?.archivesIdentity || ''
					this.formData.meterNo = this.detailData?.meterNo || ''
				}
			},
		},
	},
	data() {
		return {
			formData: {
				archivesIdentity: '',
				meterNo: '',
				disableDate: '',
				disablePerson: this.$store.getters.userInfo.staffName,
				disableReason: '',
			},
			formItems: getFormItems(),
			formAttrs: {
				rules: {
					disableDate: [ruleRequired('必填')],
					disableReason: [ruleRequired('必填')],
				},
			},
		}
	},
	methods: {
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			let formObj = trimParams(removeNullParams(this.formData))
			const { archivesId } = this.detailData
			delete formObj.meterNo
			delete formObj.archivesIdentity
			const dateStr = this.dayjs(formObj.disableDate).format('YYYY-MM-DD')
			Object.assign(formObj, { disableDate: dateStr, archivesId })

			const apiMethods = {
				cpm_archives_disable: apiDisableArchives,
				cpm_archives_disable4: apiDisableArchives4,
			}
			await apiMethods[this.permissionCode](formObj)
			this.$message.success('停水成功')
			this.handleClose()
			this.$emit('refresh')
		},
	},
}
</script>
