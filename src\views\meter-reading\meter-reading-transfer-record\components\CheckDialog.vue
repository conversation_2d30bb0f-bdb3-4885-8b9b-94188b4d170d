<template>
	<gc-el-dialog
		:show="isShow"
		title="移交清单查看"
		custom-top="120px"
		width="800px"
		:show-footer="false"
		@open="getList(1)"
		@close="handleClose"
	>
		<div class="table-wrap">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			/>
		</div>
	</gc-el-dialog>
</template>

<script>
import { meterCardRecordPreviewPage } from '@/api/meterReading.api'

export default {
	components: {},
	props: {
		// 弹窗显示/隐藏
		show: {
			type: Boolean,
			default: false,
		},
		data: {
			require: true,
			type: Object,
		},
	},
	data() {
		return {
			loading: false,
			columns: [
				{
					key: 'orgName',
					name: '营业分公司',
					tooltip: true,
				},
				{
					key: 'alleyName',
					name: '坊别',
					tooltip: true,
				},
				{
					key: 'bookNo',
					name: '表册编号',
					tooltip: true,
				},
				{
					key: 'bookSeq',
					name: '册内序号',
					tooltip: true,
				},
				{
					key: 'meterNo',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'userName',
					name: '户名',
					tooltip: true,
				},
				{
					key: 'addressName',
					name: '地址',
					tooltip: true,
				},
				{
					key: 'tapWaterCode',
					name: '自来水号',
					tooltip: true,
				},
				{
					key: 'simpleAddressName',
					name: '简写地址',
					tooltip: true,
				},
				{
					key: 'households',
					name: '户数',
					tooltip: true,
				},
				{
					key: 'resiPopulation',
					name: '人口',
					tooltip: true,
				},
				{
					key: 'priceCode',
					name: '价格编号',
					tooltip: true,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await meterCardRecordPreviewPage({
					size,
					current,
					handOver: 1,
					recordId: this.data.recordId,
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handleClose() {
			this.loading = false
			this.tableData = []
			this.isShow = false
		},
	},
}
</script>

<style lang="scss" scoped>
.table-wrap {
	height: 400px;
}
</style>
