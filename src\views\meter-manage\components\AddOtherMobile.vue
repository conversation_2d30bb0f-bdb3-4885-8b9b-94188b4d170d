<template>
	<div class="mobile-container">
		<div class="fn-flex top-flex">
			<el-input v-model="mobileValue" placeholder="请输入" :disabled="remainder == 0"></el-input>
			<el-button icon="el-icon-plus" @click="handleAdd">添加</el-button>
		</div>
		<div class="bottom-flex">
			<div class="tip" v-show="remainder != 0">还可以添加{{ remainder }}个</div>
			<div class="fn-flex mobile-view" v-for="(item, index) in mobileList" :key="index">
				<span>{{ item }}</span>
				<i class="el-icon-delete" @click="handleDelete(index)"></i>
			</div>
		</div>
	</div>
</template>

<script>
import { RULE_PHONE } from '@/utils/rules'
export default {
	name: '',
	components: {},
	props: {
		value: {
			type: String,
			default: '',
		},
		mobileList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			mobileValue: this.value,
		}
	},
	watch: {
		value(newVal) {
			this.mobileValue = newVal
		},
		mobileValue(newVal) {
			this.$emit('input', newVal)
		},
		mobileList: {
			handler(newVal) {
				this.$emit('update:mobileList', newVal)
			},
			deep: true,
		},
	},
	computed: {
		remainder() {
			return 3 - this.mobileList.length
		},
	},
	created() {},
	methods: {
		handleAdd() {
			if (this.mobileList.length >= 3) {
				this.$message.error('最多添加3个手机号')
				return
			}
			if (!this.mobileValue) {
				this.$message.error('请输入手机号')
				return
			}
			if (!RULE_PHONE.pattern.test(this.mobileValue)) {
				this.$message.error(RULE_PHONE.message)
				return
			}
			if (this.mobileList.includes(this.mobileValue)) {
				this.$message.error('手机号已存在')
				return
			}
			this.mobileList.push(this.mobileValue)
			this.mobileValue = ''
		},
		handleDelete(index) {
			this.mobileList.splice(index, 1)
		},
	},
}
</script>

<style lang="scss" scoped>
.fn-flex {
	display: flex;
	align-items: center;
}
.top-flex {
	gap: 20px;
	margin-bottom: 10px;
}
.bottom-flex {
	.tip {
		font-family: Source Han Sans CN;
		font-size: 12px;
		font-weight: 350;
	}
	.mobile-view {
		justify-content: space-between;
		padding: 2px 4px 2px 5px;
		border-radius: 2px 0px 0px 0px;
		margin-bottom: 8px;
		width: 168px;
		height: 20px;
		background-color: #f4f5fb;
		.el-icon-delete {
			cursor: pointer;
		}
	}
}
</style>
