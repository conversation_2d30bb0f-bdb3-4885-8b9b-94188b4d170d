<template>
	<div class="page-layout">
		<div class="page-left">
			<div class="left-title">册本移交简目录</div>
			<GcTab :tabList="tabList" @changeTab="changeTab" :defaultTab="activeTab" />
		</div>
		<div class="page-right">
			<!-- 表卡选择 -->
			<CardChoose
				ref="cardChooseRef"
				v-show="activeTab === 'cardChoose'"
				@reset="handleReset"
				@next="changeTab"
				@choose="handleChoose"
			/>
			<!-- 移交清单查看 -->
			<TransferListCheck
				ref="transferListCheckRef"
				v-show="activeTab === 'transferListCheck'"
				@next="changeTab"
				@delete="handleTranferListDelete"
			/>
			<!-- 移交登记 -->
			<TransferCheck ref="transferCheckRef" v-show="activeTab === 'transferCheck'" @check="handleCheck" />
		</div>
	</div>
</template>

<script>
import CardChoose from './card-choose'
import TransferListCheck from './transfer-list-check'
import TransferCheck from './transfer-check'

export default {
	components: { CardChoose, TransferListCheck, TransferCheck },
	data() {
		return {
			isInit: true,
			tabList: [
				{
					label: '表卡选择',
					value: 'cardChoose',
					status: 1,
					disabled: false,
					tip: '待选择',
				},
				{
					label: '移交清单查看',
					value: 'transferListCheck',
					status: 1,
					disabled: true,
					tip: ' ',
				},
				{
					label: '移交登记',
					value: 'transferCheck',
					status: 1,
					disabled: true,
					tip: '待完善',
				},
			],
			activeTab: '',

			// 列表选择的数据
			selectedData: [],
		}
	},
	methods: {
		changeTab(v) {
			if (this.activeTab === v) return

			this.activeTab = v

			if (this.isInit) {
				this.isInit = false
				return
			}
			if (v === 'cardChoose') {
				this.$nextTick(() => {
					this.$refs.cardChooseRef.handleSearch()
				})
			} else if (v === 'transferListCheck') {
				this.$nextTick(() => {
					this.$refs.transferListCheckRef.getList()
				})
			} else if (v === 'transferCheck') {
				this.$nextTick(() => {
					this.$refs.transferCheckRef.$refs.formRef.resetFields()
					this.$refs.transferCheckRef.formData.bookType = this.$refs.cardChooseRef.formData.bookType
				})
			}
		},
		// 表卡选择底部重置
		handleReset() {
			this.tabList.forEach((item, index) => {
				if (index === 0) {
					item.status = 1
					item.disabled = false
				} else {
					item.status = 1
					item.disabled = true
				}
			})
		},
		// 表卡选择事件
		handleChoose(flag) {
			this.tabList[1].disabled = this.tabList[2].disabled = !flag
			this.tabList[0].status = this.tabList[1].status = flag ? 2 : 1
		},
		// 移交清单列表删除事件
		handleTranferListDelete(flag) {
			if (!flag) {
				// 移交清单删除了最后一条数据 禁用移交清单查看tab
				this.tabList[1].disabled = !flag
				this.tabList[1].status = 1
				// 手动选择表卡选择tab
				this.changeTab('cardChoose')
			}
		},
		// 移交登记完成事件
		handleCheck() {
			// 切换到表卡选择tab
			this.activeTab = 'cardChoose'
			this.tabList[1].disabled = this.tabList[2].disabled = true
			this.tabList[0].status = this.tabList[1].status = 1
			this.$nextTick(() => {
				this.$refs.cardChooseRef.handleBottomReset()
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.page-left {
	position: relative;
	margin-right: 0;
	.left-title {
		height: 48px;
		line-height: 48px;
		color: #000;
		font-family: Source Han Sans CN;
		font-size: 16px;
		font-weight: 500;
	}
	&::after {
		content: '';
		position: absolute;
		top: 20px;
		bottom: 20px;
		right: 0;
		display: block;
		clear: both;
		width: 1px;
		border-right: 1px dashed #eef0f3;
	}
}
</style>
