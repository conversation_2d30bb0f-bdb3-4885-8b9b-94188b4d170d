<template>
	<GcElDialog
		:show="isShow"
		title="余额充值"
		okText="确认"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs" />
	</GcElDialog>
</template>

<script>
import { ruleRequired, RULE_FEES } from '@/utils/rules'
import { removeNullParams, trimParams, convertUTC } from '@/utils/index.js'
import { reChargeRecord } from '@/api/costManage.api'
export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	data() {
		return {
			formData: {
				rechargeAmount: '',
				payMode: '1',
				operationTime: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '充值账户',
					prop: 'accountNo',
					attrs: { disabled: true },
				},
				{
					type: 'el-input',
					label: '充值金额',
					prop: 'rechargeAmount',
					attrs: {},
				},
				{
					type: 'el-select',
					label: '渠道',
					prop: 'payMode',
					options: [
						{
							value: '1',
							label: '现金',
						},
					],
					attrs: {
						clearable: false,
					},
				},
				{
					type: 'el-date-picker',
					label: '操作时间',
					prop: 'operationTime',
					attrs: {
						placeholder: '请选择',
						type: 'datetime',
						valueFormat: 'yyyy-MM-dd HH:mm:ss',
					},
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					operationTime: [ruleRequired('必填')],
					rechargeAmount: [
						ruleRequired('必填'),
						RULE_FEES,
						{
							validator: (rule, value, callback) => {
								if (value <= 0) {
									callback(new Error(`输入值必须大于 0`))
								} else {
									callback()
								}
							},
							trigger: '',
						},
					],
				},
			},
		}
	},
	methods: {
		assignForm({ row }) {
			this.formData.accountNo = row.archivesIdentity || row.enterpriseNumber || ''
			this.formData.accountId = row.accountId
			this.formData.inputBalanceAmount = ''
			this.formData.operationTime = convertUTC(new Date())
			this.formData.payMode = '1'
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const formParams = trimParams(removeNullParams(this.formData))
			delete formParams.accountNo
			const result = await reChargeRecord(formParams).catch(e => {
				this.$message.error(e.message || '充值失败！')
			})
			if (result === null) {
				this.$message.success('充值成功')
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetForm()
			this.$nextTick(() => {
				this.$refs.formRef.clearValidate()
			})
			this.isShow = false
		},
	},
}
</script>
