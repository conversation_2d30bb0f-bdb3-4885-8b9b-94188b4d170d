import { apiGetRegion, apiGetArea } from '@/api/archives.api'
import identity from '@/mixin/identity.js'

export default {
	mixins: [identity],
	props: {
		// city
		cityCode: {
			type: String,
			default: '',
		},
		adminOrgCode: {
			type: [Array, String],
			default: () => {
				return []
			},
		},
	},
	data() {
		return {
			district: '', //区/县
			street: '', //街道+小区
			districtList: [], //区/县下拉列表
			streetList: [], //街道+小区下拉列表
		}
	},
	watch: {
		cityCode(newVal) {
			if (newVal) {
				this.getDistrictList(newVal)
			} else {
				this.districtList = []
			}
		},
	},
	mounted() {
		if (this.cityCode) {
			this.getDistrictList(this.cityCode)
		}
	},
	methods: {
		// 获取区/县下拉列表
		getDistrictList(val) {
			this.district = ''
			return apiGetRegion({
				regionCode: val,
			}).then(res => {
				this.districtList = res.records
			})
		},
		// 获取街道+小区下拉列表
		getStreetList(key, val) {
			let params = {
				regionCode: val,
				level: 6,
				size: 9999,
			}
			// 添加orgcode参数
			if (this.adminOrgCode.length > 0) {
				params['orgCode'] = this.adminOrgCode[this.adminOrgCode.length - 1]
			}
			// 添加tenantId参数
			this.addTenantId(params)
			this[key] = '' //要置空的数据的key
			return apiGetArea(params).then(res => {
				this.streetList = res.records.map(item => {
					const { streetName = '', addressAreaName } = item
					const streetArea = streetName ? streetName + addressAreaName : addressAreaName
					return {
						...item,
						streetArea,
					}
				})
			})
		},
	},
}
