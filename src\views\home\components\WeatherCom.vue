<template>
	<div class="weather-status">
		<template v-for="(item, index) in weaterStatus">
			<el-tooltip class="item" :content="`${item.content}:${item.value}`" placement="top" :key="index">
				<span :key="index">
					<svg class="icon" aria-hidden="true">
						<use :xlink:href="`#${item.icon}`"></use>
					</svg>
					<span>{{ item.value || '--' }}</span>
					<span class="unit">{{ item.unit }}</span>
				</span>
			</el-tooltip>
		</template>
	</div>
</template>

<script>
import { getWeatherFromIp } from '@/api/home.api.js'
import axios from 'axios'

export default {
	name: 'WeatherCom',
	data() {
		return {
			weaterStatus: [
				{
					icon: 'icon-temperature',
					value: 0,
					unit: '℃',
					content: '温度',
				},
				{ icon: 'icon-humidity', value: 0, unit: '%', content: '湿度' },
				{ icon: 'icon-PM25', value: 0, unit: '', content: 'PM2.5' },
				{ icon: 'icon-AQI', value: 0, unit: '', content: 'AQI生活指数' },
			],
		}
	},
	mounted() {
		// 跨域运维以及manage域名的不获取天气信息
		if (!(location.hash.indexOf('om-login') > -1 || location.hostname.indexOf('manage.') > -1)) {
			this.getUserIp()
		}
	},
	methods: {
		// 获取用户ip
		getUserIp() {
			axios.get('https://api.ipify.org/').then(res => {
				this.getWeather(res?.data)
			})
		},
		// 获取天气信息
		getWeather(ip) {
			// 有天气缓存,缓存时间小于8小时,不再额外请求
			//   window.cip = "**************";hangzhou
			//   https://ipapi.co/json/
			getWeatherFromIp(ip).then(res => {
				const weather = {
					temp: res.temperature && res.temperature.replace(/[^\d.]/g, ''),
					sd: res.humidity && res.humidity.replace(/[^\d.]/g, ''),
					'pm_2.5': res.pm2_5,
					aqi: res.aqi,
					'pm_2.5_quality': res.pm2_5Quality,
					aqiQuality: res.aqiQuality,
				}
				this.changeWeaterStatus(weather)
			})
		},
		changeWeaterStatus(weather) {
			// 数据不多，手动赋值，数值多时，规划映射
			this.weaterStatus[0].value = weather.temp
			this.weaterStatus[1].value = weather.sd
			this.weaterStatus[2].value = weather['pm_2.5']
			this.weaterStatus[2].unit = weather['pm_2.5_quality']
			this.weaterStatus[3].value = weather.aqi
			this.weaterStatus[3].unit = weather.aqiQuality
		},
	},
}
</script>
<style lang="scss" scoped>
.weather-status {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	cursor: pointer;
	.icon {
		width: 18px;
		height: 18px;
		vertical-align: sub;
	}
	.unit {
		font-size: 10px;
		margin-left: 2px;
	}
}
</style>
