import { toFixed } from '@/utils'

/**
 * toFixed方法
 * @param {Number} n 保留小数位数
 * @param {Boolean} native 默认为false表示截取，设置true表示采用四舍五入
 * @returns {String}
 */
export default function (n = 0, native = false) {
	if (!this || this === 'null') return `0.${'0'.padStart(n, '0')}`
	const value = this * 1 // 值转换，例如 2e3 => 2000
	if (native) return toFixed(value, n)
	let [integer, decimal = ''] = value.toString().split('.')
	let result = integer
	if (n) {
		decimal = decimal.slice(0, n).padEnd(n, 0)
		result = `${integer}.${decimal}`
	}
	// 当截取后出现负号且值为0时，去除符号（例如-0.00 => 0.00）
	if (/^-0/.test(result) && result == 0) {
		result = result.replace('-', '')
	}
	return result
}
