<template>
	<gc-el-dialog :show="isShow" :title="`${typeText}用水性质`" width="500px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template #waterSort>
				<el-cascader
					v-if="editType === 'add'"
					ref="waterSortRef"
					v-model="formData.priceNatureId"
					:options="treeData"
					filterable
					clearable
					:show-all-levels="false"
					placeholder="请选择用水分类"
					:props="{
						label: 'natureName',
						value: 'priceNatureId',
						emitPath: false,
						checkStrictly: true,
					}"
				/>
				<el-input v-else-if="editType === 'edit'" v-model="formData.parentNatureName" disabled />
			</template>
		</GcFormSimple>
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { ruleMaxLength } from '@/utils/rules.js'
import { addWaterNature, updateWaterNature } from '@/api/basicConfig.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		editType: {
			type: String,
			default: 'add',
		},
		treeData: {
			type: Array,
			default: () => {
				return []
			},
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},

		typeText() {
			return this.editType === 'add' ? '新增' : '编辑'
		},
	},
	data() {
		return {
			formData: {
				natureName: '',
				priceNatureId: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '用水性质',
					prop: 'natureName',
					attrs: {
						clearable: true,
						placeholder: '请输入用水性质',
					},
				},
				{
					type: 'slot',
					slotName: 'waterSort',
					label: '用水分类',
					prop: 'priceNatureId',
				},
			],
			formAttrs: {
				labelWidth: '90px',
				rules: {
					natureName: [
						{
							required: true,
							message: '请输入用水性质',
							trigger: 'blur',
						},
						ruleMaxLength(32, '用水性质'),
					],
					priceNatureId: [
						{
							required: true,
							message: '请选择用水分类',
							trigger: 'change',
						},
					],
				},
			},
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				if (this.editType === 'add') {
					const { level, natureNo: parentNatureNo } = this.$refs.waterSortRef.getCheckedNodes()[0].data
					const { natureName } = this.formData
					await addWaterNature({
						natureName,
						level: level + 1,
						parentNatureNo,
					})
				} else {
					const { natureName, priceNatureId, level, parentNatureNo } = this.formData
					await updateWaterNature({
						natureName,
						priceNatureId,
						level,
						parentNatureNo,
					})
				}
				this.$message.success(`${this.typeText}用水性质成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
.el-cascader {
	width: 100%;
}
</style>
