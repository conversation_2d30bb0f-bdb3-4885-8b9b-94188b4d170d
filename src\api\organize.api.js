import service from './request'

// 查询角色列表数据信息
export function apiRoleList(params) {
	return service({
		url: `/v1/tos/role/list`,
		method: 'get',
		params,
	})
}

// 查询公司分类列表信息
export function apiOrgstrutureList(params) {
	return service({
		url: `/v1/tos/orgstruture/list`,
		method: 'get',
		params,
	})
}

// 租户管理查看权限组数据
export function apiRoleTree(params) {
	return service({
		url: `/auth/authz/permission/group/tenant/role/tree`,
		method: 'post',
		params,
	})
}

// 更新组织权限信息
export function apiModifyRole(params, data) {
	return service({
		url: `v1/tos/role/modify`,
		method: 'put',
		params,
		data,
	})
}

// 删除部门员工角色权限
export function apiDeleteRole(params) {
	return service({
		url: `/v1/tos/role/delete?id=${params.id}&tid=${params.tid}`,
		method: 'delete',
	})
}

// 创建和拷贝角色权限
export function apiCreateRole(params, data) {
	return service({
		url: `/v1/tos/role/create`,
		method: 'post',
		data,
	})
}

// 获取应用模板角色列表
export function apiTemplaterole() {
	return service({
		url: `/v1/tos/templaterole/list?paged=false`,
		method: 'get',
	})
}

// 模板角色列表应用
export function apiApplyTemplate(data) {
	return service({
		url: `/v1/tos/role/templateroles/apply`,
		method: 'post',
		data,
	})
}

// 查询应用模板角色组列表
export function apiRoleGrouopTemp() {
	return service({
		url: `/v1/tos/roletemplate/list`,
		method: 'get',
	})
}

// 模板角色组应用
export function apiApplyRoleTemp(params, data) {
	return service({
		url: `/v1/tos/role/roletemplate/apply`,
		method: 'post',
		params,
		data,
	})
}

// 获取部门层级结构树列表
export function apiOrganizeTree(params) {
	return service({
		url: `/v1/tos/organization/tree`,
		method: 'get',
		params,
	})
}
// 获取租户组织结构列表 方便联调 待合并
export function apiGetTenantOrgList(parameter, others) {
	let obj = {
		url: 'v1/tos/organization/sys/tree',
		method: 'GET',
		params: parameter,
	}
	if (others && others.constructor === Object) {
		obj = { ...obj, ...others }
	}
	return service(obj)
}

// 查询营业厅下所有人员列表
export function apiStaffList(params) {
	return service({
		url: `/v1/tos/staff/list`,
		method: 'get',
		params,
	})
}

// 获取省份、市code，name
export function apiRegion(regionCode) {
	return service({
		url: `/cpm/region/children?regionCode=${regionCode}`,
		method: 'get',
	})
}

// 创建租户员工
export function apiCreateStaff(data) {
	return service({
		url: `/v1/tos/staff/create`,
		method: 'post',
		data,
	})
}

// 修改租户员工
export function apiModifyStaff(data, id) {
	return service({
		url: `/v1/tos/staff/modify?id=${id}`,
		method: 'put',
		data,
	})
}
export function apiModifyStaff2(data, id) {
	return service({
		url: `/v1/tos/staff/modify2?id=${id}`,
		method: 'put',
		data,
	})
}

// 锁定部门
export function apiLockdepart(id) {
	return service({
		url: `v1/tos/organization/lock?id=${id}`,
		method: 'put',
	})
}

// 解锁部门
export function apiUnlockdepart(id) {
	return service({
		url: `v1/tos/organization/unlock?id=${id}`,
		method: 'put',
	})
}

// 组织部门数据更新
export function apiModifydepart(data, id) {
	return service({
		url: `/v1/tos/organization/modify?id=${id}`,
		method: 'put',
		data,
	})
}

// 创建组织部门
export function apiCreatedepart(data) {
	return service({
		url: `/v1/tos/organization/create`,
		method: 'post',
		data,
	})
}

// 删除部门组织
export function apiDeleteDepart(id) {
	return service({
		url: `/v1/tos/organization/delete?id=${id}`,
		method: 'delete',
	})
}

// 租户管理员修改员工密码
export function apiUpdateAccount(params) {
	return service({
		url: `/auth/aggregation/password/tenant/manage/update`,
		method: 'post',
		params,
	})
}

// 查询证书列表
export function apiLoginverifyList(params) {
	return service({
		url: `/v1/tos/loginverify/list`,
		method: 'get',
		params,
	})
}

// 新增证书
export function apiLoginverifyCreate(tid, data) {
	return service({
		url: `/v1/tos/loginverify/create?tid=${tid}`,
		method: 'post',
		data,
	})
}

// 删除证书
export function apiLoginverifyDelete(params) {
	return service({
		url: `/v1/tos/loginverify/delete`,
		method: 'delete',
		params,
	})
}
