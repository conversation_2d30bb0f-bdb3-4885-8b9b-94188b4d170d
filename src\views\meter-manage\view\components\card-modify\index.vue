<template>
	<GcElDialog
		:show="isShow"
		title="修改表卡信息"
		custom-top="50px"
		width="1200px"
		okText="保存"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSave"
	>
		<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:fullAddress>
				<el-form-item label="详细地址" prop="addressName">
					<AddressSearch
						:searchName.sync="formData.addressName"
						:isDisabled="!formData.regionCode"
						:regionCode="formData.regionCode"
						:addressAreaCode="addressAreaCode"
						style="width: 50%"
					/>
				</el-form-item>
			</template>
			<template v-slot:businessLicenseUrl>
				<GcUploadFile v-model="formData.businessLicenseUrl" />
			</template>
			<template v-slot:purchaseContractUrl>
				<GcUploadFile v-model="formData.purchaseContractUrl" />
			</template>
		</GcFormRow>
	</GcElDialog>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { isBlank } from '@/utils/validate.js'
import { ruleRequired, RULE_INTEGERONLY, ruleMaxLength } from '@/utils/rules.js'
import { getFormItems } from './form.js'
import { apiArchivesUpdate, apiArchivesUpdate1 } from '@/api/meterManage.api.js'
import { archivesMeterTypeOptions } from '@/consts/optionList'
import { apiGetRegion, apiGetAddressAreaMap } from '@/api/addressManage.api.js'
import AddressSearch from '@/views/meter-manage/components/AddressSearch.vue'
export default {
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		detailData: {
			type: Object,
			default: () => {
				return {}
			},
		},
		permissionCode: {
			type: String,
			default: 'cpm_archives_update',
		},
	},
	components: { AddressSearch },
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
	},
	watch: {
		isShow: {
			handler(val) {
				if (val) {
					const newData = Object.assign({}, ...Object.values(this.detailData))
					this.formItems = newData.userType === 3 ? getFormItems(this).resident : getFormItems(this).company
					this.changeSummaryArchives(newData.summaryArchives)
					Object.keys(this.formData).forEach(key => {
						this.assignForm(key, newData)
					})
					this.updateAddress()
					this.addressAreaCode = newData.addressAreaCode
					if (newData.addressFullName) {
						this.addressFullName = newData.addressFullName.endsWith(newData.addressName)
							? newData.addressFullName.slice(0, -newData.addressName.length)
							: newData.addressFullName
					}
				}
			},
		},
	},
	data() {
		return {
			formItems: [],
			formData: {
				archivesIdentity: '',
				summaryArchives: 0,
				archivesMeterType: 1,
				accountNumber: '',
				regionCode: '',
				streetCode: '',
				communityCode: '',
				buildingCode: '',
				takeOver: 0,
				addressName: '', // 详细地址
				tapWaterNo: '',
				houseYear: '',
				floorNum: '',
				pressureZone: '',
				gisCode: '',
				pipeNetworkCode: '',
				households: '',
				resiPopulation: '',
				propertyOwner: '',
				contractNum: '',
				businessLicenseUrl: [],
				purchaseContractUrl: [],
				stationNo: '', // 站点号
			},
			formAttrs: {
				labelWidth: '120px',
				rules: {
					archivesIdentity: [ruleRequired('必填'), RULE_INTEGERONLY],
					summaryArchives: [ruleRequired('必填')],
					accountNumber: [ruleMaxLength(32)],
					regionCode: [ruleRequired('必填', 'blur')],
					streetCode: [ruleRequired('必填', 'blur')],
					addressName: [ruleRequired('必填', 'blur'), ruleMaxLength(64)],

					tapWaterNo: [ruleMaxLength(16)],
					houseYear: [ruleMaxLength(16)],
					floorNum: [
						{
							pattern: /^(?:[0-9]{1,4}|9999)$/,
							message: '请输入0-9999的整数',
							trigger: '',
						},
					],
					pressureZone: [ruleMaxLength(32)],
					gisCode: [ruleMaxLength(32)],
					pipeNetworkCode: [ruleMaxLength(32)],
					households: [
						{
							pattern: /^(?:[0-9]{1,2}|30)$/,
							message: '请输入0-30的整数',
							trigger: '',
						},
					],
					resiPopulation: [
						{
							pattern: /^(?:[0-9]{1,2}|64)$/,
							message: '请输入0-64的整数',
							trigger: '',
						},
					],
					propertyOwner: [ruleMaxLength(32)],
					contractNum: [ruleMaxLength(64)],
				},
			},
			addressAreaCode: '', // 街道/小区/乡镇/村庄
			addressFullName: '', // 完整地址
		}
	},
	methods: {
		changeSummaryArchives(v) {
			this.formItems = this.formItems.filter(item => item.prop !== 'archivesMeterType')
			if (v) {
				this.formItems.splice(3, 0, {
					type: 'el-select',
					label: '贸易结算类型',
					prop: 'archivesMeterType',
					options: archivesMeterTypeOptions,
					attrs: {
						col: 12,
						clearable: true,
					},
				})
			} else {
				this.formData.archivesMeterType = 1
			}
		},
		assignForm(key, obj) {
			if (obj && Object.prototype.hasOwnProperty.call(obj, key) && !isBlank(obj[key])) {
				if (['businessLicenseUrl', 'purchaseContractUrl'].includes(key)) {
					this.formData[key] = obj[key] ? JSON.parse(obj[key]) : []
				} else {
					this.formData[key] = obj[key]
				}
			}
		},
		// 页面激活时：地址接口和表单信息更新
		async updateAddress() {
			const { regionCode, streetCode, communityCode, buildingCode } = this.formData || {}
			const promises = []

			promises.push(this._getRegionData())
			if (regionCode) {
				promises.push(this._getAddressAreaMap(regionCode, 'streetCode'))
			}
			if (streetCode) {
				promises.push(this._getAddressAreaMap(streetCode, 'communityCode'))
			}
			if (communityCode) {
				promises.push(this._getAddressAreaMap(communityCode, 'buildingCode'))
			}
			await Promise.all(promises)
			const regionIndex = this.formItems.findIndex(item => item.prop === 'regionCode')
			const streetIndex = this.formItems.findIndex(item => item.prop === 'streetCode')
			const communityIndex = this.formItems.findIndex(item => item.prop === 'communityCode')
			const buildingIndex = this.formItems.findIndex(item => item.prop === 'buildingCode')
			const takeOverIndex = this.formItems.findIndex(item => item.prop == 'takeOver')
			if (regionCode) {
				const ifExist = this.formItems[regionIndex].options.some(item => item.value === regionCode)
				if (!ifExist) {
					this.formData.regionCode = ''
					this.formItems[streetIndex].options = []
					this.formItems[communityIndex].options = []
					this.formItems[buildingIndex].options = []
				}
			}
			if (streetCode) {
				const ifExist = this.formItems[streetIndex].options.some(item => item.value === streetCode)
				if (!ifExist) {
					this.formData.streetCode = ''
					this.formItems[communityIndex].options = []
					this.formItems[buildingIndex].options = []
				}
			}
			if (communityCode) {
				const ifExist = this.formItems[communityIndex].options.some(item => item.value === communityCode)
				if (!ifExist) {
					this.formData.communityCode = ''
					this.formItems[buildingIndex].options = []
					if (takeOverIndex !== -1) {
						this.formItems.splice(takeOverIndex, 1)
					}
				} else if (takeOverIndex === -1) {
					this.formItems.splice(buildingIndex, 0, {
						type: 'el-radio',
						label: '是否接管',
						prop: 'takeOver',
						options: [
							{ label: '未接管', value: 0 },
							{ label: '已接管', value: 1 },
						],
						attrs: {
							col: 12,
							disabled: true,
						},
					})
				}
			}
			if (buildingCode) {
				// 如果增加了是否接管影响buildingIndex
				const newBuildingIndex = this.formItems.findIndex(item => item.prop === 'buildingCode')
				const ifExist = this.formItems[newBuildingIndex].options.some(item => item.value === buildingCode)
				if (!ifExist) {
					this.formData.buildingCode = ''
				}
			}
		},
		// 获取区县数据
		async _getRegionData() {
			const { records } = await apiGetRegion({ regionCode: 2102 })
			const obj = this.formItems.find(item => item.prop === 'regionCode')
			if (!obj) return
			obj.options = records.map(item => {
				return {
					value: item.regionCode,
					label: item.regionName,
				}
			})
		},
		// 获取街道、小区、楼栋下拉数据
		async _getAddressAreaMap(value, key) {
			const data = await apiGetAddressAreaMap({
				parentCode: value,
				status: 1, // 启用
			})
			const obj = this.formItems.find(item => item.prop === key)
			if (!obj) return
			obj.options = data.map(item => {
				let label = item.addressAreaName
				if (key === 'buildingCode') {
					label = label + item.unit
				}
				return {
					value: item.addressAreaCode,
					label,
					...item,
				}
			})
		},
		// 改变地址
		async handleChangeAddress(value, type) {
			let takeOverIndex = this.formItems.findIndex(item => item.prop == 'takeOver')
			if (takeOverIndex !== -1 && type !== 'buildingCode') {
				this.formItems.splice(takeOverIndex, 1)
				takeOverIndex = -1
			}

			const regionIndex = this.formItems.findIndex(item => item.prop === 'regionCode')
			const streetIndex = this.formItems.findIndex(item => item.prop === 'streetCode')
			const communityIndex = this.formItems.findIndex(item => item.prop === 'communityCode')
			const buildingIndex = this.formItems.findIndex(item => item.prop === 'buildingCode')
			const regionObj = this.formItems[regionIndex].options.find(item => item.value === this.formData.regionCode)
			const streetObj = this.formItems[streetIndex].options.find(item => item.value === this.formData.streetCode)
			const communityObj = this.formItems[communityIndex].options.find(
				item => item.value === this.formData.communityCode,
			)
			const buildingObj = this.formItems[buildingIndex].options.find(
				item => item.value === this.formData.buildingCode,
			)
			const regionFullName = regionObj?.label || ''
			const streetFullName = regionFullName + (streetObj?.label || '')
			const communityFullName = streetFullName + (communityObj?.label || '')
			const buildingFullName = communityFullName + (buildingObj?.label || '')

			if (type === 'regionCode') {
				this.formData.streetCode = ''
				this.formData.communityCode = ''
				this.formData.buildingCode = ''
				this.formItems[streetIndex].options = []
				this.formItems[communityIndex].options = []
				this.formItems[buildingIndex].options = []
				if (value) {
					await this._getAddressAreaMap(value, 'streetCode')
					this.addressAreaCode = regionObj?.value || ''
					this.addressFullName = regionFullName
				} else {
					this.addressFullName = ''
					this.addressAreaCode = ''
				}
			} else if (type === 'streetCode') {
				this.formData.communityCode = ''
				this.formData.buildingCode = ''
				this.formItems[communityIndex].options = []
				this.formItems[buildingIndex].options = []
				if (value) {
					await this._getAddressAreaMap(value, 'communityCode')
					this.addressFullName = streetFullName
					this.addressAreaCode = value
				} else {
					this.addressFullName = regionFullName
					this.addressAreaCode = regionObj?.value || ''
				}
			} else if (type === 'communityCode') {
				this.formData.buildingCode = ''
				this.formItems[buildingIndex].options = []
				if (value) {
					if (takeOverIndex === -1) {
						this.formItems.splice(buildingIndex, 0, {
							type: 'el-radio',
							label: '是否接管',
							prop: 'takeOver',
							options: [
								{ label: '未接管', value: 0 },
								{ label: '已接管', value: 1 },
							],
							attrs: {
								col: 12,
								disabled: true,
							},
						})
					}
					await this._getAddressAreaMap(value, 'buildingCode')
					this.addressFullName = communityFullName
					this.addressAreaCode = value
					this.formData.takeOver = communityObj?.takeOver || 0
				} else {
					this.addressFullName = streetFullName
					this.addressAreaCode = streetObj?.value || ''
				}
			} else if (type === 'buildingCode') {
				if (value) {
					this.addressFullName = buildingFullName
					this.addressAreaCode = value
				} else {
					this.addressFullName = communityFullName
					this.addressAreaCode = communityObj?.value || ''
				}
			}
		},
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) {
				this.$message.error('表单信息未完善')
				return
			}
			const newData = Object.assign({}, ...Object.values(this.detailData))
			const params = trimParams(removeNullParams(this.formData))
			const purchaseContractUrl =
				params.purchaseContractUrl && params.purchaseContractUrl.length
					? JSON.stringify(
							params.purchaseContractUrl.map(item => {
								return {
									name: item.name,
									url: item.url,
								}
							}),
					  )
					: ''
			const businessLicenseUrl =
				params.businessLicenseUrl && params.businessLicenseUrl.length
					? JSON.stringify(
							params.businessLicenseUrl.map(item => {
								return {
									name: item.name,
									url: item.url,
								}
							}),
					  )
					: ''

			const residentParams = {
				archives: {
					archivesId: newData.archivesId,
					archivesIdentity: params.archivesIdentity,
					accountNumber: params.accountNumber,
					archivesMeterType: params.archivesMeterType,
					purchaseContractUrl,
					summaryArchives: params.summaryArchives,
					contractNum: params.contractNum,
					households: params.households,
					resiPopulation: params.resiPopulation,
					propertyOwner: params.propertyOwner,
				},
				address: {
					addressId: newData.addressId,
					regionCode: params.regionCode,
					addressAreaCode: this.addressAreaCode,
					addressFullName: this.addressFullName + params.addressName,
					addressName: params.addressName,
					houseYear: params.houseYear,
					floorNum: params.floorNum,
					pressureZone: params.pressureZone,
					gisCode: params.gisCode,
					tapWaterNo: params.tapWaterNo,
					pipeNetworkCode: params.pipeNetworkCode,
				},
			}
			const companyParams = {
				archives: {
					archivesId: newData.archivesId,
					archivesIdentity: params.archivesIdentity,
					accountNumber: params.accountNumber,
					archivesMeterType: params.archivesMeterType,
					businessLicenseUrl,
					purchaseContractUrl,
					summaryArchives: params.summaryArchives,
					contractNum: params.contractNum,
				},
				address: {
					addressId: newData.addressId,
					regionCode: params.regionCode,
					addressAreaCode: this.addressAreaCode,
					addressFullName: this.addressFullName + params.addressName,
					addressName: params.addressName,
					pressureZone: params.pressureZone,
					houseYear: params.houseYear,
					floorNum: params.floorNum,
					gisCode: params.gisCode,
					tapWaterNo: params.tapWaterNo,
					pipeNetworkCode: params.pipeNetworkCode,
				},
			}
			const newParams = newData.userType === 3 ? residentParams : companyParams
			try {
				const apiMethods = {
					cpm_archives_update: apiArchivesUpdate,
					cpm_archives_update1: apiArchivesUpdate1,
				}
				await apiMethods[this.permissionCode](newParams)
				this.$message.success('表卡修改成功')
				this.handleClose()
				this.$emit('refresh')
			} catch (error) {
				console.log(error)
			}
		},
		handleClose() {
			this.isShow = false
		},
	},
}
</script>
