# gcCloudTab 简单使用教学

<gc-detail-tab :tabList="tabList"></gc-detail-tab>

## tabLIst示例如下
## list内的component的参数需要引入组件,name的值需要与component的命名保持一致
#  import basicInfo from "./detailPages/basicInfo";
#  import orgStruct from "./detailPages/orgStruct";
#  import rightAllocation from "./detailPages/rightAllocation";
#  import meterType from "./detailPages/meterType";
#  ...
## 
    tabList: [
      {
        name: "basicInfo",
        label: "基本信息",
        component: basicInfo,
        data: {
          tenantID: "",
        },
      },
      {
        name: "orgStruct",
        label: "组织结构分配",
        component: orgStruct,
        data: {
          text: "啦啦啦",
        },
      },
      {
        name: "rightAllocation",
        label: "权限分配",
        component: rightAllocation,
        data: {
          text: "啦啦啦",
        },
      },
      {
        name: "meterType",
        label: "表类型开通",
        component: meterType,
        data: {
          text: "啦啦啦",
        },
      },
      {
        name: "typeOfDTU",
        label: "DTU类型开通",
        component: typeOfDTU,
        data: {
          text: "啦啦啦",
        },
      },
      {
        name: "operateRecord",
        label: "操作记录",
        component: operateRecord,
        data: {
          text: "啦啦啦",
        },
      },
      {
        name: "msgConfig",
        label: "消息业务配置",
        component: msgConfig,
        data: {
          text: "啦啦啦",
        },
      },
    ],
## 新增defaultActiveName属性，可以设置页面初始默认的tab页，入参为希望展示的tab页的name
### 上面list里面可以看到一个data传参，如果需要给某个tab页面的组件传参就写在这里面，data必须是一个对象，在自定义组件里面props接收，接收时必须要使用下面的声明，因为组件封装的时候用的这个名字，多担待！！！
props: {
    tabData: {
      type: Object,
      default: () => {},
    },
  },

#### 以上就是简单使用啦，有什么疑问随时找我~